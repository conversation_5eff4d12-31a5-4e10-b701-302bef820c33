"use strict";exports.id=4881,exports.ids=[4881],exports.modules={6211:(e,t,a)=>{a.d(t,{A0:()=>i,BF:()=>r,Hj:()=>o,XI:()=>l,nA:()=>c,nd:()=>d});var s=a(60687);a(43210);var n=a(36966);function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",e),...t})}function r({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},14583:(e,t,a)=>{a.d(t,{d:()=>c});var s=a(60687),n=a(29523),l=a(15079),i=a(63826),r=a(47033),o=a(14952),d=a(16145);function c({currentPage:e,pageSize:t,totalCount:a,totalPages:c,isLoading:x=!1,isChangingPageSize:u=!1,pageSizeOptions:m=[10,20,30,40,50],rowsLabel:h="row(s)",isUnknownTotalCount:g=!1,onPageChange:p,onPageSizeChange:f}){let b=u?Math.max(c,Math.ceil(a/t)):c,j=e>1,v=e<b,w=e=>{p(Math.max(1,Math.min(e,b)))},N=t=>{w(e+t)};return(0,s.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,s.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:a>0?(0,s.jsxs)(s.Fragment,{children:[Math.min((e-1)*t+1,a),"-",Math.min(e*t,a)," of ",a," ",h]}):"No results"}),(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,s.jsxs)(l.l6,{value:String(t),onValueChange:e=>f(Number(e)),disabled:x,children:[(0,s.jsx)(l.bq,{className:"h-8 w-[70px]",children:(0,s.jsx)(l.yv,{placeholder:t})}),(0,s.jsx)(l.gC,{side:"top",children:m.map(e=>(0,s.jsx)(l.eb,{value:String(e),children:e},e))})]})]}),(0,s.jsxs)("div",{className:"flex w-[100px] items-center justify-center text-sm font-medium",children:["Page ",e," of ",x||u?"...":g&&v?`${b}+`:b]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(n.$,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>w(1),disabled:!j||x,children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to first page"}),(0,s.jsx)(i.A,{className:"h-4 w-4"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>N(-1),disabled:!j||x,children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to previous page"}),(0,s.jsx)(r.A,{className:"h-4 w-4"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>N(1),disabled:!v||x,children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to next page"}),(0,s.jsx)(o.A,{className:"h-4 w-4"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>w(b),disabled:!v||x,children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to last page"}),(0,s.jsx)(d.A,{className:"h-4 w-4"})]})]})]})]})}},15079:(e,t,a)=>{a.d(t,{bq:()=>x,eb:()=>m,gC:()=>u,l6:()=>d,yv:()=>c});var s=a(60687);a(43210);var n=a(69875),l=a(78272),i=a(13964),r=a(3589),o=a(36966);function d({...e}){return(0,s.jsx)(n.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(n.WT,{"data-slot":"select-value",...e})}function x({className:e,size:t="default",children:a,...i}){return(0,s.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[a,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:t,position:a="popper",...l}){return(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...l,children:[(0,s.jsx)(h,{}),(0,s.jsx)(n.LM,{className:(0,o.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(g,{})]})})}function m({className:e,children:t,...a}){return(0,s.jsxs)(n.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(n.p4,{children:t})]})}function h({className:e,...t}){return(0,s.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(r.A,{className:"size-4"})})}function g({className:e,...t}){return(0,s.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}},34208:(e,t,a)=>{a.d(t,{w:()=>x});var s=a(60687),n=a(89422),l=a(2975),i=a(17971),r=a(12597),o=a(36966),d=a(29523),c=a(21342);function x({column:e,title:t,className:a}){return e.getCanSort()?(0,s.jsx)("div",{className:(0,o.cn)("flex items-center space-x-2",a),children:(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"ghost",size:"sm",className:"-ml-3 h-8 data-[state=open]:bg-accent",children:[(0,s.jsx)("span",{children:t}),(()=>{let t=e.getIsSorted();return"desc"===t?(0,s.jsx)(n.A,{}):"asc"===t?(0,s.jsx)(l.A,{}):(0,s.jsx)(i.A,{})})()]})}),(0,s.jsxs)(c.SQ,{align:"start",children:[(0,s.jsxs)(c._2,{onClick:()=>e.toggleSorting(!1),children:[(0,s.jsx)(l.A,{className:"h-3.5 w-3.5 text-muted-foreground/70"}),"Asc"]}),(0,s.jsxs)(c._2,{onClick:()=>e.toggleSorting(!0),children:[(0,s.jsx)(n.A,{className:"h-3.5 w-3.5 text-muted-foreground/70"}),"Desc"]}),(0,s.jsx)(c.mB,{}),(0,s.jsxs)(c._2,{onClick:()=>e.toggleVisibility(!1),children:[(0,s.jsx)(r.A,{className:"h-3.5 w-3.5 text-muted-foreground/70"}),"Hide"]})]})]})}):(0,s.jsx)("div",{className:(0,o.cn)(a),children:t})}},50723:(e,t,a)=>{a.d(t,{b:()=>o});var s=a(60687),n=a(43210),l=a(56090),i=a(93772),r=a(6211);function o({columns:e,data:t,table:a,onRowClick:o,isLoading:d=!1,totalCount:c}){let[x,u]=n.useState({}),[m,h]=n.useState({}),[g,p]=n.useState([]),[f,b]=n.useState([]),j=(0,l.N4)({data:t,columns:e,state:{sorting:f,columnVisibility:m,rowSelection:x,columnFilters:g},enableRowSelection:!0,onRowSelectionChange:u,onSortingChange:b,onColumnFiltersChange:p,onColumnVisibilityChange:h,getCoreRowModel:(0,i.HT)(),getFilteredRowModel:(0,i.hM)(),getPaginationRowModel:(0,i.kW)(),getSortedRowModel:(0,i.h5)(),getFacetedRowModel:(0,i.kQ)(),getFacetedUniqueValues:(0,i.oS)(),...void 0!==c&&{manualPagination:!0,pageCount:Math.max(1,Math.ceil(c/10))}}),v=a??j;n.useEffect(()=>{if(console.log("DataTable component received totalCount:",c),v){let e=v.getState().pagination.pageSize,t=void 0!==c?Math.max(1,Math.ceil(c/e)):1;console.log("DataTable pagination state:",{totalCount:c,pageSize:e,calculatedPageCount:t,tablePageCount:v.getPageCount(),currentPage:v.getState().pagination.pageIndex+1,pageIndex:v.getState().pagination.pageIndex})}},[c,v]);let w=(e,t)=>{let a=e.target;if(a.closest('[role="dialog"]')||a.closest('[data-state="open"]')||a.closest(".dialog")||a.closest(".dropdown-menu"))return;let s=a.closest("td");if(s){let e=s.getAttribute("data-column-id");if("select"===e||"actions"===e)return}o?.(t)};return(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"rounded-md border relative",children:[d&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-background/80 z-10",children:(0,s.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"})}),(0,s.jsxs)(r.XI,{className:"dark:bg-neutral-900",children:[(0,s.jsx)(r.A0,{children:v.getHeaderGroups().map(e=>(0,s.jsx)(r.Hj,{children:e.headers.map(e=>(0,s.jsx)(r.nd,{colSpan:e.colSpan,children:e.isPlaceholder?null:(0,l.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,s.jsx)(r.BF,{children:v.getRowModel().rows?.length?v.getRowModel().rows.map(e=>(0,s.jsx)(r.Hj,{"data-state":e.getIsSelected()&&"selected",className:o?"cursor-pointer hover:bg-muted/50 ":"",onClick:t=>o&&w(t,e.original),children:e.getVisibleCells().map(e=>{let t="";return"select"===e.column.id?t="w-12 min-w-[48px] max-w-[48px] px-2":"actions"===e.column.id&&(t="w-16 min-w-[60px] max-w-[60px] px-2"),(0,s.jsx)(r.nA,{"data-column-id":e.column.id,className:t,children:(0,l.Kv)(e.column.columnDef.cell,e.getContext())},e.id)})},e.id)):(0,s.jsx)(r.Hj,{children:(0,s.jsx)(r.nA,{colSpan:e.length,className:"h-24 text-center",children:d?"Loading...":"No results."})})})]})]})})}},56896:(e,t,a)=>{a.d(t,{S:()=>r});var s=a(60687);a(43210);var n=a(6945),l=a(13964),i=a(36966);function r({className:e,...t}){return(0,s.jsx)(n.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-orange-600 data-[state=checked]:text-white dark:data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600 focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(l.A,{className:"size-3.5"})})})}}};