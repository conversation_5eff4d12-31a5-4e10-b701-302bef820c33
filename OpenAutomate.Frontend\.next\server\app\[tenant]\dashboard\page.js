(()=>{var e={};e.id=7068,e.ids=[7068],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3633:(e,t,r)=>{"use strict";r.d(t,{StatisticalStatus:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call StatisticalStatus() from the server but StatisticalStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\layout\\statistical-status.tsx","StatisticalStatus")},5780:(e,t,r)=>{Promise.resolve().then(r.bind(r,47961)),Promise.resolve().then(r.bind(r,13521)),Promise.resolve().then(r.bind(r,42811))},10034:(e,t,r)=>{var n=r(2984),a=r(22),o=r(46063);e.exports=function(e,t){return e&&e.length?n(e,a(t,2),o):void 0}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13521:(e,t,r)=>{"use strict";r.d(t,{SectionCards:()=>p});var n=r(60687),a=r(44493),o=r(39582),i=r(63600),s=r(71769),l=r(58036),c=r(37337),u=r(31207),d=r(70891);function p(){let{data:e}=(0,u.Ay)(d.DC.organizationUnits(),()=>(0,i.Bx)({$count:!0,$top:1})),{data:t}=(0,u.Ay)(d.DC.agentsWithOData({$count:!0,$top:1}),()=>(0,o.dT)({$count:!0,$top:1})),{data:r}=(0,u.Ay)(d.DC.assetsWithOData({$count:!0,$top:1}),()=>(0,s.Lm)({$count:!0,$top:1})),{data:p}=(0,u.Ay)(d.DC.schedulesWithOData({$count:!0,$top:1}),()=>(0,l.ye)({$count:!0,$top:1})),{data:f}=(0,u.Ay)(d.DC.packagesWithOData({$count:!0,$top:1}),()=>(0,c.Cb)({$count:!0,$top:1})),h=e?.["@odata.count"]??0,m=t?.["@odata.count"]??0,y=r?.["@odata.count"]??0,g=p?.["@odata.count"]??0,v=f?.["@odata.count"]??0;return(0,n.jsxs)("div",{className:" *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-5 grid grid-cols-1 gap-4 *:data-[slot=card]:bg-white  dark:*:data-[slot=card]:bg-neutral-900 ",children:[(0,n.jsxs)(a.Zp,{className:"@container/card",children:[(0,n.jsxs)(a.aR,{className:"relative",children:[(0,n.jsx)(a.ZB,{children:"Users"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(a.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:h.toLocaleString()})})]}),(0,n.jsx)(a.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:" Total organization users"})})]}),(0,n.jsxs)(a.Zp,{className:"@container/card",children:[(0,n.jsxs)(a.aR,{className:"relative",children:[(0,n.jsx)(a.ZB,{children:"Agents"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(a.BT,{className:"@[250px]/card:text-3xl text-orange-600  text-2xl font-semibold tabular-nums",children:m.toLocaleString()})})]}),(0,n.jsx)(a.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:" Total active agents"})})]}),(0,n.jsxs)(a.Zp,{className:"@container/card",children:[(0,n.jsxs)(a.aR,{className:"relative",children:[(0,n.jsx)(a.ZB,{children:"Assets"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(a.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:y.toLocaleString()})})]}),(0,n.jsx)(a.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Resources under control"})})]}),(0,n.jsxs)(a.Zp,{className:"@container/card",children:[(0,n.jsxs)(a.aR,{className:"relative",children:[(0,n.jsx)(a.ZB,{children:"Schedules"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(a.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:g.toLocaleString()})})]}),(0,n.jsx)(a.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Scheduled tasks"})})]}),(0,n.jsxs)(a.Zp,{className:"@container/card",children:[(0,n.jsxs)(a.aR,{className:"relative",children:[(0,n.jsx)(a.ZB,{children:"Packages"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(a.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:v.toLocaleString()})})]}),(0,n.jsx)(a.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Automation packages"})})]})]})}},14162:(e,t,r)=>{Promise.resolve().then(r.bind(r,93971)),Promise.resolve().then(r.bind(r,64539)),Promise.resolve().then(r.bind(r,3633))},17687:(e,t,r)=>{"use strict";r.d(t,{II:()=>f,Nt:()=>h,at:()=>d});var n=r(60687),a=r(43210),o=r(48482),i=r(38246),s=r(57359),l=r(36966);let c={light:"",dark:".dark"},u=a.createContext(null);function d({id:e,className:t,children:r,config:i,...s}){let c=a.useId(),d=`chart-${e||c.replace(/:/g,"")}`;return(0,n.jsx)(u.Provider,{value:{config:i},children:(0,n.jsxs)("div",{"data-slot":"chart","data-chart":d,className:(0,l.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",t),...s,children:[(0,n.jsx)(p,{id:d,config:i}),(0,n.jsx)(o.u,{children:r})]})})}let p=({id:e,config:t})=>{let r=Object.entries(t).filter(([,e])=>e.theme||e.color);return r.length?(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(c).map(([t,n])=>`
${n} [data-chart=${e}] {
${r.map(([e,r])=>{let n=r.theme?.[t]||r.color;return n?`  --color-${e}: ${n};`:null}).join("\n")}
}
`).join("\n")}}):null},f=i.m;function h({active:e,payload:t,className:r,indicator:o="dot",hideLabel:i=!1,hideIndicator:s=!1,label:c,labelFormatter:d,labelClassName:p,formatter:f,color:h,nameKey:y,labelKey:g}){let{config:v}=function(){let e=a.useContext(u);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),b=a.useMemo(()=>{if(i||!t?.length)return null;let[e]=t,r=`${g||e?.dataKey||e?.name||"value"}`,a=m(v,e,r),o=g||"string"!=typeof c?a?.label:v[c]?.label||c;return d?(0,n.jsx)("div",{className:(0,l.cn)("font-medium",p),children:d(o,t)}):o?(0,n.jsx)("div",{className:(0,l.cn)("font-medium",p),children:o}):null},[c,d,t,i,p,v,g]);if(!e||!t?.length)return null;let x=1===t.length&&"dot"!==o;return(0,n.jsxs)("div",{className:(0,l.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",r),children:[x?null:b,(0,n.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${y||e.name||e.dataKey||"value"}`,a=m(v,e,r),i=h||e.payload.fill||e.color;return(0,n.jsx)("div",{className:(0,l.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===o&&"items-center"),children:f&&e?.value!==void 0&&e.name?f(e.value,e.name,e,t,e.payload):(0,n.jsxs)(n.Fragment,{children:[a?.icon?(0,n.jsx)(a.icon,{}):!s&&(0,n.jsx)("div",{className:(0,l.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===o,"w-1":"line"===o,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===o,"my-0.5":x&&"dashed"===o}),style:{"--color-bg":i,"--color-border":i}}),(0,n.jsxs)("div",{className:(0,l.cn)("flex flex-1 justify-between leading-none",x?"items-end":"items-center"),children:[(0,n.jsxs)("div",{className:"grid gap-1.5",children:[x?b:null,(0,n.jsx)("span",{className:"text-muted-foreground",children:a?.label||e.name})]}),e.value&&(0,n.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function m(e,t,r){if("object"!=typeof t||null===t)return;let n="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,a=r;return r in t&&"string"==typeof t[r]?a=t[r]:n&&r in n&&"string"==typeof n[r]&&(a=n[r]),a in e?e[a]:e[r]}s.s},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31599:(e,t,r)=>{"use strict";r.d(t,{c:()=>l});var n=r(43210),a=r(39989),o=r(16189),i=r(31207),s=r(70891);function l(){let e=(0,o.useRouter)(),{data:t,error:r,isLoading:l,mutate:c}=(0,i.Ay)(s.DC.organizationUnits(),()=>a.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:l,error:r?"Failed to fetch organization units. Please try again later.":null,refresh:c,selectOrganizationUnit:(0,n.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},37337:(e,t,r)=>{"use strict";r.d(t,{AW:()=>u,Cb:()=>d,QQ:()=>l,ae:()=>o,jm:()=>c,oy:()=>s,s9:()=>i});var n=r(51787);let a=()=>"default",o=async e=>{let t=a();return await n.F.get(`${t}/api/packages/${e}`)},i=async()=>{let e=a();return await n.F.get(`${e}/api/packages`)},s=async e=>{let t=a(),r=new FormData;return r.append("file",e.file),e.name&&r.append("name",e.name),e.description&&r.append("description",e.description),e.version&&r.append("version",e.version),await n.F.post(`${t}/api/packages/upload`,r)},l=async(e,t)=>{let r=a();return await n.F.get(`${r}/api/packages/${e}/versions/${t}/download`)},c=async e=>{let t=a();await n.F.delete(`${t}/api/packages/${e}`)},u=async(e,t)=>{let r=a();await n.F.delete(`${r}/api/packages/${e}/versions/${t}`)},d=async e=>{let t=a(),r=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,r])=>{null!=r&&"$count"!==e&&t.append(e,String(r))}),t.toString()}(e),o=`${t}/odata/AutomationPackages`;r&&(o+=`?${r}`),console.log("OData query endpoint:",o);try{let e=await n.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} packages from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let r=t[0];console.log(`Found array property "${r}" in response`);let n=e[r],a=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof a?a:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},39582:(e,t,r)=>{"use strict";r.d(t,{NA:()=>s,Qk:()=>u,Ri:()=>i,dT:()=>l,kz:()=>c,xR:()=>o});var n=r(51787);let a=()=>"default",o=async e=>{let t=a();return await n.F.post(`${t}/api/agents/create`,e)},i=async e=>{let t=a();return await n.F.get(`${t}/api/agents/${e}`)},s=async()=>{let e=a();return await n.F.get(`${e}/api/agents`)},l=async e=>{let t=a(),r=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,r])=>{null!=r&&"$count"!==e&&t.append(e,String(r))}),t.toString()}(e),o=`${t}/odata/BotAgents`;r&&(o+=`?${r}`),console.log("OData query endpoint:",o);try{let e=await n.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} items from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let r=t[0];console.log(`Found array property "${r}" in response`);let n=e[r],a=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof a?a:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching agents with OData:",e),{value:[]}}},c=async e=>{let t=a();await n.F.delete(`${t}/api/agents/${e}`)},u=async(e,t)=>{let r=a();return await n.F.put(`${r}/api/agents/${e}`,t)}},39989:(e,t,r)=>{"use strict";r.d(t,{K:()=>a});var n=r(51787);let a={getMyOrganizationUnits:async()=>await n.F.get("/api/ou/my-ous"),getBySlug:async e=>await n.F.get(`/api/ou/slug/${e}`),getById:async e=>await n.F.get(`/api/ou/${e}`),create:async e=>await n.F.post("/api/ou/create",e),update:async(e,t)=>await n.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await n.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await n.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await n.F.get(`/api/ou/${e}/deletion-status`)}},42811:(e,t,r)=>{"use strict";r.d(t,{StatisticalStatus:()=>d});var n=r(60687),a=r(96882),o=r(44493),i=r(90631),s=r(70891),l=r(31207),c=r(43210);let u=e=>{switch(e){case"Running":return"border-blue-500 text-blue-700 bg-blue-50 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-700";case"Pending":return"border-amber-500 text-amber-700 bg-amber-50 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-700";case"Completed":return"border-emerald-500 text-emerald-700 bg-emerald-50 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-700";case"Failed":return"border-red-500 text-red-700 bg-red-50 dark:bg-red-950 dark:text-red-300 dark:border-red-700";default:return"border-gray-500 text-gray-700 bg-gray-50 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-700"}};function d(){let{data:e}=(0,l.Ay)(s.DC.executionsWithOData({$count:!0,$top:1e3}),()=>(0,i.bF)({$count:!0,$top:1e3})),t=(0,c.useMemo)(()=>{if(!e?.value)return[{label:"Running",count:0},{label:"Pending",count:0},{label:"Completed",count:0},{label:"Failed",count:0}];let t=e.value,r={Running:0,Pending:0,Completed:0,Failed:0};return t.forEach(e=>{let t=e.status;"Running"===t?r.Running++:"Pending"===t?r.Pending++:"Completed"===t?r.Completed++:"Failed"===t&&r.Failed++}),[{label:"Running",count:r.Running},{label:"Pending",count:r.Pending},{label:"Completed",count:r.Completed},{label:"Failed",count:r.Failed}]},[e]);return(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4 *:data-[slot=card]:bg-white   dark:*:data-[slot=card]:bg-neutral-900",children:(0,n.jsxs)(o.Zp,{className:"flex flex-col h-full flex-1",children:[(0,n.jsx)(o.aR,{className:"items-center pb-4",children:(0,n.jsxs)(o.ZB,{className:"flex items-center justify-between text-lg font-medium w-full",children:[(0,n.jsx)("span",{children:"Execution Status"}),(0,n.jsx)(a.A,{className:"w-5 h-5 text-muted-foreground"})]})}),(0,n.jsx)(o.Wu,{className:"flex-1 pb-4 ",children:(0,n.jsx)("div",{className:"grid grid-cols-2 gap-3",children:t.map(e=>(0,n.jsx)(o.Zp,{className:`border-0 shadow-sm hover:shadow-md transition-shadow duration-200 ${u(e.label)}`,children:(0,n.jsxs)(o.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-3",children:[(0,n.jsx)("div",{className:"px-3 py-1.5 rounded-full text-xs font-semibold uppercase tracking-wide ",children:e.label}),(0,n.jsx)("div",{className:"text-3xl font-bold text-foreground",children:e.count.toLocaleString()}),(0,n.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.label.toLowerCase()," jobs"]})]})},e.label))})})]})})}},47961:(e,t,r)=>{"use strict";r.d(t,{ChartPieLabel:()=>eL});var n=r(60687),a=r(25541),o=r(92491),i=r(43210),s=r.n(i),l=r(5231),c=r.n(l),u=r(49384),d=r(98986),p=r(95530),f=r(54186),h=["points","className","baseLinePoints","connectNulls"];function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function y(e){return function(e){if(Array.isArray(e))return g(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return g(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var v=function(e){return e&&e.x===+e.x&&e.y===+e.y},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){v(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),v(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},x=function(e,t){var r=b(e);t&&(r=[r.reduce(function(e,t){return[].concat(y(e),y(t))},[])]);var n=r.map(function(e){return e.reduce(function(e,t,r){return"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},j=function(e,t,r){var n=x(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(x(t.reverse(),r).slice(1))},A=function(e){var t=e.points,r=e.className,n=e.baseLinePoints,a=e.connectNulls,o=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,h);if(!t||!t.length)return null;var i=(0,u.A)("recharts-polygon",r);if(n&&n.length){var l=o.stroke&&"none"!==o.stroke,c=j(t,n,a);return s().createElement("g",{className:i},s().createElement("path",m({},(0,f.J9)(o,!0),{fill:"Z"===c.slice(-1)?o.fill:"none",stroke:"none",d:c})),l?s().createElement("path",m({},(0,f.J9)(o,!0),{fill:"none",d:x(t,a)})):null,l?s().createElement("path",m({},(0,f.J9)(o,!0),{fill:"none",d:x(n,a)})):null)}var d=x(t,a);return s().createElement("path",m({},(0,f.J9)(o,!0),{fill:"Z"===d.slice(-1)?o.fill:"none",className:i,d:d}))},k=r(23561),w=r(4057),O=r(19335);function P(e){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $(){return($=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){T(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function E(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,D(n.key),n)}}function C(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(C=function(){return!!e})()}function F(e){return(F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function R(e,t){return(R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function T(e,t,r){return(t=D(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function D(e){var t=function(e,t){if("object"!=P(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=P(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==P(t)?t:t+""}var L=Math.PI/180,I=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=F(e),function(e,t){if(t&&("object"===P(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,C()?Reflect.construct(e,t||[],F(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&R(e,t)}(n,e),t=[{key:"getTickLineCoord",value:function(e){var t=this.props,r=t.cx,n=t.cy,a=t.radius,o=t.orientation,i=t.tickSize,s=(0,O.IZ)(r,n,a,e.coordinate),l=(0,O.IZ)(r,n,a+("inner"===o?-1:1)*(i||8),e.coordinate);return{x1:s.x,y1:s.y,x2:l.x,y2:l.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,r=Math.cos(-e.coordinate*L);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.radius,a=e.axisLine,o=e.axisLineType,i=N(N({},(0,f.J9)(this.props,!1)),{},{fill:"none"},(0,f.J9)(a,!1));if("circle"===o)return s().createElement(p.c,$({className:"recharts-polar-angle-axis-line"},i,{cx:t,cy:r,r:n}));var l=this.props.ticks.map(function(e){return(0,O.IZ)(t,r,n,e.coordinate)});return s().createElement(A,$({className:"recharts-polar-angle-axis-line"},i,{points:l}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,a=t.tick,o=t.tickLine,i=t.tickFormatter,l=t.stroke,c=(0,f.J9)(this.props,!1),p=(0,f.J9)(a,!1),h=N(N({},c),{},{fill:"none"},(0,f.J9)(o,!1)),m=r.map(function(t,r){var f=e.getTickLineCoord(t),m=N(N(N({textAnchor:e.getTickTextAnchor(t)},c),{},{stroke:"none",fill:l},p),{},{index:r,payload:t,x:f.x2,y:f.y2});return s().createElement(d.W,$({className:(0,u.A)("recharts-polar-angle-axis-tick",(0,O.Zk)(a)),key:"tick-".concat(t.coordinate)},(0,w.XC)(e.props,t,r)),o&&s().createElement("line",$({className:"recharts-polar-angle-axis-tick-line"},h,f)),a&&n.renderTickItem(a,m,i?i(t.value,r):t.value))});return s().createElement(d.W,{className:"recharts-polar-angle-axis-ticks"},m)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.radius,n=e.axisLine;return!(r<=0)&&t&&t.length?s().createElement(d.W,{className:(0,u.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return s().isValidElement(e)?s().cloneElement(e,t):c()(e)?e(t):s().createElement(k.E,$({},t,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],t&&E(n.prototype,t),r&&E(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);T(I,"displayName","PolarAngleAxis"),T(I,"axisType","angleAxis"),T(I,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var _=r(57088),B=r.n(_),Z=r(10034),M=r.n(Z),U=r(97633),W=["cx","cy","angle","ticks","axisLine"],K=["ticks","tick","angle","tickFormatter","stroke"];function G(e){return(G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function J(){return(J=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?q(Object(r),!0).forEach(function(t){ee(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function H(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function V(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,et(n.key),n)}}function X(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(X=function(){return!!e})()}function Q(e){return(Q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Y(e,t){return(Y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ee(e,t,r){return(t=et(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function et(e){var t=function(e,t){if("object"!=G(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=G(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==G(t)?t:t+""}var er=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=Q(e),function(e,t){if(t&&("object"===G(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,X()?Reflect.construct(e,t||[],Q(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Y(e,t)}(n,e),t=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,r=this.props,n=r.angle,a=r.cx,o=r.cy;return(0,O.IZ)(a,o,t,n)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,a=e.ticks,o=B()(a,function(e){return e.coordinate||0});return{cx:t,cy:r,startAngle:n,endAngle:n,innerRadius:M()(a,function(e){return e.coordinate||0}).coordinate||0,outerRadius:o.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,a=e.ticks,o=e.axisLine,i=H(e,W),l=a.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),c=(0,O.IZ)(t,r,l[0],n),u=(0,O.IZ)(t,r,l[1],n),d=z(z(z({},(0,f.J9)(i,!1)),{},{fill:"none"},(0,f.J9)(o,!1)),{},{x1:c.x,y1:c.y,x2:u.x,y2:u.y});return s().createElement("line",J({className:"recharts-polar-radius-axis-line"},d))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,a=t.tick,o=t.angle,i=t.tickFormatter,l=t.stroke,c=H(t,K),p=this.getTickTextAnchor(),h=(0,f.J9)(c,!1),m=(0,f.J9)(a,!1),y=r.map(function(t,r){var c=e.getTickValueCoord(t),f=z(z(z(z({textAnchor:p,transform:"rotate(".concat(90-o,", ").concat(c.x,", ").concat(c.y,")")},h),{},{stroke:"none",fill:l},m),{},{index:r},c),{},{payload:t});return s().createElement(d.W,J({className:(0,u.A)("recharts-polar-radius-axis-tick",(0,O.Zk)(a)),key:"tick-".concat(t.coordinate)},(0,w.XC)(e.props,t,r)),n.renderTickItem(a,f,i?i(t.value,r):t.value))});return s().createElement(d.W,{className:"recharts-polar-radius-axis-ticks"},y)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.axisLine,n=e.tick;return t&&t.length?s().createElement(d.W,{className:(0,u.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),U.J.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return s().isValidElement(e)?s().cloneElement(e,t):c()(e)?e(t):s().createElement(k.E,J({},t,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],t&&V(n.prototype,t),r&&V(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);ee(er,"displayName","PolarRadiusAxis"),ee(er,"axisType","radiusAxis"),ee(er,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var en=r(93492),ea=r(40491),eo=r.n(ea),ei=r(71967),es=r.n(ei),el=r(37456),ec=r.n(el),eu=r(81888),ed=r(98845),ep=r(25679),ef=r(20237),eh=r(22989),em=r(30087),ey=r(10521),eg=r(67629);function ev(e){return(ev="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eb(){return(eb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ex(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ej(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ex(Object(r),!0).forEach(function(t){eP(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ex(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eA(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,e$(n.key),n)}}function ek(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ek=function(){return!!e})()}function ew(e){return(ew=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eO(e,t){return(eO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eP(e,t,r){return(t=e$(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function e$(e){var t=function(e,t){if("object"!=ev(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ev(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ev(t)?t:t+""}var eS=function(e){var t,r;function n(e){var t,r,a;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,a=[e],r=ew(r),eP(t=function(e,t){if(t&&("object"===ev(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,ek()?Reflect.construct(r,a||[],ew(this).constructor):r.apply(this,a)),"pieRef",null),eP(t,"sectorRefs",[]),eP(t,"id",(0,eh.NF)("recharts-pie-")),eP(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),c()(e)&&e()}),eP(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),c()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eO(e,t)}(n,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,a=t.labelLine,o=t.dataKey,i=t.valueKey,l=(0,f.J9)(this.props,!1),c=(0,f.J9)(r,!1),u=(0,f.J9)(a,!1),p=r&&r.offsetRadius||20,h=e.map(function(e,t){var f=(e.startAngle+e.endAngle)/2,h=(0,O.IZ)(e.cx,e.cy,e.outerRadius+p,f),m=ej(ej(ej(ej({},l),e),{},{stroke:"none"},c),{},{index:t,textAnchor:n.getTextAnchor(h.x,e.cx)},h),y=ej(ej(ej(ej({},l),e),{},{fill:"none",stroke:e.fill},u),{},{index:t,points:[(0,O.IZ)(e.cx,e.cy,e.outerRadius,f),h]}),g=o;return ec()(o)&&ec()(i)?g="value":ec()(o)&&(g=i),s().createElement(d.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},a&&n.renderLabelLineItem(a,y,"line"),n.renderLabelItem(r,m,(0,em.kr)(e,g)))});return s().createElement(d.W,{className:"recharts-pie-labels"},h)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,n=r.activeShape,a=r.blendStroke,o=r.inactiveShape;return e.map(function(r,i){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var l=t.isActiveIndex(i),c=o&&t.hasActiveIndex()?o:null,u=ej(ej({},r),{},{stroke:a?r.fill:r.stroke,tabIndex:-1});return s().createElement(d.W,eb({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},(0,w.XC)(t.props,r,i),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(i)}),s().createElement(eg.yp,eb({option:l?n:c,isActive:l,shapeType:"sector"},u)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,n=t.isAnimationActive,a=t.animationBegin,o=t.animationDuration,i=t.animationEasing,l=t.animationId,c=this.state,u=c.prevSectors,p=c.prevIsAnimationActive;return s().createElement(en.Ay,{begin:a,duration:o,isActive:n,easing:i,from:{t:0},to:{t:1},key:"pie-".concat(l,"-").concat(p),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var n=t.t,a=[],o=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=u&&u[t],i=t>0?eo()(e,"paddingAngle",0):0;if(r){var s=(0,eh.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),l=ej(ej({},e),{},{startAngle:o+i,endAngle:o+s(n)+i});a.push(l),o=l.endAngle}else{var c=e.endAngle,d=e.startAngle,p=(0,eh.Dj)(0,c-d)(n),f=ej(ej({},e),{},{startAngle:o+i,endAngle:o+p+i});a.push(f),o=f.endAngle}}),s().createElement(d.W,null,e.renderSectorsStatically(a))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var n=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,n=this.state.prevSectors;return r&&t&&t.length&&(!n||!es()(n,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,n=t.sectors,a=t.className,o=t.label,i=t.cx,l=t.cy,c=t.innerRadius,p=t.outerRadius,f=t.isAnimationActive,h=this.state.isAnimationFinished;if(r||!n||!n.length||!(0,eh.Et)(i)||!(0,eh.Et)(l)||!(0,eh.Et)(c)||!(0,eh.Et)(p))return null;var m=(0,u.A)("recharts-pie",a);return s().createElement(d.W,{tabIndex:this.props.rootTabIndex,className:m,ref:function(t){e.pieRef=t}},this.renderSectors(),o&&this.renderLabels(n),U.J.renderCallByParent(this.props,null,!1),(!f||h)&&ed.Z.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(s().isValidElement(e))return s().cloneElement(e,t);if(c()(e))return e(t);var n=(0,u.A)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return s().createElement(eu.I,eb({},t,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(e,t,r){if(s().isValidElement(e))return s().cloneElement(e,t);var n=r;if(c()(e)&&(n=e(t),s().isValidElement(n)))return n;var a=(0,u.A)("recharts-pie-label-text","boolean"==typeof e||c()(e)?"":e.className);return s().createElement(k.E,eb({},t,{alignmentBaseline:"middle",className:a}),n)}}],t&&eA(n.prototype,t),r&&eA(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);eP(eS,"displayName","Pie"),eP(eS,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!ef.m.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),eP(eS,"parseDeltaAngle",function(e,t){return(0,eh.sA)(t-e)*Math.min(Math.abs(t-e),360)}),eP(eS,"getRealPieData",function(e){var t=e.data,r=e.children,n=(0,f.J9)(e,!1),a=(0,f.aS)(r,ep.f);return t&&t.length?t.map(function(e,t){return ej(ej(ej({payload:e},n),e),a&&a[t]&&a[t].props)}):a&&a.length?a.map(function(e){return ej(ej({},n),e.props)}):[]}),eP(eS,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,a=t.width,o=t.height,i=(0,O.lY)(a,o);return{cx:n+(0,eh.F4)(e.cx,a,a/2),cy:r+(0,eh.F4)(e.cy,o,o/2),innerRadius:(0,eh.F4)(e.innerRadius,i,0),outerRadius:(0,eh.F4)(e.outerRadius,i,.8*i),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}}),eP(eS,"getComposedData",function(e){var t,r,n=e.item,a=e.offset,o=void 0!==n.type.defaultProps?ej(ej({},n.type.defaultProps),n.props):n.props,i=eS.getRealPieData(o);if(!i||!i.length)return null;var s=o.cornerRadius,l=o.startAngle,c=o.endAngle,u=o.paddingAngle,d=o.dataKey,p=o.nameKey,f=o.valueKey,h=o.tooltipType,m=Math.abs(o.minAngle),y=eS.parseCoordinateOfPie(o,a),g=eS.parseDeltaAngle(l,c),v=Math.abs(g),b=d;ec()(d)&&ec()(f)?((0,ey.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b="value"):ec()(d)&&((0,ey.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b=f);var x=i.filter(function(e){return 0!==(0,em.kr)(e,b,0)}).length,j=v-x*m-(v>=360?x:x-1)*u,A=i.reduce(function(e,t){var r=(0,em.kr)(t,b,0);return e+((0,eh.Et)(r)?r:0)},0);return A>0&&(t=i.map(function(e,t){var n,a=(0,em.kr)(e,b,0),o=(0,em.kr)(e,p,t),i=((0,eh.Et)(a)?a:0)/A,c=(n=t?r.endAngle+(0,eh.sA)(g)*u*+(0!==a):l)+(0,eh.sA)(g)*((0!==a?m:0)+i*j),d=(n+c)/2,f=(y.innerRadius+y.outerRadius)/2,v=[{name:o,value:a,payload:e,dataKey:b,type:h}],x=(0,O.IZ)(y.cx,y.cy,f,d);return r=ej(ej(ej({percent:i,cornerRadius:s,name:o,tooltipPayload:v,midAngle:d,middleRadius:f,tooltipPosition:x},e),y),{},{value:(0,em.kr)(e,b),startAngle:n,endAngle:c,payload:e,paddingAngle:(0,eh.sA)(g)*u})})),ej(ej({},y),{},{sectors:t,data:i})});var eN=(0,o.gu)({chartName:"PieChart",GraphicalChild:eS,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:I},{axisType:"radiusAxis",AxisComp:er}],formatAxisMap:O.pr,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),eE=r(90631),eC=r(70891),eF=r(31207),eR=r(44493),eT=r(17687);let eD={executions:{label:"Executions"},running:{label:"Running",color:"hsl(var(--chart-1))"},pending:{label:"Pending",color:"hsl(var(--chart-2))"},completed:{label:"Completed",color:"hsl(var(--chart-3))"},failed:{label:"Failed",color:"hsl(var(--chart-4))"}};function eL(){let{data:e}=(0,eF.Ay)(eC.DC.executionsWithOData({$count:!0,$top:1e3}),()=>(0,eE.bF)({$count:!0,$top:1e3})),t=(0,i.useMemo)(()=>{if(!e?.value)return[{status:"running",count:0,fill:"hsl(var(--chart-1))"},{status:"pending",count:0,fill:"hsl(var(--chart-2))"},{status:"completed",count:0,fill:"hsl(var(--chart-3))"},{status:"failed",count:0,fill:"hsl(var(--chart-4))"}];let t=e.value,r={running:0,pending:0,completed:0,failed:0};return t.forEach(e=>{let t=e.status.toLowerCase();"running"===t?r.running++:"pending"===t?r.pending++:"completed"===t?r.completed++:"failed"===t&&r.failed++}),[{status:"running",count:r.running,fill:"hsl(var(--chart-1))"},{status:"pending",count:r.pending,fill:"hsl(var(--chart-2))"},{status:"completed",count:r.completed,fill:"hsl(var(--chart-3))"},{status:"failed",count:r.failed,fill:"hsl(var(--chart-4))"}].filter(e=>e.count>0)},[e]),r=t.reduce((e,t)=>e+t.count,0),o=t.find(e=>"completed"===e.status)?.count||0,s=r>0?(o/r*100).toFixed(1):"0";return(0,n.jsx)("div",{className:"  grid grid-cols-1 gap-4  dark:*:data-[slot=card]:bg-neutral-900",children:(0,n.jsxs)(eR.Zp,{className:"flex flex-col  h-full flex-1",children:[(0,n.jsxs)(eR.aR,{className:"items-center pb-0",children:[(0,n.jsx)(eR.ZB,{children:"Execution Status"}),(0,n.jsx)(eR.BT,{children:"Real-time execution distribution"})]}),(0,n.jsx)(eR.Wu,{className:"flex-1 pb-0 ",children:(0,n.jsx)(eT.at,{config:eD,className:"mx-auto aspect-square max-h-[350px]",children:(0,n.jsxs)(eN,{children:[(0,n.jsx)(eT.II,{content:(0,n.jsx)(eT.Nt,{hideLabel:!0})}),(0,n.jsx)(eS,{data:t,dataKey:"count",label:!0,nameKey:"status"})]})})}),(0,n.jsx)(eR.wL,{className:"flex-col gap-2 text-sm",children:(0,n.jsxs)("div",{className:"flex items-center gap-2 font-medium leading-none",children:[s,"% success rate (",r," total) ",(0,n.jsx)(a.A,{className:"h-7 w-4"})]})})]})})}},57088:(e,t,r)=>{var n=r(2984),a=r(99180),o=r(22);e.exports=function(e,t){return e&&e.length?n(e,o(t,2),a):void 0}},58036:(e,t,r)=>{"use strict";r.d(t,{Fs:()=>c,H4:()=>d,MK:()=>h,Os:()=>s,V5:()=>a,VD:()=>u,g8:()=>p,po:()=>f,sF:()=>i,ye:()=>l});var n=r(51787),a=function(e){return e.Once="Once",e.Minutes="Minutes",e.Hourly="Hourly",e.Daily="Daily",e.Weekly="Weekly",e.Monthly="Monthly",e.Advanced="Advanced",e}({});let o=()=>"default",i=async e=>{let t=o();return await n.F.post(`${t}/api/schedules`,e)},s=async()=>{let e=o();try{return await n.F.get(`${e}/api/schedules`)}catch(e){return console.error("Error fetching all schedules:",e),[]}},l=async e=>{let t=o(),r={...e};(void 0===r.$top||r.$top<=0)&&(r.$top=10);let a=new Date().getTime(),i=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,r])=>{null!=r&&"$count"!==e&&t.append(e,String(r))}),t.toString()}(r),s=`${t}/odata/Schedules`;i?s+=`?${i}&_t=${a}`:s+=`?_t=${a}`,console.log(`Fetching schedules with endpoint: ${s}`),console.log(`Page: ${r.$skip?r.$skip/r.$top+1:1}, Size: ${r.$top}`);try{let e=await n.F.get(s),t=function(e){return e&&"object"==typeof e?{value:Array.isArray(e.value)?e.value:[],"@odata.count":"number"==typeof e["@odata.count"]?e["@odata.count"]:void 0,"@odata.nextLink":"string"==typeof e["@odata.nextLink"]?e["@odata.nextLink"]:void 0}:{value:[]}}(e);return r.$top&&t.value.length>r.$top&&(console.warn(`OData returned ${t.value.length} items but only ${r.$top} were requested. Trimming results.`),t.value=t.value.slice(0,r.$top)),console.log(`Received ${t.value.length} schedules from OData`),t}catch(e){return console.error("Error fetching schedules with OData:",e),{value:[]}}},c=async(e,t)=>{let r=o();return await n.F.put(`${r}/api/schedules/${e}`,t)},u=async e=>{let t=o();await n.F.delete(`${t}/api/schedules/${e}`)},d=async e=>{let t=o();return await n.F.post(`${t}/api/schedules/${e}/enable`)},p=async e=>{let t=o();return await n.F.post(`${t}/api/schedules/${e}/disable`)},f=e=>{if(!e)return"Not scheduled";try{let t=new Date(e);if(isNaN(t.getTime()))return"Invalid date";return new Intl.DateTimeFormat("en-US",{dateStyle:"medium",timeStyle:"short"}).format(t)}catch{return"Invalid date"}},h=e=>{switch(e){case"Once":return"Once";case"Minutes":return"Every few minutes";case"Hourly":return"Hourly";case"Daily":return"Daily";case"Weekly":return"Weekly";case"Monthly":return"Monthly";case"Advanced":return"Custom (Cron)";default:return"Unknown"}}},61018:(e,t,r)=>{"use strict";r.d(t,{TenantGuard:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63600:(e,t,r)=>{"use strict";r.d(t,{Bx:()=>o,Gg:()=>s,H1:()=>i});var n=r(51787);function a(){return console.log("Using default tenant"),"default"}let o=async e=>{let t=a(),r=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,String(r))}),t.toString()}(e),o=`${t}/odata/OrganizationUnitUsers`;return r&&(o+=`?${r}`),function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:{value:[]}}(await n.F.get(o))},i={getUsers:async e=>(await n.F.get(`${e}/api/ou/users`)).users,getRolesInOrganizationUnit:async e=>n.F.get(`${e}/api/ou/users/roles`),assignRolesBulk:async(e,t)=>{let r=a(),o=t.map(e=>e.trim()),i=`${r}/api/author/user/${e}/assign-multiple-roles`;try{return await n.F.post(i,{authorityIds:o})}catch(e){throw console.error("Error assigning roles:",e),e}}},s=async e=>{let t=a();await n.F.delete(`${t}/api/ou/users/${e}`)}},64539:(e,t,r)=>{"use strict";r.d(t,{SectionCards:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call SectionCards() from the server but SectionCards is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\layout\\section-card\\section-cards.tsx","SectionCards")},64656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(37413),a=r(48974),o=r(31057),i=r(50417),s=r(92588),l=r(61018),c=r(2505);function u({children:e}){return(0,n.jsx)(l.TenantGuard,{children:(0,n.jsx)(c.ChatProvider,{children:(0,n.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,n.jsxs)(i.SidebarProvider,{className:"flex flex-col",children:[(0,n.jsx)(o.SiteHeader,{}),(0,n.jsxs)("div",{className:"flex flex-1",children:[(0,n.jsx)(a.AppSidebar,{}),(0,n.jsx)(i.SidebarInset,{children:(0,n.jsx)(s.SearchProvider,{children:(0,n.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,r)=>{"use strict";r.d(t,{TenantGuard:()=>l});var n=r(60687),a=r(43210),o=r(16189),i=r(31599),s=r(31568);function l({children:e}){let{tenant:t}=(0,o.useParams)();(0,o.useRouter)();let{isAuthenticated:r,isLoading:l}=(0,s.A)(),{organizationUnits:c,isLoading:u}=(0,i.c)(),[d,p]=(0,a.useState)(!0);return l||u||d?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,n.jsx)(n.Fragment,{children:e})}},71769:(e,t,r)=>{"use strict";r.d(t,{$o:()=>o,Lm:()=>l,NH:()=>s,deleteAsset:()=>c,gT:()=>i,j0:()=>d,mK:()=>p,qi:()=>u});var n=r(51787);let a=()=>"default",o=async e=>{let t=a();return n.F.post(`${t}/api/assets`,e)},i=async(e,t,r)=>{let o=a(),i=await n.F.put(`${o}/api/assets/${e}`,t);return await n.F.put(`${o}/api/assets/${e}/bot-agents`,{botAgentIds:r}),i},s=async()=>{let e=a();return await n.F.get(`${e}/api/assets`)},l=async e=>{let t=a(),r=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,String(r))}),t.toString()}(e),o=`${t}/odata/Assets`;r&&(o+=`?${r}`),console.log("OData query endpoint:",o);try{let e=await n.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} items from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":e["@odata.count"]??e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let r=t[0];console.log(`Found array property "${r}" in response`);let n=e[r],a=e["@odata.count"];return{value:n,"@odata.count":("number"==typeof a?a:void 0)??n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching assets with OData:",e),{value:[]}}},c=async e=>{let t=a();await n.F.delete(`${t}/api/assets/${e}`)},u=async e=>{let t=a();return n.F.get(`${t}/api/assets/${e}`)},d=async e=>{let t=a();return n.F.get(`${t}/api/assets/${e}/bot-agents`)},p=async()=>{let e=a();return n.F.get(`${e}/api/agents`)}},72826:(e,t,r)=>{Promise.resolve().then(r.bind(r,69231)),Promise.resolve().then(r.bind(r,83847)),Promise.resolve().then(r.bind(r,78526)),Promise.resolve().then(r.bind(r,97597)),Promise.resolve().then(r.bind(r,98641)),Promise.resolve().then(r.bind(r,80110))},76180:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(37413),a=r(64539),o=r(93971),i=r(3633);function s(){return(0,n.jsx)("div",{className:"flex flex-1 flex-col bg-muted/20 min-h-screen",children:(0,n.jsxs)("div",{className:"@container/main flex flex-1 flex-col gap-4 p-2 sm:p-4 lg:p-6 dark:bg-black/60",children:[(0,n.jsx)("div",{className:"rounded-xl",children:(0,n.jsx)(a.SectionCards,{})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-4 rounded-xl h-auto md:h-[575px]",children:[(0,n.jsx)("div",{className:"flex flex-col gap-2 min-w-0",children:(0,n.jsx)(i.StatisticalStatus,{})}),(0,n.jsx)("div",{className:"flex flex-col gap-2 min-w-0",children:(0,n.jsx)(o.ChartPieLabel,{})})]})]})})}},83442:(e,t,r)=>{Promise.resolve().then(r.bind(r,61018)),Promise.resolve().then(r.bind(r,2505)),Promise.resolve().then(r.bind(r,92588)),Promise.resolve().then(r.bind(r,48974)),Promise.resolve().then(r.bind(r,31057)),Promise.resolve().then(r.bind(r,50417))},90631:(e,t,r)=>{"use strict";r.d(t,{RT:()=>o,bF:()=>s,iX:()=>c,vH:()=>i});var n=r(51787);let a=()=>"default",o=async e=>{let t=a();return await n.F.post(`${t}/api/executions/trigger`,e)},i=async()=>{let e=a();try{return await n.F.get(`${e}/api/executions`)}catch(e){return console.error("Error fetching all executions:",e),[]}},s=async e=>{let t=a(),r={...e};(void 0===r.$top||r.$top<=0)&&(r.$top=10);let o=new Date().getTime(),i=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,r])=>{null!=r&&"$count"!==e&&t.append(e,String(r))}),t.toString()}(r),s=`${t}/odata/Executions`;i?s+=`?${i}&_t=${o}`:s+=`?_t=${o}`,console.log(`Fetching executions with endpoint: ${s}`),console.log(`Page: ${r.$skip?r.$skip/r.$top+1:1}, Size: ${r.$top}`);try{let e=await n.F.get(s),t=function(e){return e&&"object"==typeof e?{value:Array.isArray(e.value)?e.value:[],"@odata.count":"number"==typeof e["@odata.count"]?e["@odata.count"]:void 0,"@odata.nextLink":"string"==typeof e["@odata.nextLink"]?e["@odata.nextLink"]:void 0}:{value:[]}}(e);return r.$top&&t.value.length>r.$top&&(console.warn(`OData returned ${t.value.length} items but only ${r.$top} were requested. Trimming results.`),t.value=t.value.slice(0,r.$top)),console.log(`Received ${t.value.length} executions from OData`),t}catch(e){return console.error("Error fetching executions with OData:",e),{value:[]}}},l=async e=>{let t=a();return await n.F.get(`${t}/api/executions/${e}/logs/download`)},c=async(e,t)=>{try{let{downloadUrl:r}=await l(e),n=t||`execution_${e}_logs.log`;try{let e=await fetch(r,{method:"GET",headers:{Accept:"application/octet-stream"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let t=await e.blob(),a=window.URL.createObjectURL(t),o=document.createElement("a");o.href=a,o.download=n,o.style.display="none",document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(a)}catch(t){console.warn("Fetch method failed, falling back to direct link:",t);let e=document.createElement("a");e.href=r,e.download=n,e.target="_blank",e.rel="noopener noreferrer",e.style.display="none",document.body.appendChild(e),e.click(),document.body.removeChild(e)}}catch(e){throw console.error("Error downloading execution logs:",e),e}}},93971:(e,t,r)=>{"use strict";r.d(t,{ChartPieLabel:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ChartPieLabel() from the server but ChartPieLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\layout\\charts\\chart-pie-label.tsx","ChartPieLabel")},95963:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.default,__next_app__:()=>u,pages:()=>c,routeModule:()=>d,tree:()=>l});var n=r(65239),a=r(48088),o=r(31369),i=r(30893),s={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>i[e]);r.d(t,s);let l={children:["",{children:["[tenant]",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76180)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\dashboard\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[tenant]/dashboard/page",pathname:"/[tenant]/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7966,5584,5156,4654,6467,2900,6763,519],()=>r(95963));module.exports=n})();