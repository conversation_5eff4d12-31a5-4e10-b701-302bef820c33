#!/usr/bin/env python3
"""
Example usage of ExcelTemplateWriter
"""

from write_to_template import ExcelTemplateWriter
import os

def example_usage():
    """
    Example of how to use the ExcelTemplateWriter class
    """
    
    # Initialize the writer with template path
    template_path = "./template/report-template.xlsx"
    writer = ExcelTemplateWriter(template_path)
    
    # Example 1: Process with automatic filename
    export_file = "path/to/your/jira_export.xlsx"
    
    if os.path.exists(export_file):
        try:
            output_file = writer.process_export(export_file)
            print(f"Report generated: {output_file}")
        except Exception as e:
            print(f"Error: {e}")
    else:
        print(f"Export file not found: {export_file}")
    
    # Example 2: Process with custom filename
    custom_filename = "weekly_jira_report.xlsx"
    if os.path.exists(export_file):
        try:
            output_file = writer.process_export(export_file, custom_filename)
            print(f"Custom report generated: {output_file}")
        except Exception as e:
            print(f"Error: {e}")

def batch_process_example():
    """
    Example of batch processing multiple export files
    """
    template_path = "./template/report-template.xlsx"
    writer = ExcelTemplateWriter(template_path)
    
    # List of export files to process
    export_files = [
        "exports/week1_export.xlsx",
        "exports/week2_export.xlsx", 
        "exports/week3_export.xlsx"
    ]
    
    for i, export_file in enumerate(export_files, 1):
        if os.path.exists(export_file):
            try:
                filename = f"weekly_report_week{i}.xlsx"
                output_file = writer.process_export(export_file, filename)                print(f"[SUCCESS] Generated: {output_file}")
            except Exception as e:
                print(f"[ERROR] Failed to process {export_file}: {e}")
        else:
            print(f"[ERROR] File not found: {export_file}")

if __name__ == "__main__":
    print("ExcelTemplateWriter Usage Examples")
    print("="*40)
    
    print("\n1. Basic Usage:")
    example_usage()
    
    print("\n2. Batch Processing:")
    batch_process_example()
