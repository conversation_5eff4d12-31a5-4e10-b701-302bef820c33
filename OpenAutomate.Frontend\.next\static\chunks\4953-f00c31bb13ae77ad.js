"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4953],{13066:(e,t,r)=>{r.d(t,{CC:()=>n,I0:()=>u,jB:()=>i,q2:()=>a});let n=0,i=1,a=2,u=3},22436:(e,t,r)=>{var n=r(12115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,u=n.useEffect,o=n.useLayoutEffect,l=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return o(function(){i.value=r,i.getSnapshot=t,s(i)&&c({inst:i})},[e,r,t]),u(function(){return s(i)&&c({inst:i}),e(function(){s(i)&&c({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},34953:(e,t,r)=>{let n;r.d(t,{BE:()=>y,Ay:()=>b});var i=r(12115),a=r(49033),u=r(45567),o=r(13066);let l=u.i&&window.__SWR_DEVTOOLS_USE__,s=l?window.__SWR_DEVTOOLS_USE__:[],c=e=>(0,u.a)(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],d=()=>(0,u.m)(u.d,(0,i.useContext)(u.S)),f=s.concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=(0,u.s)(t),[,,,i]=u.b.get(u.c);if(n.startsWith("$inf$"))return r(...e);let a=i[n];return(0,u.e)(a)?r(...e):(delete i[n],a)});return e(t,i,n)}),g=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}};l&&(window.__SWR_DEVTOOLS_REACT__=i);let h=()=>{},p=h();new WeakMap;let v=i.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),w={dedupe:!0},y=u.O.defineProperty(u.g,"defaultValue",{value:u.d}),b=(n=(e,t,r)=>{let{cache:n,compare:l,suspense:s,fallbackData:c,revalidateOnMount:d,revalidateIfStale:f,refreshInterval:h,refreshWhenHidden:p,refreshWhenOffline:y,keepPreviousData:b}=r,[m,O,S,E]=u.b.get(n),[_,R]=(0,u.s)(e),k=(0,i.useRef)(!1),C=(0,i.useRef)(!1),L=(0,i.useRef)(_),T=(0,i.useRef)(t),j=(0,i.useRef)(r),V=()=>j.current,D=()=>V().isVisible()&&V().isOnline(),[I,x,A,P]=(0,u.z)(n,_),F=(0,i.useRef)({}).current,M=(0,u.e)(c)?(0,u.e)(r.fallback)?u.U:r.fallback[_]:c,W=(e,t)=>{for(let r in F)if("data"===r){if(!l(e[r],t[r])&&(!(0,u.e)(e[r])||!l(H,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},q=(0,i.useMemo)(()=>{let e=!!_&&!!t&&((0,u.e)(d)?!V().isPaused()&&!s&&!1!==f:d),r=t=>{let r=(0,u.m)(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=I(),i=P(),a=r(n),o=n===i?a:r(i),l=a;return[()=>{let e=r(I());return W(e,l)?(l.data=e.data,l.isLoading=e.isLoading,l.isValidating=e.isValidating,l.error=e.error,l):(l=e,e)},()=>o]},[n,_]),B=(0,a.useSyncExternalStore)((0,i.useCallback)(e=>A(_,(t,r)=>{W(r,t)||e()}),[n,_]),q[0],q[1]),U=!k.current,N=m[_]&&m[_].length>0,$=B.data,z=(0,u.e)($)?M&&(0,u.B)(M)?v(M):M:$,J=B.error,G=(0,i.useRef)(z),H=b?(0,u.e)($)?(0,u.e)(G.current)?z:G.current:$:z,K=(!N||!!(0,u.e)(J))&&(U&&!(0,u.e)(d)?d:!V().isPaused()&&(s?!(0,u.e)(z)&&f:(0,u.e)(z)||f)),Q=!!(_&&t&&U&&K),X=(0,u.e)(B.isValidating)?Q:B.isValidating,Y=(0,u.e)(B.isLoading)?Q:B.isLoading,Z=(0,i.useCallback)(async e=>{let t,n;let i=T.current;if(!_||!i||C.current||V().isPaused())return!1;let a=!0,s=e||{},c=!S[_]||!s.dedupe,d=()=>u.I?!C.current&&_===L.current&&k.current:_===L.current,f={isValidating:!1,isLoading:!1},g=()=>{x(f)},h=()=>{let e=S[_];e&&e[1]===n&&delete S[_]},p={isValidating:!0};(0,u.e)(I().data)&&(p.isLoading=!0);try{if(c&&(x(p),r.loadingTimeout&&(0,u.e)(I().data)&&setTimeout(()=>{a&&d()&&V().onLoadingSlow(_,r)},r.loadingTimeout),S[_]=[i(R),(0,u.o)()]),[t,n]=S[_],t=await t,c&&setTimeout(h,r.dedupingInterval),!S[_]||S[_][1]!==n)return c&&d()&&V().onDiscarded(_),!1;f.error=u.U;let e=O[_];if(!(0,u.e)(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return g(),c&&d()&&V().onDiscarded(_),!1;let o=I().data;f.data=l(o,t)?o:t,c&&d()&&V().onSuccess(t,_,r)}catch(r){h();let e=V(),{shouldRetryOnError:t}=e;!e.isPaused()&&(f.error=r,c&&d()&&(e.onError(r,_,e),(!0===t||(0,u.a)(t)&&t(r))&&(!V().revalidateOnFocus||!V().revalidateOnReconnect||D())&&e.onErrorRetry(r,_,e,e=>{let t=m[_];t&&t[0]&&t[0](o.I0,e)},{retryCount:(s.retryCount||0)+1,dedupe:!0})))}return a=!1,g(),!0},[_,n]),ee=(0,i.useCallback)((...e)=>(0,u.n)(n,L.current,...e),[]);if((0,u.u)(()=>{T.current=t,j.current=r,(0,u.e)($)||(G.current=$)}),(0,u.u)(()=>{if(!_)return;let e=Z.bind(u.U,w),t=0;V().revalidateOnFocus&&(t=Date.now()+V().focusThrottleInterval);let r=g(_,m,(r,n={})=>{if(r==o.CC){let r=Date.now();V().revalidateOnFocus&&r>t&&D()&&(t=r+V().focusThrottleInterval,e())}else if(r==o.jB)V().revalidateOnReconnect&&D()&&e();else if(r==o.q2)return Z();else if(r==o.I0)return Z(n)});return C.current=!1,L.current=_,k.current=!0,x({_k:R}),K&&((0,u.e)(z)||u.r?e():(0,u.t)(e)),()=>{C.current=!0,r()}},[_]),(0,u.u)(()=>{let e;function t(){let t=(0,u.a)(h)?h(I().data):h;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!I().error&&(p||V().isVisible())&&(y||V().isOnline())?Z(w).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[h,p,y,_]),(0,i.useDebugValue)(H),s&&(0,u.e)(z)&&_){if(!u.I&&u.r)throw Error("Fallback data is required when using Suspense in SSR.");T.current=t,j.current=r,C.current=!1;let e=E[_];if((0,u.e)(e)||v(ee(e)),(0,u.e)(J)){let e=Z(w);(0,u.e)(H)||(e.status="fulfilled",e.value=!0),v(e)}else throw J}return{mutate:ee,get data(){return F.data=!0,H},get error(){return F.error=!0,J},get isValidating(){return F.isValidating=!0,X},get isLoading(){return F.isLoading=!0,Y}}},function(...e){let t=d(),[r,i,a]=c(e),o=(0,u.f)(t,a),l=n,{use:s}=o,g=(s||[]).concat(f);for(let e=g.length;e--;)l=g[e](l);return l(r,i||o.fetcher||null,o)})},45567:(e,t,r)=>{r.d(t,{B:()=>g,I:()=>k,O:()=>s,S:()=>J,U:()=>l,a:()=>d,b:()=>u,c:()=>U,d:()=>$,e:()=>c,f:()=>z,g:()=>G,i:()=>w,j:()=>N,m:()=>f,n:()=>W,o:()=>M,r:()=>C,s:()=>P,t:()=>L,u:()=>T,z:()=>O});var n=r(12115),i=r(13066),a=Object.prototype.hasOwnProperty;let u=new WeakMap,o=()=>{},l=o(),s=Object,c=e=>e===l,d=e=>"function"==typeof e,f=(e,t)=>({...e,...t}),g=e=>d(e.then),h={},p={},v="undefined",w=typeof window!=v,y=typeof document!=v,b=w&&"Deno"in window,m=()=>w&&typeof window.requestAnimationFrame!=v,O=(e,t)=>{let r=u.get(e);return[()=>!c(t)&&e.get(t)||h,n=>{if(!c(t)){let i=e.get(t);t in p||(p[t]=i),r[5](t,f(i,n),i||h)}},r[6],()=>!c(t)&&t in p?p[t]:!c(t)&&e.get(t)||h]},S=!0,[E,_]=w&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[o,o],R={initFocus:e=>(y&&document.addEventListener("visibilitychange",e),E("focus",e),()=>{y&&document.removeEventListener("visibilitychange",e),_("focus",e)}),initReconnect:e=>{let t=()=>{S=!0,e()},r=()=>{S=!1};return E("online",t),E("offline",r),()=>{_("online",t),_("offline",r)}}},k=!n.useId,C=!w||b,L=e=>m()?window.requestAnimationFrame(e):setTimeout(e,1),T=C?n.useEffect:n.useLayoutEffect,j="undefined"!=typeof navigator&&navigator.connection,V=!C&&j&&(["slow-2g","2g"].includes(j.effectiveType)||j.saveData),D=new WeakMap,I=(e,t)=>s.prototype.toString.call(e)==="[object ".concat(t,"]"),x=0,A=e=>{let t,r;let n=typeof e,i=I(e,"Date"),a=I(e,"RegExp"),u=I(e,"Object");if(s(e)!==e||i||a)t=i?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=D.get(e))return t;if(t=++x+"~",D.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=A(e[r])+",";D.set(e,t)}if(u){t="#";let n=s.keys(e).sort();for(;!c(r=n.pop());)c(e[r])||(t+=r+":"+A(e[r])+",");D.set(e,t)}}return t},P=e=>{if(d(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?A(e):"",t]},F=0,M=()=>++F;async function W(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,a,o,s]=t,h=f({populateCache:!0,throwOnError:!0},"boolean"==typeof s?{revalidate:s}:s||{}),p=h.populateCache,v=h.rollbackOnError,w=h.optimisticData,y=e=>"function"==typeof v?v(e):!1!==v,b=h.throwOnError;if(d(a)){let e=[];for(let t of n.keys())!/^\$(inf|sub)\$/.test(t)&&a(n.get(t)._k)&&e.push(t);return Promise.all(e.map(m))}return m(a);async function m(e){let r;let[a]=P(e);if(!a)return;let[s,f]=O(n,a),[v,m,S,E]=u.get(n),_=()=>{let t=v[a];return(d(h.revalidate)?h.revalidate(s().data,e):!1!==h.revalidate)&&(delete S[a],delete E[a],t&&t[0])?t[0](i.q2).then(()=>s().data):s().data};if(t.length<3)return _();let R=o,k=M();m[a]=[k,0];let C=!c(w),L=s(),T=L.data,j=L._c,V=c(j)?T:j;if(C&&f({data:w=d(w)?w(V,T):w,_c:V}),d(R))try{R=R(V)}catch(e){r=e}if(R&&g(R)){if(R=await R.catch(e=>{r=e}),k!==m[a][0]){if(r)throw r;return R}r&&C&&y(r)&&(p=!0,f({data:V,_c:l}))}if(p&&!r&&(d(p)?f({data:p(R,V),error:l,_c:l}):f({data:R,error:l,_c:l})),m[a][1]=M(),Promise.resolve(_()).then(()=>{f({_c:l})}),r){if(b)throw r;return}return R}}let q=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},B=(e,t)=>{if(!u.has(e)){let r=f(R,t),n=Object.create(null),a=W.bind(l,e),s=o,c=Object.create(null),d=(e,t)=>{let r=c[e]||[];return c[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},g=(t,r,n)=>{e.set(t,r);let i=c[t];if(i)for(let e of i)e(r,n)},h=()=>{if(!u.has(e)&&(u.set(e,[n,Object.create(null),Object.create(null),Object.create(null),a,g,d]),!C)){let t=r.initFocus(setTimeout.bind(l,q.bind(l,n,i.CC))),a=r.initReconnect(setTimeout.bind(l,q.bind(l,n,i.jB)));s=()=>{t&&t(),a&&a(),u.delete(e)}}};return h(),[e,a,h,s]}return[e,u.get(e)[4]]},[U,N]=B(new Map),$=f({onLoadingSlow:o,onSuccess:o,onError:o,onErrorRetry:(e,t,r,n,i)=>{let a=r.errorRetryCount,u=i.retryCount,o=~~((Math.random()+.5)*(1<<(u<8?u:8)))*r.errorRetryInterval;(c(a)||!(u>a))&&setTimeout(n,o,i)},onDiscarded:o,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:V?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:V?5e3:3e3,compare:function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!n||"object"==typeof t){for(n in i=0,t)if(a.call(t,n)&&++i&&!a.call(r,n)||!(n in r)||!e(t[n],r[n]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r},isPaused:()=>!1,cache:U,mutate:N,fallback:{}},{isOnline:()=>S,isVisible:()=>{let e=y&&document.visibilityState;return c(e)||"hidden"!==e}}),z=(e,t)=>{let r=f(e,t);if(t){let{use:n,fallback:i}=e,{use:a,fallback:u}=t;n&&a&&(r.use=n.concat(a)),i&&u&&(r.fallback=f(i,u))}return r},J=(0,n.createContext)({}),G=e=>{let{value:t}=e,r=(0,n.useContext)(J),i=d(t),a=(0,n.useMemo)(()=>i?t(r):t,[i,r,t]),u=(0,n.useMemo)(()=>i?a:z(r,a),[i,r,a]),o=a&&a.provider,s=(0,n.useRef)(l);o&&!s.current&&(s.current=B(o(u.cache||U),a));let c=s.current;return c&&(u.cache=c[0],u.mutate=c[1]),T(()=>{if(c)return c[2]&&c[2](),c[3]},[]),(0,n.createElement)(J.Provider,f(e,{value:u}))}},49033:(e,t,r)=>{e.exports=r(22436)}}]);