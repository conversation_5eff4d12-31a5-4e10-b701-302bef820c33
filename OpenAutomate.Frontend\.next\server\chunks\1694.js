"use strict";exports.id=1694,exports.ids=[1694],exports.modules={3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},69875:(e,t,r)=>{r.d(t,{UC:()=>eL,In:()=>eM,q7:()=>eA,VF:()=>eH,p4:()=>eB,ZL:()=>eD,bL:()=>ek,wn:()=>eO,PP:()=>eV,l9:()=>eN,WT:()=>eI,LM:()=>e_});var n=r(43210),l=r(51215);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}function i(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var a=r(58571);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function c(...e){return n.useCallback(u(...e),e)}var d=r(60687),p=n.createContext(void 0),f=r(42360),h=r(1359),v=r(40481),m=r(19783),g=r(96348),w=r(96929);function x(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var o;let e,i;let a=(o=r,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(s.ref=t?u(t,a):a),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,i=n.Children.toArray(l),a=i.find(b);if(a){let e=a.props.children,l=i.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,d.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,d.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var y=Symbol("radix.slottable");function b(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}var S=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=x(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(l?r:t,{...o,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),C=r(67427),j=globalThis?.document?n.useLayoutEffect:()=>{},R=r(83721),P=r(88574),T=r(63376),E=r(42247),k=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],I="Select",[M,D,L]=(0,a.N)(I),[_,A]=function(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return l.scopeName=e,[function(t,l){let o=n.createContext(l),i=r.length;r=[...r,l];let a=t=>{let{scope:r,children:l,...a}=t,s=r?.[e]?.[i]||o,u=n.useMemo(()=>a,Object.values(a));return(0,d.jsx)(s.Provider,{value:u,children:l})};return a.displayName=t+"Provider",[a,function(r,a){let s=a?.[e]?.[i]||o,u=n.useContext(s);if(u)return u;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(l,...t)]}(I,[L,g.Bk]),B=(0,g.Bk)(),[H,V]=_(I),[O,W]=_(I),F=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:c,name:f,autoComplete:h,disabled:v,required:w,form:x}=e,y=B(t),[b,S]=n.useState(null),[j,R]=n.useState(null),[P,T]=n.useState(!1),E=function(e){let t=n.useContext(p);return e||t||"ltr"}(c),[k,N]=(0,C.i)({prop:l,defaultProp:o??!1,onChange:i,caller:I}),[D,L]=(0,C.i)({prop:a,defaultProp:s,onChange:u,caller:I}),_=n.useRef(null),A=!b||x||!!b.closest("form"),[V,W]=n.useState(new Set),F=Array.from(V).map(e=>e.props.value).join(";");return(0,d.jsx)(g.bL,{...y,children:(0,d.jsxs)(H,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:j,onValueNodeChange:R,valueNodeHasChildren:P,onValueNodeHasChildrenChange:T,contentId:(0,m.B)(),value:D,onValueChange:L,open:k,onOpenChange:N,dir:E,triggerPointerDownPosRef:_,disabled:v,children:[(0,d.jsx)(M.Provider,{scope:t,children:(0,d.jsx)(O,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{W(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),A?(0,d.jsxs)(eR,{"aria-hidden":!0,required:w,tabIndex:-1,name:f,autoComplete:h,value:D,onChange:e=>L(e.target.value),disabled:v,form:x,children:[void 0===D?(0,d.jsx)("option",{value:""}):null,Array.from(V)]},F):null]})})};F.displayName=I;var K="SelectTrigger",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,a=B(r),s=V(K,r),u=s.disabled||l,p=c(t,s.onTriggerChange),f=D(r),h=n.useRef("touch"),[v,m,w]=eT(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=eE(t,e,r);void 0!==n&&s.onValueChange(n.value)}),x=e=>{u||(s.onOpenChange(!0),w()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,d.jsx)(g.Mz,{asChild:!0,...a,children:(0,d.jsx)(S.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eP(s.value)?"":void 0,...o,ref:p,onClick:i(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&x(e)}),onPointerDown:i(o.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:i(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(x(),e.preventDefault())})})})});U.displayName=K;var $="SelectValue",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:i="",...a}=e,s=V($,r),{onValueNodeHasChildrenChange:u}=s,p=void 0!==o,f=c(t,s.onValueNodeChange);return j(()=>{u(p)},[u,p]),(0,d.jsx)(S.span,{...a,ref:f,style:{pointerEvents:"none"},children:eP(s.value)?(0,d.jsx)(d.Fragment,{children:i}):o})});z.displayName=$;var q=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,d.jsx)(S.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});q.displayName="SelectIcon";var Z=e=>(0,d.jsx)(w.Z,{asChild:!0,...e});Z.displayName="SelectPortal";var X="SelectContent",Y=n.forwardRef((e,t)=>{let r=V(X,e.__scopeSelect),[o,i]=n.useState();return(j(()=>{i(new DocumentFragment)},[]),r.open)?(0,d.jsx)(ee,{...e,ref:t}):o?l.createPortal((0,d.jsx)(G,{scope:e.__scopeSelect,children:(0,d.jsx)(M.Slot,{scope:e.__scopeSelect,children:(0,d.jsx)("div",{children:e.children})})}),o):null});Y.displayName=X;var[G,Q]=_(X),J=x("SelectContent.RemoveScroll"),ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:s,side:u,sideOffset:p,align:m,alignOffset:g,arrowPadding:w,collisionBoundary:x,collisionPadding:y,sticky:b,hideWhenDetached:S,avoidCollisions:C,...j}=e,R=V(X,r),[P,k]=n.useState(null),[N,I]=n.useState(null),M=c(t,e=>k(e)),[L,_]=n.useState(null),[A,B]=n.useState(null),H=D(r),[O,W]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(P)return(0,T.Eq)(P)},[P]),(0,h.Oh)();let K=n.useCallback(e=>{let[t,...r]=H().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&N&&(N.scrollTop=0),r===n&&N&&(N.scrollTop=N.scrollHeight),r?.focus(),document.activeElement!==l))return},[H,N]),U=n.useCallback(()=>K([L,P]),[K,L,P]);n.useEffect(()=>{O&&U()},[O,U]);let{onOpenChange:$,triggerPointerDownPosRef:z}=R;n.useEffect(()=>{if(P){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():P.contains(r.target)||$(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[P,$,z]),n.useEffect(()=>{let e=()=>$(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[$]);let[q,Z]=eT(e=>{let t=H().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eE(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Y=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&(_(e),n&&(F.current=!0))},[R.value]),Q=n.useCallback(()=>P?.focus(),[P]),ee=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&B(e)},[R.value]),en="popper"===l?er:et,el=en===er?{side:u,sideOffset:p,align:m,alignOffset:g,arrowPadding:w,collisionBoundary:x,collisionPadding:y,sticky:b,hideWhenDetached:S,avoidCollisions:C}:{};return(0,d.jsx)(G,{scope:r,content:P,viewport:N,onViewportChange:I,itemRefCallback:Y,selectedItem:L,onItemLeave:Q,itemTextRefCallback:ee,focusSelectedItem:U,selectedItemText:A,position:l,isPositioned:O,searchRef:q,children:(0,d.jsx)(E.A,{as:J,allowPinchZoom:!0,children:(0,d.jsx)(v.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:i(o,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,d.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,d.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>W(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:i(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});ee.displayName="SelectContentImpl";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...i}=e,a=V(X,r),s=Q(X,r),[u,p]=n.useState(null),[f,h]=n.useState(null),v=c(t,e=>h(e)),m=D(r),g=n.useRef(!1),w=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:b,focusSelectedItem:C}=s,R=n.useCallback(()=>{if(a.trigger&&a.valueNode&&u&&f&&x&&y&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==a.dir){let l=n.left-t.left,i=r.left-l,a=e.left-i,s=e.width+a,c=Math.max(s,t.width),d=o(i,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=s+"px",u.style.left=d+"px"}else{let l=t.right-n.right,i=window.innerWidth-r.right-l,a=window.innerWidth-e.right-i,s=e.width+a,c=Math.max(s,t.width),d=o(i,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=s+"px",u.style.right=d+"px"}let i=m(),s=window.innerHeight-20,c=x.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),v=parseInt(d.borderBottomWidth,10),w=p+h+c+parseInt(d.paddingBottom,10)+v,S=Math.min(5*y.offsetHeight,w),C=window.getComputedStyle(x),j=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),P=e.top+e.height/2-10,T=y.offsetHeight/2,E=p+h+(y.offsetTop+T);if(E<=P){let e=i.length>0&&y===i[i.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-P,T+(e?R:0)+(f.clientHeight-x.offsetTop-x.offsetHeight)+v);u.style.height=E+t+"px"}else{let e=i.length>0&&y===i[0].ref.current;u.style.top="0px";let t=Math.max(P,p+x.offsetTop+(e?j:0)+T);u.style.height=t+(w-E)+"px",x.scrollTop=E-P+x.offsetTop}u.style.margin="10px 0",u.style.minHeight=S+"px",u.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>g.current=!0)}},[m,a.trigger,a.valueNode,u,f,x,y,b,a.dir,l]);j(()=>R(),[R]);let[P,T]=n.useState();j(()=>{f&&T(window.getComputedStyle(f).zIndex)},[f]);let E=n.useCallback(e=>{e&&!0===w.current&&(R(),C?.(),w.current=!1)},[R,C]);return(0,d.jsx)(en,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:g,onScrollButtonChange:E,children:(0,d.jsx)("div",{ref:p,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:P},children:(0,d.jsx)(S.div,{...i,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});et.displayName="SelectItemAlignedPosition";var er=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,i=B(r);return(0,d.jsx)(g.UC,{...i,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});er.displayName="SelectPopperPosition";var[en,el]=_(X,{}),eo="SelectViewport",ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,a=Q(eo,r),s=el(eo,r),u=c(t,a.onViewportChange),p=n.useRef(0);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,d.jsx)(M.Slot,{scope:r,children:(0,d.jsx)(S.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:i(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(p.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,i=Math.min(n,o),a=o-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}p.current=t.scrollTop})})})]})});ei.displayName=eo;var ea="SelectGroup",[es,eu]=_(ea);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,m.B)();return(0,d.jsx)(es,{scope:r,id:l,children:(0,d.jsx)(S.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=ea;var ec="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=eu(ec,r);return(0,d.jsx)(S.div,{id:l.id,...n,ref:t})}).displayName=ec;var ed="SelectItem",[ep,ef]=_(ed),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:a,...s}=e,u=V(ed,r),p=Q(ed,r),f=u.value===l,[h,v]=n.useState(a??""),[g,w]=n.useState(!1),x=c(t,e=>p.itemRefCallback?.(e,l,o)),y=(0,m.B)(),b=n.useRef("touch"),C=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,d.jsx)(ep,{scope:r,value:l,disabled:o,textId:y,isSelected:f,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,d.jsx)(M.ItemSlot,{scope:r,value:l,disabled:o,textValue:h,children:(0,d.jsx)(S.div,{role:"option","aria-labelledby":y,"data-highlighted":g?"":void 0,"aria-selected":f&&g,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:x,onFocus:i(s.onFocus,()=>w(!0)),onBlur:i(s.onBlur,()=>w(!1)),onClick:i(s.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:i(s.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:i(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:i(s.onPointerMove,e=>{b.current=e.pointerType,o?p.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:i(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&p.onItemLeave?.()}),onKeyDown:i(s.onKeyDown,e=>{(p.searchRef?.current===""||" "!==e.key)&&(N.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ed;var ev="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:i,...a}=e,s=V(ev,r),u=Q(ev,r),p=ef(ev,r),f=W(ev,r),[h,v]=n.useState(null),m=c(t,e=>v(e),p.onItemTextChange,e=>u.itemTextRefCallback?.(e,p.value,p.disabled)),g=h?.textContent,w=n.useMemo(()=>(0,d.jsx)("option",{value:p.value,disabled:p.disabled,children:g},p.value),[p.disabled,p.value,g]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=f;return j(()=>(x(w),()=>y(w)),[x,y,w]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(S.span,{id:p.textId,...a,ref:m}),p.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(a.children,s.valueNode):null]})});em.displayName=ev;var eg="SelectItemIndicator",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(eg,r).isSelected?(0,d.jsx)(S.span,{"aria-hidden":!0,...n,ref:t}):null});ew.displayName=eg;var ex="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=Q(ex,e.__scopeSelect),l=el(ex,e.__scopeSelect),[o,i]=n.useState(!1),a=c(t,l.onScrollButtonChange);return j(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,d.jsx)(eC,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ex;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=Q(eb,e.__scopeSelect),l=el(eb,e.__scopeSelect),[o,i]=n.useState(!1),a=c(t,l.onScrollButtonChange);return j(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,d.jsx)(eC,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,a=Q("SelectScrollButton",r),s=n.useRef(null),u=D(r),c=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>c(),[c]),j(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,d.jsx)(S.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:i(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:i(o.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:i(o.onPointerLeave,()=>{c()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,d.jsx)(S.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var ej="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=B(r),o=V(ej,r),i=Q(ej,r);return o.open&&"popper"===i.position?(0,d.jsx)(g.i3,{...l,...n,ref:t}):null}).displayName=ej;var eR=n.forwardRef(({__scopeSelect:e,value:t,...r},l)=>{let o=n.useRef(null),i=c(l,o),a=(0,R.Z)(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[a,t]),(0,d.jsx)(S.select,{...r,style:{...P.Qg,...r.style},ref:i,defaultValue:t})});function eP(e){return""===e||void 0===e}function eT(e){let t=function(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),i=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,i]}function eE(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=e,l=Math.max(i,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eR.displayName="SelectBubbleInput";var ek=F,eN=U,eI=z,eM=q,eD=Z,eL=Y,e_=ei,eA=eh,eB=em,eH=ew,eV=ey,eO=eS},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},83721:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(43210);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};