# Refactoring Plan: Resolve Incorrect "Trial Already Used" State

## 1. Executive Summary & Goals
This plan addresses a critical bug where new users on a fresh database are incorrectly told they have already used their free trial after creating their first organization unit. The root cause is a combination of a cache invalidation issue and ambiguous state management between the frontend and backend.

The primary goals are:
-   **Correctness:** Ensure the system accurately reflects a user's trial status immediately after their first organization unit is created.
-   **Clarity:** Eliminate ambiguity in the trial status logic by making the API response more explicit, leading to clearer UI messaging for the user.
-   **Robustness:** Improve the overall reliability of the subscription and trial management system, particularly concerning state propagation in a cached environment.

## 2. Current Situation Analysis
The system is designed to grant a new user a one-time free trial upon the creation of their very first organization unit (OU). The trial usage is tracked on a per-user basis, not per-OU.

**Identified Pain Points:**
1.  **Delayed State Propagation:** After a new user creates their first OU, a trial `Subscription` record is correctly created in the database. However, the frontend immediately queries for the subscription status and receives stale data (indicating no subscription), leading it to incorrectly display a "Trial Already Used" message.
2.  **Caching Issue:** The delay (reported as "some minutes") before the UI updates strongly indicates that the API response for subscription status is being cached. The data-changing operation (OU and trial creation) is not triggering the necessary cache invalidation for the data-reading operation (getting subscription status).
3.  **Ambiguous Frontend Logic:** The frontend infers the "Trial Already Used" state from two separate flags: `hasSubscription: false` and `isEligibleForTrial: false`. This logic is brittle and fails when there's a delay in data propagation, as the system is temporarily in an inconsistent state from the frontend's perspective.

## 3. Proposed Solution / Refactoring Strategy
The strategy is a two-phased approach. Phase 1 provides an immediate fix for the caching bug to resolve the primary symptom. Phase 2 refactors the API and frontend logic to make the system more robust and prevent similar issues in the future.

### 3.1. High-Level Design / Architectural Overview
The solution involves enhancing the controllers that modify data to be responsible for invalidating dependent caches. We will inject `ICacheInvalidationService` into `OrganizationUnitController` to clear the subscription and user profile caches upon new OU creation.

Furthermore, we will enrich the `SubscriptionStatusResponse` DTO with an explicit `TrialStatus` enum. This removes guesswork from the frontend, allowing it to render the correct state based on an unambiguous signal from the backend, thus making the system more resilient to minor propagation delays.

```mermaid
sequenceDiagram
    participant FE as Frontend (React)
    participant API as API Controller
    participant Service as Service Layer
    participant Cache as Redis Cache
    participant DB as Database

    Note over FE, DB: Initial State: New User, Fresh Database

    FE->>API: POST /api/ou/create (CreateOrganizationUnitDto)
    API->>Service: OrganizationUnitService.CreateOrganizationUnitAsync()
    Service->>DB: Creates OrganizationUnit
    Service->>Service: SubscriptionService.StartTrialSubscriptionAsync()
    Service->>DB: Creates Subscription (Status: "trialing")
    Service->>API: Returns success
    
    %% --- This is the new, critical step --- %%
    API->>Service: ICacheInvalidationService.InvalidateCaches()
    Service->>Cache: DEL cache_key:/subscription/status
    Service->>Cache: DEL cache_key:/api/account/profile
    Note over API, Cache: Cache for subscription & profile is now cleared.

    API-->>FE: Returns 201 Created
    FE->>API: GET /{tenant}/api/subscription/status
    API->>Cache: Check for cached response (Cache Miss)
    API->>Service: SubscriptionService.GetSubscriptionStatusAsync()
    Service->>DB: Reads new "trialing" Subscription
    Service-->>API: Returns SubscriptionStatus (Trial Active)
    API->>Cache: Stores fresh SubscriptionStatus in cache
    API-->>FE: Returns correct "Trial Active" status
    FE->>FE: Renders "Trial Active" UI correctly
```

### 3.2. Key Components / Modules
-   **`OpenAutomate.API/Controllers/OrganizationUnitController.cs`**: To be modified to trigger cache invalidation.
-   **`OpenAutomate.API/Controllers/SubscriptionController.cs`**: The `SubscriptionStatusResponse` DTO within this file will be modified.
-   **`OpenAutomate.Infrastructure/Services/SubscriptionService.cs`**: The `GetSubscriptionStatusAsync` method will be updated to provide a more explicit trial status.
-   **`OpenAutomate.Frontend/src/hooks/use-subscription.ts`**: To be updated to handle the new API response structure.
-   **`OpenAutomate.Frontend/src/components/subscription/SubscriptionStatus.tsx`**: To be refactored to use the new explicit trial status, simplifying its logic.

### 3.3. Detailed Action Plan / Phases

#### Phase 1: Immediate Cache Invalidation Fix (High Priority)
-   **Objective(s):** To ensure that creating a new organization unit and its associated trial subscription immediately invalidates any cached subscription data, providing the frontend with fresh data on its next request.
-   **Priority:** High

-   **Task 1.1:** Implement Cache Invalidation on OU Creation
    -   **Rationale/Goal:** The controller handling the data modification must clear caches for related data. This is the core of the bug fix.
    -   **Estimated Effort:** Small
    -   **Deliverable/Criteria for Completion:**
        -   Inject `ICacheInvalidationService` into `OrganizationUnitController.cs`.
        -   In the `Create` method, after `_organizationUnitService.CreateOrganizationUnitAsync` succeeds, add calls to invalidate two caches:
            1.  The subscription status cache for the new tenant (`_cacheInvalidationService.InvalidateApiResponseCacheAsync("/api/subscription/status", result.Id)`).
            2.  The user profile cache for the creating user, as their `HasUsedTrial` status has now changed (`_cacheInvalidationService.InvalidateApiResponseCacheAsync("/api/account/profile", tenantId: null, userId: GetCurrentUserId())`).
        -   The code is tested to confirm the relevant Redis keys are deleted upon OU creation.

-   **Task 1.2:** Ensure Frontend SWR Revalidation
    -   **Rationale/Goal:** To ensure the frontend immediately refetches subscription and profile data after creating an OU, preventing the display of stale data from its local cache.
    -   **Estimated Effort:** Small
    -   **Deliverable/Criteria for Completion:**
        -   In the frontend's `CreateOrganizationUnitForm.tsx`, upon a successful creation, explicitly call `mutate` for the SWR keys defined in `swrKeys.subscription()` and `swrKeys.userProfile()`.
        -   This ensures that upon redirecting to the new dashboard, the data is guaranteed to be fresh.

#### Phase 2: Refactor Trial Status Logic for Robustness (Medium Priority)
-   **Objective(s):** To make the trial status communication between backend and frontend explicit, removing logical ambiguity and making the UI more resilient to edge cases.
-   **Priority:** Medium

-   **Task 2.1:** Enhance Subscription Status API Response
    -   **Rationale/Goal:** To provide the frontend with a clear, unambiguous state instead of forcing it to infer the user's trial history.
    -   **Estimated Effort:** Small
    -   **Deliverable/Criteria for Completion:**
        -   In `SubscriptionController.cs`, create a new public enum `TrialStatus { Eligible, Active, Used, NotEligible }`.
        -   Add a new property `public TrialStatus UserTrialStatus { get; set; }` to the `SubscriptionStatusResponse` DTO.
        -   Remove the now-redundant `IsEligibleForTrial` boolean property.

-   **Task 2.2:** Update Backend Service Logic
    -   **Rationale/Goal:** To implement the logic that correctly determines and populates the new `UserTrialStatus` field.
    -   **Estimated Effort:** Medium
    -   **Deliverable/Criteria for Completion:**
        -   Modify `SubscriptionService.GetSubscriptionStatusAsync`.
        -   If the OU has an active trial, set `UserTrialStatus = TrialStatus.Active`.
        -   If the OU has no subscription, call `IsOrganizationUnitEligibleForTrialAsync` to determine if the status should be `Eligible`.
        -   If not eligible, perform a cross-tenant check on the `Subscription` table for the current user (`CreatedBy == userId`) to differentiate between `Used` (a trial record exists for the user on another OU) and `NotEligible` (e.g., not the user's first OU).
        -   The service method now returns the fully populated `SubscriptionStatus` object.

-   **Task 2.3:** Refactor Frontend Subscription Components
    -   **Rationale/Goal:** To utilize the new, explicit `UserTrialStatus` from the API, simplifying the UI logic and ensuring correct messages are displayed.
    -   **Estimated Effort:** Medium
    -   **Deliverable/Criteria for Completion:**
        -   Update the `SubscriptionStatus` type in `OpenAutomate.Frontend/src/types/subscription.ts` to include `userTrialStatus`.
        -   Refactor the `SubscriptionStatus.tsx` component.
        -   Replace the complex conditional logic based on `hasSubscription` and `isEligibleForTrial` with a simple `switch` statement on the `subscription.userTrialStatus` property.
        -   Each case in the switch will render the correct UI state (e.g., "Start Trial" button for `Eligible`, countdown for `Active`, "Upgrade" message for `Used`).

### 3.5. API Design / Interface Changes
The `GET /{tenant}/api/subscription/status` endpoint's response body will be modified.

**Current `SubscriptionStatusResponse`:**
```csharp
public class SubscriptionStatusResponse
{
    public bool HasSubscription { get; set; }
    public bool IsActive { get; set; }
    public bool IsInTrial { get; set; }
    public string Status { get; set; }
    public string PlanName { get; set; }
    public DateTime? TrialEndsAt { get; set; }
    public DateTime? RenewsAt { get; set; }
    public int? DaysRemaining { get; set; }
    public Guid OrganizationUnitId { get; set; }
    public bool IsEligibleForTrial { get; set; } // To be removed
}
```

**Proposed `SubscriptionStatusResponse`:**```csharp
// New Enum to be added
public enum TrialStatus { Eligible, Active, Used, NotEligible }

public class SubscriptionStatusResponse
{
    public bool HasSubscription { get; set; }
    public bool IsActive { get; set; }
    public bool IsInTrial { get; set; }
    public string Status { get; set; }
    public string PlanName { get; set; }
    public DateTime? TrialEndsAt { get; set; }
    public DateTime? RenewsAt { get; set; }
    public int? DaysRemaining { get; set; }
    public Guid OrganizationUnitId { get; set; }
    public TrialStatus UserTrialStatus { get; set; } // New explicit status
    // IsEligibleForTrial is now redundant and will be removed.
}
```

## 4. Key Considerations & Risk Mitigation
### 4.1. Technical Risks & Challenges
-   **Incorrect Cache Invalidation:** The cache invalidation pattern must precisely match the key generation logic in `EnableResponseCacheAttribute` and `CacheKeyUtility.cs`. An incorrect pattern could either fail to clear the necessary keys or clear too many, impacting performance.
    -   **Mitigation:** Thoroughly review the key generation logic and conduct tests in a development environment to confirm that only the intended keys are deleted from Redis upon OU creation.
-   **Race Conditions:** There's a small risk of a race condition if a user creates two OUs in very quick succession.
    -   **Mitigation:** The database transactionality of the OU and initial Subscription creation largely mitigates this. The check for "first OU" is robust enough for normal user behavior.

### 4.2. Dependencies
-   **Phase 2 Frontend changes** are dependent on the completion of **Phase 2 Backend changes**. The backend API must be deployed before the frontend can be updated to consume the new `UserTrialStatus` field.

### 4.3. Non-Functional Requirements (NFRs) Addressed
-   **Reliability:** The plan directly fixes a bug where the system presents an incorrect state to the user, improving overall reliability.
-   **Usability:** By providing immediate and accurate feedback, the user experience is significantly improved, reducing confusion and frustration.
-   **Maintainability:** The refactoring in Phase 2 simplifies the frontend logic by moving complex state derivation to the backend, making the UI components easier to understand and maintain.

## 5. Success Metrics / Validation Criteria
-   **Primary Metric:** 100% of new users on a fresh database instance who create their first organization unit will immediately see the "Trial Active" status in the UI without any delay.
-   **Secondary Metric:** The "Trial Already Used" message is only displayed to users who have previously activated a trial on another organization unit.
-   **Validation Test Case:**
    1.  Wipe and recreate the database.
    2.  Register a new user account.
    3.  Log in and create a new organization unit.
    4.  **Expected Result:** The user is redirected to the dashboard for the new OU, and the subscription status component immediately displays an active trial with the correct number of days remaining. There should be no "Trial Already Used" message at any point.

## 6. Assumptions Made
-   The business logic of "one free trial per user account, tied to their first organization unit" is the intended design and should be maintained.
-   The development team has access to and can modify all relevant parts of the codebase, including controllers, services, DTOs, and frontend components.
-   The caching mechanism is Redis, and `ICacheInvalidationService` uses Redis Pub/Sub as implied by the architecture.

## 7. Open Questions / Areas for Further Investigation
-   Should there be a background job that periodically sanity-checks trial statuses, especially for trials that were created but where the Quartz job scheduling might have failed? (This is out of scope for the immediate bug fix but a good consideration for future robustness).
-   For the "Trial Already Used" message, should the UI link the user to the organization unit where their trial is active? This would be a user experience enhancement for Phase 2.