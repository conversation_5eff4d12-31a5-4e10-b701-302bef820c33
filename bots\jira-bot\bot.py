"""
this is a bot that fetch all ticket from jira and write to excel report

Please make sure you install the bot dependencies:
pip install -r requirements.txt

For OpenAutomate platform integration, ensure the OpenAutomate Agent is running.
"""

import sys
import os
from pathlib import Path

# Add framework to path
framework_path = os.path.join(os.path.dirname(__file__), 'framework')
sys.path.insert(0, framework_path)

from base_bot import BaseBot
from transaction_folders import create_transaction_folders, ensure_folder
from tasks.jira import get_all_jira_tickets, save_to_file, export_to_csv, export_to_excel
from tasks.write_to_template import ExcelTemplateWriter
from tasks.excel_to_json import excel_to_json

class Bot(BaseBot):
    """
    jira-bot - this is a bot that fetch all ticket from jira and write to excel report
      Just modify the execute() method below to add your automation logic!
    """
    def execute(self):
        """
        Main automation logic - EDIT THIS METHOD!
        
        Add your automation code here:
        - Process files from self.input_folder
        - Save results to self.output_folder  
        - Use self.get_asset('key') for secure data
        - Call self.update_status('message') for progress updates
        """
        self.logger.info(">>> Starting jira-bot...")
        
        # Create working folders automatically
        create_transaction_folders(self.bot_name, self.logger)
        output_folder = ensure_folder(self.bot_name, "output")
        
        # Update status (visible in OpenAutomate platform)
        self.update_status("Fetching Jira tickets...")
        
        try:
            # Fetch all Jira tickets
            self.logger.info("[INFO] Fetching all Jira tickets from OpenAutomate project...")
            tickets_data = get_all_jira_tickets()
            
            self.logger.info(f"[STATS] Total tickets retrieved: {tickets_data['total']}")
            
            # Export to multiple formats in output folder
            json_file = output_folder / "jira_tickets.json"
            csv_file = output_folder / "jira_tickets.csv"
            excel_file = output_folder / "jira_tickets.xlsx"
            # Save in different formats
            # Uncomment only the formats you want:
            # save_to_file(tickets_data, str(json_file))       # JSON
            # export_to_csv(tickets_data, str(csv_file))       # CSV *** This is what you mainly want
            export_to_excel(tickets_data, str(excel_file))   # Excel
            excel_to_json(str(excel_file), str(output_folder / "jira_tickets_filtered.json"))  # Excel to JSON
            
            # Generate template report
            self.update_status("Generating template report...")
            self.logger.info("[INFO] Generating template report...")
            
            try:
                # Initialize template writer
                template_path = Path(__file__).parent / "template" / "report-template.xlsx"
                template_writer = ExcelTemplateWriter(str(template_path), str(output_folder))
                
                # Generate template report from Excel export
                template_report_file = template_writer.process_export(
                    str(excel_file), 
                    "jira_template_report.xlsx"
                )
                self.logger.info(f"[SUCCESS] Template report generated: {template_report_file}")
                
            except Exception as template_error:
                self.logger.warning(f"[WARNING] Could not generate template report: {template_error}")
                template_report_file = None
            
            # Generate summary
            issue_types = {}
            statuses = {}
            
            for issue in tickets_data['issues']:
                # Count by issue type
                issue_type = issue['fields']['issuetype']['name']
                issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
                
                # Count by status
                status = issue['fields']['status']['name']
                statuses[status] = statuses.get(status, 0) + 1
              # Log summary
            self.logger.info("[SUMMARY] Ticket Summary:")
            self.logger.info("By Issue Type:")
            for issue_type, count in sorted(issue_types.items()):
                self.logger.info(f"  {issue_type}: {count}")
            
            self.logger.info("By Status:")
            for status, count in sorted(statuses.items()):
                self.logger.info(f"  {status}: {count}")
            self.update_status("Jira tickets exported successfully")
            self.logger.info("[SUCCESS] jira-bot completed!")
            self.logger.info("[FILES] Files generated:")
            self.logger.info(f"   - {json_file} (Raw JSON data)")
            self.logger.info(f"   - {csv_file} (CSV format with all fields)")
            self.logger.info(f"   - {excel_file} (Excel with summary sheets)")
            if template_report_file:
                self.logger.info(f"   - {template_report_file} (Template report)")
            
            files_dict = {
                'json': str(json_file),
                'csv': str(csv_file),
                'excel': str(excel_file)
            }
            if template_report_file:
                files_dict['template_report'] = str(template_report_file)
            
            return {
                'message': '[SUCCESS] Jira tickets exported to CSV, Excel, JSON and template report successfully!',
                'data': {
                    'total_tickets': tickets_data['total'],
                    'issue_types': issue_types,
                    'statuses': statuses,
                    'files': files_dict
                }
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error fetching/exporting Jira tickets: {str(e)}")
            self.update_status(f"Error: {str(e)}")
            raise
# Run the bot
if __name__ == "__main__":
    print(">>> Starting jira-bot...")
    
    # Create and run bot
    bot = Bot("jira-bot")
    results = bot.run()
    
    # Print results
    if results['success']:
        print(f"[SUCCESS]: {results['message']}")
        print(f"[TIME] Completed in {results['execution_time']:.2f} seconds")
    else:
        print(f"[FAILED]: {results['message']}")
    
    print(">>> Bot finished!")