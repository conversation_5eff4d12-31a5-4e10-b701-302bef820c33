"""
Email Service Module

Handles email sending functionality using Outlook for the certificate notification bot.
Provides a clean interface for sending certificate notifications with attachments.
"""

import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import sys
import os

# Add framework to path for Outlook import
framework_path = os.path.join(os.path.dirname(__file__), '..', 'framework')
sys.path.insert(0, framework_path)

from outlook import Outlook, OutlookException


class EmailService:
    """
    Service class for handling email operations using Outlook.
    
    This service:
    - Manages Outlook connection and configuration
    - Sends certificate notification emails with proper UTF-8 encoding for Vietnamese text
    - Handles email attachments
    - Provides clean error handling and logging
    """
    
    def __init__(self, outlook_account: str = None, logger: Optional[logging.Logger] = None):
        """
        Initialize the email service.
        
        Args:
            outlook_account: Email account to use for sending (from OpenAutomate assets)
            logger: Optional logger instance
        """
        self.logger = logger or logging.getLogger(__name__)
        self.outlook_account = outlook_account
        self.outlook_client = None
        
        # Initialize Outlook connection
        self._initialize_outlook()
    
    def _initialize_outlook(self):
        """Initialize the Outlook client connection."""
        try:
            self.outlook_client = Outlook()
            self.logger.info("Outlook email service initialized successfully")
            
            if self.outlook_account:
                self.logger.info(f"Configured to send emails from: {self.outlook_account}")
            else:
                self.logger.info("Using default Outlook account for sending emails")
                
        except OutlookException as e:
            self.logger.error(f"Failed to initialize Outlook: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error initializing Outlook: {str(e)}")
            raise OutlookException(f"Failed to initialize Outlook: {str(e)}")
    
    def _ensure_utf8_encoding(self, text: str) -> str:
        """
        Ensure text is properly encoded for Vietnamese characters.
        
        Args:
            text: Input text string
            
        Returns:
            Properly encoded text string
        """
        if not text:
            return text
            
        try:
            # Ensure the text is properly decoded/encoded as UTF-8
            if isinstance(text, bytes):
                text = text.decode('utf-8')
            elif isinstance(text, str):
                # Ensure it can be encoded/decoded properly
                text = text.encode('utf-8').decode('utf-8')
            return text
        except (UnicodeDecodeError, UnicodeEncodeError) as e:
            self.logger.warning(f"Encoding issue with text: {e}")
            # Fallback: remove problematic characters
            return text.encode('utf-8', errors='replace').decode('utf-8')
    
    def send_certificate_notification(self, student_data: Dict[str, Any], 
                                    certificate_path: str, 
                                    recipient_email: str = None) -> bool:
        """
        Send a certificate notification email to a student with proper Vietnamese text encoding.
        
        Args:
            student_data: Dictionary containing student information
            certificate_path: Path to the generated certificate PDF
            recipient_email: Recipient email address (if None, uses email from student data)
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            # Use email from student data if not provided
            if not recipient_email:
                recipient_email = student_data.get('EMAIL', '')
                if not recipient_email:
                    self.logger.error(f"No email address found for student {student_data.get('TEN_UNG_VIEN', 'Unknown')}")
                    return False
            
            # Create email content with proper encoding
            subject = self._ensure_utf8_encoding(self._create_email_subject(student_data))
            body = self._ensure_utf8_encoding(self._create_email_body(student_data))
            
            self.logger.info(f"Sending email with Vietnamese text to {recipient_email}")
            self.logger.debug(f"Email subject: {subject}")
            
            # Prepare attachments
            attachments = []
            if certificate_path and Path(certificate_path).exists():
                attachments.append(certificate_path)
                self.logger.info(f"Attached certificate: {Path(certificate_path).name}")
            else:
                self.logger.warning(f"Certificate file not found: {certificate_path}")
            
            # Send email using Outlook with UTF-8 encoded content
            success, error_message = self.outlook_client.send_mail_message(
                mail_from=self.outlook_account,
                mail_to=recipient_email,
                subject=subject,
                body=body,
                attachments=attachments,
                is_body_html=False
            )
            
            if success:
                self.logger.info(f"Certificate email sent successfully to {student_data['TEN_UNG_VIEN']} ({recipient_email})")
                return True
            else:
                self.logger.error(f"Failed to send email to {recipient_email}: {error_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending certificate email: {str(e)}")
            return False
    
    def _create_email_subject(self, student_data: Dict[str, Any]) -> str:
        """
        Create email subject line with proper Vietnamese text handling.
        
        Args:
            student_data: Student information dictionary
            
        Returns:
            Email subject string with Vietnamese characters
        """
        course_name = student_data.get('TEN_KHOA_HOC', 'Khóa học không xác định')
        return f"Chứng chỉ hoàn thành khóa học - {course_name}"
    
    def _create_email_body(self, student_data: Dict[str, Any]) -> str:
        """
        Create email body content with proper Vietnamese text handling.
        
        Args:
            student_data: Student information dictionary
            
        Returns:
            Email body string with Vietnamese characters
        """
        student_name = student_data.get('TEN_UNG_VIEN', 'Tên không xác định')
        course_name = student_data.get('TEN_KHOA_HOC', 'Khóa học không xác định')
        employee_id = student_data.get('MA_NHAN_VIEN', 'Mã không xác định')
        training_period = student_data.get('THOI_GIAN_DAO_TAO', 'Thời gian không xác định')
        certificate_date = student_data.get('NGAY_CAP_CHUNG_CHI', 'Ngày không xác định')
        
        body = f"""Kính chào {student_name},

Chúc mừng bạn đã hoàn thành khóa học "{course_name}"!

Thông tin chứng chỉ:
- Họ và tên: {student_name}
- Mã nhân viên: {employee_id}
- Khóa học: {course_name}
- Thời gian đào tạo: {training_period}
- Ngày cấp chứng chỉ: {certificate_date}

Chứng chỉ được đính kèm trong email này.

Trân trọng,
Phòng Đào tạo"""
        
        return body
    
    def send_bulk_notifications(self, students_data: List[Dict[str, Any]], 
                              certificates_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Send certificate notifications to multiple students.
        
        Args:
            students_data: List of student data dictionaries
            certificates_data: List of certificate data (student_data, certificate_path)
            
        Returns:
            Dictionary with results summary
        """
        results = {
            'total_attempted': len(certificates_data),
            'emails_sent': 0,
            'emails_failed': 0,
            'failed_students': []
        }
        
        for cert_data in certificates_data:
            student_data = cert_data.get('student_data', {})
            certificate_path = cert_data.get('certificate_path', '')
            
            success = self.send_certificate_notification(student_data, certificate_path)
            
            if success:
                results['emails_sent'] += 1
            else:
                results['emails_failed'] += 1
                results['failed_students'].append(student_data.get('TEN_UNG_VIEN', 'Unknown'))
        
        self.logger.info(f"Bulk email results: {results['emails_sent']} sent, {results['emails_failed']} failed")
        return results
    
    def test_connection(self) -> bool:
        """
        Test the Outlook connection.
        
        Returns:
            True if connection is working, False otherwise
        """
        try:
            if self.outlook_client is None:
                self._initialize_outlook()
            
            self.logger.info("Outlook connection test successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Outlook connection test failed: {str(e)}")
            return False


def test_email_service():
    """Test function for the email service."""
    try:
        # Test data
        student_data = {
            "TEN_UNG_VIEN": "Test Student",
            "TEN_KHOA_HOC": "Test Course",
            "THOI_GIAN_DAO_TAO": "01/01/2024 - 15/01/2024",
            "NGAY_CAP_CHUNG_CHI": "16/01/2024",
            "MA_NHAN_VIEN": "TEST001",
            "EMAIL": "<EMAIL>"
        }
        
        # Initialize email service
        email_service = EmailService()
        
        # Test connection
        if email_service.test_connection():
            print("✅ Email service connection successful")
        else:
            print("❌ Email service connection failed")
            
        # Note: Don't actually send test email to avoid spam
        print("📧 Email service ready for certificate notifications")
        
    except Exception as e:
        print(f"❌ Error testing email service: {str(e)}")


if __name__ == "__main__":
    test_email_service() 