#!/usr/bin/env python3
"""
Excel to JSON Converter for JIRA Tickets
Reads jira_tickets.xlsx and converts to JSON with specific fields only.
"""

import pandas as pd
import json
import os
from pathlib import Path

def excel_to_json(excel_file_path, output_file_path=None):
    """
    Convert Excel file to JSON with specific JIRA fields only.
    
    Args:
        excel_file_path (str): Path to the Excel file
        output_file_path (str): Path for the output JSON file (optional)
    
    Returns:
        str: Path to the output JSON file
    """
    # Define the specific fields we want to extract
    required_fields = [
        'Key', 'Summary', 'Description', 'Issue Type', 'Status', 
        'Priority', 'Resolution', 'Created', 'Updated', 'Resolution Date',
        'Assignee', 'Assignee Email', 'Reporter', 'Reporter Email'
    ]
    
    try:
        # Read the Excel file
        print(f"Reading Excel file: {os.path.abspath(excel_file_path)}")
        df = pd.read_excel(excel_file_path)
        
        # Check if all required fields exist in the Excel file
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            print(f"Warning: Missing fields in Excel file: {missing_fields}")
            # Use only the fields that exist
            available_fields = [field for field in required_fields if field in df.columns]
        else:
            available_fields = required_fields
        
        # Select only the required fields
        filtered_df = df[available_fields].copy()
        
        # Convert dates to string format for JSON serialization
        date_columns = ['Created', 'Updated', 'Resolution Date']
        for col in date_columns:
            if col in filtered_df.columns:
                filtered_df[col] = pd.to_datetime(filtered_df[col], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Handle NaN values
        filtered_df = filtered_df.fillna('')
        
        # Convert to JSON format
        json_data = filtered_df.to_dict('records')
        
        # Set output file path if not provided
        if output_file_path is None:
            base_path = Path(excel_file_path).parent
            output_file_path = base_path / 'jira_tickets_filtered.json'
        
        # Write to JSON file
        with open(output_file_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully converted to JSON: {os.path.abspath(output_file_path)}")
        print(f"Total records: {len(json_data)}")
        print(f"Fields included: {available_fields}")
        
        return str(output_file_path)
        
    except FileNotFoundError:
        print(f"Error: Excel file not found: {excel_file_path}")
        return None
    except Exception as e:
        print(f"Error converting Excel to JSON: {str(e)}")
        return None

def main():
    """Main function to run the conversion."""
    # Define file paths
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent
    excel_file = project_dir / 'jira_tickets.xlsx'
    output_file = project_dir / 'jira_tickets_filtered.json'
    
    # Check if Excel file exists
    if not excel_file.exists():
        print(f"Error: Excel file not found at {excel_file}")
        return
      # Convert Excel to JSON
    result = excel_to_json(str(excel_file), str(output_file))
    
    if result:
        print("Conversion completed successfully!")
        print(f"Output saved to: {result}")
    else:
        print("Conversion failed!")

if __name__ == "__main__":
    main()