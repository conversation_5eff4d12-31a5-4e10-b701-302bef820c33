#!/usr/bin/env python3
"""
Excel Template Writer for JIRA Export Data
Maps JIRA export data to predefined template format
"""

import os
import shutil
from pathlib import Path
import pandas as pd
from openpyxl import load_workbook
from datetime import datetime


class ExcelTemplateWriter:
    def __init__(self, template_path, output_dir="output"):
        """
        Initialize the Excel Template Writer
        
        Args:
            template_path (str): Path to the Excel template file
            output_dir (str): Directory to save output files
        """
        self.template_path = Path(template_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
          # Destination folder configuration
        self.destination_dir = Path(r"D:\OneDrive\SEP490_shared\01 - WIP\Reports")
        self.final_filename = "SE_01_OpenAutomate_Report3_Project Tracking-auto.xlsx"
        
        # Column mapping: template_column -> export_column
        self.column_mapping = {
            "Project / Version / Ticket": "Summary",
            "Key": "Key", 
            "Type": "Issue Type",
            "Assignee": "Assignee",
            "Priority": "Priority",
            "Status": "Status",
            "Finish": "Resolution Date"
        }
        
        # Template configuration
        self.header_row = 7  # Headers are in row 7 (1-indexed)
        self.data_start_row = 9  # Data starts from row 9 (1-indexed)
    
    def read_export_data(self, export_file_path):
        """
        Read JIRA export data from Excel file
        
        Args:
            export_file_path (str): Path to the JIRA export Excel file
              Returns:
            pandas.DataFrame: Export data
        """
        try:
            # Try reading as Excel file
            df = pd.read_excel(export_file_path)
            print(f"Successfully read {len(df)} rows from export file")
            return df
        except Exception as e:
            print(f"Error reading export file: {e}")
            return None
    
    def copy_template(self, output_filename=None):
        """
        Copy template file to output directory
        
        Args:
            output_filename (str): Name for output file. If None, generates timestamp-based name
            
        Returns:
            Path: Path to the copied template file
        """
        if output_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"jira_report_{timestamp}.xlsx"
        
        output_path = self.output_dir / output_filename
        shutil.copy2(self.template_path, output_path)
        print(f"Template copied to: {output_path}")
        return output_path
    
    def _get_template_headers(self, worksheet):
        """
        Extract template headers and their column positions
        
        Args:
            worksheet: Excel worksheet object
            
        Returns:
            dict: Mapping of header names to column numbers
        """
        template_headers = {}
        for col in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=self.header_row, column=col).value
            if cell_value:
                template_headers[cell_value] = col
        return template_headers
    
    def _format_cell_value(self, cell_value):
        """
        Format cell value based on data type
        
        Args:
            cell_value: Raw cell value
            
        Returns:
            Formatted cell value
        """
        if pd.isna(cell_value):
            return ""
        elif isinstance(cell_value, pd.Timestamp):
            return cell_value.strftime("%Y-%m-%d")
        return cell_value
    
    def _write_data_row(self, worksheet, row_data, template_headers, current_row):
        """
        Write a single data row to the worksheet
        
        Args:
            worksheet: Excel worksheet object
            row_data: Pandas Series with row data
            template_headers: Dict mapping header names to column numbers
            current_row: Current row number to write to
        """
        for template_col, export_col in self.column_mapping.items():
            if template_col in template_headers and export_col in row_data.index:
                col_num = template_headers[template_col]
                cell_value = self._format_cell_value(row_data[export_col])
                worksheet.cell(row=current_row, column=col_num, value=cell_value)
    
    def write_data_to_template(self, export_data, output_file_path):
        """
        Write export data to the template file based on column mapping
        
        Args:
            export_data (pandas.DataFrame): JIRA export data
            output_file_path (Path): Path to the output template file
        """
        try:
            # Load the workbook and select active sheet
            workbook = load_workbook(output_file_path)
            worksheet = workbook.active
            
            # Read template headers to understand column positions
            template_headers = self._get_template_headers(worksheet)
            print(f"Found template headers: {list(template_headers.keys())}")
            
            # Write data rows
            current_row = self.data_start_row
            for index, row in export_data.iterrows():
                self._write_data_row(worksheet, row, template_headers, current_row)
                current_row += 1
            
            # Save the workbook
            workbook.save(output_file_path)
            print(f"Successfully wrote {len(export_data)} rows to template")
            
        except Exception as e:
            print(f"Error writing data to template: {e}")
            raise
    
    def process_export(self, export_file_path, output_filename=None):
        """
        Complete process: read export, copy template, write data
        
        Args:
            export_file_path (str): Path to JIRA export file
            output_filename (str): Optional output filename
            
        Returns:
            Path: Path to the generated report file
        """
        print("Starting Excel template processing...")
        
        # Read export data
        export_data = self.read_export_data(export_file_path)
        if export_data is None:
            raise ValueError("Failed to read export data")
        
        # Sort data by Key in ascending order (commented out)
        if 'Key' in export_data.columns:
            export_data = export_data.sort_values('Key', ascending=True)
            print("Data sorted by Key in ascending order")
        else:
            print("Warning: 'Key' column not found, data will not be sorted")
          # Sort data by Finish date (Resolution Date) in ascending order
        # if 'Resolution Date' in export_data.columns:
        #     export_data = export_data.sort_values('Resolution Date', ascending=True, na_position='last')
        #     print("Data sorted by Finish date (Resolution Date) in ascending order")
        # else:
        #     print("Warning: 'Resolution Date' column not found, data will not be sorted")
        
        # Verify required columns exist
        missing_columns = []
        for template_col, export_col in self.column_mapping.items():
            if export_col not in export_data.columns:
                missing_columns.append(export_col)
        
        if missing_columns:
            print(f"Warning: Missing columns in export data: {missing_columns}")
            print(f"Available columns: {list(export_data.columns)}")
        
        # Copy template
        output_path = self.copy_template(output_filename)
          # Write data to template
        self.write_data_to_template(export_data, output_path)
        
        # Copy report to destination folder
        final_destination = self.copy_to_destination(output_path)
        
        print(f"Report generated successfully: {output_path}")
        print(f"Final report location: {final_destination}")
        return final_destination
    
    def copy_to_destination(self, source_file_path):
        """
        Copy the generated report to the destination folder with the final filename
        
        Args:
            source_file_path (Path): Path to the source file to copy
            
        Returns:
            Path: Path to the final destination file
        """
        try:
            # Ensure destination directory exists
            self.destination_dir.mkdir(parents=True, exist_ok=True)
            
            # Final destination path
            destination_path = self.destination_dir / self.final_filename
            
            # Copy file to destination
            shutil.copy2(source_file_path, destination_path)
            print(f"Report copied to final destination: {destination_path}")
            
            return destination_path
            
        except Exception as e:
            print(f"Error copying report to destination: {e}")
            raise
    

def main():
    """
    Main function for command-line usage
    """
    # Configuration
    template_path = "./template/report-template.xlsx"
    export_file = input("Enter path to JIRA export file: ").strip()
    
    if not os.path.exists(export_file):
        print(f"Error: Export file not found: {export_file}")
        return
    
    # Initialize writer
    writer = ExcelTemplateWriter(template_path)
    try:
        # Process the export
        output_file = writer.process_export(export_file)
        print(f"\n[SUCCESS] Report generated: {output_file}")
        
    except Exception as e:
        print(f"[ERROR] Error processing export: {e}")


if __name__ == "__main__":
    main()