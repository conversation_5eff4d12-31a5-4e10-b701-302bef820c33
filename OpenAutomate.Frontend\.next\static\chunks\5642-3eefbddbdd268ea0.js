"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5642],{7283:(e,t,r)=>{r.d(t,{F:()=>j,fetchApi:()=>v});var a=r(48133),s=r(67938);let n=s.$.api.defaultHeaders,l=!1,o=[],i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;o.forEach(r=>{e?r.reject(e):r.resolve(t)}),o=[]},c=async e=>{let t={message:e.statusText,status:e.status};try{let r=await e.json();r.message?(t.message=r.message,t.details=r.details||r.message):r.error?(t.message=r.error,t.details=r.error):t.details=JSON.stringify(r)}catch(r){t.details=e.statusText}return t},d=()=>{window.dispatchEvent(new Event("auth:token-expired"))},u=e=>{if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw{message:"Network error. Please check your connection.",status:0,details:e.message};if(e instanceof Error)throw{message:e.message||"Network error occurred",status:0,details:e.stack||e.message};throw{message:"An unexpected error occurred",status:0,details:String(e)}},h=async e=>{if(204===e.status)return{};let t=e.headers.get("content-type"),r=e.headers.get("content-length");if(!t||-1===t.indexOf("application/json")||"0"===r)return{};let a=await e.text();return a?JSON.parse(a):{}},m=async()=>{if(l)return new Promise((e,t)=>{o.push({resolve:e,reject:t})});l=!0;try{let e=(await v("api/auth/refresh-token",{method:"POST",credentials:"include"})).token;return(0,a.O5)(e),i(null,e),e}catch(e){throw i(e),e}finally{l=!1}},f=async(e,t,r,a,s)=>{if(e.includes("refresh-token")||e.includes("login"))return d(),null;try{let e=await m();if(!e)return null;let a=p(r,s);a.Authorization="Bearer ".concat(e);let{body:n}=g(s),l=await fetch(t,{...r,body:n,headers:a,credentials:"include"});if(l.ok)return h(l);return null}catch(e){return d(),console.error("Token refresh failed:",e),null}},x=e=>{if(e.startsWith("http"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat(s.$.api.baseUrl,"/").concat(t)},g=e=>e?e instanceof FormData?{body:e,headers:{}}:{body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}:{body:void 0,headers:{}},p=(e,t)=>{let r={...t instanceof FormData?{Accept:n.Accept}:{...n},...e.headers};if(!r.Authorization){let e=(0,a.c4)();e&&(r.Authorization="Bearer ".concat(e))}return r};async function v(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,a=x(e),{body:s,headers:n}=g(r),l={...p(t,r),...n};try{let n=await fetch(a,{...t,body:s,headers:l,credentials:"include"});if(n.ok)return h(n);if(401===n.status){let s=await f(e,a,t,l,r);if(s)return s}throw await c(n)}catch(e){if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;throw u(e)}}let j={get:(e,t)=>v(e,{...t,method:"GET"}),post:(e,t,r)=>{let{body:a,headers:s}=g(t);return v(e,{...r,method:"POST",body:a,headers:{...s,...null==r?void 0:r.headers}},t)},put:(e,t,r)=>{let{body:a,headers:s}=g(t);return v(e,{...r,method:"PUT",body:a,headers:{...s,...null==r?void 0:r.headers}},t)},patch:(e,t,r)=>{let{body:a,headers:s}=g(t);return v(e,{...r,method:"PATCH",body:a,headers:{...s,...null==r?void 0:r.headers}},t)},delete:(e,t)=>v(e,{...t,method:"DELETE"})}},17759:(e,t,r)=>{r.d(t,{C5:()=>v,MJ:()=>g,Rr:()=>p,eI:()=>f,lR:()=>x,lV:()=>c,zB:()=>u});var a=r(95155),s=r(12115),n=r(66634),l=r(62177),o=r(36928),i=r(85057);let c=l.Op,d=s.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(d.Provider,{value:{name:t.name},children:(0,a.jsx)(l.xI,{...t})})},h=()=>{let e=s.useContext(d),t=s.useContext(m),{getFieldState:r}=(0,l.xW)(),a=(0,l.lN)({name:e.name}),n=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...n}},m=s.createContext({});function f(e){let{className:t,...r}=e,n=s.useId();return(0,a.jsx)(m.Provider,{value:{id:n},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...r})})}function x(e){let{className:t,...r}=e,{error:s,formItemId:n}=h();return(0,a.jsx)(i.J,{"data-slot":"form-label","data-error":!!s,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:n,...r})}function g(e){let{...t}=e,{error:r,formItemId:s,formDescriptionId:l,formMessageId:o}=h();return(0,a.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":r?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!r,...t})}function p(e){let{className:t,...r}=e,{formDescriptionId:s}=h();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,o.cn)("text-muted-foreground text-sm",t),...r})}function v(e){var t;let{className:r,...s}=e,{error:n,formMessageId:l}=h(),i=n?String(null!==(t=null==n?void 0:n.message)&&void 0!==t?t:""):s.children;return i?(0,a.jsx)("p",{"data-slot":"form-message",id:l,className:(0,o.cn)("text-destructive text-sm",r),...s,children:i}):null}},36928:(e,t,r)=>{r.d(t,{cn:()=>n});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},48133:(e,t,r)=>{r.d(t,{O5:()=>c,c4:()=>i,gV:()=>u,m_:()=>h,wz:()=>d});var a=r(67938);let s=a.$.auth.tokenStorageKey,n=a.$.auth.userStorageKey,l=null,o=null,i=()=>{if(l)return l;try{let e=localStorage.getItem(s);if(e)return l=e,e}catch(e){console.error("Error accessing localStorage",e)}return null},c=e=>{l=e;try{e?localStorage.setItem(s,e):localStorage.removeItem(s)}catch(e){console.error("Error accessing localStorage",e)}},d=()=>{if(o)return o;try{let e=localStorage.getItem(n);if(e)try{let t=JSON.parse(e);return o=t,t}catch(e){console.error("Error parsing stored user data",e)}}catch(e){console.error("Error accessing localStorage",e)}return null},u=e=>{o=e;try{e?localStorage.setItem(n,JSON.stringify(e)):localStorage.removeItem(n)}catch(e){console.error("Error accessing localStorage",e)}},h=()=>{l=null,o=null;try{localStorage.removeItem(s),localStorage.removeItem(n)}catch(e){console.error("Error accessing localStorage",e)}}},55365:(e,t,r)=>{r.d(t,{Fc:()=>i,TN:()=>d,XL:()=>c});var a=r(95155),s=r(12115),n=r(74466),l=r(36928);let o=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),i=s.forwardRef((e,t)=>{let{className:r,variant:s,...n}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,l.cn)(o({variant:s}),r),...n})});i.displayName="Alert";let c=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return s||console.warn("AlertTitle must have content for accessibility"),(0,a.jsx)("h5",{ref:t,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",r),...n,children:s})});c.displayName="AlertTitle";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",r),...s})});d.displayName="AlertDescription"},67938:(e,t,r)=>{r.d(t,{$:()=>a});let a={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},75074:(e,t,r)=>{r.d(t,{Header:()=>S});var a=r(95155),s=r(6874),n=r.n(s),l=r(35695),o=r(36928);function i(e){let{user:t,...r}=e,s=(0,l.usePathname)();return(0,a.jsx)("div",{className:"mr-4 hidden md:flex",...r,children:(0,a.jsx)("nav",{className:"flex items-center space-x-6 text-sm font-medium",children:[{title:"About Us",href:"/about"},{title:"Guides",href:"/guide"},{title:"Contact Us",href:"/contact"}].map(e=>{let{title:t,href:r}=e;return(0,a.jsx)(n(),{href:r,className:(0,o.cn)("relative px-2 py-1.5 transition-all duration-200 hover:text-orange-600",s===r?"text-orange-600 font-semibold after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:bg-orange-600 after:rounded-full":"text-foreground/60 hover:text-orange-600"),children:t},r)})})})}var c=r(12115),d=r(54629),u=r(30285),h=r(11758);function m(e){let{className:t,children:r,...s}=e;return(0,a.jsxs)(h.bL,{"data-slot":"scroll-area",className:(0,o.cn)("relative",t),...s,children:[(0,a.jsx)(h.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:r}),(0,a.jsx)(f,{}),(0,a.jsx)(h.OK,{})]})}function f(e){let{className:t,orientation:r="vertical",...s}=e;return(0,a.jsx)(h.VM,{"data-slot":"scroll-area-scrollbar",orientation:r,className:(0,o.cn)("flex touch-none p-px transition-colors select-none","vertical"===r&&"h-full w-2.5 border-l border-l-transparent","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent",t),...s,children:(0,a.jsx)(h.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}var x=r(38382);function g(e){let{user:t}=e,r=(0,l.usePathname)(),[s,i]=c.useState(!1),h=[{title:"About Us",href:"/about",icon:(0,a.jsx)(d.F.about,{className:"mr-2 h-4 w-4"}),requiresAuth:!0},{title:"Guides",href:"/guide",icon:(0,a.jsx)(d.F.guide,{className:"mr-2 h-4 w-4"}),requiresAuth:!0},{title:"Contact Us",href:"/contact",icon:(0,a.jsx)(d.F.contact,{className:"mr-2 h-4 w-4"}),requiresAuth:!0}];return(0,a.jsxs)(x.cj,{open:s,onOpenChange:i,children:[(0,a.jsx)(x.CG,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-orange-600/10 hover:text-orange-600 focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden",children:[(0,a.jsxs)("svg",{strokeWidth:"1.5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",children:[(0,a.jsx)("path",{d:"M3 5H11",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M3 12H16",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M3 19H21",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,a.jsxs)(x.h,{side:"left",className:"pr-0",children:[(0,a.jsx)("div",{className:"px-7",children:(0,a.jsx)(n(),{href:"/",className:"flex items-center text-lg font-bold hover:text-orange-600 transition-colors",onClick:()=>i(!1),children:"OpenAutomate"})}),(0,a.jsx)(m,{className:"my-4 h-[calc(100vh-8rem)] pb-10 pl-6",children:(0,a.jsx)("div",{className:"pl-1 pr-7",children:(0,a.jsxs)("nav",{className:"flex flex-col space-y-2",children:[h.map(e=>{let{title:t,href:s,icon:l}=e;return(0,a.jsxs)(n(),{href:s,onClick:()=>i(!1),className:(0,o.cn)("flex items-center rounded-md px-3 py-2 text-sm font-medium transition-all duration-200",r===s?"bg-orange-600/10 text-orange-600":"hover:bg-orange-600/10 hover:text-orange-600"),children:[l,t]},s)}),!t&&(0,a.jsxs)(n(),{href:"/login",onClick:()=>i(!1),className:"flex items-center rounded-md px-3 py-2 text-sm font-medium mt-4 bg-orange-600 text-white hover:bg-orange-700 transition-colors",children:[(0,a.jsx)(d.F.user,{className:"mr-2 h-4 w-4"}),"Sign In"]})]})})})]})]})}var p=r(71185),v=r(44838),j=r(53166);function b(e){let{className:t,...r}=e;return(0,a.jsx)(j.bL,{"data-slot":"avatar",className:(0,o.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...r})}function N(e){let{className:t,...r}=e;return(0,a.jsx)(j._V,{"data-slot":"avatar-image",className:(0,o.cn)("aspect-square size-full",t),...r})}function w(e){let{className:t,...r}=e;return(0,a.jsx)(j.H4,{"data-slot":"avatar-fallback",className:(0,o.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...r})}var y=r(67057);function k(e){var t,r;let{user:s}=e,{logout:o}=(0,y.A)(),i=(0,l.useParams)(),c=null==i?void 0:i.tenant;if(!s)return null;let h=e=>c?"/".concat(c).concat(e):"/tenant-selector";return(0,a.jsxs)(v.rI,{children:[(0,a.jsx)(v.ty,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsxs)(b,{className:"h-8 w-8",children:[(0,a.jsx)(N,{src:"/avatars/01.png",alt:s.firstName}),(0,a.jsx)(w,{children:s?"".concat((null===(t=s.firstName)||void 0===t?void 0:t[0])||"").concat((null===(r=s.lastName)||void 0===r?void 0:r[0])||"").toUpperCase():"??"})]})})}),(0,a.jsxs)(v.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,a.jsx)(v.lp,{className:"font-normal",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsxs)("p",{className:"text-sm font-medium leading-none",children:[s.firstName," ",s.lastName]}),(0,a.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:s.email})]})}),(0,a.jsx)(v.mB,{}),(0,a.jsxs)(v.I,{children:[(0,a.jsx)(v._2,{asChild:!0,children:(0,a.jsxs)(n(),{href:h("/tenant-selector"),children:[(0,a.jsx)(d.F.home,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Dashboard"})]})}),(0,a.jsx)(v._2,{asChild:!0,children:(0,a.jsxs)(n(),{href:h("/profile"),children:[(0,a.jsx)(d.F.user,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Profile"})]})}),(0,a.jsx)(v._2,{asChild:!0,children:(0,a.jsxs)(n(),{href:h("/settings"),children:[(0,a.jsx)(d.F.settings,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Settings"})]})}),(0,a.jsx)(v._2,{asChild:!0,children:(0,a.jsxs)(n(),{href:"/tenant-selector",children:[(0,a.jsx)(d.F.settings,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Switch Organization"})]})})]}),(0,a.jsx)(v.mB,{}),(0,a.jsxs)(v._2,{onClick:()=>o(),className:"cursor-pointer",children:[(0,a.jsx)(d.F.logout,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Log out"})]})]})]})}function S(){let{user:e,isAuthenticated:t}=(0,y.A)();return(0,a.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background backdrop-blur supports-[backdrop-filter]:bg-background",children:(0,a.jsxs)("div",{className:"container flex h-16 items-center",children:[(0,a.jsx)(n(),{href:"/",className:"flex items-center space-x-2",children:(0,a.jsx)("span",{className:" font-bold text-xl text-orange-600",children:"OpenAutomate"})}),(0,a.jsx)("div",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",children:(0,a.jsx)(i,{user:e})}),(0,a.jsx)(g,{user:e}),(0,a.jsx)("div",{className:"flex flex-1 items-center justify-end space-x-4",children:(0,a.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.U,{}),t?(0,a.jsx)(k,{user:e}):(0,a.jsx)(n(),{href:"/login",children:(0,a.jsx)(u.$,{variant:"outline",size:"sm",className:"transition-all duration-300 hover:translate-y-[-2px]",children:"Sign In"})})]})})]})})}},85057:(e,t,r)=>{r.d(t,{J:()=>l});var a=r(95155);r(12115);var s=r(24265),n=r(36928);function l(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}}}]);