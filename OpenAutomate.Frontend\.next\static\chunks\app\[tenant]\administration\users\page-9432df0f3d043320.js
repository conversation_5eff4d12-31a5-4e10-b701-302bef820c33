(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9369],{5623:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},7283:(e,t,a)=>{"use strict";a.d(t,{F:()=>j,fetchApi:()=>v});var r=a(48133),s=a(67938);let n=s.$.api.defaultHeaders,l=!1,i=[],o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;i.forEach(a=>{e?a.reject(e):a.resolve(t)}),i=[]},c=async e=>{let t={message:e.statusText,status:e.status};try{let a=await e.json();a.message?(t.message=a.message,t.details=a.details||a.message):a.error?(t.message=a.error,t.details=a.error):t.details=JSON.stringify(a)}catch(a){t.details=e.statusText}return t},d=()=>{window.dispatchEvent(new Event("auth:token-expired"))},u=e=>{if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw{message:"Network error. Please check your connection.",status:0,details:e.message};if(e instanceof Error)throw{message:e.message||"Network error occurred",status:0,details:e.stack||e.message};throw{message:"An unexpected error occurred",status:0,details:String(e)}},m=async e=>{if(204===e.status)return{};let t=e.headers.get("content-type"),a=e.headers.get("content-length");if(!t||-1===t.indexOf("application/json")||"0"===a)return{};let r=await e.text();return r?JSON.parse(r):{}},h=async()=>{if(l)return new Promise((e,t)=>{i.push({resolve:e,reject:t})});l=!0;try{let e=(await v("api/auth/refresh-token",{method:"POST",credentials:"include"})).token;return(0,r.O5)(e),o(null,e),e}catch(e){throw o(e),e}finally{l=!1}},x=async(e,t,a,r,s)=>{if(e.includes("refresh-token")||e.includes("login"))return d(),null;try{let e=await h();if(!e)return null;let r=g(a,s);r.Authorization="Bearer ".concat(e);let{body:n}=p(s),l=await fetch(t,{...a,body:n,headers:r,credentials:"include"});if(l.ok)return m(l);return null}catch(e){return d(),console.error("Token refresh failed:",e),null}},f=e=>{if(e.startsWith("http"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat(s.$.api.baseUrl,"/").concat(t)},p=e=>e?e instanceof FormData?{body:e,headers:{}}:{body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}:{body:void 0,headers:{}},g=(e,t)=>{let a={...t instanceof FormData?{Accept:n.Accept}:{...n},...e.headers};if(!a.Authorization){let e=(0,r.c4)();e&&(a.Authorization="Bearer ".concat(e))}return a};async function v(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0,r=f(e),{body:s,headers:n}=p(a),l={...g(t,a),...n};try{let n=await fetch(r,{...t,body:s,headers:l,credentials:"include"});if(n.ok)return m(n);if(401===n.status){let s=await x(e,r,t,l,a);if(s)return s}throw await c(n)}catch(e){if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;throw u(e)}}let j={get:(e,t)=>v(e,{...t,method:"GET"}),post:(e,t,a)=>{let{body:r,headers:s}=p(t);return v(e,{...a,method:"POST",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},put:(e,t,a)=>{let{body:r,headers:s}=p(t);return v(e,{...a,method:"PUT",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},patch:(e,t,a)=>{let{body:r,headers:s}=p(t);return v(e,{...a,method:"PATCH",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},delete:(e,t)=>v(e,{...t,method:"DELETE"})}},11840:(e,t,a)=>{Promise.resolve().then(a.bind(a,82955))},19040:(e,t,a)=>{"use strict";a.d(t,{Bx:()=>n,Gg:()=>i,H1:()=>l});var r=a(7283);function s(){{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return console.log("Current tenant:",e[1]),e[1]}return console.log("Using default tenant"),"default"}let n=async e=>{let t=s(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[a,r]=e;null!=r&&t.append(a,String(r))}),t.toString()}(e),n="".concat(t,"/odata/OrganizationUnitUsers");return a&&(n+="?".concat(a)),function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:{value:[]}}(await r.F.get(n))},l={getUsers:async e=>(await r.F.get("".concat(e,"/api/ou/users"))).users,getRolesInOrganizationUnit:async e=>r.F.get("".concat(e,"/api/ou/users/roles")),assignRolesBulk:async(e,t)=>{let a=s(),n=t.map(e=>e.trim()),l="".concat(a,"/api/author/user/").concat(e,"/assign-multiple-roles");try{return await r.F.post(l,{authorityIds:n})}catch(e){throw console.error("Error assigning roles:",e),e}}},i=async e=>{let t=s();await r.F.delete("".concat(t,"/api/ou/users/").concat(e))}},24265:(e,t,a)=>{"use strict";a.d(t,{b:()=>d});var r=a(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}a(47650);var n=a(95155),l=Symbol("radix.slottable");function i(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...n}=e;if(r.isValidElement(a)){var l;let e,i;let o=(l=a,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let a={...t};for(let r in t){let s=e[r],n=t[r];/^on[A-Z]/.test(r)?s&&n?a[r]=(...e)=>{n(...e),s(...e)}:s&&(a[r]=s):"style"===r?a[r]={...s,...n}:"className"===r&&(a[r]=[s,n].filter(Boolean).join(" "))}return{...e,...a}}(n,a.props);return a.type!==r.Fragment&&(c.ref=t?function(...e){return t=>{let a=!1,r=e.map(e=>{let r=s(e,t);return a||"function"!=typeof r||(a=!0),r});if(a)return()=>{for(let t=0;t<r.length;t++){let a=r[t];"function"==typeof a?a():s(e[t],null)}}}}(t,o):o),r.cloneElement(a,c)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=r.forwardRef((e,a)=>{let{children:s,...l}=e,o=r.Children.toArray(s),c=o.find(i);if(c){let e=c.props.children,s=o.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...l,ref:a,children:r.isValidElement(e)?r.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...l,ref:a,children:s})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:s,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?a:t,{...l,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),c=r.forwardRef((e,t)=>(0,n.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var d=c},36928:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}},46102:(e,t,a)=>{"use strict";a.d(t,{Bc:()=>l,ZI:()=>c,k$:()=>o,m_:()=>i});var r=a(95155);a(12115);var s=a(9635),n=a(36928);function l(e){let{delayDuration:t=0,...a}=e;return(0,r.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function i(e){let{...t}=e;return(0,r.jsx)(l,{children:(0,r.jsx)(s.bL,{"data-slot":"tooltip",...t})})}function o(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"tooltip-trigger",...t})}function c(e){let{className:t,sideOffset:a=0,children:l,...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...i,children:[l,(0,r.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},48133:(e,t,a)=>{"use strict";a.d(t,{O5:()=>c,c4:()=>o,gV:()=>u,m_:()=>m,wz:()=>d});var r=a(67938);let s=r.$.auth.tokenStorageKey,n=r.$.auth.userStorageKey,l=null,i=null,o=()=>{if(l)return l;try{let e=localStorage.getItem(s);if(e)return l=e,e}catch(e){console.error("Error accessing localStorage",e)}return null},c=e=>{l=e;try{e?localStorage.setItem(s,e):localStorage.removeItem(s)}catch(e){console.error("Error accessing localStorage",e)}},d=()=>{if(i)return i;try{let e=localStorage.getItem(n);if(e)try{let t=JSON.parse(e);return i=t,t}catch(e){console.error("Error parsing stored user data",e)}}catch(e){console.error("Error accessing localStorage",e)}return null},u=e=>{i=e;try{e?localStorage.setItem(n,JSON.stringify(e)):localStorage.removeItem(n)}catch(e){console.error("Error accessing localStorage",e)}},m=()=>{l=null,i=null;try{localStorage.removeItem(s),localStorage.removeItem(n)}catch(e){console.error("Error accessing localStorage",e)}}},49103:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>h,Es:()=>f,HM:()=>u,L3:()=>p,c7:()=>x,lG:()=>o,rr:()=>g,zM:()=>c});var r=a(95155),s=a(12115),n=a(59096),l=a(54416),i=a(36928);let o=n.bL,c=n.l9,d=n.ZL,u=n.bm,m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",a),...s})});m.displayName=n.hJ.displayName;let h=s.forwardRef((e,t)=>{let{className:a,children:s,...o}=e;return(0,r.jsxs)(d,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(n.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",a),...o,children:[s,(0,r.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});h.displayName=n.UC.displayName;let x=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};x.displayName="DialogHeader";let f=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};f.displayName="DialogFooter";let p=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...s})});p.displayName=n.hE.displayName;let g=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...s})});g.displayName=n.VY.displayName},66932:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},67938:(e,t,a)=>{"use strict";a.d(t,{$:()=>r});let r={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},74126:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},79285:(e,t,a)=>{"use strict";a.d(t,{g:()=>s});var r=a(7283);let s={inviteUser:async(e,t)=>r.F.post("".concat(e,"/api/organization-unit-invitation"),{email:t}),acceptInvitation:async(e,t)=>{try{let a=await r.F.post("".concat(e,"/api/organization-unit-invitation/accept"),{token:t});if(!a)throw Error("Empty response received");if(void 0===a.success)return{success:!0};return a}catch(e){return function(e){var t,a;if((null==e?void 0:e.status)===409)return{success:!0};if(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)throw Error(e.response.data.message);if(null==e?void 0:e.message)throw Error(e.message);throw Error("Failed to accept invitation.")}(e)}},checkInvitation:async(e,t)=>r.F.get("".concat(e,"/api/organization-unit-invitation/check?email=").concat(encodeURIComponent(t))),checkInvitationToken:async(e,t)=>{try{return await r.F.get("".concat(e,"/api/organization-unit-invitation/check-token?token=").concat(encodeURIComponent(t)))}catch(e){if(console.error("Error checking invitation token:",e),(null==e?void 0:e.status)===404)return{status:"Invalid",recipientEmail:"",expiresAt:"",organizationUnitId:""};throw e}},listInvitations:async(e,t)=>{let a="/".concat(e,"/odata/OrganizationUnitInvitations");if(t){let e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,r]=t;void 0!==r&&e.append(a,r)}),a+="?".concat(e.toString())}return function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:"object"==typeof e&&null!==e&&"invitations"in e?{value:e.invitations,"@odata.count":e.count}:{value:[]}}(await r.F.get(a))}}},82955:(e,t,a)=>{"use strict";a.d(t,{default:()=>H});var r=a(95155),s=a(12115),n=a(49103),l=a(30285),i=a(47262),o=a(46102),c=a(87570),d=a(5623);let u=(0,a(19946).A)("user-check",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);var m=a(74126),h=a(54416),x=a(51154),f=a(54165),p=a(19040),g=a(88262),v=a(44838),j=a(34953),y=a(45567),w=a(85127),N=a(59409);function b(e){let{isOpen:t,onClose:a,userId:n,email:i,refreshUsersList:c}=e,{toast:d}=(0,g.d)(),u=window.location.pathname.split("/")[1],{data:h,isLoading:x}=(0,j.Ay)(t&&u?"ou-roles-".concat(u):null,()=>p.H1.getRolesInOrganizationUnit(u)),{data:v,isLoading:b}=(0,j.Ay)(t&&u?"ou-users-".concat(u):null,()=>p.H1.getUsers(u)),S=(0,s.useMemo)(()=>{if(!v)return[];let e=v.find(e=>e.userId===n);return e?(e.roles||[]).map(e=>{let t=null==h?void 0:h.find(t=>t.name===e);return t?{id:t.id,name:t.name,description:t.description}:{id:e,name:e,description:""}}):[]},[v,n,h]),[A,k]=(0,s.useState)(""),[C,E]=(0,s.useState)([]),[R,I]=(0,s.useState)(!1);s.useEffect(()=>{t&&(S&&Array.isArray(S)?E(S):E([]))},[t,S]);let z=(0,s.useMemo)(()=>h?h.filter(e=>!C.some(t=>t.id===e.id)):[],[h,C]),L=e=>{E(t=>t.filter(t=>t.id!==e))},$=async()=>{I(!0);try{await p.H1.assignRolesBulk(n,C.map(e=>e.id)),d({title:"Success",description:"Roles updated successfully."}),E([]),(0,y.j)("user-roles-".concat(n)),(0,y.j)(e=>Array.isArray(e)&&"organization-unit-users"===e[0]),c&&c(),a()}catch(e){console.error("Failed to update roles:",e),d({title:"Error",description:"Failed to update roles.",variant:"destructive"})}finally{I(!1)}};return(0,r.jsx)(f.lG,{open:t,onOpenChange:a,children:(0,r.jsxs)(f.Cf,{className:"max-w-[700px]",children:[(0,r.jsx)(f.c7,{children:(0,r.jsxs)(f.L3,{children:["Set Roles for ",i]})}),(0,r.jsxs)("div",{className:"flex gap-2 items-end mb-4",children:[(0,r.jsxs)(N.l6,{value:A,onValueChange:k,children:[(0,r.jsx)(N.bq,{className:"w-64",children:(0,r.jsx)(N.yv,{placeholder:"Select a role to add"})}),(0,r.jsx)(N.gC,{children:z.map(e=>(0,r.jsx)(N.eb,{value:e.id,children:e.name},e.id))})]}),(0,r.jsx)(l.$,{onClick:()=>{if(!A)return;let e=null==h?void 0:h.find(e=>e.id===A);e&&!C.some(t=>t.id===e.id)&&(E(t=>[...t,e]),k(""))},disabled:!A||x||b,type:"button",children:"Add"})]}),(0,r.jsx)("div",{className:"w-full max-w-[650px] max-h-[50vh] overflow-y-auto overflow-x-auto",children:(0,r.jsxs)(w.XI,{className:"min-w-full table-fixed",children:[(0,r.jsx)(w.A0,{children:(0,r.jsxs)(w.Hj,{children:[(0,r.jsx)(w.nd,{className:"w-1/3 text-left max-w-[300px] truncate",children:"Role Name"}),(0,r.jsx)(w.nd,{className:"w-1/3 text-left max-w-[200px] truncate",children:"Description"}),(0,r.jsx)(w.nd,{className:"w-1/3 text-right",children:"Action"})]})}),(0,r.jsxs)(w.BF,{children:[b&&(0,r.jsx)(w.Hj,{children:(0,r.jsx)(w.nA,{colSpan:3,className:"text-center text-muted-foreground",children:"Loading..."})}),!b&&0===C.length&&(0,r.jsx)(w.Hj,{children:(0,r.jsx)(w.nA,{colSpan:3,className:"text-center text-muted-foreground",children:"No roles added"})}),!b&&C.length>0&&(0,r.jsx)(r.Fragment,{children:C.map(e=>(0,r.jsxs)(w.Hj,{children:[(0,r.jsx)(w.nA,{className:"w-1/3 text-left max-w-[300px] truncate",children:(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("span",{className:"truncate cursor-pointer",children:e.name})}),(0,r.jsx)(o.ZI,{children:e.name})]})})}),(0,r.jsx)(w.nA,{className:"w-1/3 text-left max-w-[200px] truncate",children:e.description?(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("span",{className:"truncate cursor-pointer",children:e.description})}),(0,r.jsx)(o.ZI,{children:e.description})]})}):null}),(0,r.jsx)(w.nA,{className:"w-1/3 text-right",children:(0,r.jsx)(l.$,{size:"icon",variant:"ghost",onClick:()=>L(e.id),className:"text-red-400 hover:text-red-600",children:(0,r.jsx)(m.A,{className:"w-4 h-4"})})})]},e.id))})]})]})}),(0,r.jsxs)(f.Es,{children:[(0,r.jsx)(l.$,{variant:"outline",onClick:a,type:"button",children:"Cancel"}),(0,r.jsx)(l.$,{onClick:$,disabled:R||x||b,type:"button",children:R?"Saving...":"Save"})]})]})})}function S(e){let{row:t,onDeleted:a}=e,[n,i]=(0,s.useState)(!1),[o,c]=(0,s.useState)(!1),[j,y]=(0,s.useState)(!1),{toast:w}=(0,g.d)(),N=async()=>{c(!0);try{await (0,p.Gg)(t.original.userId),i(!1),a&&a()}catch(t){let e="Delete failed!";t&&"object"==typeof t&&"message"in t&&"string"==typeof t.message&&(e=t.message),w({title:"Delete User Failed",description:e,variant:"destructive",style:{background:"#ff6a6a",color:"#fff"}})}finally{c(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(v.rI,{children:[(0,r.jsx)(v.ty,{asChild:!0,children:(0,r.jsxs)(l.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,r.jsxs)(v.SQ,{align:"start",className:"w-[160px]",children:[(0,r.jsxs)(v._2,{onClick:()=>y(!0),className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"min-w-[1.25rem] flex justify-center",children:(0,r.jsx)(u,{className:"w-4 h-4 text-foreground"})}),(0,r.jsx)("span",{children:"Set Role"})]}),(0,r.jsxs)(v._2,{className:"flex items-center gap-2 text-destructive focus:text-destructive",onClick:()=>i(!0),children:[(0,r.jsx)("span",{className:"min-w-[1.25rem] flex justify-center",children:(0,r.jsx)(m.A,{className:"w-4 h-4 text-destructive","aria-hidden":"true"})}),(0,r.jsx)("span",{children:"Delete"})]})]})]}),(0,r.jsx)(b,{isOpen:j,onClose:()=>y(!1),userId:t.original.userId,email:t.original.email}),(0,r.jsx)(f.lG,{open:n,onOpenChange:i,children:(0,r.jsxs)(f.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsxs)(f.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,r.jsx)(f.c7,{children:(0,r.jsx)(f.L3,{children:"Confirm Delete"})}),(0,r.jsxs)("div",{children:["Are you sure you want to delete this user ",(0,r.jsx)("b",{children:t.original.email}),"?"]}),(0,r.jsxs)(f.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,r.jsx)(l.$,{variant:"outline",onClick:()=>i(!1),disabled:o,children:"Cancel"}),(0,r.jsxs)(l.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:N,disabled:o,children:[o&&(0,r.jsx)(x.A,{className:"animate-spin w-4 h-4 mr-2"}),o?"Deleting...":"Delete"]})]})]})})]})}let A=[{id:"select",header:e=>{let{table:t}=e;return(0,r.jsx)(i.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e;return(0,r.jsx)(i.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]"})},enableSorting:!1,enableHiding:!1},{id:"actions",header:"Action",cell:e=>{let{row:t}=e;return(0,r.jsx)(S,{row:t})}},{accessorKey:"email",header:e=>{let{column:t}=e;return(0,r.jsx)(c.w,{column:t,title:"Email"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("span",{children:t.getValue("email")})}},{accessorKey:"firstName",header:e=>{let{column:t}=e;return(0,r.jsx)(c.w,{column:t,title:"First Name"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("span",{children:t.getValue("firstName")})}},{accessorKey:"lastName",header:e=>{let{column:t}=e;return(0,r.jsx)(c.w,{column:t,title:"Last Name"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("span",{children:t.getValue("lastName")})}},{accessorKey:"roles",header:e=>{let{column:t}=e;return(0,r.jsx)(c.w,{column:t,title:"Roles"})},cell:e=>{let{row:t}=e,a=t.getValue("roles"),s=[];return Array.isArray(a)?s=a:"string"==typeof a&&(s=a.split(",").map(e=>e.trim()).filter(Boolean)),(0,r.jsx)(o.Bc,{children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 max-w-[320px]",children:[s.slice(0,3).map(e=>(0,r.jsx)("span",{className:"px-2 py-0.5 rounded text-xs truncate max-w-[90px] bg-muted text-muted-foreground dark:bg-secondary dark:text-secondary-foreground",title:e,children:e},e)),s.length>3&&(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsxs)("span",{className:"text-xs text-gray-500 cursor-pointer",children:["+",s.length-3," more"]})}),(0,r.jsx)(o.ZI,{children:s.slice(3).join(", ")})]})]})})}},{accessorKey:"joinedAt",header:e=>{let{column:t}=e;return(0,r.jsx)(c.w,{column:t,title:"Joined At"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("span",{children:t.getValue("joinedAt")})}}];var k=a(54333),C=a(85057),E=a(35695),R=a(79285);function I(e){let{isOpen:t,onClose:a}=e,[n,i]=(0,s.useState)(""),[o,c]=(0,s.useState)(!1),{toast:d}=(0,g.d)(),u=(0,E.useParams)().tenant,m=async e=>{if(e.preventDefault(),!n.trim()){d({title:"No email entered",description:"Please enter an email address.",variant:"destructive"});return}c(!0);try{if((await R.g.checkInvitation(u,n.trim())).invited){d({title:"Email already invited",description:"".concat(n.trim()," has already been invited to this organization."),variant:"default"}),c(!1);return}await R.g.inviteUser(u,n.trim()),d({title:"Invitation sent",description:"Successfully sent invitation to ".concat(n.trim())}),i(""),a()}catch(a){let e="";if("object"==typeof a&&null!==a){var t,r;e=null!==(r=null!==(t=a.details)&&void 0!==t?t:a.message)&&void 0!==r?r:""}else"string"==typeof a&&(e=a);d({title:"Failed to send invitation",description:(null==e?void 0:e.includes("already a member of this organization"))?"This user is already a member of this organization.":e||"An unknown error occurred",variant:"destructive",style:{background:"#ff6a6a",color:"#fff"}})}finally{c(!1)}};return(0,r.jsx)(f.lG,{open:t,onOpenChange:a,children:(0,r.jsxs)(f.Cf,{className:"sm:max-w-[400px] p-6",children:[(0,r.jsx)(f.c7,{children:(0,r.jsx)(f.L3,{children:"Invite User"})}),(0,r.jsxs)("form",{onSubmit:m,children:[(0,r.jsx)("div",{className:"grid gap-4 py-4",children:(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(C.J,{htmlFor:"email",children:"Email address"}),(0,r.jsx)("input",{id:"email",type:"email",value:n,onChange:e=>i(e.target.value),placeholder:"Enter email address",className:"flex-1 bg-background border rounded-md p-2 text-sm",required:!0})]})}),(0,r.jsxs)(f.Es,{className:"pt-4",children:[(0,r.jsx)(l.$,{variant:"outline",onClick:a,type:"button",children:"Cancel"}),(0,r.jsx)(l.$,{type:"submit",disabled:o,children:o?"Sending...":"Send Invitation"})]})]})]})})}var z=a(47924),L=a(66932),$=a(62523),O=a(29797),P=a(26126);let F=[{id:"select",header:e=>{let{table:t}=e;return(0,r.jsx)(i.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e;return(0,r.jsx)(i.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]"})},enableSorting:!1,enableHiding:!1},{id:"actions",header:"Action",cell:()=>(0,r.jsx)(T,{})},{accessorKey:"recipientEmail",header:e=>{let{column:t}=e;return(0,r.jsx)(c.w,{column:t,title:"Email"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("span",{children:t.getValue("recipientEmail")})},enableSorting:!0},{accessorKey:"status",header:e=>{let{column:t}=e;return(0,r.jsx)(c.w,{column:t,title:"Status"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("span",{children:t.getValue("status")})},enableSorting:!0},{accessorKey:"expiresAt",header:e=>{let{column:t}=e;return(0,r.jsx)(c.w,{column:t,title:"Expires At"})},cell:e=>{let{row:t}=e;try{let e;let a=t.getValue("expiresAt");if(!a)return(0,r.jsx)("span",{children:"-"});if("string"==typeof a||"number"==typeof a)e=String(a);else{if(!(a instanceof Date))return console.warn("Invalid date format received:",a),(0,r.jsx)("span",{children:"Invalid format"});e=a.toISOString()}let s=new Date(e);if(isNaN(s.getTime()))return(0,r.jsx)("span",{children:"Invalid date"});return(0,r.jsx)("span",{children:s.toISOString().replace("T"," ").slice(0,10)})}catch(e){return console.error("Error formatting date:",e),(0,r.jsx)("span",{children:"Error"})}},enableSorting:!0}];function T(){return(0,r.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}let U=["All","Pending","Accepted","Expired","Revoked"];function M(){var e,t;let a=(0,E.useParams)().tenant,[i,o]=(0,s.useState)(""),[c,d]=(0,s.useState)("All"),[u,m]=(0,s.useState)(0),[x,f]=(0,s.useState)(10),[p,g]=(0,s.useState)(!1),v=(0,s.useRef)(0),[y,w]=(0,s.useState)(!1),b=(0,s.useRef)(null),S=(0,s.useRef)(null),A={$filter:[i?"contains(tolower(recipientEmail),'".concat(i.toLowerCase(),"')"):void 0,"All"!==c?"status eq '".concat(c,"'"):void 0].filter(Boolean).join(" and ")||void 0,$top:x,$skip:u*x,$count:!0},{data:C,error:T,isLoading:M,mutate:D}=(0,j.Ay)(["organization-invitations",a,A],()=>R.g.listInvitations(a,A));(0,s.useEffect)(()=>{document.activeElement!==b.current&&null!==S.current&&b.current&&(b.current.focus(),b.current.setSelectionRange(S.current,S.current))},[M]);let _=null!==(e=null==C?void 0:C.value)&&void 0!==e?e:[],V=null!==(t=null==C?void 0:C["@odata.count"])&&void 0!==t?t:_.length;(0,s.useEffect)(()=>{if(C){if("number"==typeof C["@odata.count"])v.current=C["@odata.count"],w(!0);else{let e=u*x+_.length;e>v.current&&(v.current=e),_.length===x&&0===u&&(v.current=e+1),w(!1)}}},[C,u,x,_.length]);let H=(0,s.useMemo)(()=>{let e=Math.max(1,Math.ceil(V/x)),t=_.length===x&&V<=x*(u+1),a=u+1;return t?Math.max(a,e,u+2):Math.max(a,e)},[V,x,u,_.length]),B=!y&&_.length===x,K=i||"All"!==c,J=[i,"All"!==c?c:""].filter(Boolean).length;return(0,r.jsxs)("div",{className:"flex flex-col h-full w-full space-y-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center w-full flex-wrap gap-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Invitations"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[V>0&&(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("span",{children:["Total: ",V," invitation",1!==V?"s":""]})}),(0,r.jsxs)(l.$,{onClick:()=>g(!0),className:"flex items-center justify-center",children:[(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"})," Invite User"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mb-2 w-full",children:[(0,r.jsxs)("div",{className:"relative w-48",children:[(0,r.jsx)(z.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)($.p,{ref:b,placeholder:"Search by Email",value:i,onChange:e=>{S.current=e.target.selectionStart,o(e.target.value)},className:"pl-8 pr-8",disabled:M,onFocus:e=>{S.current=e.target.selectionStart}}),i&&(0,r.jsx)(h.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>o("")})]}),(0,r.jsx)("div",{className:"relative w-48",children:(0,r.jsxs)(N.l6,{value:c,onValueChange:d,disabled:M,children:[(0,r.jsx)(N.bq,{className:"pl-8",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(L.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)(N.yv,{placeholder:"Filter Status"})]})}),(0,r.jsxs)(N.gC,{children:[(0,r.jsx)(N.eb,{value:"All",children:"All Statuses"}),U.filter(e=>"All"!==e).map(e=>(0,r.jsx)(N.eb,{value:e,children:e},e))]})]})}),J>0&&(0,r.jsxs)(P.E,{variant:"secondary",className:"rounded-sm px-1",children:[J," active ",1===J?"filter":"filters"]}),K&&(0,r.jsxs)(l.$,{variant:"ghost",onClick:()=>{o(""),d("All")},className:"h-8 px-2 lg:px-3",disabled:M,children:["Reset",(0,r.jsx)(h.A,{className:"ml-2 h-4 w-4"})]})]}),(0,r.jsx)(k.b,{columns:F,data:_,isLoading:M,totalCount:V}),(0,r.jsx)(O.d,{currentPage:u+1,pageSize:x,totalCount:V,totalPages:H,isUnknownTotalCount:B,onPageChange:e=>m(e-1),onPageSizeChange:f}),T&&(0,r.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,r.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load invitations"}),(0,r.jsx)(l.$,{variant:"outline",className:"mt-2",onClick:()=>D(),children:"Retry"})]}),!M&&0===_.length&&!T&&(0,r.jsx)("div",{className:"text-center py-10 text-muted-foreground",children:(0,r.jsx)("p",{children:"No invitations found."})}),(0,r.jsx)(I,{isOpen:p,onClose:()=>{g(!1),D()}})]})}var D=a(55594);function _(e){let{searchEmail:t,setSearchEmail:a,searchFirstName:n,setSearchFirstName:i,searchLastName:c,setSearchLastName:d,searchRole:u,setSearchRole:m,roleOptions:x,loading:f=!1,onReset:p}=e,g=[t,n,c,"ALL"!==u?u:""].filter(Boolean).length,v=(0,s.useRef)(null),j=(0,s.useRef)(null),y=(0,s.useRef)(null),w=(0,s.useRef)(null),b=(0,s.useRef)(null),S=(0,s.useRef)(null);return(0,s.useEffect)(()=>{document.activeElement!==v.current&&null!==w.current&&v.current&&(v.current.focus(),v.current.setSelectionRange(w.current,w.current)),document.activeElement!==j.current&&null!==b.current&&j.current&&(j.current.focus(),j.current.setSelectionRange(b.current,b.current)),document.activeElement!==y.current&&null!==S.current&&y.current&&(y.current.focus(),y.current.setSelectionRange(S.current,S.current))},[f]),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mb-2",children:[(0,r.jsxs)("div",{className:"relative w-48",children:[(0,r.jsx)(z.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)($.p,{ref:v,placeholder:"Search by Email",value:t,onChange:e=>{w.current=e.target.selectionStart,a(e.target.value)},className:"pl-8 pr-8",disabled:f,onFocus:e=>{w.current=e.target.selectionStart}}),t&&(0,r.jsx)(h.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>a("")})]}),(0,r.jsxs)("div",{className:"relative w-48",children:[(0,r.jsx)(z.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)($.p,{ref:j,placeholder:"Search by First Name",value:n,onChange:e=>{b.current=e.target.selectionStart,i(e.target.value)},className:"pl-8 pr-8",disabled:f,onFocus:e=>{b.current=e.target.selectionStart}}),n&&(0,r.jsx)(h.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>i("")})]}),(0,r.jsxs)("div",{className:"relative w-48",children:[(0,r.jsx)(z.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)($.p,{ref:y,placeholder:"Search by Last Name",value:c,onChange:e=>{S.current=e.target.selectionStart,d(e.target.value)},className:"pl-8 pr-8",disabled:f,onFocus:e=>{S.current=e.target.selectionStart}}),c&&(0,r.jsx)(h.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>d("")})]}),(0,r.jsx)("div",{className:"relative w-48",children:(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsxs)(N.l6,{value:u,onValueChange:m,disabled:f,children:[(0,r.jsx)(N.bq,{className:"pl-8 max-w-[180px] truncate",children:(0,r.jsxs)("div",{className:"flex items-center min-w-0",children:[(0,r.jsx)(L.A,{className:"mr-2 h-4 w-4 shrink-0"}),(0,r.jsx)("span",{className:"truncate",title:"ALL"!==u?u:"All Roles",children:(0,r.jsx)(N.yv,{placeholder:"All Roles"})})]})}),(0,r.jsxs)(N.gC,{children:[(0,r.jsx)(N.eb,{value:"ALL",children:"All Roles"}),x.map(e=>(0,r.jsx)(N.eb,{value:e,children:e},e))]})]})}),(0,r.jsx)(o.ZI,{children:"ALL"!==u?u:"All Roles"})]})})}),g>0&&(0,r.jsxs)(P.E,{variant:"secondary",className:"rounded-sm px-1",children:[g," active ",1===g?"filter":"filters"]}),g>0&&(0,r.jsxs)(l.$,{variant:"ghost",onClick:p,className:"h-8 px-2 lg:px-3",disabled:f,children:["Reset",(0,r.jsx)(h.A,{className:"ml-2 h-4 w-4"})]})]})}function V(e,t){let[a,r]=(0,s.useState)(e);return(0,s.useEffect)(()=>{let a=setTimeout(()=>r(e),t);return()=>clearTimeout(a)},[e,t]),a}function H(){var e,t;let[a,i]=(0,s.useState)(""),[o,c]=(0,s.useState)(""),[d,u]=(0,s.useState)(""),[m,h]=(0,s.useState)("ALL"),[x,f]=(0,s.useState)(!1),[g,v]=(0,s.useState)("user"),[y,w]=(0,s.useState)(0),[N,b]=(0,s.useState)(10),C=(0,s.useRef)(0),[E,R]=(0,s.useState)(!1),z=V(a,400),L=V(o,400),$=V(d,400),P=window.location.pathname.split("/")[1],{data:F}=(0,j.Ay)(P?"ou-roles-".concat(P):null,()=>p.H1.getRolesInOrganizationUnit(P)),T=(0,s.useMemo)(()=>{if(!F)return[];let e=new Map;for(let t of F){let a=t.name.trim().toLowerCase();e.has(a)||e.set(a,t.name)}return Array.from(e.values())},[F]),U={$filter:[z?"contains(tolower(email),'".concat(z.toLowerCase(),"')"):void 0,L?"contains(tolower(firstName),'".concat(L.toLowerCase(),"')"):void 0,$?"contains(tolower(lastName),'".concat($.toLowerCase(),"')"):void 0,"ALL"!==m?"roles/any(r: tolower(r) eq '".concat(m.toLowerCase(),"')"):void 0].filter(Boolean).join(" and ")||void 0,$top:N,$skip:y*N,$count:!0},{data:D,error:H,isLoading:B,mutate:K}=(0,j.Ay)(["organization-unit-users",U],()=>(0,p.Bx)(U),{keepPreviousData:!0}),J=null!==(e=null==D?void 0:D.value)&&void 0!==e?e:[],q=null!==(t=null==D?void 0:D["@odata.count"])&&void 0!==t?t:J.length;(0,s.useEffect)(()=>{if(D){if("number"==typeof D["@odata.count"])C.current=D["@odata.count"],R(!0);else{let e=y*N+J.length;e>C.current&&(C.current=e),J.length===N&&0===y&&(C.current=e+1),R(!1)}}},[D,y,N,J.length]);let Z=(0,s.useMemo)(()=>{let e=Math.max(1,Math.ceil(q/N)),t=J.length===N&&q<=N*(y+1),a=y+1;return t?Math.max(a,e,y+2):Math.max(a,e)},[q,N,y,J.length]),G=!E&&J.length===N,[W,Q]=(0,s.useState)(null);(0,s.useEffect)(()=>{H?Q("Failed to load users"):Q(null)},[H]);let Y=A.map(e=>"actions"===e.id?{...e,cell:e=>{let{row:t}=e;return(0,r.jsx)(S,{row:t,onDeleted:K})}}:e);return(0,r.jsxs)("div",{className:"flex flex-col h-full w-full space-y-8",children:[(0,r.jsx)("div",{className:"mb-4 border-b w-full",children:(0,r.jsxs)("nav",{className:"flex space-x-8","aria-label":"Tabs",children:[(0,r.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"user"===g,type:"button",onClick:()=>v("user"),children:"User"}),(0,r.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"invitation"===g,type:"button",onClick:()=>v("invitation"),children:"Invitation"})]})}),"user"===g&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center w-full flex-wrap gap-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Users"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[q>0&&(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("span",{children:["Total: ",q," user",1!==q?"s":""]})}),(0,r.jsxs)(l.$,{onClick:()=>f(!0),className:"flex items-center justify-center",children:[(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"})," Invite User"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,r.jsx)(_,{searchEmail:a,setSearchEmail:i,searchFirstName:o,setSearchFirstName:c,searchLastName:d,setSearchLastName:u,searchRole:m,setSearchRole:h,roleOptions:T,loading:B,onReset:()=>{i(""),c(""),u(""),h("ALL")}}),(0,r.jsx)(k.b,{columns:Y,data:J.map(function(e){return{userId:e.userId,email:e.email,firstName:e.firstName,lastName:e.lastName,roles:Array.isArray(e.roles)?e.roles.join(", "):"",joinedAt:new Date(e.joinedAt).toISOString().replace("T"," ").slice(0,10)}}),isLoading:B,totalCount:q}),(0,r.jsx)(O.d,{currentPage:y+1,pageSize:N,totalCount:q,totalPages:Z,isUnknownTotalCount:G,onPageChange:e=>w(e-1),onPageSizeChange:b}),W&&(0,r.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,r.jsx)("p",{className:"text-red-800 dark:text-red-300",children:W}),(0,r.jsx)(l.$,{variant:"outline",className:"mt-2",onClick:()=>K(),children:"Retry"})]}),(0,r.jsx)(I,{isOpen:x,onClose:()=>f(!1)})]})]}),"invitation"===g&&(0,r.jsx)(M,{})]})}D.z.object({email:D.z.string(),firstName:D.z.string(),lastName:D.z.string(),roles:D.z.string(),joinedAt:D.z.string()})},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(95155);a(12115);var s=a(24265),n=a(36928);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},88262:(e,t,a)=>{"use strict";a.d(t,{$:()=>s,d:()=>n});var r=a(12115);let s=(0,r.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function n(){let e=(0,r.useContext)(s);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,2178,8852,5699,5594,8523,9483,3085,9635,5224,8441,1684,7358],()=>t(11840)),_N_E=e.O()}]);