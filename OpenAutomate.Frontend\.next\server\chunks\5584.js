"use strict";exports.id=5584,exports.ids=[5584],exports.modules={1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(43210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},14952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19783:(e,t,n)=>{n.d(t,{B:()=>u});var r=n(43210),o=n.t(r,2),i=globalThis?.document?r.useLayoutEffect:()=>{},a=o[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=r.useState(a());return i(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},40481:(e,t,n)=>{n.d(t,{n:()=>m});var r=n(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}n(51215);var a=n(60687),l=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var a;let e,l;let u=(a=n,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(c.ref=t?i(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),c=l.find(u);if(c){let e=c.props.children,o=l.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}var d="focusScope.autoFocusOnMount",f="focusScope.autoFocusOnUnmount",p={bubbles:!1,cancelable:!0},m=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:o=!1,onMountAutoFocus:l,onUnmountAutoFocus:u,...m}=e,[b,E]=r.useState(null),w=s(l),C=s(u),x=r.useRef(null),N=function(...e){return r.useCallback(i(...e),e)}(t,e=>E(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(o){let e=function(e){if(R.paused||!b)return;let t=e.target;b.contains(t)?x.current=t:y(x.current,{select:!0})},t=function(e){if(R.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||y(x.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&y(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[o,b,R.paused]),r.useEffect(()=>{if(b){g.add(R);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(d,p);b.addEventListener(d,w),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(y(r,{select:t}),document.activeElement!==n)return}(v(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&y(b))}return()=>{b.removeEventListener(d,w),setTimeout(()=>{let t=new CustomEvent(f,p);b.addEventListener(f,C),b.dispatchEvent(t),t.defaultPrevented||y(e??document.body,{select:!0}),b.removeEventListener(f,C),g.remove(R)},0)}}},[b,w,C,R]);let O=r.useCallback(e=>{if(!n&&!o||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=v(e);return[h(t,e),h(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&y(i,{select:!0})):(e.preventDefault(),n&&y(o,{select:!0})):r===t&&e.preventDefault()}},[n,o,R.paused]);return(0,a.jsx)(c.div,{tabIndex:-1,...m,ref:N,onKeyDown:O})});function v(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function h(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function y(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}m.displayName="FocusScope";var g=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=b(e,t)).unshift(t)},remove(t){e=b(e,t),e[0]?.resume()}}}();function b(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},42247:(e,t,n)=>{n.d(t,{A:()=>q});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(43210)),l="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=o({async:!0,ssr:!1},e),a}(),m=function(){},v=a.forwardRef(function(e,t){var n,r,l,u,f=a.useRef(null),v=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=v[0],y=v[1],g=e.forwardProps,b=e.children,E=e.className,w=e.removeScrollBar,C=e.enabled,x=e.shards,N=e.sideCar,R=e.noIsolation,O=e.inert,S=e.allowPinchZoom,P=e.as,j=e.gapMode,D=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),A=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(l=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,u=l.facade,s(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(u,n)},[n]),u),M=o(o({},D),h);return a.createElement(a.Fragment,null,C&&a.createElement(N,{sideCar:p,removeScrollBar:w,shards:x,noIsolation:R,inert:O,setCallbacks:y,allowPinchZoom:!!S,lockRef:f,gapMode:j}),g?a.cloneElement(a.Children.only(b),o(o({},M),{ref:A})):a.createElement(void 0===P?"div":P,o({},M,{className:E,ref:A}),b))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:u,zeroRight:l};var h=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,o({},n))};h.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},g=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=g();return function(t){return e(t.styles,t.dynamic),null}},E={left:0,top:0,right:0,gap:0},w=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[w(n),w(r),w(o)]},x=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return E;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},N=b(),R="data-scroll-locked",O=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},S=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},P=function(){a.useEffect(function(){return document.body.setAttribute(R,(S()+1).toString()),function(){var e=S()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},j=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;P();var i=a.useMemo(function(){return x(o)},[o]);return a.createElement(N,{styles:O(i,!t,o,n?"":"!important")})},D=!1;if("undefined"!=typeof window)try{var A=Object.defineProperty({},"passive",{get:function(){return D=!0,!0}});window.addEventListener("test",A,A),window.removeEventListener("test",A,A)}catch(e){D=!1}var M=!!D&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},T=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=k(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},k=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{var m=k(e,u),v=m[0],h=m[1]-m[2]-a*v;(v||h)&&I(e,u)&&(f+=h,p+=v),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},_=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},F=function(e){return[e.deltaX,e.deltaY]},$=function(e){return e&&"current"in e?e.current:e},B=0,V=[];let U=(p.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(B++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map($),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=_(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=T(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=T(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(V.length&&V[V.length-1]===i){var n="deltaY"in e?F(e):_(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map($).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=_(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,_(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return V.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,M),document.addEventListener("touchmove",c,M),document.addEventListener("touchstart",d,M),function(){V=V.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,M),document.removeEventListener("touchmove",c,M),document.removeEventListener("touchstart",d,M)}},[]);var m=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(j,{gapMode:e.gapMode}):null)}),h);var Z=a.forwardRef(function(e,t){return a.createElement(v,o({},e,{ref:t,sideCar:U}))});Z.classNames=v.classNames;let q=Z},42360:(e,t,n)=>{n.d(t,{qW:()=>g});var r,o=n(43210);function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var a=n(51215);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function c(...e){return o.useCallback(u(...e),e)}var s=n(60687),d=Symbol("radix.slottable");function f(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){var i;let e,a;let l=(i=n,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(c.ref=t?u(t,l):l),o.cloneElement(n,c)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,a=o.Children.toArray(r),l=a.find(f);if(l){let e=l.props.children,r=a.map(t=>t!==l?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,s.jsx)(t,{...i,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function m(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var v=n(5881),h="dismissableLayer.update",y=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),g=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:d,onDismiss:f,...g}=e,w=o.useContext(y),[C,x]=o.useState(null),N=C?.ownerDocument??globalThis?.document,[,R]=o.useState({}),O=c(t,e=>x(e)),S=Array.from(w.layers),[P]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),j=S.indexOf(P),D=C?S.indexOf(C):-1,A=w.layersWithOutsidePointerEventsDisabled.size>0,M=D>=j,L=function(e,t=globalThis?.document){let n=m(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){E("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));!M||n||(l?.(e),d?.(e),e.defaultPrevented||f?.())},N),T=function(e,t=globalThis?.document){let n=m(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&E("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...w.branches].some(e=>e.contains(t))||(u?.(e),d?.(e),e.defaultPrevented||f?.())},N);return(0,v.U)(e=>{D===w.layers.size-1&&(a?.(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},N),o.useEffect(()=>{if(C)return n&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(r=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(C)),w.layers.add(C),b(),()=>{n&&1===w.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=r)}},[C,N,n,w]),o.useEffect(()=>()=>{C&&(w.layers.delete(C),w.layersWithOutsidePointerEventsDisabled.delete(C),b())},[C,w]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(h,e),()=>document.removeEventListener(h,e)},[]),(0,s.jsx)(p.div,{...g,ref:O,style:{pointerEvents:A?M?"auto":"none":void 0,...e.style},onFocusCapture:i(e.onFocusCapture,T.onFocusCapture),onBlurCapture:i(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:i(e.onPointerDownCapture,L.onPointerDownCapture)})});function b(){let e=new CustomEvent(h);document.dispatchEvent(e)}function E(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&o.addEventListener(e,t,{once:!0}),r)o&&a.flushSync(()=>o.dispatchEvent(i));else o.dispatchEvent(i)}g.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(y),r=o.useRef(null),i=c(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(p.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},63376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),m=function(e){!(!e||f.has(e))&&(f.add(e),m(e.parentNode))};c.forEach(m);var v=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,n,"aria-hidden")):function(){return null}}},78122:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},88562:(e,t,n)=>{n.d(t,{bm:()=>es,UC:()=>el,VY:()=>ec,hJ:()=>ea,ZL:()=>ei,bL:()=>er,hE:()=>eu,l9:()=>eo});var r=n(43210);function o(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function l(...e){return r.useCallback(a(...e),e)}var u=n(60687),c=n(19783),s=n(67427),d=n(42360),f=n(40481),p=n(96929),m=globalThis?.document?r.useLayoutEffect:()=>{},v=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef({}),l=r.useRef(e),u=r.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=h(a.current);u.current="mounted"===c?e:"none"},[c]),m(()=>{let t=a.current,n=l.current;if(n!==e){let r=u.current,o=h(t);e?s("MOUNT"):"none"===o||t?.display==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),l.current=e}},[e,s]),m(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=h(a.current).includes(n.animationName);if(n.target===o&&r&&(s("ANIMATION_END"),!l.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(u.current=h(a.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}s("ANIMATION_END")},[o,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),a=l(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||o.isPresent?r.cloneElement(i,{ref:a}):null};function h(e){return e?.animationName||"none"}function y(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,l;let u=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(c.ref=t?a(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,a=r.Children.toArray(o),l=a.find(b);if(l){let e=l.props.children,o=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,u.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}v.displayName="Presence",n(51215);var g=Symbol("radix.slottable");function b(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===g}var E=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=y(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),w=n(1359),C=n(42247),x=n(63376),N="Dialog",[R,O]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),a=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,c=n?.[e]?.[a]||i,s=r.useMemo(()=>l,Object.values(l));return(0,u.jsx)(c.Provider,{value:s,children:o})};return l.displayName=t+"Provider",[l,function(n,l){let u=l?.[e]?.[a]||i,c=r.useContext(u);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(N),[S,P]=R(N),j=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:l=!0}=e,d=r.useRef(null),f=r.useRef(null),[p,m]=(0,s.i)({prop:o,defaultProp:i??!1,onChange:a,caller:N});return(0,u.jsx)(S,{scope:t,triggerRef:d,contentRef:f,contentId:(0,c.B)(),titleId:(0,c.B)(),descriptionId:(0,c.B)(),open:p,onOpenChange:m,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),modal:l,children:n})};j.displayName=N;var D="DialogTrigger",A=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=P(D,n),a=l(t,i.triggerRef);return(0,u.jsx)(E.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":J(i.open),...r,ref:a,onClick:o(e.onClick,i.onOpenToggle)})});A.displayName=D;var M="DialogPortal",[L,T]=R(M,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=P(M,t);return(0,u.jsx)(L,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,u.jsx)(v,{present:n||a.open,children:(0,u.jsx)(p.Z,{asChild:!0,container:i,children:e})}))})};I.displayName=M;var k="DialogOverlay",W=r.forwardRef((e,t)=>{let n=T(k,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=P(k,e.__scopeDialog);return i.modal?(0,u.jsx)(v,{present:r||i.open,children:(0,u.jsx)(F,{...o,ref:t})}):null});W.displayName=k;var _=y("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=P(k,n);return(0,u.jsx)(C.A,{as:_,allowPinchZoom:!0,shards:[o.contentRef],children:(0,u.jsx)(E.div,{"data-state":J(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),$="DialogContent",B=r.forwardRef((e,t)=>{let n=T($,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=P($,e.__scopeDialog);return(0,u.jsx)(v,{present:r||i.open,children:i.modal?(0,u.jsx)(V,{...o,ref:t}):(0,u.jsx)(U,{...o,ref:t})})});B.displayName=$;var V=r.forwardRef((e,t)=>{let n=P($,e.__scopeDialog),i=r.useRef(null),a=l(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,x.Eq)(e)},[]),(0,u.jsx)(Z,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:o(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:o(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:o(e.onFocusOutside,e=>e.preventDefault())})}),U=r.forwardRef((e,t)=>{let n=P($,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,u.jsx)(Z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...c}=e,s=P($,n),p=r.useRef(null),m=l(t,p);return(0,w.Oh)(),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(f.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,u.jsx)(d.qW,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":J(s.open),...c,ref:m,onDismiss:()=>s.onOpenChange(!1)})}),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(et,{titleId:s.titleId}),(0,u.jsx)(en,{contentRef:p,descriptionId:s.descriptionId})]})]})}),q="DialogTitle",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=P(q,n);return(0,u.jsx)(E.h2,{id:o.titleId,...r,ref:t})});K.displayName=q;var Y="DialogDescription",X=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=P(Y,n);return(0,u.jsx)(E.p,{id:o.descriptionId,...r,ref:t})});X.displayName=Y;var z="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=P(z,n);return(0,u.jsx)(E.button,{type:"button",...r,ref:t,onClick:o(e.onClick,()=>i.onOpenChange(!1))})});function J(e){return e?"open":"closed"}H.displayName=z;var G="DialogTitleWarning",[Q,ee]=function(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,i=r.useMemo(()=>o,Object.values(o));return(0,u.jsx)(n.Provider,{value:i,children:t})};return o.displayName=e+"Provider",[o,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}(G,{contentName:$,titleName:q,docsSlug:"dialog"}),et=({titleId:e})=>{let t=ee(G),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},en=({contentRef:e,descriptionId:t})=>{let n=ee("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},er=j,eo=A,ei=I,ea=W,el=B,eu=K,ec=X,es=H},96929:(e,t,n)=>{n.d(t,{Z:()=>d});var r=n(43210),o=n(51215);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var a=n(60687),l=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var a;let e,l;let u=(a=n,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(c.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),c=l.find(u);if(c){let e=c.props.children,o=l.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),s=globalThis?.document?r.useLayoutEffect:()=>{},d=r.forwardRef((e,t)=>{let{container:n,...i}=e,[l,u]=r.useState(!1);s(()=>u(!0),[]);let d=n||l&&globalThis?.document?.body;return d?o.createPortal((0,a.jsx)(c.div,{...i,ref:t}),d):null});d.displayName="Portal"}};