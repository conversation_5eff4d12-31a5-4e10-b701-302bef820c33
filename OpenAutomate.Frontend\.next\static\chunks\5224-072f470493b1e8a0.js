"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5224],{26126:(e,t,a)=>{a.d(t,{E:()=>d});var s=a(95155);a(12115);var n=a(66634),r=a(74466),i=a(36928);let o=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,asChild:r=!1,...d}=e,l=r?n.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:a}),t),...d})}},29797:(e,t,a)=>{a.d(t,{d:()=>c});var s=a(95155),n=a(30285),r=a(59409),i=a(52278),o=a(42355),d=a(13052),l=a(12767);function c(e){let{currentPage:t,pageSize:a,totalCount:c,totalPages:u,isLoading:x=!1,isChangingPageSize:g=!1,pageSizeOptions:m=[10,20,30,40,50],rowsLabel:p="row(s)",isUnknownTotalCount:h=!1,onPageChange:v,onPageSizeChange:f}=e,b=g?Math.max(u,Math.ceil(c/a)):u,j=t>1,w=t<b,N=e=>{v(Math.max(1,Math.min(e,b)))},y=e=>{N(t+e)};return(0,s.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,s.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:c>0?(0,s.jsxs)(s.Fragment,{children:[Math.min((t-1)*a+1,c),"-",Math.min(t*a,c)," of ",c," ",p]}):"No results"}),(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,s.jsxs)(r.l6,{value:String(a),onValueChange:e=>f(Number(e)),disabled:x,children:[(0,s.jsx)(r.bq,{className:"h-8 w-[70px]",children:(0,s.jsx)(r.yv,{placeholder:a})}),(0,s.jsx)(r.gC,{side:"top",children:m.map(e=>(0,s.jsx)(r.eb,{value:String(e),children:e},e))})]})]}),(0,s.jsxs)("div",{className:"flex w-[100px] items-center justify-center text-sm font-medium",children:["Page ",t," of ",x||g?"...":h&&w?"".concat(b,"+"):b]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(n.$,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>N(1),disabled:!j||x,children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to first page"}),(0,s.jsx)(i.A,{className:"h-4 w-4"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>y(-1),disabled:!j||x,children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to previous page"}),(0,s.jsx)(o.A,{className:"h-4 w-4"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>y(1),disabled:!w||x,children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to next page"}),(0,s.jsx)(d.A,{className:"h-4 w-4"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>N(b),disabled:!w||x,children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to last page"}),(0,s.jsx)(l.A,{className:"h-4 w-4"})]})]})]})]})}},30285:(e,t,a)=>{a.d(t,{$:()=>l,r:()=>d});var s=a(95155),n=a(12115),r=a(66634),i=a(74466),o=a(36928);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef((e,t)=>{let{className:a,variant:n,size:i,asChild:l=!1,...c}=e,u=l?r.DX:"button";return(0,s.jsx)(u,{"data-slot":"button",className:(0,o.cn)(d({variant:n,size:i,className:a})),ref:t,...c})});l.displayName="Button"},44838:(e,t,a)=>{a.d(t,{I:()=>c,SQ:()=>l,_2:()=>u,hO:()=>x,lp:()=>g,mB:()=>m,rI:()=>o,ty:()=>d});var s=a(95155);a(12115);var n=a(18289),r=a(5196),i=a(36928);function o(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function d(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function l(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function c(e){let{...t}=e;return(0,s.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...t})}function u(e){let{className:t,inset:a,variant:r="default",...o}=e;return(0,s.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o})}function x(e){let{className:t,children:a,checked:o,...d}=e;return(0,s.jsxs)(n.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:o,...d,children:[(0,s.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(r.A,{className:"size-4"})})}),a]})}function g(e){let{className:t,inset:a,...r}=e;return(0,s.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,i.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},47262:(e,t,a)=>{a.d(t,{S:()=>o});var s=a(95155);a(12115);var n=a(9483),r=a(5196),i=a(36928);function o(e){let{className:t,...a}=e;return(0,s.jsx)(n.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-orange-600 data-[state=checked]:text-white dark:data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600 focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(r.A,{className:"size-3.5"})})})}},54333:(e,t,a)=>{a.d(t,{b:()=>d});var s=a(95155),n=a(12115),r=a(36268),i=a(11032),o=a(85127);function d(e){var t;let{columns:a,data:d,table:l,onRowClick:c,isLoading:u=!1,totalCount:x}=e,[g,m]=n.useState({}),[p,h]=n.useState({}),[v,f]=n.useState([]),[b,j]=n.useState([]),w=(0,r.N4)({data:d,columns:a,state:{sorting:b,columnVisibility:p,rowSelection:g,columnFilters:v},enableRowSelection:!0,onRowSelectionChange:m,onSortingChange:j,onColumnFiltersChange:f,onColumnVisibilityChange:h,getCoreRowModel:(0,i.HT)(),getFilteredRowModel:(0,i.hM)(),getPaginationRowModel:(0,i.kW)(),getSortedRowModel:(0,i.h5)(),getFacetedRowModel:(0,i.kQ)(),getFacetedUniqueValues:(0,i.oS)(),...void 0!==x&&{manualPagination:!0,pageCount:Math.max(1,Math.ceil(x/10))}}),N=null!=l?l:w;n.useEffect(()=>{if(console.log("DataTable component received totalCount:",x),N){let e=N.getState().pagination.pageSize,t=void 0!==x?Math.max(1,Math.ceil(x/e)):1;console.log("DataTable pagination state:",{totalCount:x,pageSize:e,calculatedPageCount:t,tablePageCount:N.getPageCount(),currentPage:N.getState().pagination.pageIndex+1,pageIndex:N.getState().pagination.pageIndex})}},[x,N]);let y=(e,t)=>{let a=e.target;if(a.closest('[role="dialog"]')||a.closest('[data-state="open"]')||a.closest(".dialog")||a.closest(".dropdown-menu"))return;let s=a.closest("td");if(s){let e=s.getAttribute("data-column-id");if("select"===e||"actions"===e)return}null==c||c(t)};return(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"rounded-md border relative",children:[u&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-background/80 z-10",children:(0,s.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"})}),(0,s.jsxs)(o.XI,{className:"dark:bg-neutral-900",children:[(0,s.jsx)(o.A0,{children:N.getHeaderGroups().map(e=>(0,s.jsx)(o.Hj,{children:e.headers.map(e=>(0,s.jsx)(o.nd,{colSpan:e.colSpan,children:e.isPlaceholder?null:(0,r.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,s.jsx)(o.BF,{children:(null===(t=N.getRowModel().rows)||void 0===t?void 0:t.length)?N.getRowModel().rows.map(e=>(0,s.jsx)(o.Hj,{"data-state":e.getIsSelected()&&"selected",className:c?"cursor-pointer hover:bg-muted/50 ":"",onClick:t=>c&&y(t,e.original),children:e.getVisibleCells().map(e=>{let t="";return"select"===e.column.id?t="w-12 min-w-[48px] max-w-[48px] px-2":"actions"===e.column.id&&(t="w-16 min-w-[60px] max-w-[60px] px-2"),(0,s.jsx)(o.nA,{"data-column-id":e.column.id,className:t,children:(0,r.Kv)(e.column.columnDef.cell,e.getContext())},e.id)})},e.id)):(0,s.jsx)(o.Hj,{children:(0,s.jsx)(o.nA,{colSpan:a.length,className:"h-24 text-center",children:u?"Loading...":"No results."})})})]})]})})}},59409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>g,gC:()=>x,l6:()=>l,yv:()=>c});var s=a(95155);a(12115);var n=a(46002),r=a(66474),i=a(5196),o=a(47863),d=a(36928);function l(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(n.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:i,...o}=e;return(0,s.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[i,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:r="popper",...i}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...i,children:[(0,s.jsx)(m,{}),(0,s.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(p,{})]})})}function g(e){let{className:t,children:a,...r}=e;return(0,s.jsxs)(n.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(n.p4,{children:a})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(o.A,{className:"size-4"})})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(r.A,{className:"size-4"})})}},62523:(e,t,a)=>{a.d(t,{p:()=>r});var s=a(95155);a(12115);var n=a(36928);function r(e){let{className:t,type:a,...r}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},85127:(e,t,a)=>{a.d(t,{A0:()=>i,BF:()=>o,Hj:()=>d,XI:()=>r,nA:()=>c,nd:()=>l});var s=a(95155);a(12115);var n=a(36928);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",t),...a})})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},87570:(e,t,a)=>{a.d(t,{w:()=>u});var s=a(95155),n=a(58832),r=a(39881),i=a(10081),o=a(78749),d=a(36928),l=a(30285),c=a(44838);function u(e){let{column:t,title:a,className:u}=e;return t.getCanSort()?(0,s.jsx)("div",{className:(0,d.cn)("flex items-center space-x-2",u),children:(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"ghost",size:"sm",className:"-ml-3 h-8 data-[state=open]:bg-accent",children:[(0,s.jsx)("span",{children:a}),(()=>{let e=t.getIsSorted();return"desc"===e?(0,s.jsx)(n.A,{}):"asc"===e?(0,s.jsx)(r.A,{}):(0,s.jsx)(i.A,{})})()]})}),(0,s.jsxs)(c.SQ,{align:"start",children:[(0,s.jsxs)(c._2,{onClick:()=>t.toggleSorting(!1),children:[(0,s.jsx)(r.A,{className:"h-3.5 w-3.5 text-muted-foreground/70"}),"Asc"]}),(0,s.jsxs)(c._2,{onClick:()=>t.toggleSorting(!0),children:[(0,s.jsx)(n.A,{className:"h-3.5 w-3.5 text-muted-foreground/70"}),"Desc"]}),(0,s.jsx)(c.mB,{}),(0,s.jsxs)(c._2,{onClick:()=>t.toggleVisibility(!1),children:[(0,s.jsx)(o.A,{className:"h-3.5 w-3.5 text-muted-foreground/70"}),"Hide"]})]})]})}):(0,s.jsx)("div",{className:(0,d.cn)(u),children:a})}}}]);