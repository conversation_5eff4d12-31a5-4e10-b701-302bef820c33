(()=>{var e={};e.id=9e3,e.ids=[9e3],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15123:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var n=r(65239),s=r(48088),a=r(31369),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["[tenant]",{children:["administration",{children:["license",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49580)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\license\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\license\\[id]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[tenant]/administration/license/[id]/page",pathname:"/[tenant]/administration/license/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31599:(e,t,r)=>{"use strict";r.d(t,{c:()=>d});var n=r(43210),s=r(39989),a=r(16189),i=r(31207),o=r(70891);function d(){let e=(0,a.useRouter)(),{data:t,error:r,isLoading:d,mutate:l}=(0,i.Ay)(o.DC.organizationUnits(),()=>s.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:d,error:r?"Failed to fetch organization units. Please try again later.":null,refresh:l,selectOrganizationUnit:(0,n.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},39989:(e,t,r)=>{"use strict";r.d(t,{K:()=>s});var n=r(51787);let s={getMyOrganizationUnits:async()=>await n.F.get("/api/ou/my-ous"),getBySlug:async e=>await n.F.get(`/api/ou/slug/${e}`),getById:async e=>await n.F.get(`/api/ou/${e}`),create:async e=>await n.F.post("/api/ou/create",e),update:async(e,t)=>await n.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await n.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await n.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await n.F.get(`/api/ou/${e}/deletion-status`)}},49580:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(37413);function s(){return(0,n.jsx)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:(0,n.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"})})}},61018:(e,t,r)=>{"use strict";r.d(t,{TenantGuard:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(37413),s=r(48974),a=r(31057),i=r(50417),o=r(92588),d=r(61018),l=r(2505);function c({children:e}){return(0,n.jsx)(d.TenantGuard,{children:(0,n.jsx)(l.ChatProvider,{children:(0,n.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,n.jsxs)(i.SidebarProvider,{className:"flex flex-col",children:[(0,n.jsx)(a.SiteHeader,{}),(0,n.jsxs)("div",{className:"flex flex-1",children:[(0,n.jsx)(s.AppSidebar,{}),(0,n.jsx)(i.SidebarInset,{children:(0,n.jsx)(o.SearchProvider,{children:(0,n.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,r)=>{"use strict";r.d(t,{TenantGuard:()=>d});var n=r(60687),s=r(43210),a=r(16189),i=r(31599),o=r(31568);function d({children:e}){let{tenant:t}=(0,a.useParams)();(0,a.useRouter)();let{isAuthenticated:r,isLoading:d}=(0,o.A)(),{organizationUnits:l,isLoading:c}=(0,i.c)(),[u,p]=(0,s.useState)(!0);return d||c||u?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,n.jsx)(n.Fragment,{children:e})}},72826:(e,t,r)=>{Promise.resolve().then(r.bind(r,69231)),Promise.resolve().then(r.bind(r,83847)),Promise.resolve().then(r.bind(r,78526)),Promise.resolve().then(r.bind(r,97597)),Promise.resolve().then(r.bind(r,98641)),Promise.resolve().then(r.bind(r,80110))},78335:()=>{},83442:(e,t,r)=>{Promise.resolve().then(r.bind(r,61018)),Promise.resolve().then(r.bind(r,2505)),Promise.resolve().then(r.bind(r,92588)),Promise.resolve().then(r.bind(r,48974)),Promise.resolve().then(r.bind(r,31057)),Promise.resolve().then(r.bind(r,50417))},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7966,5584,5156,4654,6467,6763,519],()=>r(15123));module.exports=n})();