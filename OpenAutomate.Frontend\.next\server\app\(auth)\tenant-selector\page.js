(()=>{var e={};e.id=2467,e.ids=[2467],e.modules={1303:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:a,quality:s}=e,i=s||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+a+"&q="+i+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},14959:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.AmpContext},17903:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ImageConfigContext},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21195:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\(auth)\\\\tenant-selector\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\tenant-selector\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},defaultHead:function(){return u}});let n=r(14985),a=r(40740),s=r(60687),i=a._(r(43210)),o=n._(r(47755)),l=r(14959),d=r(89513),c=r(34604);function u(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function m(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(50148);let f=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(m,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return a=>{let s=!0,i=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){i=!0;let t=a.key.slice(a.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(a.type){case"title":case"base":t.has(a.type)?s=!1:t.add(a.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(a.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?s=!1:r.add(t);else{let e=a.props[t],r=n[t]||new Set;("name"!==t||!i)&&r.has(e)?s=!1:(r.add(e),n[t]=r)}}}}return s}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:n})})}let g=function(e){let{children:t}=e,r=(0,i.useContext)(l.AmpStateContext),n=(0,i.useContext)(d.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:p,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let n=r(14985),a=r(44953),s=r(46533),i=n._(r(1933));function o(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},31568:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210),a=r(86522);function s(){let e=(0,n.useContext)(a.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},31599:(e,t,r)=>{"use strict";r.d(t,{c:()=>l});var n=r(43210),a=r(39989),s=r(16189),i=r(31207),o=r(70891);function l(){let e=(0,s.useRouter)(),{data:t,error:r,isLoading:l,mutate:d}=(0,i.Ay)(o.DC.organizationUnits(),()=>a.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:l,error:r?"Failed to fetch organization units. Please try again later.":null,refresh:d,selectOrganizationUnit:(0,n.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},34604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},39989:(e,t,r)=>{"use strict";r.d(t,{K:()=>a});var n=r(51787);let a={getMyOrganizationUnits:async()=>await n.F.get("/api/ou/my-ous"),getBySlug:async e=>await n.F.get(`/api/ou/slug/${e}`),getById:async e=>await n.F.get(`/api/ou/${e}`),create:async e=>await n.F.post("/api/ou/create",e),update:async(e,t)=>await n.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await n.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await n.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await n.F.get(`/api/ou/${e}/deletion-status`)}},41480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:a,blurDataURL:s,objectFit:i}=e,o=n?40*n:t,l=a?40*a:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===i?"xMidYMid":"cover"===i?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},44953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(50148);let n=r(41480),a=r(12756);function s(e){return void 0!==e.default}function i(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r,o;let l,d,c,{src:u,sizes:m,unoptimized:f=!1,priority:p=!1,loading:g,className:h,quality:x,width:v,height:b,fill:j=!1,style:y,overrideSrc:w,onLoad:N,onLoadingComplete:_,placeholder:C="empty",blurDataURL:z,fetchPriority:P,decoding:O="async",layout:S,objectFit:k,objectPosition:E,lazyBoundary:A,lazyRoot:R,...M}=e,{imgConf:I,showAltText:F,blurComplete:D,defaultLoader:$}=t,T=I||a.imageConfigDefault;if("allSizes"in T)l=T;else{let e=[...T.deviceSizes,...T.imageSizes].sort((e,t)=>e-t),t=T.deviceSizes.sort((e,t)=>e-t),n=null==(r=T.qualities)?void 0:r.sort((e,t)=>e-t);l={...T,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===$)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=M.loader||$;delete M.loader,delete M.srcSet;let U="__next_img_default"in G;if(U){if("custom"===l.loader)throw Object.defineProperty(Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:r,...n}=t;return e(n)}}if(S){"fill"===S&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(y={...y,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!m&&(m=t)}let L="",q=i(v),B=i(b);if((o=u)&&"object"==typeof o&&(s(o)||void 0!==o.src)){let e=s(u)?u.default:u;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,z=z||e.blurDataURL,L=e.src,!j){if(q||B){if(q&&!B){let t=q/e.width;B=Math.round(e.height*t)}else if(!q&&B){let t=B/e.height;q=Math.round(e.width*t)}}else q=e.width,B=e.height}}let V=!p&&("lazy"===g||void 0===g);(!(u="string"==typeof u?u:L)||u.startsWith("data:")||u.startsWith("blob:"))&&(f=!0,V=!1),l.unoptimized&&(f=!0),U&&!l.dangerouslyAllowSVG&&u.split("?",1)[0].endsWith(".svg")&&(f=!0);let J=i(x),X=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:k,objectPosition:E}:{},F?{}:{color:"transparent"},y),Y=D||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:q,heightInt:B,blurWidth:d,blurHeight:c,blurDataURL:z||"",objectFit:X.objectFit})+'")':'url("'+C+'")',W=Y?{backgroundSize:X.objectFit||"cover",backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},H=function(e){let{config:t,src:r,unoptimized:n,width:a,quality:s,sizes:i,loader:o}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:n,allSizes:a}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);n)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:a,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))],kind:"x"}}(t,a,i),c=l.length-1;return{sizes:i||"w"!==d?i:"100vw",srcSet:l.map((e,n)=>o({config:t,src:r,quality:s,width:e})+" "+("w"===d?e:n+1)+d).join(", "),src:o({config:t,src:r,quality:s,width:l[c]})}}({config:l,src:u,unoptimized:f,width:q,quality:J,sizes:m,loader:G});return{props:{...M,loading:V?"lazy":g,fetchPriority:P,width:q,height:B,decoding:O,className:h,style:{...X,...W},sizes:H.sizes,srcSet:H.srcSet,src:w||H.src},meta:{unoptimized:f,priority:p,placeholder:C,fill:j}}}},46533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return j}});let n=r(14985),a=r(40740),s=r(60687),i=a._(r(43210)),o=n._(r(51215)),l=n._(r(30512)),d=r(44953),c=r(12756),u=r(17903);r(50148);let m=r(69148),f=n._(r(1933)),p=r(53038),g={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function h(e,t,r,n,a,s,i){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&a(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,a=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>a,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{a=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function x(e){return i.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,i.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:a,height:o,width:l,decoding:d,className:c,style:u,fetchPriority:m,placeholder:f,loading:g,unoptimized:v,fill:b,onLoadRef:j,onLoadingCompleteRef:y,setBlurComplete:w,setShowAltText:N,sizesInput:_,onLoad:C,onError:z,...P}=e,O=(0,i.useCallback)(e=>{e&&(z&&(e.src=e.src),e.complete&&h(e,f,j,y,w,v,_))},[r,f,j,y,w,z,v,_]),S=(0,p.useMergedRef)(t,O);return(0,s.jsx)("img",{...P,...x(m),loading:g,width:l,height:o,decoding:d,"data-nimg":b?"fill":"1",className:c,style:u,sizes:a,srcSet:n,src:r,ref:S,onLoad:e=>{h(e.currentTarget,f,j,y,w,v,_)},onError:e=>{N(!0),"empty"!==f&&w(!0),z&&z(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...x(r.fetchPriority)};return t&&o.default.preload?(o.default.preload(r.src,n),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let j=(0,i.forwardRef)((e,t)=>{let r=(0,i.useContext)(m.RouterContext),n=(0,i.useContext)(u.ImageConfigContext),a=(0,i.useMemo)(()=>{var e;let t=g||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),a=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:a,qualities:s}},[n]),{onLoad:o,onLoadingComplete:l}=e,p=(0,i.useRef)(o);(0,i.useEffect)(()=>{p.current=o},[o]);let h=(0,i.useRef)(l);(0,i.useEffect)(()=>{h.current=l},[l]);let[x,j]=(0,i.useState)(!1),[y,w]=(0,i.useState)(!1),{props:N,meta:_}=(0,d.getImgProps)(e,{defaultLoader:f.default,imgConf:a,blurComplete:x,showAltText:y});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v,{...N,unoptimized:_.unoptimized,placeholder:_.placeholder,fill:_.fill,onLoadRef:p,onLoadingCompleteRef:h,setBlurComplete:j,setShowAltText:w,sizesInput:e.sizes,ref:t}),_.priority?(0,s.jsx)(b,{isAppRouter:!r,imgAttributes:N}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(43210),a=()=>{},s=()=>{};function i(e){var t;let{headManager:r,reduceComponentsToState:i}=e;function o(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(i(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),a(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),a(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),s(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>f,Es:()=>g,HM:()=>u,L3:()=>h,c7:()=>p,lG:()=>l,rr:()=>x,zM:()=>d});var n=r(60687),a=r(43210),s=r(88562),i=r(11860),o=r(36966);let l=s.bL,d=s.l9,c=s.ZL,u=s.bm,m=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.hJ,{ref:r,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));m.displayName=s.hJ.displayName;let f=a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(c,{children:[(0,n.jsx)(m,{}),(0,n.jsxs)(s.UC,{ref:a,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...r,children:[t,(0,n.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(i.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=s.UC.displayName;let p=({className:e,...t})=>(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let g=({className:e,...t})=>(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});g.displayName="DialogFooter";let h=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.hE,{ref:r,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=s.hE.displayName;let x=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.VY,{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=s.VY.displayName},69148:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.RouterContext},71669:(e,t,r)=>{"use strict";r.d(t,{C5:()=>v,MJ:()=>h,Rr:()=>x,eI:()=>p,lR:()=>g,lV:()=>d,zB:()=>u});var n=r(60687),a=r(43210),s=r(11329),i=r(27605),o=r(36966),l=r(80013);let d=i.Op,c=a.createContext({}),u=({...e})=>(0,n.jsx)(c.Provider,{value:{name:e.name},children:(0,n.jsx)(i.xI,{...e})}),m=()=>{let e=a.useContext(c),t=a.useContext(f),{getFieldState:r}=(0,i.xW)(),n=(0,i.lN)({name:e.name}),s=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...s}},f=a.createContext({});function p({className:e,...t}){let r=a.useId();return(0,n.jsx)(f.Provider,{value:{id:r},children:(0,n.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...t})})}function g({className:e,...t}){let{error:r,formItemId:a}=m();return(0,n.jsx)(l.J,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...t})}function h({...e}){let{error:t,formItemId:r,formDescriptionId:a,formMessageId:i}=m();return(0,n.jsx)(s.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${a} ${i}`:`${a}`,"aria-invalid":!!t,...e})}function x({className:e,...t}){let{formDescriptionId:r}=m();return(0,n.jsx)("p",{"data-slot":"form-description",id:r,className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function v({className:e,...t}){let{error:r,formMessageId:a}=m(),s=r?String(r?.message??""):t.children;return s?(0,n.jsx)("p",{"data-slot":"form-message",id:a,className:(0,o.cn)("text-destructive text-sm",e),...t,children:s}):null}},75048:(e,t,r)=>{Promise.resolve().then(r.bind(r,77142))},77142:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var n=r(60687),a=r(43210),s=r(31568),i=r(31599),o=r(16189),l=r(31261),d=r.n(l),c=r(44493),u=r(29523),m=r(78122),f=r(1303),p=r(14952),g=r(27605),h=r(63442),x=r(45880),v=r(71669),b=r(89667),j=r(93613),y=r(91821),w=r(39989),N=r(36966),_=r(31898),C=r(70891);let z=x.Ik({name:x.Yj().min(3,"Name must be at least 3 characters"),description:x.Yj().optional()});function P({onSuccess:e,onCancel:t}){let r=(0,o.useRouter)(),[s,i]=a.useState(!1),[l,d]=a.useState(null),c=(0,g.mN)({resolver:(0,h.u)(z),defaultValues:{name:"",description:""}});async function m(t){i(!0),d(null);try{let n=await w.K.create({name:t.name,description:t.description});await (0,_.j)(C.DC.subscription()),await (0,_.j)("user-profile"),await (0,_.j)(C.DC.organizationUnits()),e?e(n.slug):r.push(`/${n.slug}/tenant-selector`)}catch(e){console.error("Organization unit creation failed",e),d(e instanceof Error?e.message:"Failed to create organization. Please try again.")}finally{i(!1)}}return(0,n.jsxs)("div",{className:"grid gap-6",children:[l&&(0,n.jsxs)(y.Fc,{variant:"destructive",children:[(0,n.jsx)(j.A,{className:"h-4 w-4"}),(0,n.jsx)(y.TN,{children:l})]}),(0,n.jsx)(v.lV,{...c,children:(0,n.jsxs)("form",{onSubmit:c.handleSubmit(m),className:"space-y-4",children:[(0,n.jsx)(v.zB,{control:c.control,name:"name",render:({field:e})=>(0,n.jsxs)(v.eI,{children:[(0,n.jsx)(v.lR,{children:"Organization Name"}),(0,n.jsx)(v.MJ,{children:(0,n.jsx)(b.p,{placeholder:"My Organization",...e,disabled:s})}),(0,n.jsx)(v.Rr,{children:"This will be the name of your new organization."}),(0,n.jsx)(v.C5,{})]})}),(0,n.jsx)(v.zB,{control:c.control,name:"description",render:({field:e})=>(0,n.jsxs)(v.eI,{children:[(0,n.jsx)(v.lR,{children:"Description (Optional)"}),(0,n.jsx)(v.MJ,{children:(0,n.jsx)("textarea",{placeholder:"A brief description of your organization",...e,disabled:s,className:(0,N.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","resize-none"),rows:3})}),(0,n.jsx)(v.C5,{})]})}),(0,n.jsxs)("div",{className:"flex gap-3 pt-2",children:[(0,n.jsx)(u.$,{type:"submit",className:"flex-1 bg-orange-600 hover:bg-orange-700 transition-all duration-300 hover:translate-y-[-2px]",disabled:s,children:s?"Creating...":"Create Organization"}),t&&(0,n.jsx)(u.$,{type:"button",variant:"outline",onClick:t,disabled:s,className:"flex-1",children:"Cancel"})]})]})})]})}var O=r(63503);function S(){let{user:e,isAuthenticated:t,isLoading:r}=(0,s.A)(),{organizationUnits:l,isLoading:g,error:h,selectOrganizationUnit:x,refresh:v}=(0,i.c)(),b=(0,o.useRouter)(),j=(0,o.useSearchParams)(),[y,w]=(0,a.useState)(!1),[N,_]=(0,a.useState)(!1);j.get("force");let C="true"===j.get("unauthorized"),[z,S]=(0,a.useState)(!0),k=async()=>{_(!0),await v(),setTimeout(()=>_(!1),500)};if(r||g)return(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})});let E=e=>{v(),z||x(e)};return(0,n.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"w-full max-w-md p-6 space-y-8",children:[(0,n.jsxs)("div",{className:"flex flex-col items-center text-center mb-8",children:[(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)(d(),{src:"/logo-oa.png",alt:"OpenAutomate Logo",width:500,height:76,priority:!0})}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,n.jsx)("div",{className:"relative h-8 w-8 rounded-full overflow-hidden border",children:(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground",children:e?.firstName?.[0]??e?.email?.[0]??"U"})}),(0,n.jsx)("p",{className:"text-muted-foreground",children:e?.email})]})]}),C&&(0,n.jsxs)("div",{className:"mb-4 p-4 bg-destructive/10 rounded-md text-destructive text-center",children:[(0,n.jsx)("p",{className:"font-medium",children:"Access Denied"}),(0,n.jsx)("p",{className:"text-sm",children:"You do not have permission to access that organization unit."})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h2",{className:"text-xl font-medium",children:"Your Organization Units"}),(0,n.jsxs)(u.$,{size:"icon",variant:"ghost",onClick:k,disabled:N||g,className:"w-8 h-8",children:[(0,n.jsx)(m.A,{className:`h-4 w-4 ${N?"animate-spin":""}`}),(0,n.jsx)("span",{className:"sr-only",children:"Refresh"})]})]}),0===l.length?(0,n.jsxs)("div",{className:"text-center space-y-4",children:[(0,n.jsx)("p",{className:"text-muted-foreground",children:"You don't belong to any organization units yet."}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Create your first organization to get started."}),(0,n.jsx)(c.Zp,{className:"p-6 mt-6",children:y?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium",children:"Create New Organization"}),(0,n.jsx)(P,{onSuccess:E,onCancel:()=>w(!1)})]}):(0,n.jsxs)(u.$,{className:"w-full bg-orange-600 hover:bg-orange-700 transition-all duration-300 hover:translate-y-[-2px]",onClick:()=>w(!0),children:[(0,n.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Create New Organization"]})})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"text-center mb-6",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"Choose an organization unit to continue:"})}),(0,n.jsx)("div",{className:"space-y-4",children:l.map(e=>(0,n.jsx)(c.Zp,{className:"hover:bg-accent transition-colors cursor-pointer",onClick:()=>x(e.slug),children:(0,n.jsxs)("div",{className:"flex items-center justify-between p-4",children:[(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("span",{className:"font-medium text-lg",children:e.name}),(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:e.description||`(${e.slug})`})]}),(0,n.jsx)(u.$,{variant:"ghost",size:"icon",className:"ml-2",children:(0,n.jsx)(p.A,{className:"h-5 w-5"})})]})},e.id))}),(0,n.jsxs)(O.lG,{children:[(0,n.jsx)(O.zM,{asChild:!0,children:(0,n.jsxs)(u.$,{variant:"outline",className:"w-full mt-4 border-dashed",children:[(0,n.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Create New Organization"]})}),(0,n.jsxs)(O.Cf,{children:[(0,n.jsxs)(O.c7,{children:[(0,n.jsx)(O.L3,{children:"Create New Organization"}),(0,n.jsx)(O.rr,{children:"Create a new organization unit to manage your automation processes."})]}),(0,n.jsx)(P,{onSuccess:E})]})]})]}),h&&(0,n.jsx)("div",{className:"mt-6 p-4 bg-destructive/10 rounded-md text-destructive text-center",children:h}),(0,n.jsx)("div",{className:"mt-4 text-center",children:(0,n.jsx)(u.$,{variant:"link",className:"text-xs text-muted-foreground",onClick:()=>{let e=!z;S(e),window.history.replaceState({},"",e?"/tenant-selector?force=true":"/tenant-selector?force=false")},children:z?"Enable auto-redirect to default organization":"Disable auto-redirect to default organization"})}),(0,n.jsx)("div",{className:"mt-2 text-center",children:(0,n.jsx)(u.$,{variant:"link",className:"text-primary underline",onClick:()=>b.push("/login?signout=true"),children:"Need to sign in to another account?"})})]})})}function k(){return(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})})}function E(){return(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(k,{}),children:(0,n.jsx)(S,{})})}},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var n=r(60687);r(43210);var a=r(61170),s=r(36966);function i({className:e,...t}){return(0,n.jsx)(a.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},88200:(e,t,r)=>{Promise.resolve().then(r.bind(r,21195))},89513:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.HeadManagerContext},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(60687);r(43210);var a=r(36966);function s({className:e,type:t,...r}){return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91425:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var n=r(65239),a=r(48088),s=r(31369),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let l={children:["",{children:["(auth)",{children:["tenant-selector",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21195)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\tenant-selector\\page.tsx"]}]},{}]},{forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\tenant-selector\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(auth)/tenant-selector/page",pathname:"/tenant-selector",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>c,XL:()=>d});var n=r(60687),a=r(43210),s=r(24224),i=r(36966);let o=(0,s.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),l=a.forwardRef(({className:e,variant:t,...r},a)=>(0,n.jsx)("div",{ref:a,role:"alert",className:(0,i.cn)(o({variant:t}),e),...r}));l.displayName="Alert";let d=a.forwardRef(({className:e,children:t,...r},a)=>(t||console.warn("AlertTitle must have content for accessibility"),(0,n.jsx)("h5",{ref:a,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...r,children:t})));d.displayName="AlertTitle";let c=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));c.displayName="AlertDescription"}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7966,5584,5880,7943,6763],()=>r(91425));module.exports=n})();