(()=>{var e={};e.id=5162,e.ids=[5162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23904:(e,r,t)=>{Promise.resolve().then(t.bind(t,72128)),Promise.resolve().then(t.bind(t,2505)),Promise.resolve().then(t.bind(t,92588)),Promise.resolve().then(t.bind(t,48974)),Promise.resolve().then(t.bind(t,31057)),Promise.resolve().then(t.bind(t,50417))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38475:(e,r,t)=>{Promise.resolve().then(t.bind(t,52162)),Promise.resolve().then(t.bind(t,83847)),Promise.resolve().then(t.bind(t,78526)),Promise.resolve().then(t.bind(t,97597)),Promise.resolve().then(t.bind(t,98641)),Promise.resolve().then(t.bind(t,80110))},52162:(e,r,t)=>{"use strict";t.d(r,{AdminRouteGuard:()=>d});var s=t(60687),n=t(86522),o=t(16189);t(43210);var i=t(85726);function d({children:e,redirectPath:r="/tenant-selector",loadingComponent:t}){let{isSystemAdmin:d,isLoading:a,isLogout:l}=(0,n.A)();return((0,o.useRouter)(),a)?t||(0,s.jsxs)("div",{className:"w-full p-8 space-y-4",children:[(0,s.jsx)(i.E,{className:"h-12 w-full rounded-lg"}),(0,s.jsx)(i.E,{className:"h-60 w-full rounded-lg"}),(0,s.jsx)(i.E,{className:"h-12 w-2/3 rounded-lg"})]}):d?(0,s.jsx)(s.Fragment,{children:e}):null}},58658:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(37413);function n(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h1",{className:"text-2xl md:text-3xl font-bold tracking-tight text-foreground",children:"System Settings"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Configure system-wide settings and preferences here."})]}),(0,s.jsx)("div",{className:"bg-card rounded-xl border border-border p-6",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Settings configuration will be implemented here."})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72128:(e,r,t)=>{"use strict";t.d(r,{AdminRouteGuard:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AdminRouteGuard() from the server but AdminRouteGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\admin-route-guard.tsx","AdminRouteGuard")},78335:()=>{},82275:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>a});var s=t(65239),n=t(48088),o=t(31369),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let a={children:["",{children:["(systemAdmin)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,58658)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96707)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\settings\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(systemAdmin)/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:a}})},96487:()=>{},96707:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(37413),n=t(50417),o=t(48974),i=t(31057),d=t(92588),a=t(2505),l=t(72128);function u({children:e}){return(0,s.jsx)(l.AdminRouteGuard,{children:(0,s.jsx)(a.ChatProvider,{children:(0,s.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,s.jsxs)(n.SidebarProvider,{className:"flex flex-col",children:[(0,s.jsx)(i.SiteHeader,{}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)(o.AppSidebar,{}),(0,s.jsx)(n.SidebarInset,{children:(0,s.jsx)(d.SearchProvider,{children:(0,s.jsx)("main",{className:"",children:e})})})]})]})})})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7966,5584,5156,4654,6467,6763,519],()=>t(82275));module.exports=s})();