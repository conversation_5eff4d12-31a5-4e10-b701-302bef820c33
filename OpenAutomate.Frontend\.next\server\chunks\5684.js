"use strict";exports.id=5684,exports.ids=[5684],exports.modules={63756:(e,r,t)=>{t.d(r,{OK:()=>ee,bL:()=>J,VM:()=>P,lr:()=>$,LM:()=>Q});var n=t(43210);function o(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function l(...e){return r=>{let t=!1,n=e.map(e=>{let n=o(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():o(e[r],null)}}}}function i(...e){return n.useCallback(l(...e),e)}t(51215);var a=t(60687),s=n.forwardRef((e,r)=>{let{children:t,...o}=e,l=n.Children.toArray(t),i=l.find(d);if(i){let e=i.props.children,t=l.map(r=>r!==i?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(u,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,t):null})}return(0,a.jsx)(u,{...o,ref:r,children:t})});s.displayName="Slot";var u=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t),i=function(e,r){let t={...r};for(let n in r){let o=e[n],l=r[n];/^on[A-Z]/.test(n)?o&&l?t[n]=(...e)=>{l(...e),o(...e)}:o&&(t[n]=o):"style"===n?t[n]={...o,...l}:"className"===n&&(t[n]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==n.Fragment&&(i.ref=r?l(r,e):e),n.cloneElement(t,i)}return n.Children.count(t)>1?n.Children.only(null):null});u.displayName="SlotClone";var c=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===c}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=n.forwardRef((e,t)=>{let{asChild:n,...o}=e,l=n?s:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...o,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{}),p=globalThis?.document?n.useLayoutEffect:()=>{},m=e=>{let{present:r,children:t}=e,o=function(e){var r,t;let[o,l]=n.useState(),i=n.useRef({}),a=n.useRef(e),s=n.useRef("none"),[u,c]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,r)=>t[e][r]??e,r));return n.useEffect(()=>{let e=v(i.current);s.current="mounted"===u?e:"none"},[u]),p(()=>{let r=i.current,t=a.current;if(t!==e){let n=s.current,o=v(r);e?c("MOUNT"):"none"===o||r?.display==="none"?c("UNMOUNT"):t&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),p(()=>{if(o){let e;let r=o.ownerDocument.defaultView??window,t=t=>{let n=v(i.current).includes(t.animationName);if(t.target===o&&n&&(c("ANIMATION_END"),!a.current)){let t=o.style.animationFillMode;o.style.animationFillMode="forwards",e=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=t)})}},n=e=>{e.target===o&&(s.current=v(i.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{e&&(i.current=getComputedStyle(e)),l(e)},[])}}(r),l="function"==typeof t?t({present:o.isPresent}):n.Children.only(t),a=i(o.ref,function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof t||o.isPresent?n.cloneElement(l,{ref:a}):null};function v(e){return e?.animationName||"none"}function h(e){let r=n.useRef(e);return n.useEffect(()=>{r.current=e}),n.useMemo(()=>(...e)=>r.current?.(...e),[])}m.displayName="Presence";var w=n.createContext(void 0);function g(e,r,{checkForDefaultPrevented:t=!0}={}){return function(n){if(e?.(n),!1===t||!n.defaultPrevented)return r?.(n)}}var y="ScrollArea",[b,S]=function(e,r=[]){let t=[],o=()=>{let r=t.map(e=>n.createContext(e));return function(t){let o=t?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...t,[e]:o}}),[t,o])}};return o.scopeName=e,[function(r,o){let l=n.createContext(o),i=t.length;t=[...t,o];let s=r=>{let{scope:t,children:o,...s}=r,u=t?.[e]?.[i]||l,c=n.useMemo(()=>s,Object.values(s));return(0,a.jsx)(u.Provider,{value:c,children:o})};return s.displayName=r+"Provider",[s,function(t,a){let s=a?.[e]?.[i]||l,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=t.reduce((r,{useScope:t,scopeName:n})=>{let o=t(e)[`__scope${n}`];return{...r,...o}},{});return n.useMemo(()=>({[`__scope${r.scopeName}`]:o}),[o])}};return t.scopeName=r.scopeName,t}(o,...r)]}(y),[E,x]=b(y),R=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:o="hover",dir:l,scrollHideDelay:s=600,...u}=e,[c,d]=n.useState(null),[p,m]=n.useState(null),[v,h]=n.useState(null),[g,y]=n.useState(null),[b,S]=n.useState(null),[x,R]=n.useState(0),[C,N]=n.useState(0),[T,P]=n.useState(!1),[L,j]=n.useState(!1),_=i(r,e=>d(e)),O=function(e){let r=n.useContext(w);return e||r||"ltr"}(l);return(0,a.jsx)(E,{scope:t,type:o,dir:O,scrollHideDelay:s,scrollArea:c,viewport:p,onViewportChange:m,content:v,onContentChange:h,scrollbarX:g,onScrollbarXChange:y,scrollbarXEnabled:T,onScrollbarXEnabledChange:P,scrollbarY:b,onScrollbarYChange:S,scrollbarYEnabled:L,onScrollbarYEnabledChange:j,onCornerWidthChange:R,onCornerHeightChange:N,children:(0,a.jsx)(f.div,{dir:O,...u,ref:_,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});R.displayName=y;var C="ScrollAreaViewport",N=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:o,nonce:l,...s}=e,u=x(C,t),c=i(r,n.useRef(null),u.onViewportChange);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,a.jsx)(f.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,a.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});N.displayName=C;var T="ScrollAreaScrollbar",P=n.forwardRef((e,r)=>{let{forceMount:t,...o}=e,l=x(T,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=l,u="horizontal"===e.orientation;return n.useEffect(()=>(u?i(!0):s(!0),()=>{u?i(!1):s(!1)}),[u,i,s]),"hover"===l.type?(0,a.jsx)(L,{...o,ref:r,forceMount:t}):"scroll"===l.type?(0,a.jsx)(j,{...o,ref:r,forceMount:t}):"auto"===l.type?(0,a.jsx)(_,{...o,ref:r,forceMount:t}):"always"===l.type?(0,a.jsx)(O,{...o,ref:r}):null});P.displayName=T;var L=n.forwardRef((e,r)=>{let{forceMount:t,...o}=e,l=x(T,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),s(!0)},n=()=>{r=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,a.jsx)(m,{present:t||i,children:(0,a.jsx)(_,{"data-state":i?"visible":"hidden",...o,ref:r})})}),j=n.forwardRef((e,r)=>{var t;let{forceMount:o,...l}=e,i=x(T,e.__scopeScrollArea),s="horizontal"===e.orientation,u=K(()=>d("SCROLL_END"),100),[c,d]=(t={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,r)=>t[e][r]??e,"hidden"));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>d("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,i.scrollHideDelay,d]),n.useEffect(()=>{let e=i.viewport,r=s?"scrollLeft":"scrollTop";if(e){let t=e[r],n=()=>{let n=e[r];t!==n&&(d("SCROLL"),u()),t=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,s,d,u]),(0,a.jsx)(m,{present:o||"hidden"!==c,children:(0,a.jsx)(O,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:r,onPointerEnter:g(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:g(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),_=n.forwardRef((e,r)=>{let t=x(T,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,c=K(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;s(u?e:r)}},10);return G(t.viewport,c),G(t.content,c),(0,a.jsx)(m,{present:o||i,children:(0,a.jsx)(O,{"data-state":i?"visible":"hidden",...l,ref:r})})}),O=n.forwardRef((e,r)=>{let{orientation:t="vertical",...o}=e,l=x(T,e.__scopeScrollArea),i=n.useRef(null),s=n.useRef(0),[u,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=Y(u.viewport,u.content),f={...o,sizes:u,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,r){return function(e,r,t,n="ltr"){let o=k(t),l=r||o/2,i=t.scrollbar.paddingStart+l,a=t.scrollbar.size-t.scrollbar.paddingEnd-(o-l),s=t.content-t.viewport;return q([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,u,r)}return"horizontal"===t?(0,a.jsx)(A,{...f,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=B(l.viewport.scrollLeft,u,l.dir);i.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===t?(0,a.jsx)(D,{...f,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=B(l.viewport.scrollTop,u);i.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),A=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:o,...l}=e,s=x(T,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=i(r,d,s.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,a.jsx)(W,{"data-orientation":"horizontal",...l,ref:f,sizes:t,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":k(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(s.viewport){let n=s.viewport.scrollLeft+r.deltaX;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:X(u.paddingLeft),paddingEnd:X(u.paddingRight)}})}})}),D=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:o,...l}=e,s=x(T,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=i(r,d,s.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,a.jsx)(W,{"data-orientation":"vertical",...l,ref:f,sizes:t,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":k(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(s.viewport){let n=s.viewport.scrollTop+r.deltaY;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:X(u.paddingTop),paddingEnd:X(u.paddingBottom)}})}})}),[M,I]=b(T),W=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:o,hasThumb:l,onThumbChange:s,onThumbPointerUp:u,onThumbPointerDown:c,onThumbPositionChange:d,onDragScroll:p,onWheelScroll:m,onResize:v,...w}=e,y=x(T,t),[b,S]=n.useState(null),E=i(r,e=>S(e)),R=n.useRef(null),C=n.useRef(""),N=y.viewport,P=o.content-o.viewport,L=h(m),j=h(d),_=K(v,10);function O(e){R.current&&p({x:e.clientX-R.current.left,y:e.clientY-R.current.top})}return n.useEffect(()=>{let e=e=>{let r=e.target;b?.contains(r)&&L(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[N,b,P,L]),n.useEffect(j,[o,j]),G(b,_),G(y.content,_),(0,a.jsx)(M,{scope:t,scrollbar:b,hasThumb:l,onThumbChange:h(s),onThumbPointerUp:h(u),onThumbPositionChange:j,onThumbPointerDown:h(c),children:(0,a.jsx)(f.div,{...w,ref:E,style:{position:"absolute",...w.style},onPointerDown:g(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),R.current=b.getBoundingClientRect(),C.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),O(e))}),onPointerMove:g(e.onPointerMove,O),onPointerUp:g(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=C.current,y.viewport&&(y.viewport.style.scrollBehavior=""),R.current=null})})})}),U="ScrollAreaThumb",$=n.forwardRef((e,r)=>{let{forceMount:t,...n}=e,o=I(U,e.__scopeScrollArea);return(0,a.jsx)(m,{present:t||o.hasThumb,children:(0,a.jsx)(H,{ref:r,...n})})}),H=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:o,...l}=e,s=x(U,t),u=I(U,t),{onThumbPositionChange:c}=u,d=i(r,e=>u.onThumbChange(e)),p=n.useRef(void 0),m=K(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{m(),p.current||(p.current=Z(e,c),c())};return c(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,m,c]),(0,a.jsx)(f.div,{"data-state":u.hasThumb?"visible":"hidden",...l,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:g(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,n=e.clientY-r.top;u.onThumbPointerDown({x:t,y:n})}),onPointerUp:g(e.onPointerUp,u.onThumbPointerUp)})});$.displayName=U;var V="ScrollAreaCorner",z=n.forwardRef((e,r)=>{let t=x(V,e.__scopeScrollArea),n=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&n?(0,a.jsx)(F,{...e,ref:r}):null});z.displayName=V;var F=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,...o}=e,l=x(V,t),[i,s]=n.useState(0),[u,c]=n.useState(0),d=!!(i&&u);return G(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),c(e)}),G(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),s(e)}),d?(0,a.jsx)(f.div,{...o,ref:r,style:{width:i,height:u,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function X(e){return e?parseInt(e,10):0}function Y(e,r){let t=e/r;return isNaN(t)?0:t}function k(e){let r=Y(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function B(e,r,t="ltr"){let n=k(r),o=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-o,i=r.content-r.viewport,a=function(e,[r,t]){return Math.min(t,Math.max(r,e))}(e,"ltr"===t?[0,i]:[-1*i,0]);return q([0,i],[0,l-n])(a)}function q(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let n=(r[1]-r[0])/(e[1]-e[0]);return r[0]+n*(t-e[0])}}var Z=(e,r=()=>{})=>{let t={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=t.left!==l.left,a=t.top!==l.top;(i||a)&&r(),t=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function K(e,r){let t=h(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(t,r)},[t,r])}function G(e,r){let t=h(r);p(()=>{let r=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return n.observe(e),()=>{window.cancelAnimationFrame(r),n.unobserve(e)}}},[e,t])}var J=R,Q=N,ee=z},75593:(e,r,t)=>{t.d(r,{H4:()=>C,_V:()=>R,bL:()=>x});var n=t(43210),o=t(60687),l=globalThis?.document?n.useLayoutEffect:()=>{};function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}t(51215);var a=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){var l;let e,a;let s=(l=t,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),u=function(e,r){let t={...r};for(let n in r){let o=e[n],l=r[n];/^on[A-Z]/.test(n)?o&&l?t[n]=(...e)=>{l(...e),o(...e)}:o&&(t[n]=o):"style"===n?t[n]={...o,...l}:"className"===n&&(t[n]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==n.Fragment&&(u.ref=r?function(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}(r,s):s),n.cloneElement(t,u)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:l,...i}=e,a=n.Children.toArray(l),u=a.find(s);if(u){let e=u.props.children,l=a.map(r=>r!==u?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,o.jsx)(r,{...i,ref:t,children:l})});return t.displayName=`${e}.Slot`,t}(`Primitive.${r}`),l=n.forwardRef((e,n)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?t:r,{...i,ref:n})});return l.displayName=`Primitive.${r}`,{...e,[r]:l}},{}),c=t(57379);function d(){return()=>{}}var f="Avatar",[p,m]=function(e,r=[]){let t=[],l=()=>{let r=t.map(e=>n.createContext(e));return function(t){let o=t?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...t,[e]:o}}),[t,o])}};return l.scopeName=e,[function(r,l){let i=n.createContext(l),a=t.length;t=[...t,l];let s=r=>{let{scope:t,children:l,...s}=r,u=t?.[e]?.[a]||i,c=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:l})};return s.displayName=r+"Provider",[s,function(t,o){let s=o?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==l)return l;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=t.reduce((r,{useScope:t,scopeName:n})=>{let o=t(e)[`__scope${n}`];return{...r,...o}},{});return n.useMemo(()=>({[`__scope${r.scopeName}`]:o}),[o])}};return t.scopeName=r.scopeName,t}(l,...r)]}(f),[v,h]=p(f),w=n.forwardRef((e,r)=>{let{__scopeAvatar:t,...l}=e,[i,a]=n.useState("idle");return(0,o.jsx)(v,{scope:t,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,o.jsx)(u.span,{...l,ref:r})})});w.displayName=f;var g="AvatarImage",y=n.forwardRef((e,r)=>{let{__scopeAvatar:t,src:i,onLoadingStatusChange:a=()=>{},...s}=e,f=h(g,t),p=function(e,{referrerPolicy:r,crossOrigin:t}){let o=(0,c.useSyncExternalStore)(d,()=>!0,()=>!1),i=n.useRef(null),a=o?(i.current||(i.current=new window.Image),i.current):null,[s,u]=n.useState(()=>E(a,e));return l(()=>{u(E(a,e))},[a,e]),l(()=>{let e=e=>()=>{u(e)};if(!a)return;let n=e("loaded"),o=e("error");return a.addEventListener("load",n),a.addEventListener("error",o),r&&(a.referrerPolicy=r),"string"==typeof t&&(a.crossOrigin=t),()=>{a.removeEventListener("load",n),a.removeEventListener("error",o)}},[a,t,r]),s}(i,s),m=function(e){let r=n.useRef(e);return n.useEffect(()=>{r.current=e}),n.useMemo(()=>(...e)=>r.current?.(...e),[])}(e=>{a(e),f.onImageLoadingStatusChange(e)});return l(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,o.jsx)(u.img,{...s,ref:r,src:i}):null});y.displayName=g;var b="AvatarFallback",S=n.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:l,...i}=e,a=h(b,t),[s,c]=n.useState(void 0===l);return n.useEffect(()=>{if(void 0!==l){let e=window.setTimeout(()=>c(!0),l);return()=>window.clearTimeout(e)}},[l]),s&&"loaded"!==a.imageLoadingStatus?(0,o.jsx)(u.span,{...i,ref:r}):null});function E(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}S.displayName=b;var x=w,R=y,C=S}};