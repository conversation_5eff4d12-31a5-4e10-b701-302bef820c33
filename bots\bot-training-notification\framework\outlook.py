import win32com.client as client
from typing import List, Optional, <PERSON><PERSON>

class OutlookException(Exception):
    pass

class Outlook:
    """
    Automate Outlook tasks
    """

    def __init__(self) -> None:
        self.ol, self.namespace, self.default_account = self.__get_outlook_config()

    @staticmethod
    def __get_outlook_config() -> (
        <PERSON><PERSON>[client.Dispatch, client.Dispatch, client.Dispatch]
    ):
        """
        Initialize connection to Outlook, return a tuple for Application object, namespace object, default account
        """
        try:
            ol = client.Dispatch("outlook.application")
            namespace = ol.GetNamespace("MAPI")
            default_account = ol.Session.Accounts[0]
            return ol, namespace, default_account
        except Exception as e:
            raise OutlookException(f"Error initializing Outlook connection: {str(e)}")
    def send_mail_message(
        self,
        mail_to: str,
        mail_from: Optional[str] = None,
        mail_cc: Optional[str] = None,
        subject: Optional[str] = None,
        body: Optional[str] = None,
        attachments: Optional[List[str]] = [],
        is_body_html: Optional[bool] = False,
    ) -> <PERSON><PERSON>[bool, str]:
        """
        Send email using Outlook:
        - mail_from (str): account used to send email
        - mail_to (str): recipient(s)' email address
        - mail_cc (str): recipient(s)' email address for CC
        - subject (str): email's subject
        - body (str): email's body
        - attachments (list): list of attachments
        - is_body_html (bool): True if the body is HTML, False if it's plain text
        """
        try:
            mail_item = self.ol.CreateItem(0)

            # Find the account to use for sending
            if mail_from is None or mail_from == "":
                used_account = self.default_account
                print(f"DEBUG: Using default account: {used_account.SmtpAddress}")
            else:
                print(f"DEBUG: Looking for account: {mail_from}")
                matching_accounts = [
                    account
                    for account in self.namespace.Accounts
                    if account.SmtpAddress.lower() == mail_from.lower()
                ]
                print(f"DEBUG: Found {len(matching_accounts)} matching accounts")
                if matching_accounts:
                    used_account = matching_accounts[0]
                    print(f"DEBUG: Selected account: {used_account.SmtpAddress}")
                else:
                    print(f"DEBUG: No matching account found for {mail_from}")
                    return False, f"Account with email '{mail_from}' not found"

            print(f"DEBUG: Setting SendUsingAccount to: {used_account.SmtpAddress}")

            # Use _oleobj_.Invoke to set the SendUsingAccount property
            # Property ID for SendUsingAccount is typically 0x0065
            try:
                mail_item._oleobj_.Invoke(*(64209, 0, 8, 0, used_account))
                print(f"DEBUG: Successfully set SendUsingAccount via Invoke")
            except Exception as invoke_error:
                print(f"DEBUG: Invoke failed, trying standard property: {invoke_error}")
                mail_item.SendUsingAccount = used_account

            mail_item.To = mail_to
            mail_item.CC = mail_cc if (mail_cc is not None and mail_cc != "") else ""
            mail_item.Subject = (
                subject if subject is not None and subject != "" else ""
            )

            if body is not None and body != "":
                if is_body_html:
                    mail_item.HTMLBody = body
                else:
                    mail_item.Body = body

            for attachment in attachments:
                mail_item.Attachments.Add(attachment)

            print(f"DEBUG: About to send email from: {used_account.SmtpAddress}")
            mail_item.Send()
            print(f"DEBUG: Email sent successfully from: {used_account.SmtpAddress}")
            return True, ""
        except Exception as e:
            return False, f"Error sending email: {str(e)}"
