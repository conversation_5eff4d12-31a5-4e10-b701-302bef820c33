(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6464],{7283:(e,t,a)=>{"use strict";a.d(t,{F:()=>j,fetchApi:()=>v});var r=a(48133),s=a(67938);let n=s.$.api.defaultHeaders,l=!1,i=[],o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;i.forEach(a=>{e?a.reject(e):a.resolve(t)}),i=[]},c=async e=>{let t={message:e.statusText,status:e.status};try{let a=await e.json();a.message?(t.message=a.message,t.details=a.details||a.message):a.error?(t.message=a.error,t.details=a.error):t.details=JSON.stringify(a)}catch(a){t.details=e.statusText}return t},d=()=>{window.dispatchEvent(new Event("auth:token-expired"))},u=e=>{if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw{message:"Network error. Please check your connection.",status:0,details:e.message};if(e instanceof Error)throw{message:e.message||"Network error occurred",status:0,details:e.stack||e.message};throw{message:"An unexpected error occurred",status:0,details:String(e)}},g=async e=>{if(204===e.status)return{};let t=e.headers.get("content-type"),a=e.headers.get("content-length");if(!t||-1===t.indexOf("application/json")||"0"===a)return{};let r=await e.text();return r?JSON.parse(r):{}},m=async()=>{if(l)return new Promise((e,t)=>{i.push({resolve:e,reject:t})});l=!0;try{let e=(await v("api/auth/refresh-token",{method:"POST",credentials:"include"})).token;return(0,r.O5)(e),o(null,e),e}catch(e){throw o(e),e}finally{l=!1}},h=async(e,t,a,r,s)=>{if(e.includes("refresh-token")||e.includes("login"))return d(),null;try{let e=await m();if(!e)return null;let r=p(a,s);r.Authorization="Bearer ".concat(e);let{body:n}=f(s),l=await fetch(t,{...a,body:n,headers:r,credentials:"include"});if(l.ok)return g(l);return null}catch(e){return d(),console.error("Token refresh failed:",e),null}},x=e=>{if(e.startsWith("http"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat(s.$.api.baseUrl,"/").concat(t)},f=e=>e?e instanceof FormData?{body:e,headers:{}}:{body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}:{body:void 0,headers:{}},p=(e,t)=>{let a={...t instanceof FormData?{Accept:n.Accept}:{...n},...e.headers};if(!a.Authorization){let e=(0,r.c4)();e&&(a.Authorization="Bearer ".concat(e))}return a};async function v(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0,r=x(e),{body:s,headers:n}=f(a),l={...p(t,a),...n};try{let n=await fetch(r,{...t,body:s,headers:l,credentials:"include"});if(n.ok)return g(n);if(401===n.status){let s=await h(e,r,t,l,a);if(s)return s}throw await c(n)}catch(e){if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;u(e)}}let j={get:(e,t)=>v(e,{...t,method:"GET"}),post:(e,t,a)=>{let{body:r,headers:s}=f(t);return v(e,{...a,method:"POST",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},put:(e,t,a)=>{let{body:r,headers:s}=f(t);return v(e,{...a,method:"PUT",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},patch:(e,t,a)=>{let{body:r,headers:s}=f(t);return v(e,{...a,method:"PATCH",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},delete:(e,t)=>v(e,{...t,method:"DELETE"})}},11832:(e,t,a)=>{"use strict";a.d(t,{i:()=>o});var r=a(95155),s=a(18289),n=a(47330),l=a(30285),i=a(44838);function o(e){let{table:t}=e;return(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(s.ty,{asChild:!0,children:(0,r.jsxs)(l.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,r.jsx)(n.A,{}),"View"]})}),(0,r.jsxs)(i.SQ,{align:"end",className:"w-[150px]",children:[(0,r.jsx)(i.lp,{children:"Toggle columns"}),(0,r.jsx)(i.mB,{}),t.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,r.jsx)(i.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},15426:(e,t,a)=>{"use strict";function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{dateStyle:a="medium",timeStyle:r="short",fallback:s="N/A",customFormat:n,locale:l=navigator.language||"en-US"}=t;if(!e)return s;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug("Corrected UTC date format: ".concat(e," -> ").concat(a))),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn("Invalid date provided to formatUtcToLocal: ".concat(e)),s;if(n)return function(e,t){let a=e.getFullYear(),r=e.getMonth()+1,s=e.getDate(),n=e.getHours(),l=e.getMinutes(),i={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][r-1],dd:s.toString().padStart(2,"0"),h:(n%12||12).toString(),mm:l.toString().padStart(2,"0"),a:n>=12?"PM":"AM"},o=t;return Object.entries(i).forEach(e=>{let[t,a]=e;o=o.replace(RegExp(t,"g"),a)}),o}(t,n);return new Intl.DateTimeFormat(l,{dateStyle:a,timeStyle:r}).format(t)}catch(t){return console.error("Error formatting date ".concat(e,":"),t),s}}a.d(t,{Ej:()=>r})},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},36928:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}},39113:(e,t,a)=>{"use strict";a.d(t,{default:()=>P});var r=a(95155),s=a(30285),n=a(66695),l=a(47262),i=a(26126),o=a(87570),c=a(15426);let d=[{id:"select",header:e=>{let{table:t}=e;return(0,r.jsx)(l.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e;return(0,r.jsx)(l.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]"})},enableSorting:!1,enableHiding:!1},{accessorKey:"name",header:e=>{let{column:t}=e;return(0,r.jsx)(o.w,{column:t,title:"Name"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("span",{className:"font-medium",children:t.getValue("name")})})}},{accessorKey:"slug",header:e=>{let{column:t}=e;return(0,r.jsx)(o.w,{column:t,title:"Slug"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("code",{className:"text-sm bg-muted px-2 py-1 rounded",children:t.getValue("slug")})})}},{accessorKey:"description",header:e=>{let{column:t}=e;return(0,r.jsx)(o.w,{column:t,title:"Description"})},cell:e=>{let{row:t}=e,a=t.getValue("description");return(0,r.jsx)("div",{className:"max-w-[200px] truncate",children:a||(0,r.jsx)("span",{className:"text-muted-foreground",children:"No description"})})}},{accessorKey:"isActive",header:e=>{let{column:t}=e;return(0,r.jsx)(o.w,{column:t,title:"Status"})},cell:e=>{let{row:t}=e,a=t.getValue("isActive");return(0,r.jsx)(i.E,{variant:a?"default":"secondary",children:a?"Active":"Inactive"})}},{accessorKey:"createdAt",header:e=>{let{column:t}=e;return(0,r.jsx)(o.w,{column:t,title:"Created At"})},cell:e=>{let{row:t}=e,a=t.getValue("createdAt"),s=(0,c.Ej)(a,{fallback:"-"});return(0,r.jsx)("div",{className:"text-sm",children:s})}}];var u=a(54333),g=a(12115),m=a(35695),h=a(47924),x=a(51154),f=a(54416),p=a(66932),v=a(62523),j=a(11832),y=a(59409);function S(e){var t,a;let{table:n,statuses:l,onSearch:o,onStatusChange:c,searchValue:d="",isFiltering:u=!1,isPending:m=!1}=e,S=n.getState().columnFilters.length>0,b=n.getState().columnFilters.length,w=(0,g.useRef)(null),N=(0,g.useRef)(null);(0,g.useEffect)(()=>{document.activeElement!==w.current&&null!==N.current&&w.current&&(w.current.focus(),null!==N.current&&w.current.setSelectionRange(N.current,N.current))},[m,u]);let C=e=>{if(w.current&&(N.current=w.current.selectionStart),o)o(e);else{var t;null===(t=n.getColumn("name"))||void 0===t||t.setFilterValue(e)}};return(0,r.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,r.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,r.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,r.jsx)(h.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(v.p,{ref:w,placeholder:"Search by Name or Slug...",value:d,onChange:e=>C(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:u,onFocus:()=>{w.current&&(N.current=w.current.selectionStart)}}),u&&(0,r.jsx)(x.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!u&&""!==d&&(0,r.jsx)(f.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>C("")})]}),n.getColumn("isActive")&&(0,r.jsx)("div",{className:"flex items-center space-x-1",children:(0,r.jsxs)(y.l6,{onValueChange:e=>{if(c)c(e);else{var t;null===(t=n.getColumn("isActive"))||void 0===t||t.setFilterValue("all"===e?"":e)}},value:(null===(t=n.getColumn("isActive"))||void 0===t?void 0:t.getFilterValue())||"all",disabled:u||m,children:[(0,r.jsx)(y.bq,{className:"h-10 sm:w-[180px]",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)(y.yv,{placeholder:"Filter status"}),(null===(a=n.getColumn("isActive"))||void 0===a?void 0:a.getFilterValue())&&(0,r.jsx)(i.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,r.jsxs)(y.gC,{children:[(0,r.jsx)(y.eb,{value:"all",children:"All Statuses"}),l.map(e=>(0,r.jsx)(y.eb,{value:e.value,children:e.label},e.value))]})]})}),b>0&&(0,r.jsxs)(i.E,{variant:"secondary",className:"rounded-sm px-1",children:[b," active ",1===b?"filter":"filters"]}),S&&(0,r.jsxs)(s.$,{variant:"ghost",onClick:()=>{n.resetColumnFilters(),o&&o("")},className:"h-8 px-2 lg:px-3",disabled:u,children:["Reset",(0,r.jsx)(f.A,{className:"ml-2 h-4 w-4"})]})]}),(0,r.jsx)(j.i,{table:n})]})}var b=a(36268),w=a(11032),N=a(45995),C=a(62668),A=a(29797);function k(){return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-gray-100"})})}function z(){var e;let t=(0,m.useRouter)(),a=(0,m.usePathname)(),l=(0,m.useSearchParams)(),{updateUrl:i}=(0,C.z)(),[o,c]=(0,g.useState)([]),[h,x]=(0,g.useState)(!0),[f,p]=(0,g.useState)(null),[v,j]=(0,g.useState)({}),[y,k]=(0,g.useState)({}),[z,P]=(0,g.useState)(!1),[E,I]=(0,g.useState)(!1),R=(0,g.useRef)(null),T=(0,g.useRef)(!0),[O,F]=(0,g.useState)(()=>{let e=[],t=l.get("name");t&&e.push({id:"name",value:t});let a=l.get("status");return a&&e.push({id:"isActive",value:a}),e}),[M,U]=(0,g.useState)(()=>{let e=l.get("sort"),t=l.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[V,L]=(0,g.useState)(()=>{let e=l.get("page"),t=l.get("size");return{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:t?parseInt(t):10}}),[Z,$]=(0,g.useState)(null!==(e=l.get("name"))&&void 0!==e?e:""),_=(0,g.useCallback)(async()=>{try{x(!0),p(null);let e=await N.i.getAllOrganizationUnits();c(e)}catch(e){p("Failed to fetch organization units"),console.error("Error fetching organization units:",e)}finally{x(!1)}},[]);(0,g.useEffect)(()=>{_()},[_]);let B=(e,t,a)=>{let r=e[a.id],s=t[a.id];if("string"==typeof r&&"string"==typeof s&&(r=r.toLowerCase(),s=s.toLowerCase()),null==r)return null==s?0:a.desc?1:-1;if(null==s)return a.desc?-1:1;let n=0;return r<s?n=-1:r>s&&(n=1),a.desc?-n:n},J=(0,g.useMemo)(()=>{let e=[...o];if(O.forEach(t=>{if("name"===t.id&&t.value){let a=t.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(a)||e.slug.toLowerCase().includes(a))}if("isActive"===t.id&&t.value){let a="true"===t.value;e=e.filter(e=>e.isActive===a)}}),M.length>0){let t=M[0];e.sort((e,a)=>B(e,a,t))}return e},[o,O,M]),H=J.length,K=Math.max(1,Math.ceil(H/V.pageSize)),W=(0,g.useMemo)(()=>{let e=V.pageIndex*V.pageSize,t=e+V.pageSize;return J.slice(e,t)},[J,V]),D=(0,b.N4)({data:W,columns:d,state:{sorting:M,columnVisibility:y,rowSelection:v,columnFilters:O,pagination:V},enableRowSelection:!0,onRowSelectionChange:j,onSortingChange:e=>{let t="function"==typeof e?e(M):e;U(t),t.length>0?i(a,{sort:t[0].id,order:t[0].desc?"desc":"asc",page:"1"}):i(a,{sort:null,order:null,page:"1"}),L(e=>({...e,pageIndex:0}))},onColumnFiltersChange:F,onColumnVisibilityChange:k,onPaginationChange:e=>{let t="function"==typeof e?e(V):e;L(t),i(a,{page:(t.pageIndex+1).toString(),size:t.pageSize.toString()})},getCoreRowModel:(0,w.HT)(),getFilteredRowModel:(0,w.hM)(),getPaginationRowModel:(0,w.kW)(),getSortedRowModel:(0,w.h5)(),getFacetedRowModel:(0,w.kQ)(),getFacetedUniqueValues:(0,w.oS)(),manualPagination:!0,pageCount:K,manualSorting:!0,manualFiltering:!0}),q=o.length,G=o.filter(e=>e.isActive).length,Q=(0,g.useCallback)(()=>{_()},[_]);(0,g.useEffect)(()=>{if(T.current){T.current=!1;let e=l.get("page"),t=l.get("size");e&&t||i(a,{page:null!=e?e:"1",size:null!=t?t:"10"})}},[l,i,a]);let Y=(0,g.useCallback)(e=>{$(e),P(!0),R.current&&clearTimeout(R.current),R.current=setTimeout(()=>{let t=D.getColumn("name");t&&(t.setFilterValue(e),i(a,{name:e||null,page:"1"})),L(e=>({...e,pageIndex:0})),P(!1)},500)},[D,i,a]),X=(0,g.useCallback)(e=>{let t=D.getColumn("isActive");if(t){let r="all"===e?"":e;t.setFilterValue(r),i(a,{status:r||null,page:"1"})}L(e=>({...e,pageIndex:0}))},[D,i,a]);return(0,g.useEffect)(()=>()=>{R.current&&clearTimeout(R.current)},[]),(0,r.jsx)("div",{className:"h-full overflow-y-auto bg-background p-6",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h1",{className:"text-2xl md:text-3xl font-bold tracking-tight text-foreground",children:"Organization Units"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage organization units and their structure"})]}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium text-card-foreground",children:"Total Organization Units"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:q}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Organization units managed"})]})]}),(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium text-card-foreground",children:"Active Organization Units"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:G}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently operational"})]})]}),(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium text-card-foreground",children:"Inactive Organization Units"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:q-G}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending deletion"})]})]})]}),f&&(0,r.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,r.jsx)("p",{className:"text-red-800 dark:text-red-300",children:f}),(0,r.jsx)(s.$,{variant:"outline",className:"mt-2",onClick:Q,children:"Retry"})]}),(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{className:"text-xl font-bold tracking-tight",children:"Organization Units"}),H>0&&(0,r.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:["Total: ",H," unit",1!==H?"s":""]})]})})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsx)(S,{table:D,statuses:[{value:"true",label:"Active"},{value:"false",label:"Inactive"}],onSearch:Y,onStatusChange:X,searchValue:Z,isFiltering:h,isPending:z}),(0,r.jsx)(u.b,{data:W,columns:d,onRowClick:e=>{let a="/org-unit-management/".concat(e.id);t.push(a)},table:D,isLoading:h,totalCount:H}),(0,r.jsx)(A.d,{currentPage:V.pageIndex+1,pageSize:V.pageSize,totalCount:H,totalPages:K,isLoading:h,isChangingPageSize:E,isUnknownTotalCount:!1,onPageChange:e=>{L({...V,pageIndex:e-1}),i(a,{page:e.toString()})},onPageSizeChange:e=>{I(!0);let t=Math.floor(V.pageIndex*V.pageSize/e);L({pageSize:e,pageIndex:t}),i(a,{size:e.toString(),page:(t+1).toString()}),setTimeout(()=>I(!1),100)}}),!h&&0===W.length&&!f&&(0,r.jsx)("div",{className:"text-center py-10 text-muted-foreground",children:(0,r.jsxs)("p",{children:["No organization units found. ",Z||O.some(e=>"isActive"===e.id&&e.value)?"Try adjusting your filters.":"No units available."]})})]})]})]})})}function P(){return(0,r.jsx)(g.Suspense,{fallback:(0,r.jsx)(k,{}),children:(0,r.jsx)(z,{})})}},45995:(e,t,a)=>{"use strict";a.d(t,{i:()=>s});var r=a(7283);let s={getAllUsers:async()=>(0,r.fetchApi)("api/admin/user/get-all",{method:"GET"}),getUserById:async e=>(0,r.fetchApi)("api/admin/user/detail/".concat(e),{method:"GET"}),updateUserInfo:async(e,t)=>(0,r.fetchApi)("api/admin/user/update-detail/".concat(e),{method:"PUT",body:JSON.stringify(t)}),changeUserPassword:async(e,t)=>(0,r.fetchApi)("api/admin/user/change-password/".concat(e),{method:"POST",body:JSON.stringify(t)}),getAllOrganizationUnits:async()=>await r.F.get("/api/admin/organization-unit/get-all"),deleteOrganizationUnit:async e=>{await r.F.delete("/api/admin/organization-unit/".concat(e))}}},47330:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},48133:(e,t,a)=>{"use strict";a.d(t,{O5:()=>c,c4:()=>o,gV:()=>u,m_:()=>g,wz:()=>d});var r=a(67938);let s=r.$.auth.tokenStorageKey,n=r.$.auth.userStorageKey,l=null,i=null,o=()=>{if(l)return l;try{let e=localStorage.getItem(s);if(e)return l=e,e}catch(e){console.error("Error accessing localStorage",e)}return null},c=e=>{l=e;try{e?localStorage.setItem(s,e):localStorage.removeItem(s)}catch(e){console.error("Error accessing localStorage",e)}},d=()=>{if(i)return i;try{let e=localStorage.getItem(n);if(e)try{let t=JSON.parse(e);return i=t,t}catch(e){console.error("Error parsing stored user data",e)}}catch(e){console.error("Error accessing localStorage",e)}return null},u=e=>{i=e;try{e?localStorage.setItem(n,JSON.stringify(e)):localStorage.removeItem(n)}catch(e){console.error("Error accessing localStorage",e)}},g=()=>{l=null,i=null;try{localStorage.removeItem(s),localStorage.removeItem(n)}catch(e){console.error("Error accessing localStorage",e)}}},62668:(e,t,a)=>{"use strict";a.d(t,{z:()=>n});var r=a(35695),s=a(12115);function n(){let e=(0,r.useRouter)(),t=(0,r.useSearchParams)(),a=(0,s.useCallback)(e=>{let a=new URLSearchParams(t.toString());return Object.entries(e).forEach(e=>{let[t,r]=e;null===r?a.delete(t):a.set(t,r)}),a.toString()},[t]),n=(0,s.useCallback)((t,r)=>{let s=a(r);e.push("".concat(t,"?").concat(s),{scroll:!1})},[a,e]);return{createQueryString:a,updateUrl:n}}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>d});var r=a(95155);a(12115);var s=a(36928);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},66932:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},67938:(e,t,a)=>{"use strict";a.d(t,{$:()=>r});let r={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},71887:(e,t,a)=>{Promise.resolve().then(a.bind(a,39113))}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6341,8852,5699,8523,9483,3085,5224,8441,1684,7358],()=>t(71887)),_N_E=e.O()}]);