(()=>{var e={};e.id=7589,e.ids=[7589],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15581:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>s.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var n=a(65239),r=a(48088),s=a(31369),l=a(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let o={children:["",{children:["[tenant]",{children:["automation",{children:["schedule",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94565)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\automation\\schedule\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\automation\\schedule\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[tenant]/automation/schedule/page",pathname:"/[tenant]/automation/schedule",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},16162:(e,t,a)=>{"use strict";a.d(t,{default:()=>e8});var n=a(60687),r=a(1303),s=a(29523),l=a(43210),i=a(56896),o=a(96834);function c(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function d(...e){return l.useCallback(function(...e){return t=>{let a=!1,n=e.map(e=>{let n=c(e,t);return a||"function"!=typeof n||(a=!0),n});if(a)return()=>{for(let t=0;t<n.length;t++){let a=n[t];"function"==typeof a?a():c(e[t],null)}}}}(...e),e)}var u=a(67427),m=a(83721),h=a(88805);a(51215);var p=Symbol("radix.slottable");function x(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===p}var g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=l.forwardRef((e,t)=>{var a;let n,r;let{children:s,...i}=e,o=d(l.isValidElement(s)?(a=s,(r=(n=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?a.ref:(r=(n=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?a.props.ref:a.props.ref||a.ref):void 0,t);if(l.isValidElement(s)){let e=function(e,t){let a={...t};for(let n in t){let r=e[n],s=t[n];/^on[A-Z]/.test(n)?r&&s?a[n]=(...e)=>{let t=s(...e);return r(...e),t}:r&&(a[n]=r):"style"===n?a[n]={...r,...s}:"className"===n&&(a[n]=[r,s].filter(Boolean).join(" "))}return{...e,...a}}(i,s.props);return s.type!==l.Fragment&&(e.ref=o),l.cloneElement(s,e)}return l.Children.count(s)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=l.forwardRef((e,a)=>{let{children:r,...s}=e,i=l.Children.toArray(r),o=i.find(x);if(o){let e=o.props.children,r=i.map(t=>t!==o?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...s,ref:a,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,n.jsx)(t,{...s,ref:a,children:r})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),r=l.forwardRef((e,r)=>{let{asChild:s,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?a:t,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),f="Switch",[y,v]=function(e,t=[]){let a=[],r=()=>{let t=a.map(e=>l.createContext(e));return function(a){let n=a?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...a,[e]:n}}),[a,n])}};return r.scopeName=e,[function(t,r){let s=l.createContext(r),i=a.length;a=[...a,r];let o=t=>{let{scope:a,children:r,...o}=t,c=a?.[e]?.[i]||s,d=l.useMemo(()=>o,Object.values(o));return(0,n.jsx)(c.Provider,{value:d,children:r})};return o.displayName=t+"Provider",[o,function(a,n){let o=n?.[e]?.[i]||s,c=l.useContext(o);if(c)return c;if(void 0!==r)return r;throw Error(`\`${a}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let a=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=a.reduce((t,{useScope:a,scopeName:n})=>{let r=a(e)[`__scope${n}`];return{...t,...r}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return a.scopeName=t.scopeName,a}(r,...t)]}(f),[j,b]=y(f),N=l.forwardRef((e,t)=>{let{__scopeSwitch:a,name:r,checked:s,defaultChecked:i,required:o,disabled:c,value:m="on",onCheckedChange:h,form:p,...x}=e,[y,v]=l.useState(null),b=d(t,e=>v(e)),N=l.useRef(!1),w=!y||p||!!y.closest("form"),[C,M]=(0,u.i)({prop:s,defaultProp:i??!1,onChange:h,caller:f});return(0,n.jsxs)(j,{scope:a,checked:C,disabled:c,children:[(0,n.jsx)(g.button,{type:"button",role:"switch","aria-checked":C,"aria-required":o,"data-state":S(C),"data-disabled":c?"":void 0,disabled:c,value:m,...x,ref:b,onClick:function(e,t,{checkForDefaultPrevented:a=!0}={}){return function(n){if(e?.(n),!1===a||!n.defaultPrevented)return t?.(n)}}(e.onClick,e=>{M(e=>!e),w&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),w&&(0,n.jsx)(k,{control:y,bubbles:!N.current,name:r,value:m,checked:C,required:o,disabled:c,form:p,style:{transform:"translateX(-100%)"}})]})});N.displayName=f;var w="SwitchThumb",C=l.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,s=b(w,a);return(0,n.jsx)(g.span,{"data-state":S(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t})});C.displayName=w;var k=l.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:r=!0,...s},i)=>{let o=l.useRef(null),c=d(o,i),u=(0,m.Z)(a),p=(0,h.X)(t);return l.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==a&&t){let n=new Event("click",{bubbles:r});t.call(e,a),e.dispatchEvent(n)}},[u,a,r]),(0,n.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...s,tabIndex:-1,ref:c,style:{...s.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function S(e){return e?"checked":"unchecked"}k.displayName="SwitchBubbleInput";var M=a(36966);function $({className:e,...t}){return(0,n.jsx)(N,{"data-slot":"switch",className:(0,M.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,n.jsx)(C,{"data-slot":"switch-thumb",className:(0,M.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}var D=a(41862),A=a(34208),E=a(58036),T=a(93661),O=a(63143),P=a(62688);let V=(0,P.A)("power-off",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),I=(0,P.A)("power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]]);var R=a(96362),F=a(11860),z=a(21342),_=a(63503),H=a(20140),L=a(59321);function U({schedule:e,onDeleted:t,onToggleEnabled:a,onEdit:r}){let[i,o]=(0,l.useState)(!1),[c,d]=(0,l.useState)(!1),[u,m]=(0,l.useState)(!1),{toast:h}=(0,H.d)(),p=()=>{r&&r()},x=async()=>{d(!0);try{await (0,E.VD)(e.id),h({title:"Schedule Deleted",description:`Schedule "${e.name}" has been deleted successfully.`}),o(!1),t&&t()}catch(e){console.error("Delete failed:",e),h((0,L.m4)(e))}finally{d(!1)}},g=async()=>{if(a){await a(e);return}m(!0);try{let t=e.isEnabled?await (0,E.g8)(e.id):await (0,E.H4)(e.id);h({title:`Schedule ${t.isEnabled?"Enabled":"Disabled"}`,description:`Schedule "${e.name}" has been ${t.isEnabled?"enabled":"disabled"}.`})}catch(e){console.error("Toggle enable failed:",e),h((0,L.m4)(e))}finally{m(!1)}},f=e=>{e.stopPropagation()};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(z.rI,{children:[(0,n.jsx)(z.ty,{asChild:!0,onClick:f,children:(0,n.jsx)(s.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted","aria-label":"Open menu",onClick:f,children:(0,n.jsx)(T.A,{className:"h-4 w-4"})})}),(0,n.jsxs)(z.SQ,{align:"start",className:"w-[180px]",onClick:f,onPointerDown:f,onMouseDown:f,children:[(0,n.jsxs)(z._2,{onClick:e=>{e.stopPropagation(),p()},children:[(0,n.jsx)(O.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,n.jsx)("span",{children:"Edit"})]}),(0,n.jsxs)(z._2,{onClick:e=>{e.stopPropagation(),g()},disabled:u,children:[u?(0,n.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin","aria-hidden":"true"}):e.isEnabled?(0,n.jsx)(V,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}):(0,n.jsx)(I,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,n.jsx)("span",{children:u?`${e.isEnabled?"Disabling":"Enabling"}...`:e.isEnabled?"Disable":"Enable"})]}),(0,n.jsx)(z.mB,{}),(0,n.jsxs)(z._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e.stopPropagation(),o(!0)},children:[(0,n.jsx)(R.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,n.jsx)("span",{children:"Delete"})]})]})]}),(0,n.jsx)(_.lG,{open:i,onOpenChange:o,children:(0,n.jsxs)(_.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,n.jsxs)(_.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(F.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,n.jsx)(_.c7,{children:(0,n.jsx)(_.L3,{children:"Confirm Delete"})}),(0,n.jsxs)("div",{children:["Are you sure you want to delete this schedule? ",(0,n.jsx)("br",{}),(0,n.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Schedule: ",(0,n.jsx)("b",{children:e.name})]}),(0,n.jsx)("br",{}),(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:"This action cannot be undone."})]}),(0,n.jsxs)(_.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,n.jsx)(s.$,{variant:"outline",onClick:e=>{e.stopPropagation(),o(!1)},disabled:c,children:"Cancel"}),(0,n.jsxs)(s.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:e=>{e.stopPropagation(),x()},disabled:c,children:[c&&(0,n.jsx)(D.A,{className:"animate-spin w-4 h-4 mr-2"}),c?"Deleting...":"Delete"]})]})]})})]})}var q=a(27590);let W=({schedule:e,onToggleEnabled:t})=>{let[a,r]=(0,l.useState)(!1),s=async()=>{if(t&&!a){console.log(`Toggling schedule ${e.name} from ${e.isEnabled} to ${!e.isEnabled}`),r(!0);try{await t(e),console.log(`Successfully toggled schedule ${e.name}`)}catch(e){console.error("Toggle failed:",e)}finally{r(!1)}}};return(0,n.jsx)("div",{className:"flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:(0,n.jsxs)("div",{className:"relative",children:[a&&(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-background/80 rounded-full z-10",children:(0,n.jsx)(D.A,{className:"h-3 w-3 animate-spin text-muted-foreground"})}),(0,n.jsx)($,{checked:e.isEnabled,onCheckedChange:s,disabled:a,"aria-label":`${e.isEnabled?"Disable":"Enable"} schedule "${e.name}"`,className:`transition-all duration-200 ${a?"opacity-50":"hover:scale-105"} cursor-pointer`})]})})},G=({onDeleted:e,onToggleEnabled:t,onEdit:a}={})=>[{id:"select",header:({table:e})=>(0,n.jsx)(i.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>{let t=e=>{e.stopPropagation()};return(0,n.jsx)("span",{onClick:t,onMouseDown:t,onPointerDown:t,children:(0,n.jsx)(i.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]",onClick:t})})},enableSorting:!1,enableHiding:!1},{id:"actions",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Actions"}),cell:({row:r})=>(0,n.jsx)(U,{schedule:r.original,onDeleted:e,onToggleEnabled:t,onEdit:a?()=>a(r.original):void 0}),enableSorting:!1,enableHiding:!1},{accessorKey:"name",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Schedule Name"}),cell:({row:e})=>{let t=e.original;return(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("span",{className:"font-medium",children:t.name}),t.description&&(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:t.description})]})}},{accessorKey:"isEnabled",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Status"}),cell:({row:e})=>{let a=e.original;return(0,n.jsx)(W,{schedule:a,onToggleEnabled:t})}},{accessorKey:"recurrenceType",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Recurrence"}),cell:({row:e})=>{let t=e.getValue("recurrenceType");return(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)(o.E,{variant:"outline",children:(0,E.MK)(t)})})}},{accessorKey:"automationPackageName",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Package"}),cell:({row:e})=>(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("span",{children:e.getValue("automationPackageName")||"N/A"})})},{accessorKey:"botAgentName",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Agent"}),cell:({row:e})=>(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("span",{children:e.getValue("botAgentName")||"N/A"})})},{accessorKey:"nextRunTime",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Next Run"}),cell:({row:e})=>{let t=e.getValue("nextRunTime");return(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("span",{className:"text-sm",children:(0,E.po)(t)})})}},{accessorKey:"timeZoneId",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Timezone"}),cell:({row:e})=>(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:e.getValue("timeZoneId")||"UTC"})})},{accessorKey:"createdAt",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Created"}),cell:({row:e})=>{let t=e.getValue("createdAt"),a=(0,q.Ej)(t,{fallback:"-"});return(0,n.jsx)("span",{className:"text-sm",children:a})}},{accessorKey:"cronExpression",header:({column:e})=>(0,n.jsx)(A.w,{column:e,title:"Cron Expression"}),cell:({row:e})=>{let t=e.getValue("cronExpression");return e.original.recurrenceType===E.V5.Advanced&&t?(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("code",{className:"text-xs bg-muted px-2 py-1 rounded",children:t})}):(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:"-"})}}];G();var K=a(50723),Z=a(31207),B=a(89667),J=a(15079);function X(e,t,{checkForDefaultPrevented:a=!0}={}){return function(n){if(e?.(n),!1===a||!n.defaultPrevented)return t?.(n)}}var Q=a(7);function Y(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function ee(...e){return t=>{let a=!1,n=e.map(e=>{let n=Y(e,t);return a||"function"!=typeof n||(a=!0),n});if(a)return()=>{for(let t=0;t<n.length;t++){let a=n[t];"function"==typeof a?a():Y(e[t],null)}}}}var et=globalThis?.document?l.useLayoutEffect:()=>{},ea=e=>{let{present:t,children:a}=e,n=function(e){var t,a;let[n,r]=l.useState(),s=l.useRef({}),i=l.useRef(e),o=l.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",a={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},l.useReducer((e,t)=>a[e][t]??e,t));return l.useEffect(()=>{let e=en(s.current);o.current="mounted"===c?e:"none"},[c]),et(()=>{let t=s.current,a=i.current;if(a!==e){let n=o.current,r=en(t);e?d("MOUNT"):"none"===r||t?.display==="none"?d("UNMOUNT"):a&&n!==r?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),et(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,a=a=>{let r=en(s.current).includes(a.animationName);if(a.target===n&&r&&(d("ANIMATION_END"),!i.current)){let a=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=a)})}},r=e=>{e.target===n&&(o.current=en(s.current))};return n.addEventListener("animationstart",r),n.addEventListener("animationcancel",a),n.addEventListener("animationend",a),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",r),n.removeEventListener("animationcancel",a),n.removeEventListener("animationend",a)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:l.useCallback(e=>{e&&(s.current=getComputedStyle(e)),r(e)},[])}}(t),r="function"==typeof a?a({present:n.isPresent}):l.Children.only(a),s=function(...e){return l.useCallback(ee(...e),e)}(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,a=t&&"isReactWarning"in t&&t.isReactWarning;return a?e.ref:(a=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r));return"function"==typeof a||n.isPresent?l.cloneElement(r,{ref:s}):null};function en(e){return e?.animationName||"none"}ea.displayName="Presence";var er=Symbol("radix.slottable");function es(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===er}var el=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:a,...n}=e;if(l.isValidElement(a)){var r;let e,s;let i=(r=a,(s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?r.ref:(s=(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?r.props.ref:r.props.ref||r.ref),o=function(e,t){let a={...t};for(let n in t){let r=e[n],s=t[n];/^on[A-Z]/.test(n)?r&&s?a[n]=(...e)=>{s(...e),r(...e)}:r&&(a[n]=r):"style"===n?a[n]={...r,...s}:"className"===n&&(a[n]=[r,s].filter(Boolean).join(" "))}return{...e,...a}}(n,a.props);return a.type!==l.Fragment&&(o.ref=t?ee(t,i):i),l.cloneElement(a,o)}return l.Children.count(a)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=l.forwardRef((e,a)=>{let{children:r,...s}=e,i=l.Children.toArray(r),o=i.find(es);if(o){let e=o.props.children,r=i.map(t=>t!==o?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...s,ref:a,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,n.jsx)(t,{...s,ref:a,children:r})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),r=l.forwardRef((e,r)=>{let{asChild:s,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?a:t,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),ei=l.createContext(void 0),eo=a(19783),ec="Tabs",[ed,eu]=function(e,t=[]){let a=[],r=()=>{let t=a.map(e=>l.createContext(e));return function(a){let n=a?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...a,[e]:n}}),[a,n])}};return r.scopeName=e,[function(t,r){let s=l.createContext(r),i=a.length;a=[...a,r];let o=t=>{let{scope:a,children:r,...o}=t,c=a?.[e]?.[i]||s,d=l.useMemo(()=>o,Object.values(o));return(0,n.jsx)(c.Provider,{value:d,children:r})};return o.displayName=t+"Provider",[o,function(a,n){let o=n?.[e]?.[i]||s,c=l.useContext(o);if(c)return c;if(void 0!==r)return r;throw Error(`\`${a}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let a=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=a.reduce((t,{useScope:a,scopeName:n})=>{let r=a(e)[`__scope${n}`];return{...t,...r}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return a.scopeName=t.scopeName,a}(r,...t)]}(ec,[Q.RG]),em=(0,Q.RG)(),[eh,ep]=ed(ec),ex=l.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,onValueChange:s,defaultValue:i,orientation:o="horizontal",dir:c,activationMode:d="automatic",...m}=e,h=function(e){let t=l.useContext(ei);return e||t||"ltr"}(c),[p,x]=(0,u.i)({prop:r,onChange:s,defaultProp:i??"",caller:ec});return(0,n.jsx)(eh,{scope:a,baseId:(0,eo.B)(),value:p,onValueChange:x,orientation:o,dir:h,activationMode:d,children:(0,n.jsx)(el.div,{dir:h,"data-orientation":o,...m,ref:t})})});ex.displayName=ec;var eg="TabsList",ef=l.forwardRef((e,t)=>{let{__scopeTabs:a,loop:r=!0,...s}=e,l=ep(eg,a),i=em(a);return(0,n.jsx)(Q.bL,{asChild:!0,...i,orientation:l.orientation,dir:l.dir,loop:r,children:(0,n.jsx)(el.div,{role:"tablist","aria-orientation":l.orientation,...s,ref:t})})});ef.displayName=eg;var ey="TabsTrigger",ev=l.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,disabled:s=!1,...l}=e,i=ep(ey,a),o=em(a),c=eN(i.baseId,r),d=ew(i.baseId,r),u=r===i.value;return(0,n.jsx)(Q.q7,{asChild:!0,...o,focusable:!s,active:u,children:(0,n.jsx)(el.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":d,"data-state":u?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:c,...l,ref:t,onMouseDown:X(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(r)}),onKeyDown:X(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(r)}),onFocus:X(e.onFocus,()=>{let e="manual"!==i.activationMode;u||s||!e||i.onValueChange(r)})})})});ev.displayName=ey;var ej="TabsContent",eb=l.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,forceMount:s,children:i,...o}=e,c=ep(ej,a),d=eN(c.baseId,r),u=ew(c.baseId,r),m=r===c.value,h=l.useRef(m);return l.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,n.jsx)(ea,{present:s||m,children:({present:a})=>(0,n.jsx)(el.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!a,id:u,tabIndex:0,...o,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:a&&i})})});function eN(e,t){return`${e}-trigger-${t}`}function ew(e,t){return`${e}-content-${t}`}eb.displayName=ej;var eC=a(16189);function ek({className:e,useUrlParams:t=!1,paramName:a="tab",value:r,onValueChange:s,...i}){let o=(0,eC.useRouter)(),c=(0,eC.usePathname)(),d=(0,eC.useSearchParams)(),u=l.useCallback(e=>{if(s&&s(e),t){let t=new URLSearchParams(d.toString());t.set(a,e),o.push(`${c}?${t.toString()}`,{scroll:!1})}},[s,t,a,c,d,o]),m=t&&d.get(a)||void 0;return(0,n.jsx)(ex,{"data-slot":"tabs",className:(0,M.cn)("flex flex-col gap-2",e),value:m||r,onValueChange:u,...i})}function eS({className:e,...t}){return(0,n.jsx)(ex,{"data-slot":"tabs",className:(0,M.cn)("flex flex-col gap-2",e),...t})}function eM(e){return e.useUrlParams?(0,n.jsx)(ek,{...e}):(0,n.jsx)(eS,{...e})}function e$({className:e,...t}){return(0,n.jsx)(ef,{"data-slot":"tabs-list",className:(0,M.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function eD({className:e,...t}){return(0,n.jsx)(ev,{"data-slot":"tabs-trigger",className:(0,M.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function eA({className:e,...t}){return(0,n.jsx)(eb,{"data-slot":"tabs-content",className:(0,M.cn)("flex-1 outline-none",e),...t})}var eE=a(37337),eT=a(70891),eO=a(48754),eP=a(40988),eV=a(78272);function eI({recurrence:e,onUpdate:t}){let[a,r]=(0,l.useState)(!1),[s,i]=(0,l.useState)(!1);return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"recurrence",className:"text-sm font-medium",children:["Recurrence",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)(J.l6,{value:e.type,onValueChange:e=>t({type:e}),children:[(0,n.jsx)(J.bq,{children:(0,n.jsx)(J.yv,{})}),(0,n.jsxs)(J.gC,{children:[(0,n.jsx)(J.eb,{value:E.V5.Once,children:"Once"}),(0,n.jsx)(J.eb,{value:E.V5.Minutes,children:"Minutes"}),(0,n.jsx)(J.eb,{value:E.V5.Hourly,children:"Hourly"}),(0,n.jsx)(J.eb,{value:E.V5.Daily,children:"Daily"}),(0,n.jsx)(J.eb,{value:E.V5.Weekly,children:"Weekly"}),(0,n.jsx)(J.eb,{value:E.V5.Monthly,children:"Monthly"})]})]})]}),e.type===E.V5.Once&&(0,n.jsx)(eR,{recurrence:e,onUpdate:t,startDateOpen:a,setStartDateOpen:r}),e.type===E.V5.Daily&&(0,n.jsx)(eF,{recurrence:e,onUpdate:t,startDateOpen:a,setStartDateOpen:r,endDateOpen:s,setEndDateOpen:i}),e.type===E.V5.Weekly&&(0,n.jsx)(ez,{recurrence:e,onUpdate:t,updateDaySelection:(a,n)=>{let r=e.selectedDays??[];n?t({selectedDays:[...r,a]}):t({selectedDays:r.filter(e=>e!==a)})},startDateOpen:a,setStartDateOpen:r,endDateOpen:s,setEndDateOpen:i}),e.type===E.V5.Monthly&&(0,n.jsx)(e_,{recurrence:e,onUpdate:t,updateMonthSelection:(a,n)=>{let r=e.selectedMonths??[];n?t({selectedMonths:[...r,a]}):t({selectedMonths:r.filter(e=>e!==a)})},startDateOpen:a,setStartDateOpen:r,endDateOpen:s,setEndDateOpen:i}),(e.type===E.V5.Minutes||e.type===E.V5.Hourly)&&(0,n.jsx)(eH,{recurrence:e,onUpdate:t,startDateOpen:a,setStartDateOpen:r,endDateOpen:s,setEndDateOpen:i})]})}function eR({recurrence:e,onUpdate:t,startDateOpen:a,setStartDateOpen:r}){return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(eL,{label:"Start Date",required:!0,date:e.startDate,onDateChange:e=>t({startDate:e}),isOpen:a,setIsOpen:r}),(0,n.jsx)(eU,{label:"At:",hour:e.dailyHour??"09",minute:e.dailyMinute??"00",onHourChange:e=>t({dailyHour:e}),onMinuteChange:e=>t({dailyMinute:e})})]})}function eF({recurrence:e,onUpdate:t}){return(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsx)(eU,{label:"At:",hour:e.dailyHour??"09",minute:e.dailyMinute??"00",onHourChange:e=>t({dailyHour:e}),onMinuteChange:e=>t({dailyMinute:e})})})}function ez({recurrence:e,onUpdate:t,updateDaySelection:a}){let r=e.selectedDays??[];return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(eU,{label:"Every day at:",hour:e.weeklyHour??"17",minute:e.weeklyMinute??"00",onHourChange:e=>t({weeklyHour:e}),onMinuteChange:e=>t({weeklyMinute:e})}),(0,n.jsx)(eq,{days:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],selectedDays:r,onDayToggle:a})]})}function e_({recurrence:e,onUpdate:t,updateMonthSelection:a}){let r=e.selectedMonths??[];return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(eU,{label:"At:",hour:e.monthlyHour??"00",minute:e.monthlyMinute??"00",onHourChange:e=>t({monthlyHour:e}),onMinuteChange:e=>t({monthlyMinute:e})}),(0,n.jsx)(eG,{recurrence:e,onUpdate:t}),(0,n.jsx)(eW,{months:["January","February","March","April","May","June","July","August","September","October","November","December"],selectedMonths:r,onMonthToggle:a})]})}function eH({recurrence:e,onUpdate:t}){return(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm",children:"Every"}),(0,n.jsxs)(J.l6,{value:e.value,onValueChange:e=>t({value:e}),children:[(0,n.jsx)(J.bq,{className:"w-20",children:(0,n.jsx)(J.yv,{})}),(0,n.jsxs)(J.gC,{children:[(0,n.jsx)(J.eb,{value:"1",children:"1"}),(0,n.jsx)(J.eb,{value:"5",children:"5"}),(0,n.jsx)(J.eb,{value:"10",children:"10"}),(0,n.jsx)(J.eb,{value:"15",children:"15"}),(0,n.jsx)(J.eb,{value:"30",children:"30"})]})]}),(0,n.jsx)("span",{className:"text-sm",children:e.type===E.V5.Hourly?"Hours":"minute(s)"})]})})}function eL({label:e,required:t,date:a,onDateChange:r,isOpen:l,setIsOpen:i,minDate:o}){return(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:[e," ",t&&(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)(eP.AM,{open:l,onOpenChange:i,children:[(0,n.jsx)(eP.Wv,{asChild:!0,children:(0,n.jsxs)(s.$,{variant:"outline",className:"w-full justify-between font-normal",children:[a?a.toLocaleDateString():"Select date",(0,n.jsx)(eV.A,{className:"h-4 w-4"})]})}),(0,n.jsx)(eP.hl,{className:"w-auto overflow-hidden p-0",align:"start",children:(0,n.jsx)(eO.V,{mode:"single",selected:a,captionLayout:"dropdown",disabled:e=>{let t=new Date(new Date().setHours(0,0,0,0));return e<(o?new Date(o):t)},onSelect:e=>{r(e||void 0),i(!1)}})})]})]})}function eU({label:e,hour:t,minute:a,onHourChange:r,onMinuteChange:s}){return(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm",children:e}),(0,n.jsxs)(J.l6,{value:t,onValueChange:r,children:[(0,n.jsx)(J.bq,{className:"w-20",children:(0,n.jsx)(J.yv,{})}),(0,n.jsx)(J.gC,{children:Array.from({length:24},(e,t)=>(0,n.jsx)(J.eb,{value:t.toString().padStart(2,"0"),children:t.toString().padStart(2,"0")},t))})]}),(0,n.jsx)("span",{className:"text-sm",children:":"}),(0,n.jsxs)(J.l6,{value:a,onValueChange:s,children:[(0,n.jsx)(J.bq,{className:"w-20",children:(0,n.jsx)(J.yv,{})}),(0,n.jsx)(J.gC,{children:Array.from({length:60},(e,t)=>(0,n.jsx)(J.eb,{value:t.toString().padStart(2,"0"),children:t.toString().padStart(2,"0")},t))})]})]})}function eq({days:e,selectedDays:t,onDayToggle:a}){return(0,n.jsx)("div",{className:"space-y-2",children:(0,n.jsx)("div",{className:"grid grid-cols-4 gap-4",children:e.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(i.S,{id:e,checked:t.includes(e),onCheckedChange:t=>a(e,!!t)}),(0,n.jsx)("label",{htmlFor:e,className:"text-sm font-medium",children:e})]},e))})})}function eW({months:e,selectedMonths:t,onMonthToggle:a}){return(0,n.jsx)("div",{className:"space-y-2",children:(0,n.jsx)("div",{className:"grid grid-cols-3 gap-4",children:e.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(i.S,{id:e,checked:t.includes(e),onCheckedChange:t=>a(e,!!t)}),(0,n.jsx)("label",{htmlFor:e,className:"text-sm font-medium",children:e})]},e))})})}function eG({recurrence:e,onUpdate:t}){return(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"On:"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"radio",id:"day-option",name:"monthly-on",checked:"day"===e.monthlyOnType,onChange:()=>t({monthlyOnType:"day"})}),(0,n.jsx)("label",{htmlFor:"day-option",className:"text-sm font-medium",children:"Day"})]}),(0,n.jsxs)(J.l6,{value:e.selectedDay??"31",onValueChange:e=>t({selectedDay:e}),disabled:"day"!==e.monthlyOnType,children:[(0,n.jsx)(J.bq,{className:"w-40",children:(0,n.jsx)(J.yv,{placeholder:"Select day"})}),(0,n.jsx)(J.gC,{children:Array.from({length:31},(e,t)=>(0,n.jsx)(J.eb,{value:(t+1).toString(),children:t+1},t+1))})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"radio",id:"the-option",name:"monthly-on",checked:"the"===e.monthlyOnType,onChange:()=>t({monthlyOnType:"the"})}),(0,n.jsx)("label",{htmlFor:"the-option",className:"text-sm font-medium",children:"The"})]}),(0,n.jsxs)(J.l6,{value:e.selectedOrdinal??"2nd",onValueChange:e=>t({selectedOrdinal:e}),disabled:"the"!==e.monthlyOnType,children:[(0,n.jsx)(J.bq,{className:"w-20",children:(0,n.jsx)(J.yv,{})}),(0,n.jsxs)(J.gC,{children:[(0,n.jsx)(J.eb,{value:"1st",children:"1st"}),(0,n.jsx)(J.eb,{value:"2nd",children:"2nd"}),(0,n.jsx)(J.eb,{value:"3rd",children:"3rd"}),(0,n.jsx)(J.eb,{value:"4th",children:"4th"}),(0,n.jsx)(J.eb,{value:"5th",children:"5th"})]})]}),(0,n.jsxs)(J.l6,{value:e.selectedWeekday??"Wednesday",onValueChange:e=>t({selectedWeekday:e}),disabled:"the"!==e.monthlyOnType,children:[(0,n.jsx)(J.bq,{className:"w-32",children:(0,n.jsx)(J.yv,{})}),(0,n.jsxs)(J.gC,{children:[(0,n.jsx)(J.eb,{value:"Monday",children:"Monday"}),(0,n.jsx)(J.eb,{value:"Tuesday",children:"Tuesday"}),(0,n.jsx)(J.eb,{value:"Wednesday",children:"Wednesday"}),(0,n.jsx)(J.eb,{value:"Thursday",children:"Thursday"}),(0,n.jsx)(J.eb,{value:"Friday",children:"Friday"}),(0,n.jsx)(J.eb,{value:"Saturday",children:"Saturday"}),(0,n.jsx)(J.eb,{value:"Sunday",children:"Sunday"})]})]})]})]})]})}let eK=(0,P.A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),eZ=(0,P.A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),eB=(0,P.A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var eJ=a(99270),eX=a(39582);function eQ({selectedAgentId:e,onAgentSelect:t}){let[a,r]=(0,l.useState)(""),{data:s=[],error:i,isLoading:c}=(0,Z.Ay)(eT.DC.agents(),eX.NA),d=(0,l.useMemo)(()=>{let e=s.filter(e=>e.status&&"disconnected"!==e.status.toLowerCase()&&"offline"!==e.status.toLowerCase());return a?e.filter(e=>e.name.toLowerCase().includes(a.toLowerCase())||e.machineName?.toLowerCase().includes(a.toLowerCase())):e},[s,a]),u=e=>{switch(e.toLowerCase()){case"available":case"online":return{variant:"default",icon:eK,className:"bg-green-100 text-green-700"};case"busy":case"running":return{variant:"secondary",icon:eZ,className:"bg-yellow-100 text-yellow-700"};default:return{variant:"destructive",icon:eB,className:"bg-red-100 text-red-700"}}},m=a=>{t&&t(e===a?"":a)};return c?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:["Execution Agent",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading agents..."})]}),(0,n.jsx)("div",{className:"h-32 bg-muted/20 rounded-md animate-pulse"})]}):i?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:["Execution Agent",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"text-sm text-destructive",children:"Failed to load agents"})]}),(0,n.jsx)("div",{className:"p-3 bg-destructive/10 rounded-md border border-destructive/20",children:(0,n.jsx)("div",{className:"text-sm text-destructive",children:"Unable to connect to the server. Please try again."})})]}):(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:["Execution Agent",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Select an agent to execute this schedule (only connected agents shown)"})]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(eJ.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,n.jsx)(B.p,{placeholder:"Search agents by name or machine...",value:a,onChange:e=>r(e.target.value),className:"pl-10"})]}),(0,n.jsxs)("div",{className:"border rounded-md",children:[(0,n.jsxs)("div",{className:"bg-muted/50 grid grid-cols-12 py-3 px-4 text-sm font-medium border-b",children:[(0,n.jsx)("div",{className:"col-span-1",children:"Select"}),(0,n.jsx)("div",{className:"col-span-4",children:"Agent Name"}),(0,n.jsx)("div",{className:"col-span-4",children:"Machine Name"}),(0,n.jsx)("div",{className:"col-span-3",children:"Status"})]}),(0,n.jsx)("div",{className:"max-h-64 overflow-y-auto",children:0===d.length?(0,n.jsxs)("div",{className:"py-8 text-center text-sm text-muted-foreground",children:[a?"No available agents found matching your search.":"No available agents found.",s.length>0&&(0,n.jsx)("div",{className:"text-xs mt-1",children:"Only connected agents are shown for selection."})]}):d.map(t=>{let a=u(t.status),r=a.icon,s=e===t.id;return(0,n.jsxs)("div",{className:`grid grid-cols-12 py-3 px-4 border-b last:border-b-0 hover:bg-muted/30 transition-colors cursor-pointer ${s?"bg-primary/10 border-primary/20":""}`,onClick:()=>m(t.id),children:[(0,n.jsx)("div",{className:"col-span-1 flex items-center",children:(0,n.jsx)("div",{className:`w-4 h-4 rounded-full border-2 flex items-center justify-center ${s?"border-primary bg-primary":"border-muted-foreground"}`,children:s&&(0,n.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})}),(0,n.jsx)("div",{className:"col-span-4 flex items-center",children:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:t.name}),t.description&&(0,n.jsx)("div",{className:"text-xs text-muted-foreground",children:t.description})]})}),(0,n.jsx)("div",{className:"col-span-4 flex items-center",children:(0,n.jsx)("span",{className:"text-sm",children:t.machineName||"N/A"})}),(0,n.jsx)("div",{className:"col-span-3 flex items-center",children:(0,n.jsxs)(o.E,{variant:a.variant,className:a.className,children:[(0,n.jsx)(r,{className:"w-3 h-3 mr-1"}),t.status]})})]},t.id)})})]}),e&&(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Selected Agent"}),(0,n.jsx)("div",{className:"p-3 bg-primary/5 rounded-md border border-primary/20",children:(()=>{let t=s.find(t=>t.id===e);if(!t)return(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Agent not found"});let a=u(t.status),r=a.icon;return(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:t.name}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:t.machineName})]}),(0,n.jsxs)(o.E,{variant:a.variant,className:a.className,children:[(0,n.jsx)(r,{className:"w-3 h-3 mr-1"}),t.status]})]})})()})]}),(0,n.jsx)("div",{className:"text-xs text-muted-foreground",children:"Only connected agents are available for selection. Click an agent to select it for schedule execution."})]})}function eY({isOpen:e,onClose:t,mode:a,editingSchedule:r,onSuccess:i}){let{toast:o}=(0,H.d)(),[c,d]=(0,l.useState)(!1),[u,m]=(0,l.useState)("trigger"),[h,p]=(0,l.useState)({name:r?.name??"",packageId:r?.packageId??"",packageVersion:r?.packageVersion??"latest",agentId:r?.agentId??"",timezone:r?.timezone??"Asia/Ho_Chi_Minh",recurrence:{type:r?.recurrence?.type??E.V5.Daily,value:r?.recurrence?.value??"1",startTime:r?.recurrence?.startTime??"09:00",dailyHour:r?.recurrence?.dailyHour??"09",dailyMinute:r?.recurrence?.dailyMinute??"00",weeklyHour:r?.recurrence?.weeklyHour??"09",weeklyMinute:r?.recurrence?.weeklyMinute??"00",selectedDays:r?.recurrence?.selectedDays??["Monday","Tuesday","Wednesday","Thursday","Friday"],monthlyHour:r?.recurrence?.monthlyHour??"09",monthlyMinute:r?.recurrence?.monthlyMinute??"00",monthlyOnType:r?.recurrence?.monthlyOnType??"day",selectedDay:r?.recurrence?.selectedDay??"1",selectedOrdinal:r?.recurrence?.selectedOrdinal??"1st",selectedWeekday:r?.recurrence?.selectedWeekday??"Monday",selectedMonths:r?.recurrence?.selectedMonths??["January","February","March","April","May","June","July","August","September","October","November","December"]}}),x=e=>{p(t=>({...t,...e}))},g=()=>{if(!h.name.trim())return{isValid:!1,error:"Schedule name is required"};if(!h.packageId)return{isValid:!1,error:"Package selection is required"};if(!h.agentId)return{isValid:!1,error:"Agent selection is required"};if(!h.timezone)return{isValid:!1,error:"Timezone selection is required"};if(h.recurrence.type===E.V5.Once){if(!h.recurrence.startDate)return{isValid:!1,error:"Please select a date for one-time schedule"};if(!h.recurrence.dailyHour||!h.recurrence.dailyMinute)return{isValid:!1,error:"Please select a time for one-time schedule"}}return{isValid:!0}},f=()=>{let e,t;let{recurrence:a}=h;switch(a.type){case E.V5.Once:if(a.startDate&&a.dailyHour&&a.dailyMinute){let e=new Date(a.startDate);e.setHours(parseInt(a.dailyHour,10),parseInt(a.dailyMinute,10),0,0),t=e.toISOString()}else throw Error("Please select both date and time for one-time schedule");break;case E.V5.Daily:e=`0 ${a.dailyMinute} ${a.dailyHour} * * *`;break;case E.V5.Weekly:if(a.selectedDays&&a.selectedDays.length>0){let t={Sunday:"0",Monday:"1",Tuesday:"2",Wednesday:"3",Thursday:"4",Friday:"5",Saturday:"6"},n=a.selectedDays.map(e=>t[e]).join(",");e=`0 ${a.weeklyMinute} ${a.weeklyHour} * * ${n}`}break;case E.V5.Monthly:"day"===a.monthlyOnType&&a.selectedDay&&(e=`0 ${a.monthlyMinute} ${a.monthlyHour} ${a.selectedDay} * *`);break;case E.V5.Hourly:e=`0 0 */${a.value} * * *`;break;case E.V5.Minutes:e=`0 */${a.value} * * * *`}return{name:h.name.trim(),description:"",isEnabled:!0,recurrenceType:a.type,cronExpression:e,oneTimeExecution:t,timeZoneId:h.timezone,automationPackageId:h.packageId,botAgentId:h.agentId}},y=async()=>{let e=g();if(!e.isValid){o({title:"Validation Error",description:e.error,variant:"destructive"});return}d(!0);try{let e=f();if("edit"===a&&r?.id){let t=await (0,E.Fs)(r.id,e);o({title:"Success",description:`Schedule "${t.name}" updated successfully`}),i&&i({id:t.id,name:t.name})}else{let t=await (0,E.sF)(e);o({title:"Success",description:`Schedule "${t.name}" created successfully`}),i&&i({id:t.id,name:t.name})}t(!0)}catch(e){console.error(`${a} schedule failed:`,e),o((0,L.m4)(e))}finally{d(!1)}},v=()=>{t()};return(0,n.jsx)(_.lG,{open:e,onOpenChange:v,children:(0,n.jsxs)(_.Cf,{className:"sm:max-w-[800px]",children:[(0,n.jsx)(_.c7,{children:(0,n.jsx)(_.L3,{children:"edit"===a?"Edit Schedule":"Create Schedule"})}),(0,n.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,n.jsx)(e0,{formData:h,onUpdate:x}),(0,n.jsxs)(eM,{value:u,onValueChange:m,className:"w-full",children:[(0,n.jsxs)(e$,{className:"grid grid-cols-2 mb-4",children:[(0,n.jsx)(eD,{value:"trigger",children:"Trigger"}),(0,n.jsx)(eD,{value:"executionTarget",children:"Execution Target"})]}),(0,n.jsx)(eA,{value:"trigger",children:(0,n.jsx)(eI,{recurrence:h.recurrence,onUpdate:e=>{p(t=>({...t,recurrence:{...t.recurrence,...e}}))}})}),(0,n.jsx)(eA,{value:"executionTarget",children:(0,n.jsx)(eQ,{selectedAgentId:h.agentId,onAgentSelect:e=>x({agentId:e})})})]})]}),(0,n.jsxs)(_.Es,{children:[(0,n.jsx)(s.$,{variant:"outline",onClick:v,disabled:c,children:"Cancel"}),(0,n.jsx)(s.$,{onClick:y,disabled:c,children:c?"Saving...":"edit"===a?"Update":"Create"})]})]})})}function e0({formData:e,onUpdate:t}){let{data:a=[],error:r,isLoading:s}=(0,Z.Ay)(eT.DC.packages(),eE.s9),i=(0,l.useMemo)(()=>{if(!e.packageId)return[{value:"latest",label:"Latest"}];let t=a.find(t=>t.id===e.packageId);return t?.versions?[{value:"latest",label:"Latest"},...t.versions.map(e=>({value:e.versionNumber,label:`v${e.versionNumber}`}))]:[{value:"latest",label:"Latest"}]},[a,e.packageId]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"name",className:"text-sm font-medium",children:["Name",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)(B.p,{id:"name",value:e.name,onChange:e=>t({name:e.target.value}),placeholder:"Enter schedule name"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"package",className:"text-sm font-medium",children:["Package",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),s?(0,n.jsx)("div",{className:"h-10 bg-muted rounded-md animate-pulse"}):r?(0,n.jsx)("div",{className:"text-sm text-destructive",children:"Failed to load packages"}):(0,n.jsxs)(J.l6,{value:e.packageId,onValueChange:e=>t({packageId:e,packageVersion:"latest"}),children:[(0,n.jsx)(J.bq,{children:(0,n.jsx)(J.yv,{placeholder:"Choose package"})}),(0,n.jsx)(J.gC,{children:a.map(e=>(0,n.jsx)(J.eb,{value:e.id,children:e.name},e.id))})]})]}),(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"packageVersion",className:"text-sm font-medium",children:["Package Version",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)(J.l6,{value:e.packageVersion,onValueChange:e=>t({packageVersion:e}),disabled:!e.packageId,children:[(0,n.jsx)(J.bq,{children:(0,n.jsx)(J.yv,{placeholder:"Select version"})}),(0,n.jsx)(J.gC,{children:i.map(e=>(0,n.jsx)(J.eb,{value:e.value,children:e.label},e.value))})]})]})]}),(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"timezone",className:"text-sm font-medium",children:["Time Zone",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)(J.l6,{value:e.timezone,onValueChange:e=>t({timezone:e}),children:[(0,n.jsx)(J.bq,{children:(0,n.jsx)(J.yv,{placeholder:"Select time zone"})}),(0,n.jsxs)(J.gC,{children:[(0,n.jsx)(J.eb,{value:"Asia/Ho_Chi_Minh",children:"(UTC+7:00) Asia/Ho Chi Minh"}),(0,n.jsx)(J.eb,{value:"America/New_York",children:"(UTC-5:00) America/New York"}),(0,n.jsx)(J.eb,{value:"Europe/London",children:"(UTC+0:00) Europe/London"}),(0,n.jsx)(J.eb,{value:"Asia/Tokyo",children:"(UTC+9:00) Asia/Tokyo"}),(0,n.jsx)(J.eb,{value:"Australia/Sydney",children:"(UTC+10:00) Australia/Sydney"}),(0,n.jsx)(J.eb,{value:"UTC",children:"(UTC+0:00) UTC"})]})]})]})]})}var e1=a(53984);function e2({table:e,statuses:t,recurrenceTypes:a,onSearch:r,onStatusChange:l,onRecurrenceTypeChange:i,searchValue:o="",isFiltering:c=!1,isPending:d=!1,searchPlaceholder:u="Search schedules...",totalCount:m=0}){let h=e.getState().columnFilters.length>0;return(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,n.jsxs)("div",{className:"relative flex items-center",children:[(0,n.jsx)(eJ.A,{className:"absolute left-3 h-4 w-4 text-muted-foreground"}),(0,n.jsx)(B.p,{placeholder:u,value:o,onChange:e=>{let t=e.target.value;r&&r(t)},className:"h-8 w-[200px] pl-10 lg:w-[300px]",disabled:c}),d&&(0,n.jsx)(D.A,{className:"absolute right-3 h-4 w-4 animate-spin text-muted-foreground"}),o&&!d&&(0,n.jsxs)(s.$,{variant:"ghost",onClick:()=>{r&&r("")},className:"absolute right-1 h-6 w-6 p-0 hover:bg-transparent",children:[(0,n.jsx)(F.A,{className:"h-3 w-3"}),(0,n.jsx)("span",{className:"sr-only",children:"Clear search"})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Status:"}),(0,n.jsxs)(J.l6,{onValueChange:e=>{l&&l(e)},defaultValue:"all",children:[(0,n.jsx)(J.bq,{className:"h-8 w-[120px]",children:(0,n.jsx)(J.yv,{placeholder:"All"})}),(0,n.jsxs)(J.gC,{children:[(0,n.jsx)(J.eb,{value:"all",children:"All"}),t.map(e=>(0,n.jsx)(J.eb,{value:e.value,children:e.label},e.value))]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Type:"}),(0,n.jsxs)(J.l6,{onValueChange:e=>{i&&i(e)},defaultValue:"all",children:[(0,n.jsx)(J.bq,{className:"h-8 w-[140px]",children:(0,n.jsx)(J.yv,{placeholder:"All Types"})}),(0,n.jsxs)(J.gC,{children:[(0,n.jsx)(J.eb,{value:"all",children:"All Types"}),a.map(e=>(0,n.jsx)(J.eb,{value:e.value,children:e.label},e.value))]})]})]}),h&&(0,n.jsxs)(s.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),r&&r(""),l&&l("all"),i&&i("all")},className:"h-8 px-2 lg:px-3",children:["Reset",(0,n.jsx)(F.A,{className:"ml-2 h-4 w-4"})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[m>0&&(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,n.jsxs)("span",{children:[m," schedule",1!==m?"s":""]})}),(0,n.jsx)(e1.i,{table:e})]})]})}var e5=a(42300),e4=a(14583),e3=a(56090),e6=a(93772);function e8(){let e=(0,eC.useRouter)(),t=(0,eC.usePathname)(),a=(0,eC.useSearchParams)(),{updateUrl:i}=(0,e5.z)(),{toast:o}=(0,H.d)(),[c,d]=(0,l.useState)(!1),[u,m]=(0,l.useState)(null),[h,p]=(0,l.useState)({}),[x,g]=(0,l.useState)({}),[f,y]=(0,l.useState)(0),v=(0,l.useRef)(0),[j,b]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),[C,k]=(0,l.useState)(!1),S=(0,l.useRef)(null);(0,l.useRef)(!0);let[M,$]=(0,l.useState)(()=>{let e=[],t=a.get("search");t&&e.push({id:"name",value:t});let n=a.get("status");n&&e.push({id:"isEnabled",value:n});let r=a.get("type");return r&&e.push({id:"recurrenceType",value:r}),e}),[D,A]=(0,l.useState)(()=>{let e=a.get("sort"),t=a.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[T,O]=(0,l.useState)(()=>{let e=a.get("page"),t=a.get("size"),n=t?Math.max(1,parseInt(t)):10;return console.log(`Initializing pagination from URL: page=${e}, size=${t}, pageSize=${n}`),{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:n}}),[P,V]=(0,l.useState)(a.get("search")??""),I=t.split("/")[1],R=(0,l.useCallback)(()=>{let e={$top:T.pageSize,$skip:T.pageIndex*T.pageSize,$count:!0};if(D.length>0&&(e.$orderby=D.map(e=>`${e.id} ${e.desc?"desc":"asc"}`).join(",")),M.length>0){let t=M.filter(e=>"string"!=typeof e.value||""!==e.value).map(e=>{let t=e.id,a=e.value;return"string"==typeof a?"name"===t&&a?`contains(tolower(name), '${a.toLowerCase()}')`:"isEnabled"===t&&a?`isEnabled eq ${"enabled"===a?"true":"false"}`:"recurrenceType"===t&&a?`recurrenceType eq '${a}'`:`contains(tolower(${t}), '${a.toLowerCase()}')`:Array.isArray(a)?a.map(e=>`${t} eq '${e}'`).join(" or "):""}).filter(Boolean);t.length>0&&(e.$filter=t.join(" and "))}return e},[T,D,M])(),F=(0,l.useMemo)(()=>eT.DC.schedulesWithOData(R),[R]),{data:z,error:_,isLoading:U,mutate:q}=(0,Z.Ay)(F,()=>(0,E.ye)(R),{dedupingInterval:0,revalidateOnFocus:!1,revalidateIfStale:!0,keepPreviousData:!1}),{data:W,mutate:B}=(0,Z.Ay)(z?.value?.length===0||_?eT.DC.schedules():null,E.Os),J=U||_&&!W,X=(0,l.useMemo)(()=>{if(W&&(!z?.value||0===z.value.length)){console.log("Using fallback data with pagination:",T);let e=W;P&&(e=e.filter(e=>e.name&&e.name.toLowerCase().includes(P.toLowerCase())));let t=M.find(e=>"isEnabled"===e.id)?.value;t&&"all"!==t&&(e=e.filter(e=>"enabled"===t?e.isEnabled:"disabled"!==t||!e.isEnabled));let a=M.find(e=>"recurrenceType"===e.id)?.value;a&&"all"!==a&&(e=e.filter(e=>e.recurrenceType===a));let n=e.length,r=T.pageIndex*T.pageSize,s=r+T.pageSize;console.log(`Slicing fallback data from ${r} to ${s} out of ${e.length} items`);let l=e.slice(r,s);return console.log(`Returning ${l.length} items from fallback data`),f!==n&&setTimeout(()=>{y(n),v.current=n},0),l}return z?.value?(console.log(`Returning ${z.value.length} items from OData response`),z.value):(console.log("No data available from OData or fallback"),[])},[z,W,P,M,T,f]),Q=(0,l.useCallback)(e=>{let t=T.pageIndex*T.pageSize+e.length;t>v.current&&(y(t),v.current=t),e.length===T.pageSize&&0===T.pageIndex&&(y(t+1),v.current=t+1),k(!1)},[T.pageIndex,T.pageSize]);(0,l.useCallback)(e=>{if(console.log("OData response received:",e),"number"==typeof e["@odata.count"]){y(e["@odata.count"]),v.current=e["@odata.count"],k(!0);return}Array.isArray(e.value)&&Q(e.value)},[Q]),(0,l.useCallback)(()=>{if(!W||0===W.length)return;console.log("Using fallback data count:",W.length);let e=W.length;P&&(e=W.filter(e=>e.name&&e.name.toLowerCase().includes(P.toLowerCase())).length);let t=M.find(e=>"isEnabled"===e.id)?.value;t&&"all"!==t&&(e=W.filter(e=>"enabled"===t?e.isEnabled:"disabled"!==t||!e.isEnabled).length),console.log("Filtered fallback count:",e),y(e),v.current=e,k(!0)},[W,P,M]);let Y=e=>e+1,ee=(e,t)=>Math.max(1,Math.ceil(e/t)),et=(0,l.useMemo)(()=>{let e=ee(f,T.pageSize),t=X.length===T.pageSize&&f<=T.pageSize*(T.pageIndex+1),a=Y(T.pageIndex);return t?Math.max(a,e,T.pageIndex+2):Math.max(a,e)},[T.pageSize,T.pageIndex,X.length,f]),ea=(0,l.useMemo)(()=>!C&&X.length===T.pageSize,[C,X.length,T.pageSize]),en=(0,l.useMemo)(()=>G({onDeleted:()=>{q(),B()},onToggleEnabled:async e=>{try{let t=e.isEnabled?await (0,E.g8)(e.id):await (0,E.H4)(e.id);o({title:`Schedule ${t.isEnabled?"Enabled":"Disabled"}`,description:`"${e.name}" is now ${t.isEnabled?"active":"inactive"}.`,duration:3e3}),console.log("Refreshing both data sources after toggle..."),await Promise.all([q(),B()]),console.log("Data refresh completed")}catch(e){throw console.error("Toggle enable failed:",e),o((0,L.m4)(e)),e}},onEdit:async e=>{m(function(e){let t=e.recurrenceType,a={type:t};e.cronExpression&&(t===E.V5.Daily?a={...a,...function(e){let t=e.split(" "),a=t[1];return{dailyHour:t[2],dailyMinute:a}}(e.cronExpression)}:t===E.V5.Weekly?a={...a,...function(e){let t=e.split(" "),a=t[1];return{weeklyHour:t[2],weeklyMinute:a,selectedDays:t[5]?.split(",").map(e=>({0:"Sunday",1:"Monday",2:"Tuesday",3:"Wednesday",4:"Thursday",5:"Friday",6:"Saturday"})[e]).filter(Boolean)}}(e.cronExpression)}:t===E.V5.Monthly?a={...a,...function(e){let t=e.split(" "),a=t[1];return{monthlyHour:t[2],monthlyMinute:a,monthlyOnType:"day",selectedDay:t[3]}}(e.cronExpression)}:t===E.V5.Hourly?a={...a,...function(e){let t=e.split(" ");return{value:t[2].startsWith("*/")?t[2].replace("*/",""):"1"}}(e.cronExpression)}:t===E.V5.Minutes&&(a={...a,...function(e){let t=e.split(" ");return{value:t[1].startsWith("*/")?t[1].replace("*/",""):"1"}}(e.cronExpression)}));let n={id:e.id,name:e.name,packageId:e.automationPackageId,packageVersion:"latest",agentId:e.botAgentId,timezone:e.timeZoneId,recurrence:a};return t===E.V5.Once&&e.oneTimeExecution&&(n.oneTimeExecution=e.oneTimeExecution),n}(e)),d(!0)}}),[q,B,o]),er=(0,e3.N4)({data:X,columns:en,state:{sorting:D,columnVisibility:x,rowSelection:h,columnFilters:M,pagination:T},enableRowSelection:!0,onRowSelectionChange:p,onSortingChange:e=>{let a="function"==typeof e?e(D):e;A(a),a.length>0?i(t,{sort:a[0].id,order:a[0].desc?"desc":"asc",page:"1"}):i(t,{sort:null,order:null,page:"1"}),q()},onColumnFiltersChange:$,onColumnVisibilityChange:g,onPaginationChange:e=>{console.log("Pagination change triggered");let a="function"==typeof e?e(T):e;console.log("Current pagination:",T,"New pagination:",a),O(a),i(t,{page:(a.pageIndex+1).toString(),size:a.pageSize.toString()}),console.log("Forcing data reload for pagination change"),q()},getCoreRowModel:(0,e6.HT)(),getFilteredRowModel:(0,e6.hM)(),getPaginationRowModel:(0,e6.kW)(),getSortedRowModel:(0,e6.h5)(),getFacetedRowModel:(0,e6.kQ)(),getFacetedUniqueValues:(0,e6.oS)(),manualPagination:!0,pageCount:et,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.id}),es=(0,l.useCallback)(e=>{V(e),b(!0),S.current&&clearTimeout(S.current),S.current=setTimeout(()=>{let a=er.getColumn("name");a&&(a.setFilterValue(e),i(t,{search:e||null,page:"1"}),q()),b(!1)},500)},[er,i,t,q]),el=(0,l.useCallback)(e=>{let a=er.getColumn("isEnabled");if(a){let n="all"===e?"":e;a.setFilterValue(n),i(t,{status:n||null,page:"1"}),q()}},[er,i,t,q]),ei=(0,l.useCallback)(e=>{let a=er.getColumn("recurrenceType");if(a){let n="all"===e?"":e;a.setFilterValue(n),i(t,{type:n||null,page:"1"}),q()}},[er,i,t,q]),eo=(0,l.useCallback)(e=>{e&&q(t=>{if(!t)return t;if("value"in t&&Array.isArray(t.value)){let a={id:e.id,name:e.name,description:"",isEnabled:!0,recurrenceType:E.V5.Daily,timeZoneId:"UTC",automationPackageId:"",botAgentId:"",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return{...t,value:[a,...t.value],"@odata.count":(t["@odata.count"]||0)+1}}return t},!1),setTimeout(()=>{q()},2e3)},[q]),ec=Object.values(E.V5).map(e=>({value:e,label:(0,E.MK)(e)}));return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Schedules"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[f>0&&(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,n.jsxs)("span",{children:["Total: ",f," schedule",1!==f?"s":""]})}),(0,n.jsxs)(s.$,{onClick:()=>{m(null),d(!0)},className:"flex items-center justify-center",children:[(0,n.jsx)(r.A,{className:"mr-2 h-4 w-4"}),"Create Schedule"]})]})]}),_&&!W&&(0,n.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,n.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load schedules. Please try again."}),(0,n.jsx)(s.$,{variant:"outline",className:"mt-2",onClick:()=>q(),children:"Retry"})]}),(0,n.jsx)(e2,{table:er,statuses:[{value:"enabled",label:"Enabled"},{value:"disabled",label:"Disabled"}],recurrenceTypes:ec,onSearch:es,onStatusChange:el,onRecurrenceTypeChange:ei,searchValue:P,isFiltering:J,isPending:j,searchPlaceholder:"Search schedules by name...",totalCount:f}),(0,n.jsx)(K.b,{data:X,columns:en,onRowClick:a=>{let n=t.startsWith("/admin")?`/admin/schedules/${a.id}`:`/${I}/automation/schedule/${a.id}`;e.push(n)},table:er,isLoading:J,totalCount:f}),(0,n.jsx)(e4.d,{currentPage:T.pageIndex+1,pageSize:T.pageSize,totalCount:f,totalPages:et,isLoading:J,isChangingPageSize:N,isUnknownTotalCount:ea,onPageChange:e=>{console.log(`Page change requested to page ${e}`),O(t=>({...t,pageIndex:e-1})),i(t,{page:e.toString()}),console.log("Reloading data after page change"),setTimeout(()=>{q()},0)},onPageSizeChange:e=>{console.log(`Page size change requested to ${e}`),w(!0);let a=Math.floor(T.pageIndex*T.pageSize/e);O({pageSize:e,pageIndex:a}),i(t,{size:e.toString(),page:(a+1).toString()}),console.log("Reloading data after page size change"),setTimeout(()=>{q()},0)}})]}),(0,n.jsx)(eY,{isOpen:c,onClose:e=>{d(!1),m(null),e&&q()},mode:u?"edit":"create",editingSchedule:u,onSuccess:eo},u?.id??"new")]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},58036:(e,t,a)=>{"use strict";a.d(t,{Fs:()=>c,H4:()=>u,MK:()=>p,Os:()=>i,V5:()=>r,VD:()=>d,g8:()=>m,po:()=>h,sF:()=>l,ye:()=>o});var n=a(51787),r=function(e){return e.Once="Once",e.Minutes="Minutes",e.Hourly="Hourly",e.Daily="Daily",e.Weekly="Weekly",e.Monthly="Monthly",e.Advanced="Advanced",e}({});let s=()=>"default",l=async e=>{let t=s();return await n.F.post(`${t}/api/schedules`,e)},i=async()=>{let e=s();try{return await n.F.get(`${e}/api/schedules`)}catch(e){return console.error("Error fetching all schedules:",e),[]}},o=async e=>{let t=s(),a={...e};(void 0===a.$top||a.$top<=0)&&(a.$top=10);let r=new Date().getTime(),l=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(a),i=`${t}/odata/Schedules`;l?i+=`?${l}&_t=${r}`:i+=`?_t=${r}`,console.log(`Fetching schedules with endpoint: ${i}`),console.log(`Page: ${a.$skip?a.$skip/a.$top+1:1}, Size: ${a.$top}`);try{let e=await n.F.get(i),t=function(e){return e&&"object"==typeof e?{value:Array.isArray(e.value)?e.value:[],"@odata.count":"number"==typeof e["@odata.count"]?e["@odata.count"]:void 0,"@odata.nextLink":"string"==typeof e["@odata.nextLink"]?e["@odata.nextLink"]:void 0}:{value:[]}}(e);return a.$top&&t.value.length>a.$top&&(console.warn(`OData returned ${t.value.length} items but only ${a.$top} were requested. Trimming results.`),t.value=t.value.slice(0,a.$top)),console.log(`Received ${t.value.length} schedules from OData`),t}catch(e){return console.error("Error fetching schedules with OData:",e),{value:[]}}},c=async(e,t)=>{let a=s();return await n.F.put(`${a}/api/schedules/${e}`,t)},d=async e=>{let t=s();await n.F.delete(`${t}/api/schedules/${e}`)},u=async e=>{let t=s();return await n.F.post(`${t}/api/schedules/${e}/enable`)},m=async e=>{let t=s();return await n.F.post(`${t}/api/schedules/${e}/disable`)},h=e=>{if(!e)return"Not scheduled";try{let t=new Date(e);if(isNaN(t.getTime()))return"Invalid date";return new Intl.DateTimeFormat("en-US",{dateStyle:"medium",timeStyle:"short"}).format(t)}catch{return"Invalid date"}},p=e=>{switch(e){case"Once":return"Once";case"Minutes":return"Every few minutes";case"Hourly":return"Hourly";case"Daily":return"Daily";case"Weekly":return"Weekly";case"Monthly":return"Monthly";case"Advanced":return"Custom (Cron)";default:return"Unknown"}}},58131:(e,t,a)=>{Promise.resolve().then(a.bind(a,74601))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},74601:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});let n=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\automation\\\\schedule\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\automation\\schedule\\page.tsx","default")},94565:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l,metadata:()=>s});var n=a(37413),r=a(74601);let s={title:"Automation",description:"Agent management page"};function l(){return(0,n.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,n.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,n.jsx)(r.default,{})]})}},95083:(e,t,a)=>{Promise.resolve().then(a.bind(a,16162))}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[4447,7966,5584,5156,4654,6467,1694,6945,8759,3437,4503,6763,519,4881,6295],()=>a(15581));module.exports=n})();