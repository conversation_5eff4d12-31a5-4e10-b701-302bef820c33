(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[672],{7283:(e,t,a)=>{"use strict";a.d(t,{F:()=>y,fetchApi:()=>j});var r=a(48133),s=a(67938);let l=s.$.api.defaultHeaders,n=!1,o=[],i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;o.forEach(a=>{e?a.reject(e):a.resolve(t)}),o=[]},c=async e=>{let t={message:e.statusText,status:e.status};try{let a=await e.json();a.message?(t.message=a.message,t.details=a.details||a.message):a.error?(t.message=a.error,t.details=a.error):t.details=JSON.stringify(a)}catch(a){t.details=e.statusText}return t},d=()=>{window.dispatchEvent(new Event("auth:token-expired"))},u=e=>{if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw{message:"Network error. Please check your connection.",status:0,details:e.message};if(e instanceof Error)throw{message:e.message||"Network error occurred",status:0,details:e.stack||e.message};throw{message:"An unexpected error occurred",status:0,details:String(e)}},m=async e=>{if(204===e.status)return{};let t=e.headers.get("content-type"),a=e.headers.get("content-length");if(!t||-1===t.indexOf("application/json")||"0"===a)return{};let r=await e.text();return r?JSON.parse(r):{}},h=async()=>{if(n)return new Promise((e,t)=>{o.push({resolve:e,reject:t})});n=!0;try{let e=(await j("api/auth/refresh-token",{method:"POST",credentials:"include"})).token;return(0,r.O5)(e),i(null,e),e}catch(e){throw i(e),e}finally{n=!1}},g=async(e,t,a,r,s)=>{if(e.includes("refresh-token")||e.includes("login"))return d(),null;try{let e=await h();if(!e)return null;let r=p(a,s);r.Authorization="Bearer ".concat(e);let{body:l}=f(s),n=await fetch(t,{...a,body:l,headers:r,credentials:"include"});if(n.ok)return m(n);return null}catch(e){return d(),console.error("Token refresh failed:",e),null}},x=e=>{if(e.startsWith("http"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat(s.$.api.baseUrl,"/").concat(t)},f=e=>e?e instanceof FormData?{body:e,headers:{}}:{body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}:{body:void 0,headers:{}},p=(e,t)=>{let a={...t instanceof FormData?{Accept:l.Accept}:{...l},...e.headers};if(!a.Authorization){let e=(0,r.c4)();e&&(a.Authorization="Bearer ".concat(e))}return a};async function j(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0,r=x(e),{body:s,headers:l}=f(a),n={...p(t,a),...l};try{let l=await fetch(r,{...t,body:s,headers:n,credentials:"include"});if(l.ok)return m(l);if(401===l.status){let s=await g(e,r,t,n,a);if(s)return s}throw await c(l)}catch(e){if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;throw u(e)}}let y={get:(e,t)=>j(e,{...t,method:"GET"}),post:(e,t,a)=>{let{body:r,headers:s}=f(t);return j(e,{...a,method:"POST",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},put:(e,t,a)=>{let{body:r,headers:s}=f(t);return j(e,{...a,method:"PUT",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},patch:(e,t,a)=>{let{body:r,headers:s}=f(t);return j(e,{...a,method:"PATCH",body:r,headers:{...s,...null==a?void 0:a.headers}},t)},delete:(e,t)=>j(e,{...t,method:"DELETE"})}},9224:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var r=a(95155),s=a(30285),l=a(66695),n=a(12115),o=a(34953),i=a(45995),c=a(54333),d=a(47262),u=a(87570);let m=[{id:"select",header:e=>{let{table:t}=e;return(0,r.jsx)(d.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all"})},cell:e=>{let{row:t}=e;return(0,r.jsx)(d.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row"})},enableSorting:!1,enableHiding:!1},{accessorKey:"firstName",header:e=>{let{column:t}=e;return(0,r.jsx)(u.w,{column:t,title:"First Name"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("div",{className:"font-medium",children:t.getValue("firstName")})}},{accessorKey:"lastName",header:e=>{let{column:t}=e;return(0,r.jsx)(u.w,{column:t,title:"Last Name"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("div",{className:"font-medium",children:t.getValue("lastName")})}},{accessorKey:"email",header:e=>{let{column:t}=e;return(0,r.jsx)(u.w,{column:t,title:"Email"})},cell:e=>{let{row:t}=e;return(0,r.jsx)("div",{className:"text-muted-foreground",children:t.getValue("email")})}},{accessorKey:"systemRole",header:e=>{let{column:t}=e;return(0,r.jsx)(u.w,{column:t,title:"Role"})},cell:e=>{let{row:t}=e,a=t.getValue("systemRole");return(0,r.jsx)("div",{children:"Admin"===a||1===a?"Admin":"User"})}}];var h=a(47924),g=a(51154),x=a(66932),f=a(54416),p=a(62523),j=a(11832),y=a(59409),v=a(26126);function b(e){let{table:t,roles:a,onSearch:l,onRoleChange:o,searchValue:i="",isFiltering:c=!1,isPending:d=!1}=e,u=(0,n.useRef)(null),m=t.getState().columnFilters.length>0;return(0,n.useEffect)(()=>{u.current&&u.current.focus()},[]),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,r.jsxs)("div",{className:"relative flex-1 md:max-w-sm",children:[(0,r.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,r.jsx)(p.p,{ref:u,placeholder:"Search users...",value:i,onChange:e=>{let t=e.target.value;null==l||l(t)},className:"pl-9 h-9",disabled:c}),c&&(0,r.jsx)(g.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsxs)(y.l6,{onValueChange:e=>{"all"===e?null==o||o(""):null==o||o(e)},disabled:c,children:[(0,r.jsx)(y.bq,{className:"h-9 w-[120px]",children:(0,r.jsx)(y.yv,{placeholder:"Role"})}),(0,r.jsxs)(y.gC,{children:[(0,r.jsx)(y.eb,{value:"all",children:"All Roles"}),a.map(e=>(0,r.jsx)(y.eb,{value:e.value,children:e.label},e.value))]})]})]}),(m||i)&&(0,r.jsxs)(s.$,{variant:"ghost",onClick:()=>{t.resetColumnFilters(),null==l||l(""),null==o||o("")},className:"h-9 px-2 lg:px-3",disabled:c,children:["Reset",(0,r.jsx)(f.A,{className:"ml-2 h-4 w-4"})]}),d&&(0,r.jsxs)(v.E,{variant:"secondary",className:"ml-2",children:[(0,r.jsx)(g.A,{className:"mr-1 h-3 w-3 animate-spin"}),"Updating..."]})]}),(0,r.jsx)(j.i,{table:t})]})}var N=a(29797),w=a(59385),S=a(36268),A=a(11032);let C=e=>{let t=e.systemRole;return"Admin"===t||t===w.i.Admin};function k(){let[e,t]=(0,n.useState)(""),[a,d]=(0,n.useState)(""),[u,h]=(0,n.useState)({pageIndex:0,pageSize:10}),[g,x]=(0,n.useState)([]),[f,p]=(0,n.useState)([]),[j,y]=(0,n.useState)({}),[v,w]=(0,n.useState)({}),{data:k,error:P,isLoading:R,mutate:I}=(0,o.Ay)(["systemAdmin-all-users"],()=>i.i.getAllUsers()),E=(0,n.useMemo)(()=>{if(!k)return[];let t=k;return e&&(t=t.filter(t=>{var a,r,s;return(null===(a=t.firstName)||void 0===a?void 0:a.toLowerCase().includes(e.toLowerCase()))||(null===(r=t.lastName)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase()))||(null===(s=t.email)||void 0===s?void 0:s.toLowerCase().includes(e.toLowerCase()))})),a&&(t=t.filter(e=>{let t=C(e);return"Admin"===a?t:!t})),t},[k,e,a]),U=(0,S.N4)({data:E,columns:m,state:{sorting:g,columnVisibility:j,rowSelection:v,columnFilters:f,pagination:u},enableRowSelection:!0,onRowSelectionChange:w,onSortingChange:x,onColumnFiltersChange:p,onColumnVisibilityChange:y,onPaginationChange:h,getCoreRowModel:(0,A.HT)(),getFilteredRowModel:(0,A.hM)(),getPaginationRowModel:(0,A.kW)(),getSortedRowModel:(0,A.h5)(),getFacetedRowModel:(0,A.kQ)(),getFacetedUniqueValues:(0,A.oS)(),manualPagination:!1}),T=E.length,z=Math.max(1,Math.ceil(T/u.pageSize)),O=(0,n.useCallback)(e=>{t(e),h(e=>({...e,pageIndex:0}))},[]),F=(0,n.useCallback)(e=>{d(e),h(e=>({...e,pageIndex:0}))},[]);return(0,r.jsx)("div",{className:"h-full overflow-y-auto bg-background p-6",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h1",{className:"text-2xl md:text-3xl font-bold tracking-tight text-foreground",children:"User Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage system users and their permissions"})]}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(l.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(l.ZB,{className:"text-sm font-medium text-card-foreground",children:"Total Users"})}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:"2,847"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12.5% from last month"})]})]}),(0,r.jsxs)(l.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(l.ZB,{className:"text-sm font-medium text-card-foreground",children:"Active Users"})}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:"2,234"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+8.1% from last month"})]})]}),(0,r.jsxs)(l.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(l.ZB,{className:"text-sm font-medium text-card-foreground",children:"Pending Invites"})}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:"156"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+3.2% from last month"})]})]}),(0,r.jsxs)(l.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(l.ZB,{className:"text-sm font-medium text-card-foreground",children:"Inactive Users"})}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:"457"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"-2.1% from last month"})]})]})]}),P&&(0,r.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,r.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load users. Please try again."}),(0,r.jsx)(s.$,{variant:"outline",className:"mt-2",onClick:()=>I(),children:"Retry"})]}),(0,r.jsxs)(l.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(l.ZB,{className:"text-xl font-bold tracking-tight",children:"Users"}),T>0&&(0,r.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:["Total: ",T," user",1!==T?"s":""]})]})})}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsx)(b,{table:U,roles:[{value:"Admin",label:"Admin"},{value:"User",label:"User"}],onSearch:O,onRoleChange:F,searchValue:e,isFiltering:R,isPending:!1}),(0,r.jsx)(c.b,{columns:m,data:E,table:U,isLoading:R,totalCount:T}),(0,r.jsx)(N.d,{currentPage:u.pageIndex+1,pageSize:u.pageSize,totalCount:T,totalPages:z,isLoading:R,isChangingPageSize:!1,isUnknownTotalCount:!1,onPageChange:e=>{h({...u,pageIndex:e-1})},onPageSizeChange:e=>{let t=Math.floor(u.pageIndex*u.pageSize/e);h({pageSize:e,pageIndex:t})}}),!R&&0===E.length&&!P&&(0,r.jsx)("div",{className:"text-center py-10 text-muted-foreground",children:(0,r.jsxs)("p",{children:["No users found."," ",e||a?"Try adjusting your filters.":"Create your first user to get started."]})})]})]})]})})}},11832:(e,t,a)=>{"use strict";a.d(t,{i:()=>i});var r=a(95155),s=a(18289),l=a(47330),n=a(30285),o=a(44838);function i(e){let{table:t}=e;return(0,r.jsxs)(o.rI,{children:[(0,r.jsx)(s.ty,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,r.jsx)(l.A,{}),"View"]})}),(0,r.jsxs)(o.SQ,{align:"end",className:"w-[150px]",children:[(0,r.jsx)(o.lp,{children:"Toggle columns"}),(0,r.jsx)(o.mB,{}),t.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,r.jsx)(o.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},36928:(e,t,a)=>{"use strict";a.d(t,{cn:()=>l});var r=a(52596),s=a(39688);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}},45995:(e,t,a)=>{"use strict";a.d(t,{i:()=>s});var r=a(7283);let s={getAllUsers:async()=>(0,r.fetchApi)("api/admin/user/get-all",{method:"GET"}),getUserById:async e=>(0,r.fetchApi)("api/admin/user/detail/".concat(e),{method:"GET"}),updateUserInfo:async(e,t)=>(0,r.fetchApi)("api/admin/user/update-detail/".concat(e),{method:"PUT",body:JSON.stringify(t)}),changeUserPassword:async(e,t)=>(0,r.fetchApi)("api/admin/user/change-password/".concat(e),{method:"POST",body:JSON.stringify(t)}),getAllOrganizationUnits:async()=>await r.F.get("/api/admin/organization-unit/get-all"),deleteOrganizationUnit:async e=>{await r.F.delete("/api/admin/organization-unit/".concat(e))}}},47330:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},48133:(e,t,a)=>{"use strict";a.d(t,{O5:()=>c,c4:()=>i,gV:()=>u,m_:()=>m,wz:()=>d});var r=a(67938);let s=r.$.auth.tokenStorageKey,l=r.$.auth.userStorageKey,n=null,o=null,i=()=>{if(n)return n;try{let e=localStorage.getItem(s);if(e)return n=e,e}catch(e){console.error("Error accessing localStorage",e)}return null},c=e=>{n=e;try{e?localStorage.setItem(s,e):localStorage.removeItem(s)}catch(e){console.error("Error accessing localStorage",e)}},d=()=>{if(o)return o;try{let e=localStorage.getItem(l);if(e)try{let t=JSON.parse(e);return o=t,t}catch(e){console.error("Error parsing stored user data",e)}}catch(e){console.error("Error accessing localStorage",e)}return null},u=e=>{o=e;try{e?localStorage.setItem(l,JSON.stringify(e)):localStorage.removeItem(l)}catch(e){console.error("Error accessing localStorage",e)}},m=()=>{n=null,o=null;try{localStorage.removeItem(s),localStorage.removeItem(l)}catch(e){console.error("Error accessing localStorage",e)}}},56061:(e,t,a)=>{Promise.resolve().then(a.bind(a,9224))},59385:(e,t,a)=>{"use strict";a.d(t,{i:()=>r});var r=function(e){return e[e.User=0]="User",e[e.Admin=1]="Admin",e}({})},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>d});var r=a(95155);a(12115);var s=a(36928);function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},66932:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},67938:(e,t,a)=>{"use strict";a.d(t,{$:()=>r});let r={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,8852,5699,8523,9483,3085,5224,8441,1684,7358],()=>t(56061)),_N_E=e.O()}]);