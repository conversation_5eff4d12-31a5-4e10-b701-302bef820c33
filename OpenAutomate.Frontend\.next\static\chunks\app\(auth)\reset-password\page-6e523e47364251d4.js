(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3180],{17330:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,74250)),Promise.resolve().then(r.bind(r,75074))},43453:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},74250:(e,s,r)=>{"use strict";r.d(s,{ResetPasswordForm:()=>P});var t=r(95155),a=r(12115),o=r(35695),n=r(62177),l=r(90221),c=r(55594),i=r(54629),d=r(30285),u=r(17759),m=r(62523),w=r(85339),g=r(43453),h=r(55365),f=r(22370);function p(e){let{password:s}=e,r=(e=>{if(!e)return 0;let s=0;return e.length>=8&&(s+=1),e.length>=12&&(s+=1),/[A-Z]/.test(e)&&(s+=1),/[a-z]/.test(e)&&(s+=1),/[0-9]/.test(e)&&(s+=1),/[^A-Za-z0-9]/.test(e)&&(s+=1),Math.min(s,5)})(s),a=e=>{switch(e){case 0:default:return"bg-gray-200";case 1:return"bg-red-500";case 2:return"bg-orange-500";case 3:return"bg-yellow-500";case 4:return"bg-blue-500";case 5:return"bg-green-500"}};return(0,t.jsxs)("div",{className:"mt-1 w-full",children:[(()=>{let e=[];for(let s=0;s<5;s++)e.push((0,t.jsx)("div",{className:"h-1.5 w-full rounded-sm transition-colors ".concat(s<r?a(r):"bg-gray-200")},s));return(0,t.jsx)("div",{className:"grid grid-cols-5 gap-1",children:e})})(),(0,t.jsx)("div",{className:"mt-1 text-xs text-gray-500 text-right",children:s&&(e=>{switch(e){case 0:return"Very weak";case 1:return"Weak";case 2:return"Medium";case 3:return"Good";case 4:return"Strong";case 5:return"Very strong";default:return""}})(r)})]})}let x=c.Yj().min(8,"Password must be at least 8 characters long").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),j=c.Ik({newPassword:x,confirmPassword:c.Yj()}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function P(){let e=(0,o.useRouter)(),s=(0,o.useSearchParams)(),[r,c]=a.useState(!1),[x,P]=a.useState(null),[b,y]=a.useState(!1),[N,v]=a.useState(""),k=s.get("token")||"",S=s.get("email")||"",[E,A]=a.useState("");a.useEffect(()=>{if(k){let e=k.trim();e.includes(" ")&&(e=e.replace(/\s/g,""));try{e.includes("%")&&(e=decodeURIComponent(e))}catch(e){console.warn("Error decoding token:",e)}A(e)}},[k]);let[R,C]=a.useState(!1);a.useEffect(()=>{C(!0)},[]);let I=(0,n.mN)({resolver:(0,l.u)(j),defaultValues:{newPassword:"",confirmPassword:""}});async function T(s){if(!E){P("Reset token is missing");return}c(!0),P(null),y(!1);try{var r,t,a;if(console.log("Attempting to reset password with token:",E.substring(0,10)+"..."),console.log("Form data:",{newPassword:s.newPassword?"[PRESENT]":"[MISSING]",confirmPassword:s.confirmPassword?"[PRESENT]":"[MISSING]",passwordLength:null===(r=s.newPassword)||void 0===r?void 0:r.length}),s.newPassword!==s.confirmPassword){P("Passwords do not match"),c(!1);return}let o={email:S,token:E,newPassword:s.newPassword,confirmPassword:s.confirmPassword};console.log("Sending reset password request with data:",{email:o.email,tokenLength:o.token.length,passwordLength:(null===(t=o.newPassword)||void 0===t?void 0:t.length)||0,confirmPasswordLength:(null===(a=o.confirmPassword)||void 0===a?void 0:a.length)||0,passwordsMatch:o.newPassword===o.confirmPassword}),await f.Z.resetPassword(o),console.log("Password reset successful"),y(!0),setTimeout(()=>{e.push("/login")},3e3)}catch(s){console.error("Password reset failed",s);let e="Password reset failed. The token may be invalid or expired.";if(s instanceof Error)e=s.message,console.error("Error instance message:",s.message);else if("object"==typeof s&&null!==s){if(console.error("Error object:",JSON.stringify(s,null,2)),s.message)e=s.message,console.error("Error message from object:",s.message);else if(s.details)e=s.details,console.error("Error details from object:",s.details);else if(s.errors){console.error("Validation errors object:",s.errors);let r=Object.entries(s.errors).map(e=>{let[s,r]=e;return"".concat(s,": ").concat(Array.isArray(r)?r.join(", "):r)}).join("; ");e="Validation errors: ".concat(r)}}console.error("Final error message:",e),P(e)}finally{c(!1)}}return(a.useEffect(()=>{E&&(console.log("Processed token length:",E.length),console.log("Token preview:",E.substring(0,10)+"...")),S&&console.log("Email:",S)},[E,S]),a.useEffect(()=>{E&&S?P(null):P("Invalid reset link. The token or email is missing.")},[E,S]),R)?(0,t.jsxs)("div",{className:"grid gap-6",children:[x&&(0,t.jsxs)(h.Fc,{variant:"destructive",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),(0,t.jsx)(h.TN,{children:x})]}),b&&(0,t.jsxs)(h.Fc,{variant:"success",className:"border-green-500 bg-green-50",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)(h.TN,{className:"text-green-700",children:"Your password has been reset successfully! You will be redirected to the login page."})]}),(0,t.jsx)(u.lV,{...I,children:(0,t.jsxs)("form",{onSubmit:I.handleSubmit(T),className:"space-y-4",suppressHydrationWarning:!0,children:[(0,t.jsx)("div",{className:"p-3 bg-gray-50 rounded-md border w-full",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-0",children:["Resetting password for: ",(0,t.jsx)("span",{className:"font-medium",children:S})]})}),(0,t.jsx)(u.zB,{control:I.control,name:"newPassword",render:e=>{let{field:s}=e;return(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"New Password"}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(m.p,{type:"password",placeholder:"••••••••",autoComplete:"new-password",...s,onChange:e=>{s.onChange(e),v(e.target.value)},disabled:r||b||!E})}),(0,t.jsx)(u.Rr,{className:"text-xs",children:"Use 8+ characters with a mix of uppercase, lowercase, numbers & symbols."}),(0,t.jsx)(p,{password:N}),(0,t.jsx)(u.C5,{})]})}}),(0,t.jsx)(u.zB,{control:I.control,name:"confirmPassword",render:e=>{let{field:s}=e;return(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"Confirm New Password"}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(m.p,{type:"password",placeholder:"••••••••",autoComplete:"new-password",...s,disabled:r||b||!E})}),(0,t.jsx)(u.C5,{})]})}}),(0,t.jsxs)(d.$,{type:"submit",className:"w-full bg-orange-600 hover:bg-orange-700 transition-all duration-300 hover:translate-y-[-2px]",disabled:r||b||!E||!S,children:[r&&(0,t.jsx)(i.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),b?"Password Reset":"Reset Password"]})]})})]}):(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)(i.F.Spinner,{className:"h-8 w-8 animate-spin text-muted-foreground"})})}},85339:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6341,2178,8852,5699,5594,6874,8594,1941,3112,7057,8916,5642,8441,1684,7358],()=>s(17330)),_N_E=e.O()}]);