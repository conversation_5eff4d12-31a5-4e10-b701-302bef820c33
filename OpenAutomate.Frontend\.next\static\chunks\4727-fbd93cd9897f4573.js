"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4727],{7283:(e,t,r)=>{r.d(t,{F:()=>w,fetchApi:()=>y});var a=r(48133),o=r(67938);let s=o.$.api.defaultHeaders,n=!1,i=[],l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;i.forEach(r=>{e?r.reject(e):r.resolve(t)}),i=[]},c=async e=>{let t={message:e.statusText,status:e.status};try{let r=await e.json();r.message?(t.message=r.message,t.details=r.details||r.message):r.error?(t.message=r.error,t.details=r.error):t.details=JSON.stringify(r)}catch(r){t.details=e.statusText}return t},u=()=>{window.dispatchEvent(new Event("auth:token-expired"))},d=e=>{if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw{message:"Network error. Please check your connection.",status:0,details:e.message};if(e instanceof Error)throw{message:e.message||"Network error occurred",status:0,details:e.stack||e.message};throw{message:"An unexpected error occurred",status:0,details:String(e)}},h=async e=>{if(204===e.status)return{};let t=e.headers.get("content-type"),r=e.headers.get("content-length");if(!t||-1===t.indexOf("application/json")||"0"===r)return{};let a=await e.text();return a?JSON.parse(a):{}},f=async()=>{if(n)return new Promise((e,t)=>{i.push({resolve:e,reject:t})});n=!0;try{let e=(await y("api/auth/refresh-token",{method:"POST",credentials:"include"})).token;return(0,a.O5)(e),l(null,e),e}catch(e){throw l(e),e}finally{n=!1}},g=async(e,t,r,a,o)=>{if(e.includes("refresh-token")||e.includes("login"))return u(),null;try{let e=await f();if(!e)return null;let a=v(r,o);a.Authorization="Bearer ".concat(e);let{body:s}=m(o),n=await fetch(t,{...r,body:s,headers:a,credentials:"include"});if(n.ok)return h(n);return null}catch(e){return u(),console.error("Token refresh failed:",e),null}},p=e=>{if(e.startsWith("http"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat(o.$.api.baseUrl,"/").concat(t)},m=e=>e?e instanceof FormData?{body:e,headers:{}}:{body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}:{body:void 0,headers:{}},v=(e,t)=>{let r={...t instanceof FormData?{Accept:s.Accept}:{...s},...e.headers};if(!r.Authorization){let e=(0,a.c4)();e&&(r.Authorization="Bearer ".concat(e))}return r};async function y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,a=p(e),{body:o,headers:s}=m(r),n={...v(t,r),...s};try{let s=await fetch(a,{...t,body:o,headers:n,credentials:"include"});if(s.ok)return h(s);if(401===s.status){let o=await g(e,a,t,n,r);if(o)return o}throw await c(s)}catch(e){if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;d(e)}}let w={get:(e,t)=>y(e,{...t,method:"GET"}),post:(e,t,r)=>{let{body:a,headers:o}=m(t);return y(e,{...r,method:"POST",body:a,headers:{...o,...null==r?void 0:r.headers}},t)},put:(e,t,r)=>{let{body:a,headers:o}=m(t);return y(e,{...r,method:"PUT",body:a,headers:{...o,...null==r?void 0:r.headers}},t)},patch:(e,t,r)=>{let{body:a,headers:o}=m(t);return y(e,{...r,method:"PATCH",body:a,headers:{...o,...null==r?void 0:r.headers}},t)},delete:(e,t)=>y(e,{...t,method:"DELETE"})}},15874:(e,t,r)=>{r.d(t,{AG:()=>n,ro:()=>s});var a=r(43630);let o=null;function s(e){a.h.setToastFunction(e)}function n(e,t){(null==t||!t.skipToast)&&((null==t?void 0:t.skipAuth)===!1||!e||"object"!=typeof e||!("status"in e)||401!==e.status)&&a.h.handleError(e,null==t?void 0:t.context)}},36928:(e,t,r)=>{r.d(t,{cn:()=>s});var a=r(52596),o=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}},43630:(e,t,r)=>{r.d(t,{h:()=>a,m:()=>o});class a{static setToastFunction(e){this.toastFunction=e}static error(e,t){this.toastFunction&&this.toastFunction({title:(null==t?void 0:t.title)||"Error",description:e,variant:"destructive",duration:(null==t?void 0:t.duration)||8e3,action:null==t?void 0:t.action})}static success(e,t){this.toastFunction&&this.toastFunction({title:(null==t?void 0:t.title)||"Success",description:e,variant:"default",duration:(null==t?void 0:t.duration)||4e3,action:null==t?void 0:t.action})}static warning(e,t){this.toastFunction&&this.toastFunction({title:(null==t?void 0:t.title)||"Warning",description:e,variant:"default",duration:(null==t?void 0:t.duration)||6e3,action:null==t?void 0:t.action})}static info(e,t){this.toastFunction&&this.toastFunction({title:(null==t?void 0:t.title)||"Information",description:e,variant:"default",duration:(null==t?void 0:t.duration)||5e3,action:null==t?void 0:t.action})}static handleError(e,t){let r="An unexpected error occurred",a="Error";if("string"==typeof e)r=e;else if(e&&"object"==typeof e){if("status"in e&&"message"in e){switch(e.status){case 400:a="Invalid Request";break;case 401:a="Authentication Required";break;case 403:a="Access Denied";break;case 404:a="Not Found";break;case 409:a="Conflict";break;case 422:a="Validation Error";break;case 429:a="Rate Limited";break;case 500:case 502:case 503:a="Server Error";break;default:a="Error"}r=e.message}else"message"in e&&"string"==typeof e.message&&(r=e.message)}t&&(a="".concat(t," Failed")),this.error(r,{title:a})}static handleSuccess(e,t){let r=t?"".concat(t," ").concat(e," successfully"):"".concat(e," completed successfully");this.success(r,{title:"Success"})}static isAvailable(){return null!==this.toastFunction}}a.toastFunction=null;let o={error:a.error.bind(a),success:a.success.bind(a),warning:a.warning.bind(a),info:a.info.bind(a),handleError:a.handleError.bind(a),handleSuccess:a.handleSuccess.bind(a)}},48133:(e,t,r)=>{r.d(t,{O5:()=>c,c4:()=>l,gV:()=>d,m_:()=>h,wz:()=>u});var a=r(67938);let o=a.$.auth.tokenStorageKey,s=a.$.auth.userStorageKey,n=null,i=null,l=()=>{if(n)return n;try{let e=localStorage.getItem(o);if(e)return n=e,e}catch(e){console.error("Error accessing localStorage",e)}return null},c=e=>{n=e;try{e?localStorage.setItem(o,e):localStorage.removeItem(o)}catch(e){console.error("Error accessing localStorage",e)}},u=()=>{if(i)return i;try{let e=localStorage.getItem(s);if(e)try{let t=JSON.parse(e);return i=t,t}catch(e){console.error("Error parsing stored user data",e)}}catch(e){console.error("Error accessing localStorage",e)}return null},d=e=>{i=e;try{e?localStorage.setItem(s,JSON.stringify(e)):localStorage.removeItem(s)}catch(e){console.error("Error accessing localStorage",e)}},h=()=>{n=null,i=null;try{localStorage.removeItem(o),localStorage.removeItem(s)}catch(e){console.error("Error accessing localStorage",e)}}},67938:(e,t,r)=>{r.d(t,{$:()=>a});let a={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}}}]);