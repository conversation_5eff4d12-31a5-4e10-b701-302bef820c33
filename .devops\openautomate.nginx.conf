# ===========================
# Frontend: cloud.openautomate.me
# ===========================

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name cloud.openautomate.me;
    return 301 https://$host$request_uri;
}

# HTTPS server for frontend
server {
    listen 443 ssl http2;
    server_name cloud.openautomate.me;

    ssl_certificate     /etc/letsencrypt/live/cloud.openautomate.me/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/cloud.openautomate.me/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# ===========================
# Backend: api.openautomate.me
# ===========================

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name api.openautomate.me;
    return 301 https://$host$request_uri;
}

# HTTPS server for backend
server {
    listen 443 ssl http2;
    server_name api.openautomate.me;

    ssl_certificate     /etc/letsencrypt/live/api.openautomate.me/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.openautomate.me/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket for SignalR
    location /hubs/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
} 