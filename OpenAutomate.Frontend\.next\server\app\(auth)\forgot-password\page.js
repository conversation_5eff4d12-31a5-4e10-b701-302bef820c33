(()=>{var e={};e.id=5890,e.ids=[5890],e.modules={462:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,30670)),Promise.resolve().then(t.bind(t,10590))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10175:(e,r,t)=>{"use strict";t.d(r,{F:()=>z});var s=t(56363),o=t(62846),n=t(63420),a=t(81639),i=t(53702),l=t(12876),d=t(32769),c=t(5148),u=t(74286),p=t(61348),m=t(65866),x=t(88971),h=t(36440),g=t(30556),f=t(34318),j=t(13461),v=t(3492),A=t(92715),b=t(63353),P=t(56074),w=t(32250),y=t(88804),F=t(90883),N=t(59574),_=t(72832),k=t(18898),C=t(24263),S=t(62817),G=t(19321),q=t(21720),E=t(79173),O=t(44239),R=t(60343);let z={logo:s.A,close:o.A,Spinner:n.A,chevronLeft:a.A,chevronRight:i.A,trash:l.A,settings:d.A,billing:c.A,ellipsis:u.A,add:p.A,warning:m.A,user:x.A,arrowRight:h.A,help:m.A,pizza:g.A,sun:f.A,moon:j.A,laptop:v.A,gitHub:A.A,twitter:b.A,check:P.A,file:w.A,fileText:y.A,image:F.A,play:N.A,pause:_.A,home:k.A,chart:C.A,cog:S.A,logout:G.A,refresh:q.A,about:E.A,guide:O.A,contact:R.A}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17583:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),o=t(48088),n=t(31369),a=t(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);t.d(r,i);let l={children:["",{children:["(auth)",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75990)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\forgot-password\\page.tsx"]}]},{}]},{forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\forgot-password\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(auth)/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30670:(e,r,t)=>{"use strict";t.d(r,{ForgotPasswordForm:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ForgotPasswordForm() from the server but ForgotPasswordForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\forms\\forgot-password-form.tsx","ForgotPasswordForm")},33873:e=>{"use strict";e.exports=require("path")},40214:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,91752)),Promise.resolve().then(t.bind(t,64147))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75990:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,metadata:()=>c});var s=t(37413),o=t(4536),n=t.n(o),a=t(61120),i=t(10590),l=t(10175),d=t(30670);let c={title:"Forgot Password | OpenAutomate",description:"Reset your OpenAutomate account password"};function u(){return(0,s.jsx)("div",{className:"grid gap-6",children:(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)(l.F.Spinner,{className:"h-8 w-8 animate-spin text-muted-foreground"})})})}function p(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.Header,{}),(0,s.jsx)("div",{className:"container flex-1 flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold tracking-tight text-orange-600",children:"Forgot Password"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enter your email to receive a password reset link"})]}),(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(u,{}),children:(0,s.jsx)(d.ForgotPasswordForm,{})}),(0,s.jsxs)("p",{className:"px-8 text-center text-sm text-muted-foreground",children:["Remember your password?"," ",(0,s.jsx)(n(),{href:"/login",className:"text-orange-600 underline underline-offset-4 hover:text-orange-700 font-medium transition-all duration-300 hover:underline-offset-8",children:"Sign in"})]})]})})]})}},91752:(e,r,t)=>{"use strict";t.d(r,{ForgotPasswordForm:()=>f});var s=t(60687),o=t(43210),n=t(27605),a=t(63442),i=t(45880),l=t(11365),d=t(29523),c=t(71669),u=t(89667),p=t(93613),m=t(14719),x=t(91821),h=t(78706);let g=i.Ik({email:i.Yj().email("Please enter a valid email")});function f(){let[e,r]=o.useState(!1),[t,i]=o.useState(null),[f,j]=o.useState(!1),v=(0,n.mN)({resolver:(0,a.u)(g),defaultValues:{email:""}});async function A(e){r(!0),i(null),j(!1);try{await h.Z.forgotPassword({email:e.email}),j(!0),v.reset()}catch(r){console.error("Forgot password request failed",r);let e="Failed to send password reset email. Please try again later.";r instanceof Error?e=r.message:"object"==typeof r&&null!==r&&(r.message?e=r.message:r.details&&(e=r.details)),i(e)}finally{r(!1)}}return(0,s.jsxs)("div",{className:"grid gap-6",children:[t&&(0,s.jsxs)(x.Fc,{variant:"destructive",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)(x.TN,{children:t})]}),f&&(0,s.jsxs)(x.Fc,{variant:"success",className:"border-green-500 bg-green-50",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,s.jsx)(x.TN,{className:"text-green-700",children:"Password reset instructions have been sent to your email. Please check your inbox."})]}),(0,s.jsx)(c.lV,{...v,children:(0,s.jsxs)("form",{onSubmit:v.handleSubmit(A),className:"space-y-4",children:[(0,s.jsx)(c.zB,{control:v.control,name:"email",render:({field:r})=>(0,s.jsxs)(c.eI,{children:[(0,s.jsx)(c.lR,{children:"Email"}),(0,s.jsx)(c.MJ,{children:(0,s.jsx)(u.p,{type:"email",placeholder:"<EMAIL>",autoComplete:"email",...r,disabled:e||f})}),(0,s.jsx)(c.C5,{})]})}),(0,s.jsxs)(d.$,{type:"submit",className:"w-full bg-orange-600 hover:bg-orange-700 transition-all duration-300 hover:translate-y-[-2px]",disabled:e||f,children:[e&&(0,s.jsx)(l.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),f?"Email Sent":"Send Reset Link"]})]})})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7966,5584,5156,4654,5880,7943,5684,6053,6763,8826],()=>t(17583));module.exports=s})();