exports.id=3210,exports.ids=[3210],exports.modules={31599:(e,t,a)=>{"use strict";a.d(t,{c:()=>l});var n=a(43210),r=a(39989),s=a(16189),o=a(31207),i=a(70891);function l(){let e=(0,s.useRouter)(),{data:t,error:a,isLoading:l,mutate:u}=(0,o.Ay)(i.DC.organizationUnits(),()=>r.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:l,error:a?"Failed to fetch organization units. Please try again later.":null,refresh:u,selectOrganizationUnit:(0,n.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},39582:(e,t,a)=>{"use strict";a.d(t,{NA:()=>i,Qk:()=>c,Ri:()=>o,dT:()=>l,kz:()=>u,xR:()=>s});var n=a(51787);let r=()=>"default",s=async e=>{let t=r();return await n.F.post(`${t}/api/agents/create`,e)},o=async e=>{let t=r();return await n.F.get(`${t}/api/agents/${e}`)},i=async()=>{let e=r();return await n.F.get(`${e}/api/agents`)},l=async e=>{let t=r(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(e),s=`${t}/odata/BotAgents`;a&&(s+=`?${a}`),console.log("OData query endpoint:",s);try{let e=await n.F.get(s);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} items from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log(`Found array property "${a}" in response`);let n=e[a],r=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof r?r:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching agents with OData:",e),{value:[]}}},u=async e=>{let t=r();await n.F.delete(`${t}/api/agents/${e}`)},c=async(e,t)=>{let a=r();return await n.F.put(`${a}/api/agents/${e}`,t)}},39989:(e,t,a)=>{"use strict";a.d(t,{K:()=>r});var n=a(51787);let r={getMyOrganizationUnits:async()=>await n.F.get("/api/ou/my-ous"),getBySlug:async e=>await n.F.get(`/api/ou/slug/${e}`),getById:async e=>await n.F.get(`/api/ou/${e}`),create:async e=>await n.F.post("/api/ou/create",e),update:async(e,t)=>await n.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await n.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await n.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await n.F.get(`/api/ou/${e}/deletion-status`)}},61018:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>n});let n=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63676:(e,t,a)=>{"use strict";a.d(t,{d:()=>r});var n=a(43210);function r(e,t){let[a,r]=(0,n.useState)({});return(0,n.useRef)(null),a}a(52974),a(22918),a(97011),a(38605)},64656:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var n=a(37413),r=a(48974),s=a(31057),o=a(50417),i=a(92588),l=a(61018),u=a(2505);function c({children:e}){return(0,n.jsx)(l.TenantGuard,{children:(0,n.jsx)(u.ChatProvider,{children:(0,n.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,n.jsxs)(o.SidebarProvider,{className:"flex flex-col",children:[(0,n.jsx)(s.SiteHeader,{}),(0,n.jsxs)("div",{className:"flex flex-1",children:[(0,n.jsx)(r.AppSidebar,{}),(0,n.jsx)(o.SidebarInset,{children:(0,n.jsx)(i.SearchProvider,{children:(0,n.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>l});var n=a(60687),r=a(43210),s=a(16189),o=a(31599),i=a(31568);function l({children:e}){let{tenant:t}=(0,s.useParams)();(0,s.useRouter)();let{isAuthenticated:a,isLoading:l}=(0,i.A)(),{organizationUnits:u,isLoading:c}=(0,o.c)(),[d,p]=(0,r.useState)(!0);return l||c||d?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,n.jsx)(n.Fragment,{children:e})}},72826:(e,t,a)=>{Promise.resolve().then(a.bind(a,69231)),Promise.resolve().then(a.bind(a,83847)),Promise.resolve().then(a.bind(a,78526)),Promise.resolve().then(a.bind(a,97597)),Promise.resolve().then(a.bind(a,98641)),Promise.resolve().then(a.bind(a,80110))},83442:(e,t,a)=>{Promise.resolve().then(a.bind(a,61018)),Promise.resolve().then(a.bind(a,2505)),Promise.resolve().then(a.bind(a,92588)),Promise.resolve().then(a.bind(a,48974)),Promise.resolve().then(a.bind(a,31057)),Promise.resolve().then(a.bind(a,50417))}};