import requests
import json
import csv
import pandas as pd
from datetime import datetime
from requests.auth import HTTPBasicAuth

# Configuration
JIRA_BASE_URL = "https://openautomate.atlassian.net"
EMAIL = "<EMAIL>"  # Replace with your Jira email
API_TOKEN = "ATATT3xFfGF0OruYTghANP-iVlUlTp-rvva-mpl1koMA3q7u6eWyz0m0S8crkmRMjg4eLjQZKmHI05fM64Lb1iX-hTYNfrgPwt0L1CoKnh94_ximZpORFc5SZ7Bffp0-G_Qlw3u7YL9God9JBNpJGKg7kR1JRMDNru_4GXiP3rxeqzAkI4nXPwQ=AB1234C2"  # Replace with your API token
PROJECT_KEY = "OA"

def get_all_jira_tickets():
    """
    Retrieve all tickets from the OpenAutomate project
    """
    all_issues = []
    start_at = 0
    max_results = 100  # <PERSON><PERSON>'s max per request
    
    while True:
        # JQL query to get all tickets in the project
        jql = f"project = {PROJECT_KEY} ORDER BY created DESC"
        
        # API endpoint
        url = f"{JIRA_BASE_URL}/rest/api/3/search"
        
        # Parameters
        params = {
            "jql": jql,
            "startAt": start_at,
            "maxResults": max_results,            "fields": [
                "key", "summary", "status", "issuetype", "assignee", 
                "created", "updated", "priority", "reporter", "description",
                "labels", "components", "fixVersions", "resolution", "resolutiondate"
            ]
        }
        
        # Make the request
        response = requests.get(
            url,
            params=params,
            auth=HTTPBasicAuth(EMAIL, API_TOKEN),
            headers={"Accept": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"Error: {response.status_code}")
            print(response.text)
            break
            
        data = response.json()
        issues = data.get("issues", [])
        
        if not issues:
            break
            
        all_issues.extend(issues)
        
        # Check if we've got all issues
        if len(issues) < max_results:
            break
            
        start_at += max_results
        print(f"Retrieved {len(all_issues)} tickets so far...")
    
    return {
        "total": len(all_issues),
        "issues": all_issues
    }

def save_to_file(data, filename="jira_tickets.json"):
    """Save the data to a JSON file"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"Data saved to {filename}")

def flatten_jira_issue(issue):
    """
    Flatten a Jira issue into a flat dictionary for CSV export
    """
    fields = issue.get('fields', {})
    
    # Extract nested values safely
    def safe_extract(obj, path, default=''):
        """Safely extract nested values"""
        try:
            for key in path:
                obj = obj[key] if obj else default
            return obj if obj is not None else default
        except (KeyError, TypeError):
            return default
    
    # Create flattened row
    row = {
        'Key': issue.get('key', ''),
        'Summary': fields.get('summary', ''),
        'Description': fields.get('description', ''),
        'Issue Type': safe_extract(fields, ['issuetype', 'name']),
        'Status': safe_extract(fields, ['status', 'name']),
        'Priority': safe_extract(fields, ['priority', 'name']),
        'Resolution': safe_extract(fields, ['resolution', 'name']),        'Created': fields.get('created', ''),
        'Updated': fields.get('updated', ''),
        'Resolution Date': fields.get('resolutiondate', ''),  # When the issue was completed/done
        'Assignee': safe_extract(fields, ['assignee', 'displayName']),
        'Assignee Email': safe_extract(fields, ['assignee', 'emailAddress']),
        'Reporter': safe_extract(fields, ['reporter', 'displayName']),
        'Reporter Email': safe_extract(fields, ['reporter', 'emailAddress']),
        'Labels': ', '.join(fields.get('labels', [])),
        'Components': ', '.join([comp.get('name', '') for comp in fields.get('components', [])]),
        'Fix Versions': ', '.join([version.get('name', '') for version in fields.get('fixVersions', [])]),
        'Affects Versions': ', '.join([version.get('name', '') for version in fields.get('versions', [])]),
        'Epic Link': safe_extract(fields, ['customfield_10014']),  # Common epic link field
        'Sprint': safe_extract(fields, ['customfield_10020']),     # Common sprint field
        'Story Points': safe_extract(fields, ['customfield_10016']), # Common story points field
        'Original Estimate': safe_extract(fields, ['timeoriginalestimate']),
        'Remaining Estimate': safe_extract(fields, ['timeestimate']),
        'Time Spent': safe_extract(fields, ['timespent']),
        'Environment': fields.get('environment', ''),
        'Due Date': fields.get('duedate', ''),
        'Project Key': safe_extract(fields, ['project', 'key']),
        'Project Name': safe_extract(fields, ['project', 'name']),
    }
    
    return row

def export_to_csv(data, filename="jira_tickets.csv"):
    """
    Export Jira tickets data to CSV with all fields
    """
    issues = data.get('issues', [])
    
    if not issues:
        print("No issues to export")
        return
    
    # Flatten all issues
    flattened_issues = [flatten_jira_issue(issue) for issue in issues]
    
    # Create DataFrame
    df = pd.DataFrame(flattened_issues)
      # Clean up the data
    # Convert dates to readable format
    date_columns = ['Created', 'Updated', 'Due Date', 'Resolution Date']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Fill NaN values with empty strings
    df = df.fillna('')
    
    # Export to CSV
    df.to_csv(filename, index=False, encoding='utf-8-sig')  # utf-8-sig for Excel compatibility    print(f"[STATS] CSV data exported to {filename}")
    print(f"[SUMMARY] Exported {len(df)} tickets with {len(df.columns)} columns")
    
    return filename

def export_to_excel(data, filename="jira_tickets.xlsx"):
    """
    Export Jira tickets data to Excel with multiple sheets and formatting
    """
    issues = data.get('issues', [])
    
    if not issues:
        print("No issues to export")
        return
    
    # Flatten all issues
    flattened_issues = [flatten_jira_issue(issue) for issue in issues]
    df = pd.DataFrame(flattened_issues)    # Clean up the data
    date_columns = ['Created', 'Updated', 'Due Date', 'Resolution Date']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce').dt.tz_localize(None)
    
    # Fill NaN values with empty strings for text columns
    text_columns = df.select_dtypes(include=['object']).columns
    df[text_columns] = df[text_columns].fillna('')
    
    # Create Excel writer with engine
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # Main data sheet
        df.to_excel(writer, sheet_name='All Tickets', index=False)
        
        # Summary sheets
        # By Status
        status_summary = df['Status'].value_counts()
        status_summary.to_excel(writer, sheet_name='Status Summary', header=['Count'])
        
        # By Issue Type
        type_summary = df['Issue Type'].value_counts()
        type_summary.to_excel(writer, sheet_name='Type Summary', header=['Count'])
        
        # By Assignee
        assignee_summary = df['Assignee'].value_counts()
        assignee_summary.to_excel(writer, sheet_name='Assignee Summary', header=['Count'])
        
        # Format the main sheet
        worksheet = writer.sheets['All Tickets']
          # Adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except Exception:
                    pass
            
            # Set width with some padding, max 50
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    print(f"[STATS] Excel data exported to {filename}")
    print(f"[SUMMARY] Exported {len(df)} tickets with {len(df.columns)} columns")
    
    return filename

def main():
    print("Fetching all Jira tickets from OpenAutomate project...")
    
    # Get all tickets
    tickets_data = get_all_jira_tickets()
    
    print(f"Total tickets retrieved: {tickets_data['total']}")
    
    # Save to different formats
    save_to_file(tickets_data)  # JSON format
    export_to_csv(tickets_data)  # CSV format
    export_to_excel(tickets_data)  # Excel format with multiple sheets
    
    # Print summary
    print("\nTicket Summary:")
    issue_types = {}
    statuses = {}
    
    for issue in tickets_data['issues']:
        # Count by issue type
        issue_type = issue['fields']['issuetype']['name']
        issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
        
        # Count by status
        status = issue['fields']['status']['name']
        statuses[status] = statuses.get(status, 0) + 1
    
    print("\nBy Issue Type:")
    for issue_type, count in sorted(issue_types.items()):
        print(f"  {issue_type}: {count}")
    
    print("\nBy Status:")
    for status, count in sorted(statuses.items()):
        print(f"  {status}: {count}")
    print("\n[SUCCESS] Export completed!")
    print("[FILES] Files generated:")
    print("   - jira_tickets.json (Raw data)")
    print("   - jira_tickets.csv (CSV format)")
    print("   - jira_tickets.xlsx (Excel with summary sheets)")

if __name__ == "__main__":
    main()