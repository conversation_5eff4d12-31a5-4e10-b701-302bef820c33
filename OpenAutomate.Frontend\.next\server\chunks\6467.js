exports.id=6467,exports.ids=[6467],exports.modules={3211:(e,t,n)=>{"use strict";n.d(t,{c:()=>s});let r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function a(e){return (t={})=>{let n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}let i={date:a({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:a({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:a({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function l(e){return(t,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,a=n?.width?String(n.width):t;r=e.formattingValues[a]||e.formattingValues[t]}else{let t=e.defaultWidth,a=n?.width?String(n.width):e.defaultWidth;r=e.values[a]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function u(e){return(t,n={})=>{let r;let a=n.width,i=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;let l=o[0],u=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],s=Array.isArray(u)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(u,e=>e.test(l)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(u,e=>e.test(l));return r=e.valueCallback?e.valueCallback(s):s,{value:r=n.valueCallback?n.valueCallback(r):r,rest:t.slice(l.length)}}}let s={code:"en-US",formatDistance:(e,t,n)=>{let a;let i=r[e];return(a="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),n?.addSuffix)?n.comparison&&n.comparison>0?"in "+a:a+" ago":a},formatLong:i,formatRelative:(e,t,n,r)=>o[e],localize:{ordinalNumber:(e,t)=>{let n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:l({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:l({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:l({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:l({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:l({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,n={})=>{let r=t.match(e.matchPattern);if(!r)return null;let a=r[0],i=t.match(e.parsePattern);if(!i)return null;let o=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:t.slice(a.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:u({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:u({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:u({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:u({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:u({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}}},6094:(e,t,n)=>{"use strict";n.d(t,{Ke:()=>C,R6:()=>N,bL:()=>E});var r=n(43210),a=n(60687),i=n(67427),o=globalThis?.document?r.useLayoutEffect:()=>{};function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function s(...e){return r.useCallback(u(...e),e)}n(51215);var d=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...a}=e;if(r.isValidElement(n)){var i;let e,o;let l=(i=n,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let a=e[r],i=t[r];/^on[A-Z]/.test(r)?a&&i?n[r]=(...e)=>{i(...e),a(...e)}:a&&(n[r]=a):"style"===r?n[r]={...a,...i}:"className"===r&&(n[r]=[a,i].filter(Boolean).join(" "))}return{...e,...n}}(a,n.props);return n.type!==r.Fragment&&(s.ref=t?u(t,l):l),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...o}=e,l=r.Children.toArray(i),u=l.find(c);if(u){let e=u.props.children,i=l.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,a.jsx)(t,{...o,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),p=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[a,i]=r.useState(),l=r.useRef({}),u=r.useRef(e),s=r.useRef("none"),[d,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=m(l.current);s.current="mounted"===d?e:"none"},[d]),o(()=>{let t=l.current,n=u.current;if(n!==e){let r=s.current,a=m(t);e?c("MOUNT"):"none"===a||t?.display==="none"?c("UNMOUNT"):n&&r!==a?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),o(()=>{if(a){let e;let t=a.ownerDocument.defaultView??window,n=n=>{let r=m(l.current).includes(n.animationName);if(n.target===a&&r&&(c("ANIMATION_END"),!u.current)){let n=a.style.animationFillMode;a.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=n)})}},r=e=>{e.target===a&&(s.current=m(l.current))};return a.addEventListener("animationstart",r),a.addEventListener("animationcancel",n),a.addEventListener("animationend",n),()=>{t.clearTimeout(e),a.removeEventListener("animationstart",r),a.removeEventListener("animationcancel",n),a.removeEventListener("animationend",n)}}c("ANIMATION_END")},[a,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),l=s(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||a.isPresent?r.cloneElement(i,{ref:l}):null};function m(e){return e?.animationName||"none"}p.displayName="Presence";var h=n(19783),y="Collapsible",[g,v]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let a=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:a}}),[n,a])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[l]||o,d=r.useMemo(()=>u,Object.values(u));return(0,a.jsx)(s.Provider,{value:d,children:i})};return u.displayName=t+"Provider",[u,function(n,a){let u=a?.[e]?.[l]||o,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=n.reduce((t,{useScope:n,scopeName:r})=>{let a=n(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}(i,...t)]}(y),[b,w]=g(y),x=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:l,disabled:u,onOpenChange:s,...d}=e,[c,p]=(0,i.i)({prop:o,defaultProp:l??!1,onChange:s,caller:y});return(0,a.jsx)(b,{scope:n,disabled:u,contentId:(0,h.B)(),open:c,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),children:(0,a.jsx)(f.div,{"data-state":j(c),"data-disabled":u?"":void 0,...d,ref:t})})});x.displayName=y;var M="CollapsibleTrigger",N=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,i=w(M,n);return(0,a.jsx)(f.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":j(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...r,ref:t,onClick:function(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}(e.onClick,i.onOpenToggle)})});N.displayName=M;var k="CollapsibleContent",C=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,i=w(k,e.__scopeCollapsible);return(0,a.jsx)(p,{present:n||i.open,children:({present:e})=>(0,a.jsx)(T,{...r,ref:t,present:e})})});C.displayName=k;var T=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:i,children:l,...u}=e,d=w(k,n),[c,p]=r.useState(i),m=r.useRef(null),h=s(t,m),y=r.useRef(0),g=y.current,v=r.useRef(0),b=v.current,x=d.open||c,M=r.useRef(x),N=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>M.current=!1);return()=>cancelAnimationFrame(e)},[]),o(()=>{let e=m.current;if(e){N.current=N.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();y.current=t.height,v.current=t.width,M.current||(e.style.transitionDuration=N.current.transitionDuration,e.style.animationName=N.current.animationName),p(i)}},[d.open,i]),(0,a.jsx)(f.div,{"data-state":j(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!x,...u,ref:h,style:{"--radix-collapsible-content-height":g?`${g}px`:void 0,"--radix-collapsible-content-width":b?`${b}px`:void 0,...e.style},children:x&&l})});function j(e){return e?"open":"closed"}var E=x},9903:(e,t,n)=>{"use strict";n.d(t,{q:()=>a});let r={};function a(){return r}},11392:(e,t,n)=>{"use strict";n.d(t,{F6:()=>o,Nw:()=>i,my:()=>r,w4:()=>a});let r=6048e5,a=864e5,i=43200,o=1440},11437:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},17313:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},17575:()=>{},17971:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},29113:(e,t,n)=>{"use strict";n.d(t,{m:()=>f});var r=n(35780),a=n(47138);function i(e,t){let n=(0,a.a)(e),r=(0,a.a)(t),i=n.getTime()-r.getTime();return i<0?-1:i>0?1:i}var o=n(11392),l=n(79186),u=n(46127),s=n(3211),d=n(9903),c=n(79943);function f(e,t){return function(e,t,n){let r,f,p;let m=(0,d.q)(),h=n?.locale??m.locale??s.c,y=i(e,t);if(isNaN(y))throw RangeError("Invalid time value");let g=Object.assign({},n,{addSuffix:n?.addSuffix,comparison:y});y>0?(r=(0,a.a)(t),f=(0,a.a)(e)):(r=(0,a.a)(e),f=(0,a.a)(t));let v=function(e,t,n){var r;return(r=void 0,e=>{let t=(r?Math[r]:Math.trunc)(e);return 0===t?0:t})((+(0,a.a)(e)-+(0,a.a)(t))/1e3)}(f,r),b=Math.round((v-((0,c.G)(f)-(0,c.G)(r))/1e3)/60);if(b<2){if(n?.includeSeconds){if(v<5)return h.formatDistance("lessThanXSeconds",5,g);if(v<10)return h.formatDistance("lessThanXSeconds",10,g);if(v<20)return h.formatDistance("lessThanXSeconds",20,g);else if(v<40)return h.formatDistance("halfAMinute",0,g);else if(v<60)return h.formatDistance("lessThanXMinutes",1,g);else return h.formatDistance("xMinutes",1,g)}return 0===b?h.formatDistance("lessThanXMinutes",1,g):h.formatDistance("xMinutes",b,g)}if(b<45)return h.formatDistance("xMinutes",b,g);if(b<90)return h.formatDistance("aboutXHours",1,g);if(b<o.F6){let e=Math.round(b/60);return h.formatDistance("aboutXHours",e,g)}if(b<2520)return h.formatDistance("xDays",1,g);else if(b<o.Nw){let e=Math.round(b/o.F6);return h.formatDistance("xDays",e,g)}else if(b<2*o.Nw)return p=Math.round(b/o.Nw),h.formatDistance("aboutXMonths",p,g);if((p=function(e,t){let n;let r=(0,a.a)(e),o=(0,a.a)(t),s=i(r,o),d=Math.abs((0,l.U)(r,o));if(d<1)n=0;else{1===r.getMonth()&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-s*d);let t=i(r,o)===-s;(function(e){let t=(0,a.a)(e);return+function(e){let t=(0,a.a)(e);return t.setHours(23,59,59,999),t}(t)==+(0,u.p)(t)})((0,a.a)(e))&&1===d&&1===i(e,o)&&(t=!1),n=s*(d-Number(t))}return 0===n?0:n}(f,r))<12){let e=Math.round(b/o.Nw);return h.formatDistance("xMonths",e,g)}{let e=p%12,t=Math.trunc(p/12);return e<3?h.formatDistance("aboutXYears",t,g):e<9?h.formatDistance("overXYears",t,g):h.formatDistance("almostXYears",t+1,g)}}(e,(0,r.w)(e,Date.now()),t)}},32940:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("life-buoy",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.93 4.93 4.24 4.24",key:"1ymg45"}],["path",{d:"m14.83 9.17 4.24-4.24",key:"1cb5xl"}],["path",{d:"m14.83 14.83 4.24 4.24",key:"q42g0n"}],["path",{d:"m9.17 14.83-4.24 4.24",key:"bqpfvv"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]])},35780:(e,t,n)=>{"use strict";function r(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}n.d(t,{w:()=>r})},41312:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},46127:(e,t,n)=>{"use strict";n.d(t,{p:()=>a});var r=n(47138);function a(e){let t=(0,r.a)(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}},47138:(e,t,n)=>{"use strict";function r(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}n.d(t,{a:()=>r})},48730:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49625:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},51214:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},56085:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},56476:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},67141:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("refresh-ccw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]])},70709:(e,t,n)=>{"use strict";n.d(t,{b:()=>f});var r=n(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}n(51215);var i=n(60687),o=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var o;let e,l;let u=(o=n,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let n={...t};for(let r in t){let a=e[r],i=t[r];/^on[A-Z]/.test(r)?a&&i?n[r]=(...e)=>{i(...e),a(...e)}:a&&(n[r]=a):"style"===r?n[r]={...a,...i}:"className"===r&&(n[r]=[a,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(s.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:a,...o}=e,u=r.Children.toArray(a),s=u.find(l);if(s){let e=s.props.children,a=u.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...o,ref:n,children:a})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:a,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?n:t,{...o,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),s="horizontal",d=["horizontal","vertical"],c=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:a=s,...o}=e,l=(n=a,d.includes(n))?a:s;return(0,i.jsx)(u.div,{"data-orientation":l,...r?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...o,ref:t})});c.displayName="Separator";var f=c},77071:(e,t,n)=>{"use strict";n.d(t,{i3:()=>K,UC:()=>G,ZL:()=>Q,Kq:()=>z,bL:()=>J,l9:()=>Z});var r=n(43210);function a(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function l(...e){return r.useCallback(o(...e),e)}var u=n(60687),s=n(42360),d=n(19783),c=n(96348),f=n(96929),p=globalThis?.document?r.useLayoutEffect:()=>{},m=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[a,i]=r.useState(),o=r.useRef({}),l=r.useRef(e),u=r.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=h(o.current);u.current="mounted"===s?e:"none"},[s]),p(()=>{let t=o.current,n=l.current;if(n!==e){let r=u.current,a=h(t);e?d("MOUNT"):"none"===a||t?.display==="none"?d("UNMOUNT"):n&&r!==a?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),p(()=>{if(a){let e;let t=a.ownerDocument.defaultView??window,n=n=>{let r=h(o.current).includes(n.animationName);if(n.target===a&&r&&(d("ANIMATION_END"),!l.current)){let n=a.style.animationFillMode;a.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=n)})}},r=e=>{e.target===a&&(u.current=h(o.current))};return a.addEventListener("animationstart",r),a.addEventListener("animationcancel",n),a.addEventListener("animationend",n),()=>{t.clearTimeout(e),a.removeEventListener("animationstart",r),a.removeEventListener("animationcancel",n),a.removeEventListener("animationend",n)}}d("ANIMATION_END")},[a,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(o.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),o=l(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||a.isPresent?r.cloneElement(i,{ref:o}):null};function h(e){return e?.animationName||"none"}m.displayName="Presence",n(51215);var y=Symbol("radix.slottable");function g(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}var v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...a}=e;if(r.isValidElement(n)){var i;let e,l;let u=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let a=e[r],i=t[r];/^on[A-Z]/.test(r)?a&&i?n[r]=(...e)=>{i(...e),a(...e)}:a&&(n[r]=a):"style"===r?n[r]={...a,...i}:"className"===r&&(n[r]=[a,i].filter(Boolean).join(" "))}return{...e,...n}}(a,n.props);return n.type!==r.Fragment&&(s.ref=t?o(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:a,...i}=e,o=r.Children.toArray(a),l=o.find(g);if(l){let e=l.props.children,a=o.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,u.jsx)(t,{...i,ref:n,children:a})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(a?n:t,{...i,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),b=n(67427),w=n(88574),[x,M]=function(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let a=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:a}}),[n,a])}};return a.scopeName=e,[function(t,a){let i=r.createContext(a),o=n.length;n=[...n,a];let l=t=>{let{scope:n,children:a,...l}=t,s=n?.[e]?.[o]||i,d=r.useMemo(()=>l,Object.values(l));return(0,u.jsx)(s.Provider,{value:d,children:a})};return l.displayName=t+"Provider",[l,function(n,l){let u=l?.[e]?.[o]||i,s=r.useContext(u);if(s)return s;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=n.reduce((t,{useScope:n,scopeName:r})=>{let a=n(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}(a,...t)]}("Tooltip",[c.Bk]),N=(0,c.Bk)(),k="TooltipProvider",C="tooltip.open",[T,j]=x(k),E=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:a=300,disableHoverableContent:i=!1,children:o}=e,l=r.useRef(!0),s=r.useRef(!1),d=r.useRef(0);return r.useEffect(()=>{let e=d.current;return()=>window.clearTimeout(e)},[]),(0,u.jsx)(T,{scope:t,isOpenDelayedRef:l,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(d.current),l.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.current=!0,a)},[a]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:o})};E.displayName=k;var R="Tooltip",[A,P]=x(R),D=e=>{let{__scopeTooltip:t,children:n,open:a,defaultOpen:i,onOpenChange:o,disableHoverableContent:l,delayDuration:s}=e,f=j(R,e.__scopeTooltip),p=N(t),[m,h]=r.useState(null),y=(0,d.B)(),g=r.useRef(0),v=l??f.disableHoverableContent,w=s??f.delayDuration,x=r.useRef(!1),[M,k]=(0,b.i)({prop:a,defaultProp:i??!1,onChange:e=>{e?(f.onOpen(),document.dispatchEvent(new CustomEvent(C))):f.onClose(),o?.(e)},caller:R}),T=r.useMemo(()=>M?x.current?"delayed-open":"instant-open":"closed",[M]),E=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,x.current=!1,k(!0)},[k]),P=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,k(!1)},[k]),D=r.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{x.current=!0,k(!0),g.current=0},w)},[w,k]);return r.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,u.jsx)(c.bL,{...p,children:(0,u.jsx)(A,{scope:t,contentId:y,open:M,stateAttribute:T,trigger:m,onTriggerChange:h,onTriggerEnter:r.useCallback(()=>{f.isOpenDelayedRef.current?D():E()},[f.isOpenDelayedRef,D,E]),onTriggerLeave:r.useCallback(()=>{v?P():(window.clearTimeout(g.current),g.current=0)},[P,v]),onOpen:E,onClose:P,disableHoverableContent:v,children:n})})};D.displayName=R;var O="TooltipTrigger",S=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...i}=e,o=P(O,n),s=j(O,n),d=N(n),f=l(t,r.useRef(null),o.onTriggerChange),p=r.useRef(!1),m=r.useRef(!1),h=r.useCallback(()=>p.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,u.jsx)(c.Mz,{asChild:!0,...d,children:(0,u.jsx)(v.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...i,ref:f,onPointerMove:a(e.onPointerMove,e=>{"touch"===e.pointerType||m.current||s.isPointerInTransitRef.current||(o.onTriggerEnter(),m.current=!0)}),onPointerLeave:a(e.onPointerLeave,()=>{o.onTriggerLeave(),m.current=!1}),onPointerDown:a(e.onPointerDown,()=>{o.open&&o.onClose(),p.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:a(e.onFocus,()=>{p.current||o.onOpen()}),onBlur:a(e.onBlur,o.onClose),onClick:a(e.onClick,o.onClose)})})});S.displayName=O;var W="TooltipPortal",[_,L]=x(W,{forceMount:void 0}),I=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:a}=e,i=P(W,t);return(0,u.jsx)(_,{scope:t,forceMount:n,children:(0,u.jsx)(m,{present:n||i.open,children:(0,u.jsx)(f.Z,{asChild:!0,container:a,children:r})})})};I.displayName=W;var F="TooltipContent",U=r.forwardRef((e,t)=>{let n=L(F,e.__scopeTooltip),{forceMount:r=n.forceMount,side:a="top",...i}=e,o=P(F,e.__scopeTooltip);return(0,u.jsx)(m,{present:r||o.open,children:o.disableHoverableContent?(0,u.jsx)(H,{side:a,...i,ref:t}):(0,u.jsx)($,{side:a,...i,ref:t})})}),$=r.forwardRef((e,t)=>{let n=P(F,e.__scopeTooltip),a=j(F,e.__scopeTooltip),i=r.useRef(null),o=l(t,i),[s,d]=r.useState(null),{trigger:c,onClose:f}=n,p=i.current,{onPointerInTransitChange:m}=a,h=r.useCallback(()=>{d(null),m(!1)},[m]),y=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},a=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,a,i)){case i:return"left";case a:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,a),...function(e){let{top:t,right:n,bottom:r,left:a}=e;return[{x:a,y:t},{x:n,y:t},{x:n,y:r},{x:a,y:r}]}(t.getBoundingClientRect())])),m(!0)},[m]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&p){let e=e=>y(e,p),t=e=>y(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,y,h]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=c?.contains(t)||p?.contains(t),a=!function(e,t){let{x:n,y:r}=e,a=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let o=t[e],l=t[i],u=o.x,s=o.y,d=l.x,c=l.y;s>r!=c>r&&n<(d-u)*(r-s)/(c-s)+u&&(a=!a)}return a}(n,s);r?h():a&&(h(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,f,h]),(0,u.jsx)(H,{...e,ref:o})}),[q,V]=x(R,{isInside:!1}),X=function(e){let t=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=y,t}("TooltipContent"),H=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:a,"aria-label":i,onEscapeKeyDown:o,onPointerDownOutside:l,...d}=e,f=P(F,n),p=N(n),{onClose:m}=f;return r.useEffect(()=>(document.addEventListener(C,m),()=>document.removeEventListener(C,m)),[m]),r.useEffect(()=>{if(f.trigger){let e=e=>{let t=e.target;t?.contains(f.trigger)&&m()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[f.trigger,m]),(0,u.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:m,children:(0,u.jsxs)(c.UC,{"data-state":f.stateAttribute,...p,...d,ref:t,style:{...d.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,u.jsx)(X,{children:a}),(0,u.jsx)(q,{scope:n,isInside:!0,children:(0,u.jsx)(w.bL,{id:f.contentId,role:"tooltip",children:i||a})})]})})});U.displayName=F;var B="TooltipArrow",Y=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,a=N(n);return V(B,n).isInside?null:(0,u.jsx)(c.i3,{...a,...r,ref:t})});Y.displayName=B;var z=E,J=D,Z=S,Q=I,G=U,K=Y},79186:(e,t,n)=>{"use strict";n.d(t,{U:()=>a});var r=n(47138);function a(e,t){let n=(0,r.a)(e),a=(0,r.a)(t);return 12*(n.getFullYear()-a.getFullYear())+(n.getMonth()-a.getMonth())}},79943:(e,t,n)=>{"use strict";n.d(t,{G:()=>a});var r=n(47138);function a(e){let t=(0,r.a)(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}},83753:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},86885:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("file-key-2",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v6",key:"rc0qvx"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"4",cy:"16",r:"2",key:"1ehqvc"}],["path",{d:"m10 10-4.5 4.5",key:"7fwrp6"}],["path",{d:"m9 11 1 1",key:"wa6s5q"}]])},88574:(e,t,n)=>{"use strict";n.d(t,{bL:()=>c,Qg:()=>s});var r=n(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}n(51215);var i=n(60687),o=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var o;let e,l;let u=(o=n,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let n={...t};for(let r in t){let a=e[r],i=t[r];/^on[A-Z]/.test(r)?a&&i?n[r]=(...e)=>{i(...e),a(...e)}:a&&(n[r]=a):"style"===r?n[r]={...a,...i}:"className"===r&&(n[r]=[a,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(s.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:a,...o}=e,u=r.Children.toArray(a),s=u.find(l);if(s){let e=s.props.children,a=u.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...o,ref:n,children:a})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:a,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?n:t,{...o,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),s=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),d=r.forwardRef((e,t)=>(0,i.jsx)(u.span,{...e,ref:t,style:{...s,...e.style}}));d.displayName="VisuallyHidden";var c=d}};