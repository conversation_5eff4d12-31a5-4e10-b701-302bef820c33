exports.id=6295,exports.ids=[6295],exports.modules={27590:(e,t,a)=>{"use strict";function r(e,t={}){let{dateStyle:a="medium",timeStyle:n="short",fallback:o="N/A",customFormat:s,locale:d="en-US"}=t;if(!e)return o;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug(`Corrected UTC date format: ${e} -> ${a}`)),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn(`Invalid date provided to formatUtcToLocal: ${e}`),o;if(s)return function(e,t){let a=e.getFullYear(),r=e.getMonth()+1,n=e.getDate(),o=e.getHours(),s=e.getMinutes(),d={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][r-1],dd:n.toString().padStart(2,"0"),h:(o%12||12).toString(),mm:s.toString().padStart(2,"0"),a:o>=12?"PM":"AM"},i=t;return Object.entries(d).forEach(([e,t])=>{i=i.replace(RegExp(e,"g"),t)}),i}(t,s);return new Intl.DateTimeFormat(d,{dateStyle:a,timeStyle:n}).format(t)}catch(t){return console.error(`Error formatting date ${e}:`,t),o}}a.d(t,{Ej:()=>r})},31599:(e,t,a)=>{"use strict";a.d(t,{c:()=>i});var r=a(43210),n=a(39989),o=a(16189),s=a(31207),d=a(70891);function i(){let e=(0,o.useRouter)(),{data:t,error:a,isLoading:i,mutate:l}=(0,s.Ay)(d.DC.organizationUnits(),()=>n.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:i,error:a?"Failed to fetch organization units. Please try again later.":null,refresh:l,selectOrganizationUnit:(0,r.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},37337:(e,t,a)=>{"use strict";a.d(t,{AW:()=>c,Cb:()=>u,QQ:()=>i,ae:()=>o,jm:()=>l,oy:()=>d,s9:()=>s});var r=a(51787);let n=()=>"default",o=async e=>{let t=n();return await r.F.get(`${t}/api/packages/${e}`)},s=async()=>{let e=n();return await r.F.get(`${e}/api/packages`)},d=async e=>{let t=n(),a=new FormData;return a.append("file",e.file),e.name&&a.append("name",e.name),e.description&&a.append("description",e.description),e.version&&a.append("version",e.version),await r.F.post(`${t}/api/packages/upload`,a)},i=async(e,t)=>{let a=n();return await r.F.get(`${a}/api/packages/${e}/versions/${t}/download`)},l=async e=>{let t=n();await r.F.delete(`${t}/api/packages/${e}`)},c=async(e,t)=>{let a=n();await r.F.delete(`${a}/api/packages/${e}/versions/${t}`)},u=async e=>{let t=n(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(e),o=`${t}/odata/AutomationPackages`;a&&(o+=`?${a}`),console.log("OData query endpoint:",o);try{let e=await r.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} packages from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log(`Found array property "${a}" in response`);let r=e[a],n=e["@odata.count"];return{value:r,"@odata.count":"number"==typeof n?n:r.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},39582:(e,t,a)=>{"use strict";a.d(t,{NA:()=>d,Qk:()=>c,Ri:()=>s,dT:()=>i,kz:()=>l,xR:()=>o});var r=a(51787);let n=()=>"default",o=async e=>{let t=n();return await r.F.post(`${t}/api/agents/create`,e)},s=async e=>{let t=n();return await r.F.get(`${t}/api/agents/${e}`)},d=async()=>{let e=n();return await r.F.get(`${e}/api/agents`)},i=async e=>{let t=n(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(e),o=`${t}/odata/BotAgents`;a&&(o+=`?${a}`),console.log("OData query endpoint:",o);try{let e=await r.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} items from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log(`Found array property "${a}" in response`);let r=e[a],n=e["@odata.count"];return{value:r,"@odata.count":"number"==typeof n?n:r.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching agents with OData:",e),{value:[]}}},l=async e=>{let t=n();await r.F.delete(`${t}/api/agents/${e}`)},c=async(e,t)=>{let a=n();return await r.F.put(`${a}/api/agents/${e}`,t)}},39989:(e,t,a)=>{"use strict";a.d(t,{K:()=>n});var r=a(51787);let n={getMyOrganizationUnits:async()=>await r.F.get("/api/ou/my-ous"),getBySlug:async e=>await r.F.get(`/api/ou/slug/${e}`),getById:async e=>await r.F.get(`/api/ou/${e}`),create:async e=>await r.F.post("/api/ou/create",e),update:async(e,t)=>await r.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await r.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await r.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await r.F.get(`/api/ou/${e}/deletion-status`)}},40988:(e,t,a)=>{"use strict";a.d(t,{AM:()=>s,Wv:()=>d,hl:()=>i});var r=a(60687);a(43210);var n=a(63749),o=a(36966);function s({...e}){return(0,r.jsx)(n.bL,{"data-slot":"popover",...e})}function d({...e}){return(0,r.jsx)(n.l9,{"data-slot":"popover-trigger",...e})}function i({className:e,align:t="center",sideOffset:a=4,...s}){return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{"data-slot":"popover-content",align:t,sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...s})})}},42300:(e,t,a)=>{"use strict";a.d(t,{z:()=>o});var r=a(16189),n=a(43210);function o(){let e=(0,r.useRouter)(),t=(0,r.useSearchParams)(),a=(0,n.useCallback)(e=>{let a=new URLSearchParams(t.toString());return Object.entries(e).forEach(([e,t])=>{null===t?a.delete(e):a.set(e,t)}),a.toString()},[t]),o=(0,n.useCallback)((t,r)=>{let n=a(r);e.push(`${t}?${n}`,{scroll:!1})},[a,e]);return{createQueryString:a,updateUrl:o}}},48754:(e,t,a)=>{"use strict";a.d(t,{V:()=>p});var r=a(60687),n=a(43210),o=a(47033),s=a(14952),d=a(78272),i=a(99471),l=a(62141),c=a(36966),u=a(29523);function p({className:e,classNames:t,showOutsideDays:a=!0,captionLayout:n="label",buttonVariant:p="ghost",formatters:m,components:f,...h}){let x=(0,i.a)();return(0,r.jsx)(l.h,{showOutsideDays:a,className:(0,c.cn)("bg-background group/calendar p-3 [--cell-size:--spacing(8)] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent",String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,e),captionLayout:n,formatters:{formatMonthDropdown:e=>e.toLocaleString("en",{month:"short"}),...m},classNames:{root:(0,c.cn)("w-fit",x.root),months:(0,c.cn)("flex gap-4 flex-col md:flex-row relative",x.months),month:(0,c.cn)("flex flex-col w-full gap-4",x.month),nav:(0,c.cn)("flex items-center gap-1 w-full absolute top-0 inset-x-0 justify-between",x.nav),button_previous:(0,c.cn)((0,u.r)({variant:p}),"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none",x.button_previous),button_next:(0,c.cn)((0,u.r)({variant:p}),"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none",x.button_next),month_caption:(0,c.cn)("flex items-center justify-center h-(--cell-size) w-full px-(--cell-size)",x.month_caption),dropdowns:(0,c.cn)("w-full flex items-center text-sm font-medium justify-center h-(--cell-size) gap-1.5 text-foreground dark:text-foreground",x.dropdowns),dropdown_root:(0,c.cn)("relative has-focus:border-ring border border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] rounded-md bg-popover text-popover-foreground dark:bg-popover dark:text-popover-foreground [&>select]:bg-popover [&>select]:text-popover-foreground dark:[&>select]:bg-popover dark:[&>select]:text-popover-foreground",x.dropdown_root),dropdown:(0,c.cn)("absolute inset-0 opacity-0 bg-popover text-popover-foreground dark:bg-popover dark:text-popover-foreground",x.dropdown),caption_label:(0,c.cn)("select-none font-medium","label"===n?"text-sm":"rounded-md pl-2 pr-1 flex items-center gap-1 text-sm h-8 [&>svg]:text-muted-foreground [&>svg]:size-3.5",x.caption_label),table:"w-full border-collapse",weekdays:(0,c.cn)("flex",x.weekdays),weekday:(0,c.cn)("text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] select-none",x.weekday),week:(0,c.cn)("flex w-full mt-2",x.week),week_number_header:(0,c.cn)("select-none w-(--cell-size)",x.week_number_header),week_number:(0,c.cn)("text-[0.8rem] select-none text-muted-foreground",x.week_number),day:(0,c.cn)("relative w-full h-full p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md group/day aspect-square select-none",x.day),range_start:(0,c.cn)("rounded-l-md bg-accent",x.range_start),range_middle:(0,c.cn)("rounded-none",x.range_middle),range_end:(0,c.cn)("rounded-r-md bg-accent",x.range_end),today:(0,c.cn)("bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none",x.today),outside:(0,c.cn)("text-muted-foreground aria-selected:text-muted-foreground",x.outside),disabled:(0,c.cn)("text-muted-foreground opacity-50",x.disabled),hidden:(0,c.cn)("invisible",x.hidden),...t},components:{Root:({className:e,rootRef:t,...a})=>(0,r.jsx)("div",{"data-slot":"calendar",ref:t,className:(0,c.cn)(e),...a}),Chevron:({className:e,orientation:t,...a})=>"left"===t?(0,r.jsx)(o.A,{className:(0,c.cn)("size-4",e),...a}):"right"===t?(0,r.jsx)(s.A,{className:(0,c.cn)("size-4",e),...a}):(0,r.jsx)(d.A,{className:(0,c.cn)("size-4",e),...a}),DayButton:g,WeekNumber:({children:e,...t})=>(0,r.jsx)("td",{...t,children:(0,r.jsx)("div",{className:"flex size-(--cell-size) items-center justify-center text-center",children:e})}),...f},...h})}function g({className:e,day:t,modifiers:a,...o}){let s=(0,i.a)(),d=n.useRef(null);return n.useEffect(()=>{a.focused&&d.current?.focus()},[a.focused]),(0,r.jsx)(u.$,{ref:d,variant:"ghost",size:"icon","data-day":t.date.toLocaleDateString(),"data-selected-single":a.selected&&!a.range_start&&!a.range_end&&!a.range_middle,"data-range-start":a.range_start,"data-range-end":a.range_end,"data-range-middle":a.range_middle,className:(0,c.cn)("data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 dark:hover:text-accent-foreground flex aspect-square size-auto w-full min-w-(--cell-size) flex-col gap-1 leading-none font-normal group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] data-[range-end=true]:rounded-md data-[range-end=true]:rounded-r-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md data-[range-start=true]:rounded-l-md [&>span]:text-xs [&>span]:opacity-70",s.day,e),...o})}},53984:(e,t,a)=>{"use strict";a.d(t,{i:()=>i});var r=a(60687),n=a(4654),o=a(56476),s=a(29523),d=a(21342);function i({table:e}){return(0,r.jsxs)(d.rI,{children:[(0,r.jsx)(n.ty,{asChild:!0,children:(0,r.jsxs)(s.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,r.jsx)(o.A,{}),"View"]})}),(0,r.jsxs)(d.SQ,{align:"end",className:"w-[150px]",children:[(0,r.jsx)(d.lp,{children:"Toggle columns"}),(0,r.jsx)(d.mB,{}),e.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,r.jsx)(d.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},61018:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>g,Es:()=>f,HM:()=>u,L3:()=>h,c7:()=>m,lG:()=>i,rr:()=>x,zM:()=>l});var r=a(60687),n=a(43210),o=a(88562),s=a(11860),d=a(36966);let i=o.bL,l=o.l9,c=o.ZL,u=o.bm,p=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.hJ,{ref:a,className:(0,d.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));p.displayName=o.hJ.displayName;let g=n.forwardRef(({className:e,children:t,...a},n)=>(0,r.jsxs)(c,{children:[(0,r.jsx)(p,{}),(0,r.jsxs)(o.UC,{ref:n,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...a,children:[t,(0,r.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(s.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));g.displayName=o.UC.displayName;let m=({className:e,...t})=>(0,r.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let f=({className:e,...t})=>(0,r.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});f.displayName="DialogFooter";let h=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.hE,{ref:a,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=o.hE.displayName;let x=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.VY,{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=o.VY.displayName},64656:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(37413),n=a(48974),o=a(31057),s=a(50417),d=a(92588),i=a(61018),l=a(2505);function c({children:e}){return(0,r.jsx)(i.TenantGuard,{children:(0,r.jsx)(l.ChatProvider,{children:(0,r.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,r.jsxs)(s.SidebarProvider,{className:"flex flex-col",children:[(0,r.jsx)(o.SiteHeader,{}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)(n.AppSidebar,{}),(0,r.jsx)(s.SidebarInset,{children:(0,r.jsx)(d.SearchProvider,{children:(0,r.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>i});var r=a(60687),n=a(43210),o=a(16189),s=a(31599),d=a(31568);function i({children:e}){let{tenant:t}=(0,o.useParams)();(0,o.useRouter)();let{isAuthenticated:a,isLoading:i}=(0,d.A)(),{organizationUnits:l,isLoading:c}=(0,s.c)(),[u,p]=(0,n.useState)(!0);return i||c||u?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,r.jsx)(r.Fragment,{children:e})}},72826:(e,t,a)=>{Promise.resolve().then(a.bind(a,69231)),Promise.resolve().then(a.bind(a,83847)),Promise.resolve().then(a.bind(a,78526)),Promise.resolve().then(a.bind(a,97597)),Promise.resolve().then(a.bind(a,98641)),Promise.resolve().then(a.bind(a,80110))},83442:(e,t,a)=>{Promise.resolve().then(a.bind(a,61018)),Promise.resolve().then(a.bind(a,2505)),Promise.resolve().then(a.bind(a,92588)),Promise.resolve().then(a.bind(a,48974)),Promise.resolve().then(a.bind(a,31057)),Promise.resolve().then(a.bind(a,50417))}};