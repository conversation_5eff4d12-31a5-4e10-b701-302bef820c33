# Product Context

## Capstone Project Name
**English**: OpenAutomate - Multi-Tenant Business Process Automation Management & Orchestration Platform using ASP.NET Core API, Worker, Next.js, Python

**Vietnamese**: OpenAutomate - Nền tảng đa người thuê để quản lý và điều phối tự động hóa quy trình doanh nghiệp sử dụng ASP.NET Core API, Worker, Next.js, Python

**Abbreviation**: BPAMS (Business Process Automation Management Platform)

## Problem Statement
In today's business environment, automation processes have become essential for efficiency and competitiveness. Many organizations are dependent on commercial automation platforms that require substantial licensing costs, specialized training, and create vendor lock-in situations. These platforms often limit flexibility and scalability while requiring significant investment in proprietary technologies.

OpenAutomate addresses these challenges by providing an open-source alternative based on Python for business process automation management. By leveraging the accessibility and extensive library ecosystem of Python, organizations can significantly reduce costs while gaining greater control over their automation solutions. This approach eliminates vendor dependency and empowers teams to create, modify, and extend automation processes without the constraints of commercial licensing.

## Target Users
- IT Administrators responsible for automation infrastructure
- Business Analysts defining automation requirements
- Technical Managers overseeing automation projects and budgets
- Software Developers extending automation capabilities

## User Experience Goals
1. Provide intuitive interfaces for automation management without extensive training
2. Enable easy monitoring and troubleshooting of automation processes
3. **Simplify the deployment and configuration of automation agents** ✅ **ACHIEVED**
4. Support seamless upgrades and version management of automation packages
5. Facilitate clear visibility into automation performance and metrics

### **Enhanced Agent Configuration Experience** ✅ **PRODUCTION READY**
**Problem Solved**: Complex agent configuration requiring both frontend and backend URLs
**Solution Delivered**:
- **Single URL Configuration**: Users enter one orchestrator URL (e.g., `https://cloud.openautomate.me/my-tenant`)
- **Automatic Discovery**: Agent automatically discovers backend API for direct connection
- **Production Reliability**: Direct backend communication eliminates frontend dependency
- **Simplified Deployment**: Reduced infrastructure complexity for production environments
- **Backward Compatibility**: Existing configurations continue working with clear migration path

## Key Features
- User Authentication and Authorization
- Bot Agent Deployment and Registration
- Real-time Monitoring of Bot Activities
- Execution Logging and Reporting
- Centralized Configuration Management
- Package Distribution System
- Scheduling capabilities
- Status Notifications and Alerts
- Performance Analytics and Metrics
- Role-based Access Control
- Audit Trail and Activity History
- Multi-environment Support

## User Workflows
### Primary Workflow
1. Admin creates and configures automation package
2. Package is distributed to registered bot agents
3. Agents execute automation tasks based on schedule or triggers
4. Monitoring system tracks execution and collects results
5. Users review performance and outcomes through dashboard

### Secondary Workflows
- Bot agent registration and configuration
- User and role management
- Automation package development and testing
- Schedule configuration and management
- Alert and notification management

## Success Metrics
- Reduction in automation platform costs
- Decrease in time to deploy new automation processes
- Increased visibility and control over automation assets
- Improved reliability and performance of automation processes
- Greater adaptability to changing business requirements

## Competitive Analysis
### Competitors
- Commercial RPA platforms (UiPath, Automation Anywhere, Blue Prism)
- Enterprise workflow automation tools (Microsoft Power Automate, Zapier)
- Custom in-house automation solutions

### Our Differentiators
- Open-source with no licensing costs
- Python-based for extensive library access and community support
- Complete control over automation assets and infrastructure
- No vendor lock-in or proprietary technologies
- Flexible and customizable to specific organizational needs
- Centralized orchestration platform with distributed execution architecture
  - OpenAutomate hosts the central management components
  - Customers host and control their own execution agents
  - Provides centralized control while giving customers flexibility in scaling

## Implementation Approach
The platform implements multi-tenancy using the shared database with tenant filtering approach:

- A single database instance hosts data for all tenants
- Each tenant-specific entity includes a reference to its tenant (OrganizationUnit)
- Queries are automatically filtered by the current tenant using Entity Framework Core global query filters
- URL format follows the pattern: `domain.com/{tenant-slug}/api/resource`
- Tenant resolution is handled through middleware that extracts tenant information from the URL path 