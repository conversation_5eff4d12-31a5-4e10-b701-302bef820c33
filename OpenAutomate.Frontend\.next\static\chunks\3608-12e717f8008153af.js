"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3608],{13717:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},25487:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},27265:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},59629:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("power-off",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},76517:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},83856:(e,t,n)=>{n.d(t,{UC:()=>D,B8:()=>T,bL:()=>I,l9:()=>S});var r=n(12115);function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var a=n(95155),o=n(82353);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}var d=globalThis?.document?r.useLayoutEffect:()=>{},s=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[i,a]=r.useState(),o=r.useRef({}),l=r.useRef(e),u=r.useRef("none"),[s,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=c(o.current);u.current="mounted"===s?e:"none"},[s]),d(()=>{let t=o.current,n=l.current;if(n!==e){let r=u.current,i=c(t);e?f("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==i?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),d(()=>{if(i){var e;let t;let n=null!==(e=i.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=c(o.current).includes(e.animationName);if(e.target===i&&r&&(f("ANIMATION_END"),!l.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},a=e=>{e.target===i&&(u.current=c(o.current))};return i.addEventListener("animationstart",a),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",a),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(o.current=getComputedStyle(e)),a(e)},[])}}(t),a="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),o=function(...e){return r.useCallback(u(...e),e)}(i.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||i.isPresent?r.cloneElement(a,{ref:o}):null};function c(e){return(null==e?void 0:e.animationName)||"none"}s.displayName="Presence",n(47650);var f=Symbol("radix.slottable");function p(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===f}var m=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var a;let e,o;let l=(a=n,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,t){let n={...t};for(let r in t){let i=e[r],a=t[r];/^on[A-Z]/.test(r)?i&&a?n[r]=(...e)=>{a(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...a}:"className"===r&&(n[r]=[i,a].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(d.ref=t?u(t,l):l),r.cloneElement(n,d)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...o}=e,l=r.Children.toArray(i),u=l.find(p);if(u){let e=u.props.children,i=l.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,a.jsx)(t,{...o,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),y=r.createContext(void 0),h=n(12640),v=n(52496),b="Tabs",[w,g]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,d=n?.[e]?.[l]||o,s=r.useMemo(()=>u,Object.values(u));return(0,a.jsx)(d.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,a){let u=a?.[e]?.[l]||o,d=r.useContext(u);if(d)return d;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(i,...t)]}(b,[o.RG]),N=(0,o.RG)(),[x,k]=w(b),M=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:i,onValueChange:o,defaultValue:l,orientation:u="horizontal",dir:d,activationMode:s="automatic",...c}=e,f=function(e){let t=r.useContext(y);return e||t||"ltr"}(d),[p,w]=(0,h.i)({prop:i,onChange:o,defaultProp:null!=l?l:"",caller:b});return(0,a.jsx)(x,{scope:n,baseId:(0,v.B)(),value:p,onValueChange:w,orientation:u,dir:f,activationMode:s,children:(0,a.jsx)(m.div,{dir:f,"data-orientation":u,...c,ref:t})})});M.displayName=b;var C="TabsList",E=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...i}=e,l=k(C,n),u=N(n);return(0,a.jsx)(o.bL,{asChild:!0,...u,orientation:l.orientation,dir:l.dir,loop:r,children:(0,a.jsx)(m.div,{role:"tablist","aria-orientation":l.orientation,...i,ref:t})})});E.displayName=C;var j="TabsTrigger",R=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:l=!1,...u}=e,d=k(j,n),s=N(n),c=_(d.baseId,r),f=P(d.baseId,r),p=r===d.value;return(0,a.jsx)(o.q7,{asChild:!0,...s,focusable:!l,active:p,children:(0,a.jsx)(m.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":f,"data-state":p?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:c,...u,ref:t,onMouseDown:i(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:i(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:i(e.onFocus,()=>{let e="manual"!==d.activationMode;p||l||!e||d.onValueChange(r)})})})});R.displayName=j;var A="TabsContent",O=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:i,forceMount:o,children:l,...u}=e,d=k(A,n),c=_(d.baseId,i),f=P(d.baseId,i),p=i===d.value,y=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(s,{present:o||p,children:n=>{let{present:r}=n;return(0,a.jsx)(m.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:y.current?"0s":void 0},children:r&&l})}})});function _(e,t){return"".concat(e,"-trigger-").concat(t)}function P(e,t){return"".concat(e,"-content-").concat(t)}O.displayName=A;var I=M,T=E,S=R,D=O},94449:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},98627:(e,t,n)=>{n.d(t,{bL:()=>k,zi:()=>M});var r=n(12115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return r.useCallback(function(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}(...e),e)}var o=n(95155),l=n(12640),u=n(45503),d=n(47602);n(47650);var s=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{var n,i,o;let l,u;let{children:d,...s}=e,c=a(r.isValidElement(d)?(u=(l=null===(i=Object.getOwnPropertyDescriptor((n=d).props,"ref"))||void 0===i?void 0:i.get)&&"isReactWarning"in l&&l.isReactWarning)?n.ref:(u=(l=null===(o=Object.getOwnPropertyDescriptor(n,"ref"))||void 0===o?void 0:o.get)&&"isReactWarning"in l&&l.isReactWarning)?n.props.ref:n.props.ref||n.ref:void 0,t);if(r.isValidElement(d)){let e=function(e,t){let n={...t};for(let r in t){let i=e[r],a=t[r];/^on[A-Z]/.test(r)?i&&a?n[r]=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=a(...t);return i(...t),r}:i&&(n[r]=i):"style"===r?n[r]={...i,...a}:"className"===r&&(n[r]=[i,a].filter(Boolean).join(" "))}return{...e,...n}}(s,d.props);return d.type!==r.Fragment&&(e.ref=c),r.cloneElement(d,e)}return r.Children.count(d)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,n)=>{let{children:i,...a}=e,l=r.Children.toArray(i),u=l.find(c);if(u){let e=u.props.children,i=l.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...a,ref:n,children:i})});return n.displayName="".concat(e,".Slot"),n}(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?n:t,{...a,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),p="Switch",[m,y]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,d=n?.[e]?.[l]||a,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(d.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,d=r.useContext(u);if(d)return d;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(i,...t)]}(p),[h,v]=m(p),b=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:i,checked:u,defaultChecked:d,required:s,disabled:c,value:m="on",onCheckedChange:y,form:v,...b}=e,[w,g]=r.useState(null),k=a(t,e=>g(e)),M=r.useRef(!1),C=!w||v||!!w.closest("form"),[E,j]=(0,l.i)({prop:u,defaultProp:null!=d&&d,onChange:y,caller:p});return(0,o.jsxs)(h,{scope:n,checked:E,disabled:c,children:[(0,o.jsx)(f.button,{type:"button",role:"switch","aria-checked":E,"aria-required":s,"data-state":x(E),"data-disabled":c?"":void 0,disabled:c,value:m,...b,ref:k,onClick:function(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}(e.onClick,e=>{j(e=>!e),C&&(M.current=e.isPropagationStopped(),M.current||e.stopPropagation())})}),C&&(0,o.jsx)(N,{control:w,bubbles:!M.current,name:i,value:m,checked:E,required:s,disabled:c,form:v,style:{transform:"translateX(-100%)"}})]})});b.displayName=p;var w="SwitchThumb",g=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,i=v(w,n);return(0,o.jsx)(f.span,{"data-state":x(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:t})});g.displayName=w;var N=r.forwardRef((e,t)=>{let{__scopeSwitch:n,control:i,checked:l,bubbles:s=!0,...c}=e,f=r.useRef(null),p=a(f,t),m=(0,u.Z)(l),y=(0,d.X)(i);return r.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==l&&t){let n=new Event("click",{bubbles:s});t.call(e,l),e.dispatchEvent(n)}},[m,l,s]),(0,o.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...c,tabIndex:-1,ref:p,style:{...c.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}N.displayName="SwitchBubbleInput";var k=b,M=g}}]);