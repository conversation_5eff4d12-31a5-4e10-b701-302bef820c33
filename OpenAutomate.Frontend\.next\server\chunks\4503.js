"use strict";exports.id=4503,exports.ids=[4503],exports.modules={1303:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},46950:(e,t,n)=>{var r,o,a,i;n.d(t,{UI:()=>r,X5:()=>i,pL:()=>o,wc:()=>a}),function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.Caption<PERSON>abel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"}(r||(r={})),function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"}(o||(o={})),function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"}(a||(a={})),function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"}(i||(i={}))},62141:(e,t,n)=>{n.d(t,{h:()=>tt});var r,o={};n.r(o),n.d(o,{Button:()=>ee,CaptionLabel:()=>et,Chevron:()=>en,Day:()=>er,DayButton:()=>eo,Dropdown:()=>ea,DropdownNav:()=>ei,Footer:()=>es,Month:()=>el,MonthCaption:()=>ed,MonthGrid:()=>eu,Months:()=>ec,MonthsDropdown:()=>em,Nav:()=>ep,NextMonthButton:()=>ey,Option:()=>ev,PreviousMonthButton:()=>eg,Root:()=>eb,Select:()=>ew,Week:()=>eM,WeekNumber:()=>eN,WeekNumberHeader:()=>eC,Weekday:()=>ek,Weekdays:()=>eD,Weeks:()=>eW,YearsDropdown:()=>eO});var a={};n.r(a),n.d(a,{formatCaption:()=>eS,formatDay:()=>eT,formatMonthCaption:()=>eI,formatMonthDropdown:()=>ex,formatWeekNumber:()=>eU,formatWeekNumberHeader:()=>eP,formatWeekdayName:()=>e_,formatYearCaption:()=>eA,formatYearDropdown:()=>eL});var i={};n.r(i),n.d(i,{labelCaption:()=>eY,labelDay:()=>eZ,labelDayButton:()=>eR,labelGrid:()=>eF,labelGridcell:()=>ej,labelMonthDropdown:()=>e$,labelNav:()=>eB,labelNext:()=>eH,labelPrevious:()=>eq,labelWeekNumber:()=>eX,labelWeekNumberHeader:()=>eG,labelWeekday:()=>ez,labelYearDropdown:()=>eJ});var s=n(43210);Symbol.for("constructDateFrom");let l={},d={};function u(e,t){try{let n=(l[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";if(n in d)return d[n];return f(n,n.split(":"))}catch{if(e in d)return d[e];let t=e?.match(c);if(t)return f(e,t.slice(1));return NaN}}let c=/([+-]\d\d):?(\d\d)?/;function f(e,t){let n=+t[0],r=+(t[1]||0);return d[e]=n>0?60*n+r:60*n-r}class h extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(u(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),y(this,NaN),p(this)):this.setTime(Date.now())}static tz(e,...t){return t.length?new h(...t,e):new h(Date.now(),e)}withTimeZone(e){return new h(+this,e)}getTimezoneOffset(){return-u(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),p(this),+this}[Symbol.for("constructDateFrom")](e){return new h(+new Date(e),this.timeZone)}}let m=/^(get|set)(?!UTC)/;function p(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function y(e){let t=u(e.timeZone,e),n=new Date(+e);n.setUTCHours(n.getUTCHours()-1);let r=-new Date(+e).getTimezoneOffset(),o=r- -new Date(+n).getTimezoneOffset(),a=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();o&&a&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+o);let i=r-t;i&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+i);let s=u(e.timeZone,e),l=-new Date(+e).getTimezoneOffset()-s-i;if(s!==t&&l){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);let t=s-u(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!m.test(e))return;let t=e.replace(m,"$1UTC");h.prototype[t]&&(e.startsWith("get")?h.prototype[e]=function(){return this.internal[t]()}:(h.prototype[e]=function(){var e;return Date.prototype[t].apply(this.internal,arguments),e=this,Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),y(e),+this},h.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),p(this),+this}))});class v extends h{static tz(e,...t){return t.length?new v(...t,e):new v(Date.now(),e)}toISOString(){let[e,t,n]=this.tzComponents(),r=`${e}${t}:${n}`;return this.internal.toISOString().slice(0,-1)+r}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[e,t,n,r]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${n} ${t} ${r}`}toTimeString(){var e,t;let n=this.internal.toUTCString().split(" ")[4],[r,o,a]=this.tzComponents();return`${n} GMT${r}${o}${a} (${e=this.timeZone,t=this,new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){let e=this.getTimezoneOffset(),t=String(Math.floor(Math.abs(e)/60)).padStart(2,"0"),n=String(Math.abs(e)%60).padStart(2,"0");return[e>0?"-":"+",t,n]}withTimeZone(e){return new v(+this,e)}[Symbol.for("constructDateFrom")](e){return new v(+new Date(e),this.timeZone)}}var g=n(46950);let b={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function w(e){return (t={})=>{let n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}let M={date:w({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:w({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:w({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},k={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function D(e){return(t,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,o=n?.width?String(n.width):t;r=e.formattingValues[o]||e.formattingValues[t]}else{let t=e.defaultWidth,o=n?.width?String(n.width):e.defaultWidth;r=e.values[o]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function N(e){return(t,n={})=>{let r;let o=n.width,a=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],i=t.match(a);if(!i)return null;let s=i[0],l=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(l,e=>e.test(s)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(l,e=>e.test(s));return r=e.valueCallback?e.valueCallback(d):d,{value:r=n.valueCallback?n.valueCallback(r):r,rest:t.slice(s.length)}}}let C={code:"en-US",formatDistance:(e,t,n)=>{let r;let o=b[e];return(r="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),n?.addSuffix)?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:M,formatRelative:(e,t,n,r)=>k[e],localize:{ordinalNumber:(e,t)=>{let n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:D({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:D({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:D({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:D({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:D({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,n={})=>{let r=t.match(e.matchPattern);if(!r)return null;let o=r[0],a=t.match(e.parsePattern);if(!a)return null;let i=e.valueCallback?e.valueCallback(a[0]):a[0];return{value:i=n.valueCallback?n.valueCallback(i):i,rest:t.slice(o.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:N({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:N({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:N({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:N({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:N({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};var W=n(47138),O=n(35780);function E(e,t){let n=(0,W.a)(e);return isNaN(t)?(0,O.w)(e,NaN):(t&&n.setDate(n.getDate()+t),n)}function S(e,t){let n=(0,W.a)(e);if(isNaN(t))return(0,O.w)(e,NaN);if(!t)return n;let r=n.getDate(),o=(0,O.w)(e,n.getTime());return(o.setMonth(n.getMonth()+t+1,0),r>=o.getDate())?o:(n.setFullYear(o.getFullYear(),o.getMonth(),r),n)}var I=n(89106),T=n(79186),x=n(9903);function U(e,t){let n=(0,x.q)(),r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,o=(0,W.a)(e),a=o.getDay();return o.setDate(o.getDate()+((a<r?-7:0)+6-(a-r))),o.setHours(23,59,59,999),o}var P=n(46127),_=n(73437),L=n(88838),A=n(96305),F=n(32637),Y=n(37074),j=n(26843),R=n(33660),Z=n(95519);function B(e,t){let n=t.startOfMonth(e),r=n.getDay();return 1===r?n:0===r?t.addDays(n,-6):t.addDays(n,-1*(r-1))}class ${constructor(e,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?v.tz(this.options.timeZone):new this.Date,this.newDate=(e,t,n)=>this.overrides?.newDate?this.overrides.newDate(e,t,n):this.options.timeZone?new v(e,t,n,this.options.timeZone):new Date(e,t,n),this.addDays=(e,t)=>this.overrides?.addDays?this.overrides.addDays(e,t):E(e,t),this.addMonths=(e,t)=>this.overrides?.addMonths?this.overrides.addMonths(e,t):S(e,t),this.addWeeks=(e,t)=>this.overrides?.addWeeks?this.overrides.addWeeks(e,t):E(e,7*t),this.addYears=(e,t)=>this.overrides?.addYears?this.overrides.addYears(e,t):S(e,12*t),this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(e,t):(0,I.m)(e,t),this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(e,t):(0,T.U)(e,t),this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(e):function(e,t){let n=(0,W.a)(e.start),r=(0,W.a)(e.end),o=+n>+r,a=o?+n:+r,i=o?r:n;i.setHours(0,0,0,0),i.setDate(1);let s=(void 0)??1;if(!s)return[];s<0&&(s=-s,o=!o);let l=[];for(;+i<=a;)l.push((0,W.a)(i)),i.setMonth(i.getMonth()+s);return o?l.reverse():l}(e),this.endOfBroadcastWeek=e=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(e):function(e,t){let n=B(e,t),r=function(e,t){let n=t.startOfMonth(e),r=n.getDay()>0?n.getDay():7,o=t.addDays(e,-r+1),a=t.addDays(o,34);return t.getMonth(e)===t.getMonth(a)?5:4}(e,t);return t.addDays(n,7*r-1)}(e,this),this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(e):U(e,{weekStartsOn:1}),this.endOfMonth=e=>this.overrides?.endOfMonth?this.overrides.endOfMonth(e):(0,P.p)(e),this.endOfWeek=(e,t)=>this.overrides?.endOfWeek?this.overrides.endOfWeek(e,t):U(e,this.options),this.endOfYear=e=>this.overrides?.endOfYear?this.overrides.endOfYear(e):function(e){let t=(0,W.a)(e),n=t.getFullYear();return t.setFullYear(n+1,0,0),t.setHours(23,59,59,999),t}(e),this.format=(e,t,n)=>{let r=this.overrides?.format?this.overrides.format(e,t,this.options):(0,_.GP)(e,t,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(r):r},this.getISOWeek=e=>this.overrides?.getISOWeek?this.overrides.getISOWeek(e):(0,L.s)(e),this.getMonth=(e,t)=>this.overrides?.getMonth?this.overrides.getMonth(e,this.options):(this.options,(0,W.a)(e).getMonth()),this.getYear=(e,t)=>this.overrides?.getYear?this.overrides.getYear(e,this.options):(this.options,(0,W.a)(e).getFullYear()),this.getWeek=(e,t)=>this.overrides?.getWeek?this.overrides.getWeek(e,this.options):(0,A.N)(e,this.options),this.isAfter=(e,t)=>this.overrides?.isAfter?this.overrides.isAfter(e,t):function(e,t){let n=(0,W.a)(e),r=(0,W.a)(t);return n.getTime()>r.getTime()}(e,t),this.isBefore=(e,t)=>{var n,r;return this.overrides?.isBefore?this.overrides.isBefore(e,t):(n=e,r=t,+(0,W.a)(n)<+(0,W.a)(r))},this.isDate=e=>this.overrides?.isDate?this.overrides.isDate(e):(0,F.$)(e),this.isSameDay=(e,t)=>{var n,r;return this.overrides?.isSameDay?this.overrides.isSameDay(e,t):(n=e,r=t,+(0,Y.o)(n)==+(0,Y.o)(r))},this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(e,t):function(e,t){let n=(0,W.a)(e),r=(0,W.a)(t);return n.getFullYear()===r.getFullYear()&&n.getMonth()===r.getMonth()}(e,t),this.isSameYear=(e,t)=>this.overrides?.isSameYear?this.overrides.isSameYear(e,t):function(e,t){let n=(0,W.a)(e),r=(0,W.a)(t);return n.getFullYear()===r.getFullYear()}(e,t),this.max=e=>this.overrides?.max?this.overrides.max(e):function(e){let t;return e.forEach(function(e){let n=(0,W.a)(e);(void 0===t||t<n||isNaN(Number(n)))&&(t=n)}),t||new Date(NaN)}(e),this.min=e=>this.overrides?.min?this.overrides.min(e):function(e){let t;return e.forEach(e=>{let n=(0,W.a)(e);(!t||t>n||isNaN(+n))&&(t=n)}),t||new Date(NaN)}(e),this.setMonth=(e,t)=>this.overrides?.setMonth?this.overrides.setMonth(e,t):function(e,t){let n=(0,W.a)(e),r=n.getFullYear(),o=n.getDate(),a=(0,O.w)(e,0);a.setFullYear(r,t,15),a.setHours(0,0,0,0);let i=function(e){let t=(0,W.a)(e),n=t.getFullYear(),r=t.getMonth(),o=(0,O.w)(e,0);return o.setFullYear(n,r+1,0),o.setHours(0,0,0,0),o.getDate()}(a);return n.setMonth(t,Math.min(o,i)),n}(e,t),this.setYear=(e,t)=>this.overrides?.setYear?this.overrides.setYear(e,t):function(e,t){let n=(0,W.a)(e);return isNaN(+n)?(0,O.w)(e,NaN):(n.setFullYear(t),n)}(e,t),this.startOfBroadcastWeek=(e,t)=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(e,this):B(e,this),this.startOfDay=e=>this.overrides?.startOfDay?this.overrides.startOfDay(e):(0,Y.o)(e),this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(e):(0,j.b)(e),this.startOfMonth=e=>this.overrides?.startOfMonth?this.overrides.startOfMonth(e):function(e){let t=(0,W.a)(e);return t.setDate(1),t.setHours(0,0,0,0),t}(e),this.startOfWeek=(e,t)=>this.overrides?.startOfWeek?this.overrides.startOfWeek(e,this.options):(0,R.k)(e,this.options),this.startOfYear=e=>this.overrides?.startOfYear?this.overrides.startOfYear(e):(0,Z.D)(e),this.options={locale:C,...e},this.overrides=t}getDigitMap(){let{numerals:e="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:e}),n={};for(let e=0;e<10;e++)n[e.toString()]=t.format(e);return n}replaceDigits(e){let t=this.getDigitMap();return e.replace(/\d/g,e=>t[e]||e)}formatNumber(e){return this.replaceDigits(e.toString())}}let H=new $;function q(e,t,n=!1,r=H){let{from:o,to:a}=e,{differenceInCalendarDays:i,isSameDay:s}=r;return o&&a?(0>i(a,o)&&([o,a]=[a,o]),i(t,o)>=+!!n&&i(a,t)>=+!!n):!n&&a?s(a,t):!n&&!!o&&s(o,t)}function z(e){return!!(e&&"object"==typeof e&&"before"in e&&"after"in e)}function X(e){return!!(e&&"object"==typeof e&&"from"in e)}function G(e){return!!(e&&"object"==typeof e&&"after"in e)}function J(e){return!!(e&&"object"==typeof e&&"before"in e)}function V(e){return!!(e&&"object"==typeof e&&"dayOfWeek"in e)}function K(e,t){return Array.isArray(e)&&e.every(t.isDate)}function Q(e,t,n=H){let r=Array.isArray(t)?t:[t],{isSameDay:o,differenceInCalendarDays:a,isAfter:i}=n;return r.some(t=>{if("boolean"==typeof t)return t;if(n.isDate(t))return o(e,t);if(K(t,n))return t.includes(e);if(X(t))return q(t,e,!1,n);if(V(t))return Array.isArray(t.dayOfWeek)?t.dayOfWeek.includes(e.getDay()):t.dayOfWeek===e.getDay();if(z(t)){let n=a(t.before,e),r=a(t.after,e),o=n>0,s=r<0;return i(t.before,t.after)?s&&o:o||s}return G(t)?a(e,t.after)>0:J(t)?a(t.before,e)>0:"function"==typeof t&&t(e)})}function ee(e){return s.createElement("button",{...e})}function et(e){return s.createElement("span",{...e})}function en(e){let{size:t=24,orientation:n="left",className:r}=e;return s.createElement("svg",{className:r,width:t,height:t,viewBox:"0 0 24 24"},"up"===n&&s.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===n&&s.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===n&&s.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===n&&s.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function er(e){let{day:t,modifiers:n,...r}=e;return s.createElement("td",{...r})}function eo(e){let{day:t,modifiers:n,...r}=e,o=s.useRef(null);return s.useEffect(()=>{n.focused&&o.current?.focus()},[n.focused]),s.createElement("button",{ref:o,...r})}function ea(e){let{options:t,className:n,components:r,classNames:o,...a}=e,i=[o[g.UI.Dropdown],n].join(" "),l=t?.find(({value:e})=>e===a.value);return s.createElement("span",{"data-disabled":a.disabled,className:o[g.UI.DropdownRoot]},s.createElement(r.Select,{className:i,...a},t?.map(({value:e,label:t,disabled:n})=>s.createElement(r.Option,{key:e,value:e,disabled:n},t))),s.createElement("span",{className:o[g.UI.CaptionLabel],"aria-hidden":!0},l?.label,s.createElement(r.Chevron,{orientation:"down",size:18,className:o[g.UI.Chevron]})))}function ei(e){return s.createElement("div",{...e})}function es(e){return s.createElement("div",{...e})}function el(e){let{calendarMonth:t,displayIndex:n,...r}=e;return s.createElement("div",{...r},e.children)}function ed(e){let{calendarMonth:t,displayIndex:n,...r}=e;return s.createElement("div",{...r})}function eu(e){return s.createElement("table",{...e})}function ec(e){return s.createElement("div",{...e})}let ef=(0,s.createContext)(void 0);function eh(){let e=(0,s.useContext)(ef);if(void 0===e)throw Error("useDayPicker() must be used within a custom component.");return e}function em(e){let{components:t}=eh();return s.createElement(t.Dropdown,{...e})}function ep(e){let{onPreviousClick:t,onNextClick:n,previousMonth:r,nextMonth:o,...a}=e,{components:i,classNames:l,labels:{labelPrevious:d,labelNext:u}}=eh(),c=(0,s.useCallback)(e=>{o&&n?.(e)},[o,n]),f=(0,s.useCallback)(e=>{r&&t?.(e)},[r,t]);return s.createElement("nav",{...a},s.createElement(i.PreviousMonthButton,{type:"button",className:l[g.UI.PreviousMonthButton],tabIndex:r?void 0:-1,"aria-disabled":!r||void 0,"aria-label":d(r),onClick:f},s.createElement(i.Chevron,{disabled:!r||void 0,className:l[g.UI.Chevron],orientation:"left"})),s.createElement(i.NextMonthButton,{type:"button",className:l[g.UI.NextMonthButton],tabIndex:o?void 0:-1,"aria-disabled":!o||void 0,"aria-label":u(o),onClick:c},s.createElement(i.Chevron,{disabled:!o||void 0,orientation:"right",className:l[g.UI.Chevron]})))}function ey(e){let{components:t}=eh();return s.createElement(t.Button,{...e})}function ev(e){return s.createElement("option",{...e})}function eg(e){let{components:t}=eh();return s.createElement(t.Button,{...e})}function eb(e){let{rootRef:t,...n}=e;return s.createElement("div",{...n,ref:t})}function ew(e){return s.createElement("select",{...e})}function eM(e){let{week:t,...n}=e;return s.createElement("tr",{...n})}function ek(e){return s.createElement("th",{...e})}function eD(e){return s.createElement("thead",{"aria-hidden":!0},s.createElement("tr",{...e}))}function eN(e){let{week:t,...n}=e;return s.createElement("th",{...n})}function eC(e){return s.createElement("th",{...e})}function eW(e){return s.createElement("tbody",{...e})}function eO(e){let{components:t}=eh();return s.createElement(t.Dropdown,{...e})}var eE=n(99471);function eS(e,t,n){return(n??new $(t)).format(e,"LLLL y")}let eI=eS;function eT(e,t,n){return(n??new $(t)).format(e,"d")}function ex(e,t=H){return t.format(e,"LLLL")}function eU(e,t=H){return e<10?t.formatNumber(`0${e.toLocaleString()}`):t.formatNumber(`${e.toLocaleString()}`)}function eP(){return""}function e_(e,t,n){return(n??new $(t)).format(e,"cccccc")}function eL(e,t=H){return t.format(e,"yyyy")}let eA=eL;function eF(e,t,n){return(n??new $(t)).format(e,"LLLL y")}let eY=eF;function ej(e,t,n,r){let o=(r??new $(n)).format(e,"PPPP");return t?.today&&(o=`Today, ${o}`),o}function eR(e,t,n,r){let o=(r??new $(n)).format(e,"PPPP");return t.today&&(o=`Today, ${o}`),t.selected&&(o=`${o}, selected`),o}let eZ=eR;function eB(){return""}function e$(e){return"Choose the Month"}function eH(e){return"Go to the Next Month"}function eq(e){return"Go to the Previous Month"}function ez(e,t,n){return(n??new $(t)).format(e,"cccc")}function eX(e,t){return`Week ${e}`}function eG(e){return"Week Number"}function eJ(e){return"Choose the Year"}let eV=e=>e instanceof HTMLElement?e:null,eK=e=>[...e.querySelectorAll("[data-animated-month]")??[]],eQ=e=>eV(e.querySelector("[data-animated-month]")),e0=e=>eV(e.querySelector("[data-animated-caption]")),e1=e=>eV(e.querySelector("[data-animated-weeks]")),e2=e=>eV(e.querySelector("[data-animated-nav]")),e3=e=>eV(e.querySelector("[data-animated-weekdays]"));function e8(e,t){let{month:n,defaultMonth:r,today:o=t.today(),numberOfMonths:a=1,endMonth:i,startMonth:s}=e,l=n||r||o,{differenceInCalendarMonths:d,addMonths:u,startOfMonth:c}=t;return i&&0>d(i,l)&&(l=u(i,-1*(a-1))),s&&0>d(l,s)&&(l=s),c(l)}class e4{constructor(e,t,n=H){this.date=e,this.displayMonth=t,this.outside=!!(t&&!n.isSameMonth(e,t)),this.dateLib=n}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class e5{constructor(e,t){this.days=t,this.weekNumber=e}}class e9{constructor(e,t){this.date=e,this.weeks=t}}function e6(e,t){let[n,r]=(0,s.useState)(e);return[void 0===t?n:t,r]}function e7(e){return!e[g.pL.disabled]&&!e[g.pL.hidden]&&!e[g.pL.outside]}function te(e,t,n=H){return q(e,t.from,!1,n)||q(e,t.to,!1,n)||q(t,e.from,!1,n)||q(t,e.to,!1,n)}function tt(e){let t=e;t.timeZone&&((t={...e}).today&&(t.today=new v(t.today,t.timeZone)),t.month&&(t.month=new v(t.month,t.timeZone)),t.defaultMonth&&(t.defaultMonth=new v(t.defaultMonth,t.timeZone)),t.startMonth&&(t.startMonth=new v(t.startMonth,t.timeZone)),t.endMonth&&(t.endMonth=new v(t.endMonth,t.timeZone)),"single"===t.mode&&t.selected?t.selected=new v(t.selected,t.timeZone):"multiple"===t.mode&&t.selected?t.selected=t.selected?.map(e=>new v(e,t.timeZone)):"range"===t.mode&&t.selected&&(t.selected={from:t.selected.from?new v(t.selected.from,t.timeZone):void 0,to:t.selected.to?new v(t.selected.to,t.timeZone):void 0}));let{components:n,formatters:l,labels:d,dateLib:u,locale:c,classNames:f}=(0,s.useMemo)(()=>{var e,n;let r={...C,...t.locale};return{dateLib:new $({locale:r,weekStartsOn:t.broadcastCalendar?1:t.weekStartsOn,firstWeekContainsDate:t.firstWeekContainsDate,useAdditionalWeekYearTokens:t.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:t.useAdditionalDayOfYearTokens,timeZone:t.timeZone,numerals:t.numerals},t.dateLib),components:(e=t.components,{...o,...e}),formatters:(n=t.formatters,n?.formatMonthCaption&&!n.formatCaption&&(n.formatCaption=n.formatMonthCaption),n?.formatYearCaption&&!n.formatYearDropdown&&(n.formatYearDropdown=n.formatYearCaption),{...a,...n}),labels:{...i,...t.labels},locale:r,classNames:{...(0,eE.a)(),...t.classNames}}},[t.locale,t.broadcastCalendar,t.weekStartsOn,t.firstWeekContainsDate,t.useAdditionalWeekYearTokens,t.useAdditionalDayOfYearTokens,t.timeZone,t.numerals,t.dateLib,t.components,t.formatters,t.labels,t.classNames]),{captionLayout:h,mode:m,navLayout:p,numberOfMonths:y=1,onDayBlur:b,onDayClick:w,onDayFocus:M,onDayKeyDown:k,onDayMouseEnter:D,onDayMouseLeave:N,onNextClick:W,onPrevClick:O,showWeekNumber:E,styles:S}=t,{formatCaption:I,formatDay:T,formatMonthDropdown:x,formatWeekNumber:U,formatWeekNumberHeader:P,formatWeekdayName:_,formatYearDropdown:L}=l,A=function(e,t){let[n,r]=function(e,t){let{startMonth:n,endMonth:r}=e,{startOfYear:o,startOfDay:a,startOfMonth:i,endOfMonth:s,addYears:l,endOfYear:d,newDate:u,today:c}=t,{fromYear:f,toYear:h,fromMonth:m,toMonth:p}=e;!n&&m&&(n=m),!n&&f&&(n=t.newDate(f,0,1)),!r&&p&&(r=p),!r&&h&&(r=u(h,11,31));let y="dropdown"===e.captionLayout||"dropdown-years"===e.captionLayout;return n?n=i(n):f?n=u(f,0,1):!n&&y&&(n=o(l(e.today??c(),-100))),r?r=s(r):h?r=u(h,11,31):!r&&y&&(r=d(e.today??c())),[n?a(n):n,r?a(r):r]}(e,t),{startOfMonth:o,endOfMonth:a}=t,i=e8(e,t),[l,d]=e6(i,e.month?i:void 0);(0,s.useEffect)(()=>{d(e8(e,t))},[e.timeZone]);let u=function(e,t,n,r){let{numberOfMonths:o=1}=n,a=[];for(let n=0;n<o;n++){let o=r.addMonths(e,n);if(t&&o>t)break;a.push(o)}return a}(l,r,e,t),c=function(e,t,n,r){let o=e[0],a=e[e.length-1],{ISOWeek:i,fixedWeeks:s,broadcastCalendar:l}=n??{},{addDays:d,differenceInCalendarDays:u,differenceInCalendarMonths:c,endOfBroadcastWeek:f,endOfISOWeek:h,endOfMonth:m,endOfWeek:p,isAfter:y,startOfBroadcastWeek:v,startOfISOWeek:g,startOfWeek:b}=r,w=l?v(o,r):i?g(o):b(o),M=u(l?f(a):i?h(m(a)):p(m(a)),w),k=c(a,o)+1,D=[];for(let e=0;e<=M;e++){let n=d(w,e);if(t&&y(n,t))break;D.push(n)}let N=(l?35:42)*k;if(s&&D.length<N){let e=N-D.length;for(let t=0;t<e;t++){let e=d(D[D.length-1],1);D.push(e)}}return D}(u,e.endMonth?a(e.endMonth):void 0,e,t),f=function(e,t,n,r){let{addDays:o,endOfBroadcastWeek:a,endOfISOWeek:i,endOfMonth:s,endOfWeek:l,getISOWeek:d,getWeek:u,startOfBroadcastWeek:c,startOfISOWeek:f,startOfWeek:h}=r,m=e.reduce((e,m)=>{let p=n.broadcastCalendar?c(m,r):n.ISOWeek?f(m):h(m),y=n.broadcastCalendar?a(m):n.ISOWeek?i(s(m)):l(s(m)),v=t.filter(e=>e>=p&&e<=y),g=n.broadcastCalendar?35:42;if(n.fixedWeeks&&v.length<g){let e=t.filter(e=>{let t=g-v.length;return e>y&&e<=o(y,t)});v.push(...e)}let b=v.reduce((e,t)=>{let o=n.ISOWeek?d(t):u(t),a=e.find(e=>e.weekNumber===o),i=new e4(t,m,r);return a?a.days.push(i):e.push(new e5(o,[i])),e},[]),w=new e9(m,b);return e.push(w),e},[]);return n.reverseMonths?m.reverse():m}(u,c,e,t),h=f.reduce((e,t)=>[...e,...t.weeks],[]),m=function(e){let t=[];return e.reduce((e,n)=>[...e,...n.weeks.reduce((e,t)=>[...e,...t.days],t)],t)}(f),p=function(e,t,n,r){if(n.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:a}=n,{startOfMonth:i,addMonths:s,differenceInCalendarMonths:l}=r,d=o?a??1:1,u=i(e);if(!t||!(0>=l(u,t)))return s(u,-d)}(l,n,e,t),y=function(e,t,n,r){if(n.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:a=1}=n,{startOfMonth:i,addMonths:s,differenceInCalendarMonths:l}=r,d=o?a:1,u=i(e);if(!t||!(l(t,e)<a))return s(u,d)}(l,r,e,t),{disableNavigation:v,onMonthChange:g}=e,b=e=>h.some(t=>t.days.some(t=>t.isEqualTo(e))),w=e=>{if(v)return;let t=o(e);n&&t<o(n)&&(t=o(n)),r&&t>o(r)&&(t=o(r)),d(t),g?.(t)};return{months:f,weeks:h,days:m,navStart:n,navEnd:r,previousMonth:p,nextMonth:y,goToMonth:w,goToDay:e=>{!b(e)&&w(e.date)}}}(t,u),{days:F,months:Y,navStart:j,navEnd:R,previousMonth:Z,nextMonth:B,goToMonth:ee}=A,et=function(e,t,n){let{disabled:r,hidden:o,modifiers:a,showOutsideDays:i,broadcastCalendar:s,today:l}=t,{isSameDay:d,isSameMonth:u,startOfMonth:c,isBefore:f,endOfMonth:h,isAfter:m}=n,p=t.startMonth&&c(t.startMonth),y=t.endMonth&&h(t.endMonth),v={[g.pL.focused]:[],[g.pL.outside]:[],[g.pL.disabled]:[],[g.pL.hidden]:[],[g.pL.today]:[]},b={};for(let t of e){let{date:e,displayMonth:c}=t,h=!!(c&&!u(e,c)),g=!!(p&&f(e,p)),w=!!(y&&m(e,y)),M=!!(r&&Q(e,r,n)),k=!!(o&&Q(e,o,n))||g||w||!s&&!i&&h||s&&!1===i&&h,D=d(e,l??n.today());h&&v.outside.push(t),M&&v.disabled.push(t),k&&v.hidden.push(t),D&&v.today.push(t),a&&Object.keys(a).forEach(r=>{let o=a?.[r];o&&Q(e,o,n)&&(b[r]?b[r].push(t):b[r]=[t])})}return e=>{let t={[g.pL.focused]:!1,[g.pL.disabled]:!1,[g.pL.hidden]:!1,[g.pL.outside]:!1,[g.pL.today]:!1},n={};for(let n in v){let r=v[n];t[n]=r.some(t=>t===e)}for(let t in b)n[t]=b[t].some(t=>t===e);return{...t,...n}}}(F,t,u),{isSelected:en,select:er,selected:eo}=function(e,t){let n=function(e,t){let{selected:n,required:r,onSelect:o}=e,[a,i]=e6(n,o?n:void 0),s=o?n:a,{isSameDay:l}=t;return{selected:s,select:(e,t,n)=>{let a=e;return!r&&s&&s&&l(e,s)&&(a=void 0),o||i(a),o?.(a,e,t,n),a},isSelected:e=>!!s&&l(s,e)}}(e,t),r=function(e,t){let{selected:n,required:r,onSelect:o}=e,[a,i]=e6(n,o?n:void 0),s=o?n:a,{isSameDay:l}=t,d=e=>s?.some(t=>l(t,e))??!1,{min:u,max:c}=e;return{selected:s,select:(e,t,n)=>{let a=[...s??[]];if(d(e)){if(s?.length===u||r&&s?.length===1)return;a=s?.filter(t=>!l(t,e))}else a=s?.length===c?[e]:[...a,e];return o||i(a),o?.(a,e,t,n),a},isSelected:d}}(e,t),o=function(e,t){let{disabled:n,excludeDisabled:r,selected:o,required:a,onSelect:i}=e,[s,l]=e6(o,i?o:void 0),d=i?o:s;return{selected:d,select:(o,s,u)=>{let{min:c,max:f}=e,h=o?function(e,t,n=0,r=0,o=!1,a=H){let i;let{from:s,to:l}=t||{},{isSameDay:d,isAfter:u,isBefore:c}=a;if(s||l){if(s&&!l)i=d(s,e)?o?{from:s,to:void 0}:void 0:c(e,s)?{from:e,to:s}:{from:s,to:e};else if(s&&l){if(d(s,e)&&d(l,e))i=o?{from:s,to:l}:void 0;else if(d(s,e))i={from:s,to:n>0?void 0:e};else if(d(l,e))i={from:e,to:n>0?void 0:e};else if(c(e,s))i={from:e,to:l};else if(u(e,s))i={from:s,to:e};else if(u(e,l))i={from:s,to:e};else throw Error("Invalid range")}}else i={from:e,to:n>0?void 0:e};if(i?.from&&i?.to){let t=a.differenceInCalendarDays(i.to,i.from);r>0&&t>r?i={from:e,to:void 0}:n>1&&t<n&&(i={from:e,to:void 0})}return i}(o,d,c,f,a,t):void 0;return r&&n&&h?.from&&h.to&&function(e,t,n=H){let r=Array.isArray(t)?t:[t];if(r.filter(e=>"function"!=typeof e).some(t=>"boolean"==typeof t?t:n.isDate(t)?q(e,t,!1,n):K(t,n)?t.some(t=>q(e,t,!1,n)):X(t)?!!t.from&&!!t.to&&te(e,{from:t.from,to:t.to},n):V(t)?function(e,t,n=H){let r=Array.isArray(t)?t:[t],o=e.from,a=Math.min(n.differenceInCalendarDays(e.to,e.from),6);for(let e=0;e<=a;e++){if(r.includes(o.getDay()))return!0;o=n.addDays(o,1)}return!1}(e,t.dayOfWeek,n):z(t)?n.isAfter(t.before,t.after)?te(e,{from:n.addDays(t.after,1),to:n.addDays(t.before,-1)},n):Q(e.from,t,n)||Q(e.to,t,n):!!(G(t)||J(t))&&(Q(e.from,t,n)||Q(e.to,t,n))))return!0;let o=r.filter(e=>"function"==typeof e);if(o.length){let t=e.from,r=n.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=r;e++){if(o.some(e=>e(t)))return!0;t=n.addDays(t,1)}}return!1}({from:h.from,to:h.to},n,t)&&(h.from=o,h.to=void 0),i||l(h),i?.(h,o,s,u),h},isSelected:e=>d&&q(d,e,!1,t)}}(e,t);switch(e.mode){case"single":return n;case"multiple":return r;case"range":return o;default:return}}(t,u)??{},{blur:ea,focused:ei,isFocusTarget:es,moveFocus:el,setFocused:ed}=function(e,t,n,o,a){let{autoFocus:i}=e,[l,d]=(0,s.useState)(),u=function(e,t,n,o){let a;let i=-1;for(let s of e){let e=t(s);e7(e)&&(e[g.pL.focused]&&i<r.FocusedModifier?(a=s,i=r.FocusedModifier):o?.isEqualTo(s)&&i<r.LastFocused?(a=s,i=r.LastFocused):n(s.date)&&i<r.Selected?(a=s,i=r.Selected):e[g.pL.today]&&i<r.Today&&(a=s,i=r.Today))}return a||(a=e.find(e=>e7(t(e)))),a}(t.days,n,o||(()=>!1),l),[c,f]=(0,s.useState)(i?u:void 0);return{isFocusTarget:e=>!!u?.isEqualTo(e),setFocused:f,focused:c,blur:()=>{d(c),f(void 0)},moveFocus:(n,r)=>{if(!c)return;let o=function e(t,n,r,o,a,i,s,l=0){if(l>365)return;let d=function(e,t,n,r,o,a,i){let{ISOWeek:s,broadcastCalendar:l}=a,{addDays:d,addMonths:u,addWeeks:c,addYears:f,endOfBroadcastWeek:h,endOfISOWeek:m,endOfWeek:p,max:y,min:v,startOfBroadcastWeek:g,startOfISOWeek:b,startOfWeek:w}=i,M=({day:d,week:c,month:u,year:f,startOfWeek:e=>l?g(e,i):s?b(e):w(e),endOfWeek:e=>l?h(e):s?m(e):p(e)})[e](n,"after"===t?1:-1);return"before"===t&&r?M=y([r,M]):"after"===t&&o&&(M=v([o,M])),M}(t,n,r.date,o,a,i,s),u=!!(i.disabled&&Q(d,i.disabled,s)),c=!!(i.hidden&&Q(d,i.hidden,s)),f=new e4(d,d,s);return u||c?e(t,n,f,o,a,i,s,l+1):f}(n,r,c,t.navStart,t.navEnd,e,a);o&&(t.goToDay(o),f(o))}}}(t,A,et,en??(()=>!1),u),{labelDayButton:eu,labelGridcell:ec,labelGrid:eh,labelMonthDropdown:em,labelNav:ep,labelPrevious:ey,labelNext:ev,labelWeekday:eg,labelWeekNumber:eb,labelWeekNumberHeader:ew,labelYearDropdown:eM}=d,ek=(0,s.useMemo)(()=>(function(e,t,n){let r=e.today(),o=t?e.startOfISOWeek(r):e.startOfWeek(r),a=[];for(let t=0;t<7;t++){let n=e.addDays(o,t);a.push(n)}return a})(u,t.ISOWeek),[u,t.ISOWeek]),eD=void 0!==m||void 0!==w,eN=(0,s.useCallback)(()=>{Z&&(ee(Z),O?.(Z))},[Z,ee,O]),eC=(0,s.useCallback)(()=>{B&&(ee(B),W?.(B))},[ee,B,W]),eW=(0,s.useCallback)((e,t)=>n=>{n.preventDefault(),n.stopPropagation(),ed(e),er?.(e.date,t,n),w?.(e.date,t,n)},[er,w,ed]),eO=(0,s.useCallback)((e,t)=>n=>{ed(e),M?.(e.date,t,n)},[M,ed]),eS=(0,s.useCallback)((e,t)=>n=>{ea(),b?.(e.date,t,n)},[ea,b]),eI=(0,s.useCallback)((e,n)=>r=>{let o={ArrowLeft:["day","rtl"===t.dir?"after":"before"],ArrowRight:["day","rtl"===t.dir?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[r.shiftKey?"year":"month","before"],PageDown:[r.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[r.key]){r.preventDefault(),r.stopPropagation();let[e,t]=o[r.key];el(e,t)}k?.(e.date,n,r)},[el,k,t.dir]),eT=(0,s.useCallback)((e,t)=>n=>{D?.(e.date,t,n)},[D]),ex=(0,s.useCallback)((e,t)=>n=>{N?.(e.date,t,n)},[N]),eU=(0,s.useCallback)(e=>t=>{let n=Number(t.target.value);ee(u.setMonth(u.startOfMonth(e),n))},[u,ee]),eP=(0,s.useCallback)(e=>t=>{let n=Number(t.target.value);ee(u.setYear(u.startOfMonth(e),n))},[u,ee]),{className:e_,style:eL}=(0,s.useMemo)(()=>({className:[f[g.UI.Root],t.className].filter(Boolean).join(" "),style:{...S?.[g.UI.Root],...t.style}}),[f,t.className,t.style,S]),eA=function(e){let t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0,"data-nav-layout":e.navLayout||void 0};return Object.entries(e).forEach(([e,n])=>{e.startsWith("data-")&&(t[e]=n)}),t}(t),eF=(0,s.useRef)(null);!function(e,t,{classNames:n,months:r,focused:o,dateLib:a}){let i=(0,s.useRef)(null),l=(0,s.useRef)(r),d=(0,s.useRef)(!1);(0,s.useLayoutEffect)(()=>{let s=l.current;if(l.current=r,!t||!e.current||!(e.current instanceof HTMLElement)||0===r.length||0===s.length||r.length!==s.length)return;let u=a.isSameMonth(r[0].date,s[0].date),c=a.isAfter(r[0].date,s[0].date),f=c?n[g.X5.caption_after_enter]:n[g.X5.caption_before_enter],h=c?n[g.X5.weeks_after_enter]:n[g.X5.weeks_before_enter],m=i.current,p=e.current.cloneNode(!0);if(p instanceof HTMLElement?(eK(p).forEach(e=>{if(!(e instanceof HTMLElement))return;let t=eQ(e);t&&e.contains(t)&&e.removeChild(t);let n=e0(e);n&&n.classList.remove(f);let r=e1(e);r&&r.classList.remove(h)}),i.current=p):i.current=null,d.current||u||o)return;let y=m instanceof HTMLElement?eK(m):[],v=eK(e.current);if(v&&v.every(e=>e instanceof HTMLElement)&&y&&y.every(e=>e instanceof HTMLElement)){d.current=!0;let t=[];e.current.style.isolation="isolate";let r=e2(e.current);r&&(r.style.zIndex="1"),v.forEach((o,a)=>{let i=y[a];if(!i)return;o.style.position="relative",o.style.overflow="hidden";let s=e0(o);s&&s.classList.add(f);let l=e1(o);l&&l.classList.add(h);let u=()=>{d.current=!1,e.current&&(e.current.style.isolation=""),r&&(r.style.zIndex=""),s&&s.classList.remove(f),l&&l.classList.remove(h),o.style.position="",o.style.overflow="",o.contains(i)&&o.removeChild(i)};t.push(u),i.style.pointerEvents="none",i.style.position="absolute",i.style.overflow="hidden",i.setAttribute("aria-hidden","true");let m=e3(i);m&&(m.style.opacity="0");let p=e0(i);p&&(p.classList.add(c?n[g.X5.caption_before_exit]:n[g.X5.caption_after_exit]),p.addEventListener("animationend",u));let v=e1(i);v&&v.classList.add(c?n[g.X5.weeks_before_exit]:n[g.X5.weeks_after_exit]),o.insertBefore(i,o.firstChild)})}})}(eF,!!t.animate,{classNames:f,months:Y,focused:ei,dateLib:u});let eY={dayPickerProps:t,selected:eo,select:er,isSelected:en,months:Y,nextMonth:B,previousMonth:Z,goToMonth:ee,getModifiers:et,components:n,classNames:f,styles:S,labels:d,formatters:l};return s.createElement(ef.Provider,{value:eY},s.createElement(n.Root,{rootRef:t.animate?eF:void 0,className:e_,style:eL,dir:t.dir,id:t.id,lang:t.lang,nonce:t.nonce,title:t.title,role:t.role,"aria-label":t["aria-label"],...eA},s.createElement(n.Months,{className:f[g.UI.Months],style:S?.[g.UI.Months]},!t.hideNavigation&&!p&&s.createElement(n.Nav,{"data-animated-nav":t.animate?"true":void 0,className:f[g.UI.Nav],style:S?.[g.UI.Nav],"aria-label":ep(),onPreviousClick:eN,onNextClick:eC,previousMonth:Z,nextMonth:B}),Y.map((e,r)=>{let o=function(e,t,n,r,o){let{startOfMonth:a,startOfYear:i,endOfYear:s,eachMonthOfInterval:l,getMonth:d}=o;return l({start:i(e),end:s(e)}).map(e=>{let i=r.formatMonthDropdown(e,o);return{value:d(e),label:i,disabled:t&&e<a(t)||n&&e>a(n)||!1}})}(e.date,j,R,l,u),a=function(e,t,n,r){if(!e||!t)return;let{startOfYear:o,endOfYear:a,addYears:i,getYear:s,isBefore:l,isSameYear:d}=r,u=o(e),c=a(t),f=[],h=u;for(;l(h,c)||d(h,c);)f.push(h),h=i(h,1);return f.map(e=>{let t=n.formatYearDropdown(e,r);return{value:s(e),label:t,disabled:!1}})}(j,R,l,u);return s.createElement(n.Month,{"data-animated-month":t.animate?"true":void 0,className:f[g.UI.Month],style:S?.[g.UI.Month],key:r,displayIndex:r,calendarMonth:e},"around"===p&&!t.hideNavigation&&0===r&&s.createElement(n.PreviousMonthButton,{type:"button",className:f[g.UI.PreviousMonthButton],tabIndex:Z?void 0:-1,"aria-disabled":!Z||void 0,"aria-label":ey(Z),onClick:eN,"data-animated-button":t.animate?"true":void 0},s.createElement(n.Chevron,{disabled:!Z||void 0,className:f[g.UI.Chevron],orientation:"rtl"===t.dir?"right":"left"})),s.createElement(n.MonthCaption,{"data-animated-caption":t.animate?"true":void 0,className:f[g.UI.MonthCaption],style:S?.[g.UI.MonthCaption],calendarMonth:e,displayIndex:r},h?.startsWith("dropdown")?s.createElement(n.DropdownNav,{className:f[g.UI.Dropdowns],style:S?.[g.UI.Dropdowns]},"dropdown"===h||"dropdown-months"===h?s.createElement(n.MonthsDropdown,{className:f[g.UI.MonthsDropdown],"aria-label":em(),classNames:f,components:n,disabled:!!t.disableNavigation,onChange:eU(e.date),options:o,style:S?.[g.UI.Dropdown],value:u.getMonth(e.date)}):s.createElement("span",null,x(e.date,u)),"dropdown"===h||"dropdown-years"===h?s.createElement(n.YearsDropdown,{className:f[g.UI.YearsDropdown],"aria-label":eM(u.options),classNames:f,components:n,disabled:!!t.disableNavigation,onChange:eP(e.date),options:a,style:S?.[g.UI.Dropdown],value:u.getYear(e.date)}):s.createElement("span",null,L(e.date,u)),s.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},I(e.date,u.options,u))):s.createElement(n.CaptionLabel,{className:f[g.UI.CaptionLabel],role:"status","aria-live":"polite"},I(e.date,u.options,u))),"around"===p&&!t.hideNavigation&&r===y-1&&s.createElement(n.NextMonthButton,{type:"button",className:f[g.UI.NextMonthButton],tabIndex:B?void 0:-1,"aria-disabled":!B||void 0,"aria-label":ev(B),onClick:eC,"data-animated-button":t.animate?"true":void 0},s.createElement(n.Chevron,{disabled:!B||void 0,className:f[g.UI.Chevron],orientation:"rtl"===t.dir?"left":"right"})),r===y-1&&"after"===p&&!t.hideNavigation&&s.createElement(n.Nav,{"data-animated-nav":t.animate?"true":void 0,className:f[g.UI.Nav],style:S?.[g.UI.Nav],"aria-label":ep(),onPreviousClick:eN,onNextClick:eC,previousMonth:Z,nextMonth:B}),s.createElement(n.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===m||"range"===m,"aria-label":eh(e.date,u.options,u)||void 0,className:f[g.UI.MonthGrid],style:S?.[g.UI.MonthGrid]},!t.hideWeekdays&&s.createElement(n.Weekdays,{"data-animated-weekdays":t.animate?"true":void 0,className:f[g.UI.Weekdays],style:S?.[g.UI.Weekdays]},E&&s.createElement(n.WeekNumberHeader,{"aria-label":ew(u.options),className:f[g.UI.WeekNumberHeader],style:S?.[g.UI.WeekNumberHeader],scope:"col"},P()),ek.map((e,t)=>s.createElement(n.Weekday,{"aria-label":eg(e,u.options,u),className:f[g.UI.Weekday],key:t,style:S?.[g.UI.Weekday],scope:"col"},_(e,u.options,u)))),s.createElement(n.Weeks,{"data-animated-weeks":t.animate?"true":void 0,className:f[g.UI.Weeks],style:S?.[g.UI.Weeks]},e.weeks.map((e,r)=>s.createElement(n.Week,{className:f[g.UI.Week],key:e.weekNumber,style:S?.[g.UI.Week],week:e},E&&s.createElement(n.WeekNumber,{week:e,style:S?.[g.UI.WeekNumber],"aria-label":eb(e.weekNumber,{locale:c}),className:f[g.UI.WeekNumber],scope:"row",role:"rowheader"},U(e.weekNumber,u)),e.days.map(e=>{let{date:r}=e,o=et(e);if(o[g.pL.focused]=!o.hidden&&!!ei?.isEqualTo(e),o[g.wc.selected]=en?.(r)||o.selected,X(eo)){let{from:e,to:t}=eo;o[g.wc.range_start]=!!(e&&t&&u.isSameDay(r,e)),o[g.wc.range_end]=!!(e&&t&&u.isSameDay(r,t)),o[g.wc.range_middle]=q(eo,r,!0,u)}let a=function(e,t={},n={}){let r={...t?.[g.UI.Day]};return Object.entries(e).filter(([,e])=>!0===e).forEach(([e])=>{r={...r,...n?.[e]}}),r}(o,S,t.modifiersStyles),i=function(e,t,n={}){return Object.entries(e).filter(([,e])=>!0===e).reduce((e,[r])=>(n[r]?e.push(n[r]):t[g.pL[r]]?e.push(t[g.pL[r]]):t[g.wc[r]]&&e.push(t[g.wc[r]]),e),[t[g.UI.Day]])}(o,f,t.modifiersClassNames),l=eD||o.hidden?void 0:ec(r,o,u.options,u);return s.createElement(n.Day,{key:`${u.format(r,"yyyy-MM-dd")}_${u.format(e.displayMonth,"yyyy-MM")}`,day:e,modifiers:o,className:i.join(" "),style:a,role:"gridcell","aria-selected":o.selected||void 0,"aria-label":l,"data-day":u.format(r,"yyyy-MM-dd"),"data-month":e.outside?u.format(r,"yyyy-MM"):void 0,"data-selected":o.selected||void 0,"data-disabled":o.disabled||void 0,"data-hidden":o.hidden||void 0,"data-outside":e.outside||void 0,"data-focused":o.focused||void 0,"data-today":o.today||void 0},!o.hidden&&eD?s.createElement(n.DayButton,{className:f[g.UI.DayButton],style:S?.[g.UI.DayButton],type:"button",day:e,modifiers:o,disabled:o.disabled||void 0,tabIndex:es(e)?0:-1,"aria-label":eu(r,o,u.options,u),onClick:eW(e,o),onBlur:eS(e,o),onFocus:eO(e,o),onKeyDown:eI(e,o),onMouseEnter:eT(e,o),onMouseLeave:ex(e,o)},T(r,u.options,u)):!o.hidden&&T(e.date,u.options,u))}))))))})),t.footer&&s.createElement(n.Footer,{className:f[g.UI.Footer],style:S?.[g.UI.Footer],role:"status","aria-live":"polite"},t.footer)))}!function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"}(r||(r={}))},63749:(e,t,n)=>{n.d(t,{UC:()=>J,ZL:()=>G,bL:()=>z,l9:()=>X});var r=n(43210);function o(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}function s(...e){return r.useCallback(i(...e),e)}var l=n(60687),d=n(42360),u=n(1359),c=n(40481),f=n(19783),h=n(96348),m=n(96929),p=globalThis?.document?r.useLayoutEffect:()=>{},y=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,a]=r.useState(),i=r.useRef({}),s=r.useRef(e),l=r.useRef("none"),[d,u]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=v(i.current);l.current="mounted"===d?e:"none"},[d]),p(()=>{let t=i.current,n=s.current;if(n!==e){let r=l.current,o=v(t);e?u("MOUNT"):"none"===o||t?.display==="none"?u("UNMOUNT"):n&&r!==o?u("ANIMATION_OUT"):u("UNMOUNT"),s.current=e}},[e,u]),p(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=v(i.current).includes(n.animationName);if(n.target===o&&r&&(u("ANIMATION_END"),!s.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(l.current=v(i.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}u("ANIMATION_END")},[o,u]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(i.current=getComputedStyle(e)),a(e)},[])}}(t),a="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),i=s(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||o.isPresent?r.cloneElement(a,{ref:i}):null};function v(e){return e?.animationName||"none"}function g(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var a;let e,s;let l=(a=n,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,t){let n={...t};for(let r in t){let o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(d.ref=t?i(t,l):l),r.cloneElement(n,d)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...a}=e,i=r.Children.toArray(o),s=i.find(w);if(s){let e=s.props.children,o=i.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...a,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}y.displayName="Presence",n(51215);var b=Symbol("radix.slottable");function w(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===b}var M=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=g(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),k=n(67427),D=n(63376),N=n(42247),C="Popover",[W,O]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let a=r.createContext(o),i=n.length;n=[...n,o];let s=t=>{let{scope:n,children:o,...s}=t,d=n?.[e]?.[i]||a,u=r.useMemo(()=>s,Object.values(s));return(0,l.jsx)(d.Provider,{value:u,children:o})};return s.displayName=t+"Provider",[s,function(n,s){let l=s?.[e]?.[i]||a,d=r.useContext(l);if(d)return d;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(C,[h.Bk]),E=(0,h.Bk)(),[S,I]=W(C),T=e=>{let{__scopePopover:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:s=!1}=e,d=E(t),u=r.useRef(null),[c,m]=r.useState(!1),[p,y]=(0,k.i)({prop:o,defaultProp:a??!1,onChange:i,caller:C});return(0,l.jsx)(h.bL,{...d,children:(0,l.jsx)(S,{scope:t,contentId:(0,f.B)(),triggerRef:u,open:p,onOpenChange:y,onOpenToggle:r.useCallback(()=>y(e=>!e),[y]),hasCustomAnchor:c,onCustomAnchorAdd:r.useCallback(()=>m(!0),[]),onCustomAnchorRemove:r.useCallback(()=>m(!1),[]),modal:s,children:n})})};T.displayName=C;var x="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:n,...o}=e,a=I(x,n),i=E(n),{onCustomAnchorAdd:s,onCustomAnchorRemove:d}=a;return r.useEffect(()=>(s(),()=>d()),[s,d]),(0,l.jsx)(h.Mz,{...i,...o,ref:t})}).displayName=x;var U="PopoverTrigger",P=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,a=I(U,n),i=E(n),d=s(t,a.triggerRef),u=(0,l.jsx)(M.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":q(a.open),...r,ref:d,onClick:o(e.onClick,a.onOpenToggle)});return a.hasCustomAnchor?u:(0,l.jsx)(h.Mz,{asChild:!0,...i,children:u})});P.displayName=U;var _="PopoverPortal",[L,A]=W(_,{forceMount:void 0}),F=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,a=I(_,t);return(0,l.jsx)(L,{scope:t,forceMount:n,children:(0,l.jsx)(y,{present:n||a.open,children:(0,l.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};F.displayName=_;var Y="PopoverContent",j=r.forwardRef((e,t)=>{let n=A(Y,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,a=I(Y,e.__scopePopover);return(0,l.jsx)(y,{present:r||a.open,children:a.modal?(0,l.jsx)(Z,{...o,ref:t}):(0,l.jsx)(B,{...o,ref:t})})});j.displayName=Y;var R=g("PopoverContent.RemoveScroll"),Z=r.forwardRef((e,t)=>{let n=I(Y,e.__scopePopover),a=r.useRef(null),i=s(t,a),d=r.useRef(!1);return r.useEffect(()=>{let e=a.current;if(e)return(0,D.Eq)(e)},[]),(0,l.jsx)(N.A,{as:R,allowPinchZoom:!0,children:(0,l.jsx)($,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:o(e.onCloseAutoFocus,e=>{e.preventDefault(),d.current||n.triggerRef.current?.focus()}),onPointerDownOutside:o(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;d.current=2===t.button||n},{checkForDefaultPrevented:!1}),onFocusOutside:o(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),B=r.forwardRef((e,t)=>{let n=I(Y,e.__scopePopover),o=r.useRef(!1),a=r.useRef(!1);return(0,l.jsx)($,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),$=r.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:p,...y}=e,v=I(Y,n),g=E(n);return(0,u.Oh)(),(0,l.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,l.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:p,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:m,onDismiss:()=>v.onOpenChange(!1),children:(0,l.jsx)(h.UC,{"data-state":q(v.open),role:"dialog",id:v.contentId,...g,...y,ref:t,style:{...y.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),H="PopoverClose";function q(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,a=I(H,n);return(0,l.jsx)(M.button,{type:"button",...r,ref:t,onClick:o(e.onClick,()=>a.onOpenChange(!1))})}).displayName=H,r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=E(n);return(0,l.jsx)(h.i3,{...o,...r,ref:t})}).displayName="PopoverArrow";var z=T,X=P,G=F,J=j},93661:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},99471:(e,t,n)=>{n.d(t,{a:()=>o});var r=n(46950);function o(){let e={};for(let t in r.UI)e[r.UI[t]]=`rdp-${r.UI[t]}`;for(let t in r.pL)e[r.pL[t]]=`rdp-${r.pL[t]}`;for(let t in r.wc)e[r.wc[t]]=`rdp-${r.wc[t]}`;for(let t in r.X5)e[r.X5[t]]=`rdp-${r.X5[t]}`;return e}}};