(()=>{var e={};e.id=72,e.ids=[72],e.modules={1939:(e,r,t)=>{Promise.resolve().then(t.bind(t,31296)),Promise.resolve().then(t.bind(t,10590))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14719:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31296:(e,r,t)=>{"use strict";t.d(r,{LoginClient:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call LoginClient() from the server but LoginClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\login\\client.tsx","LoginClient")},33873:e=>{"use strict";e.exports=require("path")},41912:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>i});var s=t(37413),n=t(10590),a=t(31296);let i={title:"Login | OpenAutomate",description:"Login to your OpenAutomate account"};function o(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.Header,{}),(0,s.jsx)("div",{className:"container flex-1 flex items-center justify-center py-12",children:(0,s.jsx)(a.LoginClient,{})})]})}},48581:(e,r,t)=>{"use strict";t.d(r,{LoginClient:()=>A});var s=t(60687),n=t(43210),a=t.n(n),i=t(85814),o=t.n(i),l=t(16189),d=t(27605),c=t(63442),u=t(45880),m=t(11365),x=t(29523),p=t(71669),h=t(89667),g=t(86522),f=t(56896),b=t(93613),j=t(4344),v=t(78706),y=t(59321),w=t(14719),k=t(91821);function N({email:e,onResendSuccess:r}){let[t,a]=(0,n.useState)(!1),[i,o]=(0,n.useState)("idle"),[l,d]=(0,n.useState)(null),c=async()=>{if(e&&!t){a(!0);try{await v.Z.resendVerificationEmailByEmail(e),o("success"),d(null),r&&r()}catch(e){o("error"),console.error("Failed to resend verification email:",e),d((0,y.PE)(e)??"Failed to resend. Please try again.")}finally{a(!1)}}};return(0,s.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,s.jsxs)(x.$,{variant:"outline",className:"w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 py-2 px-4 text-base font-semibold text-gray-700 dark:text-gray-200 shadow-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-150",onClick:c,disabled:t,children:[t&&(0,s.jsx)(m.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),t?"Sending...":"Resend verification email"]}),"success"===i&&(0,s.jsxs)(k.Fc,{variant:"success",className:"animate-fade-in border-green-500 bg-green-50 text-green-800 dark:bg-green-950 dark:border-green-700 dark:text-green-200",children:[(0,s.jsx)(w.A,{className:"h-5 w-5 text-green-500 dark:text-green-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(k.XL,{className:"font-semibold",children:"Verification email sent!"}),(0,s.jsx)(k.TN,{children:"Please check your inbox."})]})]}),"error"===i&&(0,s.jsxs)(k.Fc,{variant:"destructive",className:"animate-fade-in",children:[(0,s.jsx)(b.A,{className:"h-5 w-5 text-red-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(k.XL,{className:"font-semibold",children:"Failed to resend"}),(0,s.jsx)(k.TN,{children:l??"Failed to resend. Please try again."})]})]})]})}let P=u.Ik({email:u.Yj().email("Please enter a valid email"),password:u.Yj().min(6,"Password must be at least 6 characters"),rememberMe:u.zM().optional()});function C(){let e=(0,l.useRouter)(),r=(0,l.useSearchParams)(),{login:t,isSystemAdmin:a}=(0,g.A)(),[i,u]=n.useState(!1),[v,y]=n.useState(null),[w,k]=n.useState(null),[C,S]=n.useState(!1),A=r.get("returnUrl")??j.$.paths.auth.organizationSelector,F="true"===r.get("expired"),_="true"===r.get("needVerification"),L=r.get("email"),E=A?.includes("/invitation/accept"),z=(0,d.mN)({resolver:(0,c.u)(P),defaultValues:{email:L||"",password:"",rememberMe:!1}}),{getValues:M,setValue:R}=z;async function I(r){u(!0),y(null);try{if(await t({email:r.email,password:r.password}),E){e.push(A);return}a?e.push("/dashboard"):A&&!E?e.push(A):e.push("/tenant-selector")}catch(t){let e="Login failed. Please try again.";if("object"==typeof t&&null!==t&&((e=t.response?.data?.message??t.message??e).toLowerCase().includes("verify")||e.toLowerCase().includes("verification")||e.toLowerCase().includes("email not verified")||t.response?.data?.code==="EMAIL_NOT_VERIFIED")){k(r.email),S(!0),y("Please verify your email address before logging in. Check your inbox for a verification link or request a new one.");return}S(!1),k(null),y(e)}finally{u(!1)}}return n.useEffect(()=>{F&&y("Your session has expired. Please sign in again."),_&&L&&(k(L),S(!0),y("Please verify your email address before logging in. Check your inbox for a verification link or request a new one."),M("email")!==L&&R("email",L))},[F,_,L,M,R]),(0,s.jsxs)("div",{className:"grid gap-6",children:[v&&(0,s.jsxs)("div",{className:"flex items-center gap-3 p-4 mb-2 rounded-lg border border-red-500 dark:border-red-400 bg-red-100 dark:bg-red-950 shadow-sm fade-in",children:[(0,s.jsx)("span",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-red-200 dark:bg-red-900 text-red-700 dark:text-red-300",children:(0,s.jsx)(b.A,{className:"w-6 h-6"})}),(0,s.jsx)("span",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:v})]}),C&&w&&(0,s.jsx)(N,{email:w}),(0,s.jsx)(p.lV,{...z,children:(0,s.jsxs)("form",{onSubmit:z.handleSubmit(I),className:"space-y-4",children:[(0,s.jsx)(p.zB,{control:z.control,name:"email",render:({field:e})=>(0,s.jsxs)(p.eI,{children:[(0,s.jsx)(p.lR,{children:"Email"}),(0,s.jsx)(p.MJ,{children:(0,s.jsx)(h.p,{type:"email",autoComplete:"email",...e,disabled:i})}),(0,s.jsx)(p.C5,{})]})}),(0,s.jsx)(p.zB,{control:z.control,name:"password",render:({field:e})=>(0,s.jsxs)(p.eI,{children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)(p.lR,{children:"Password"})}),(0,s.jsx)(p.MJ,{children:(0,s.jsx)(h.p,{type:"password",autoComplete:"current-password",...e,disabled:i})}),(0,s.jsx)(p.C5,{})]})}),(0,s.jsx)(p.zB,{control:z.control,name:"rememberMe",render:({field:e})=>(0,s.jsxs)(p.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,s.jsx)(p.MJ,{children:(0,s.jsx)(f.S,{checked:e.value,onCheckedChange:e.onChange,disabled:i})}),(0,s.jsxs)("div",{className:"flex justify-between w-full items-center",children:[(0,s.jsx)(p.lR,{className:"text-sm",children:"Remember me"}),(0,s.jsx)(o(),{href:"/forgot-password",className:"text-sm font-medium text-orange-600 hover:text-orange-700",children:"Forgot password?"})]})]})}),(0,s.jsxs)(x.$,{type:"submit",className:"w-full bg-orange-600 hover:bg-orange-700 transition-all duration-300 hover:translate-y-[-2px]",disabled:i,children:[i&&(0,s.jsx)(m.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Sign In"]})]})})]})}function S(){return(0,s.jsx)("div",{className:"grid gap-6",children:(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)(m.F.Spinner,{className:"h-8 w-8 animate-spin text-muted-foreground"})})})}function A(){let[e,r]=a().useState(""),t=e?`/register?returnUrl=${encodeURIComponent(e)}`:"/register";return(0,s.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold tracking-tight text-orange-600",children:"Sign In"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enter your email and password to continue"})]}),(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)(S,{}),children:(0,s.jsx)(C,{})}),(0,s.jsx)("div",{className:"px-8 text-center space-y-2",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,s.jsx)(o(),{href:t,className:"text-orange-600 underline underline-offset-4 hover:text-orange-700 font-medium transition-all duration-300 hover:underline-offset-8",children:"Sign up"})]})})]})}},56896:(e,r,t)=>{"use strict";t.d(r,{S:()=>o});var s=t(60687);t(43210);var n=t(6945),a=t(13964),i=t(36966);function o({className:e,...r}){return(0,s.jsx)(n.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-orange-600 data-[state=checked]:text-white dark:data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600 focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,s.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(a.A,{className:"size-3.5"})})})}},62187:(e,r,t)=>{Promise.resolve().then(t.bind(t,48581)),Promise.resolve().then(t.bind(t,64147))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83721:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(43210);function n(e){let r=s.useRef({value:e,previous:e});return s.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},99639:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),n=t(48088),a=t(31369),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let l={children:["",{children:["(auth)",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,41912)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\login\\page.tsx"]}]},{}]},{forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\login\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7966,5584,5156,4654,5880,6945,7943,5684,6763,8826],()=>t(99639));module.exports=s})();