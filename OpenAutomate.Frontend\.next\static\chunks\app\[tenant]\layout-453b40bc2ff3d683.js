(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8700],{13875:(e,t,a)=>{"use strict";a.d(t,{c:()=>c});var n=a(12115),s=a(96365),i=a(35695),r=a(34953),o=a(70449);function c(){let e=(0,i.useRouter)(),{data:t,error:a,isLoading:c,mutate:u}=(0,r.Ay)(o.DC.organizationUnits(),()=>s.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:null!=t?t:[],isLoading:c,error:a?"Failed to fetch organization units. Please try again later.":null,refresh:u,selectOrganizationUnit:(0,n.useCallback)(t=>{e.push("/".concat(t,"/dashboard"))},[e])}}},31997:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>c});var n=a(95155),s=a(12115),i=a(35695),r=a(13875),o=a(22100);function c(e){let{children:t}=e,{tenant:a}=(0,i.useParams)(),c=(0,i.useRouter)(),{isAuthenticated:u,isLoading:l}=(0,o.A)(),{organizationUnits:d,isLoading:g}=(0,r.c)(),[h,p]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{if(!l&&!g){if(!u){c.push("/login");return}if(!a){c.push("/tenant-selector");return}if(!d.some(e=>e.slug===a)){console.warn("User attempted to access unauthorized tenant: ".concat(a)),c.push("/tenant-selector?force=true&unauthorized=true");return}p(!1)}},[a,u,l,d,g,c]),l||g||h)?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,n.jsx)(n.Fragment,{children:t})}},65446:(e,t,a)=>{Promise.resolve().then(a.bind(a,31997)),Promise.resolve().then(a.bind(a,68755)),Promise.resolve().then(a.bind(a,19034)),Promise.resolve().then(a.bind(a,38891)),Promise.resolve().then(a.bind(a,6470)),Promise.resolve().then(a.bind(a,7838))},96365:(e,t,a)=>{"use strict";a.d(t,{K:()=>s});var n=a(7283);let s={getMyOrganizationUnits:async()=>await n.F.get("/api/ou/my-ous"),getBySlug:async e=>await n.F.get("/api/ou/slug/".concat(e)),getById:async e=>await n.F.get("/api/ou/".concat(e)),create:async e=>await n.F.post("/api/ou/create",e),update:async(e,t)=>await n.F.put("/api/ou/".concat(e),t),requestDeletion:async e=>await n.F.post("/api/ou/".concat(e,"/request-deletion"),{}),cancelDeletion:async e=>await n.F.post("/api/ou/".concat(e,"/cancel-deletion"),{}),getDeletionStatus:async e=>await n.F.get("/api/ou/".concat(e,"/deletion-status"))}}},e=>{var t=t=>e(e.s=t);e.O(0,[5258,7598,4953,6341,2178,8852,5699,6874,8594,106,9635,1309,4727,7057,8916,5033,8441,1684,7358],()=>t(65446)),_N_E=e.O()}]);