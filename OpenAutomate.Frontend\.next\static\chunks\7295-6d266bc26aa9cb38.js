"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7295],{5623:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},15933:(e,t,n)=>{n.d(t,{_:()=>r});function r(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}},19072:(e,t,n)=>{var r,o,a,i;n.d(t,{UI:()=>r,X5:()=>i,pL:()=>o,wc:()=>a}),function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"}(r||(r={})),function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"}(o||(o={})),function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"}(a||(a={})),function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"}(i||(i={}))},47330:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},49103:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},50713:(e,t,n)=>{n.d(t,{a:()=>o});var r=n(19072);function o(){let e={};for(let t in r.UI)e[r.UI[t]]=`rdp-${r.UI[t]}`;for(let t in r.pL)e[r.pL[t]]=`rdp-${r.pL[t]}`;for(let t in r.wc)e[r.wc[t]]=`rdp-${r.wc[t]}`;for(let t in r.X5)e[r.X5[t]]=`rdp-${r.X5[t]}`;return e}},65141:(e,t,n)=>{n.d(t,{UC:()=>Q,ZL:()=>X,bL:()=>$,l9:()=>G});var r=n(12115);function o(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}function s(...e){return r.useCallback(i(...e),e)}var l=n(95155),u=n(7166),d=n(92293),c=n(12307),f=n(52496),h=n(13227),m=n(962),p=globalThis?.document?r.useLayoutEffect:()=>{},v=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,a]=r.useState(),i=r.useRef({}),s=r.useRef(e),l=r.useRef("none"),[u,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=g(i.current);l.current="mounted"===u?e:"none"},[u]),p(()=>{let t=i.current,n=s.current;if(n!==e){let r=l.current,o=g(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),p(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=g(i.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(l.current=g(i.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:r.useCallback(e=>{e&&(i.current=getComputedStyle(e)),a(e)},[])}}(t),a="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),i=s(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||o.isPresent?r.cloneElement(a,{ref:i}):null};function g(e){return(null==e?void 0:e.animationName)||"none"}function y(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var a;let e,s;let l=(a=n,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?i(t,l):l),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...a}=e,i=r.Children.toArray(o),s=i.find(b);if(s){let e=s.props.children,o=i.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...a,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}v.displayName="Presence",n(47650);var w=Symbol("radix.slottable");function b(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===w}var M=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=y(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),k=n(12640),D=n(38168),N=n(93795),C="Popover",[O,x]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let a=r.createContext(o),i=n.length;n=[...n,o];let s=t=>{let{scope:n,children:o,...s}=t,u=n?.[e]?.[i]||a,d=r.useMemo(()=>s,Object.values(s));return(0,l.jsx)(u.Provider,{value:d,children:o})};return s.displayName=t+"Provider",[s,function(n,s){let l=s?.[e]?.[i]||a,u=r.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(C,[h.Bk]),S=(0,h.Bk)(),[W,E]=O(C),T=e=>{let{__scopePopover:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:s=!1}=e,u=S(t),d=r.useRef(null),[c,m]=r.useState(!1),[p,v]=(0,k.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:C});return(0,l.jsx)(h.bL,{...u,children:(0,l.jsx)(W,{scope:t,contentId:(0,f.B)(),triggerRef:d,open:p,onOpenChange:v,onOpenToggle:r.useCallback(()=>v(e=>!e),[v]),hasCustomAnchor:c,onCustomAnchorAdd:r.useCallback(()=>m(!0),[]),onCustomAnchorRemove:r.useCallback(()=>m(!1),[]),modal:s,children:n})})};T.displayName=C;var P="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:n,...o}=e,a=E(P,n),i=S(n),{onCustomAnchorAdd:s,onCustomAnchorRemove:u}=a;return r.useEffect(()=>(s(),()=>u()),[s,u]),(0,l.jsx)(h.Mz,{...i,...o,ref:t})}).displayName=P;var I="PopoverTrigger",L=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,a=E(I,n),i=S(n),u=s(t,a.triggerRef),d=(0,l.jsx)(M.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":z(a.open),...r,ref:u,onClick:o(e.onClick,a.onOpenToggle)});return a.hasCustomAnchor?d:(0,l.jsx)(h.Mz,{asChild:!0,...i,children:d})});L.displayName=I;var U="PopoverPortal",[_,Y]=O(U,{forceMount:void 0}),F=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,a=E(U,t);return(0,l.jsx)(_,{scope:t,forceMount:n,children:(0,l.jsx)(v,{present:n||a.open,children:(0,l.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};F.displayName=U;var A="PopoverContent",j=r.forwardRef((e,t)=>{let n=Y(A,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,a=E(A,e.__scopePopover);return(0,l.jsx)(v,{present:r||a.open,children:a.modal?(0,l.jsx)(B,{...o,ref:t}):(0,l.jsx)(R,{...o,ref:t})})});j.displayName=A;var H=y("PopoverContent.RemoveScroll"),B=r.forwardRef((e,t)=>{let n=E(A,e.__scopePopover),a=r.useRef(null),i=s(t,a),u=r.useRef(!1);return r.useEffect(()=>{let e=a.current;if(e)return(0,D.Eq)(e)},[]),(0,l.jsx)(N.A,{as:H,allowPinchZoom:!0,children:(0,l.jsx)(Z,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:o(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),u.current||null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:o(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;u.current=2===t.button||n},{checkForDefaultPrevented:!1}),onFocusOutside:o(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),R=r.forwardRef((e,t)=>{let n=E(A,e.__scopePopover),o=r.useRef(!1),a=r.useRef(!1);return(0,l.jsx)(Z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let s=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),Z=r.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:p,...v}=e,g=E(A,n),y=S(n);return(0,d.Oh)(),(0,l.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,l.jsx)(u.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:p,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:m,onDismiss:()=>g.onOpenChange(!1),children:(0,l.jsx)(h.UC,{"data-state":z(g.open),role:"dialog",id:g.contentId,...y,...v,ref:t,style:{...v.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),q="PopoverClose";function z(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,a=E(q,n);return(0,l.jsx)(M.button,{type:"button",...r,ref:t,onClick:o(e.onClick,()=>a.onOpenChange(!1))})}).displayName=q,r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=S(n);return(0,l.jsx)(h.i3,{...o,...r,ref:t})}).displayName="PopoverArrow";var $=T,G=L,X=F,Q=j},65477:(e,t,n)=>{n.d(t,{h:()=>tM});var r,o={};n.r(o),n.d(o,{Button:()=>eb,CaptionLabel:()=>eM,Chevron:()=>ek,Day:()=>eD,DayButton:()=>eN,Dropdown:()=>eC,DropdownNav:()=>eO,Footer:()=>ex,Month:()=>eS,MonthCaption:()=>eW,MonthGrid:()=>eE,Months:()=>eT,MonthsDropdown:()=>eL,Nav:()=>eU,NextMonthButton:()=>e_,Option:()=>eY,PreviousMonthButton:()=>eF,Root:()=>eA,Select:()=>ej,Week:()=>eH,WeekNumber:()=>eZ,WeekNumberHeader:()=>eq,Weekday:()=>eB,Weekdays:()=>eR,Weeks:()=>ez,YearsDropdown:()=>e$});var a={};n.r(a),n.d(a,{formatCaption:()=>eX,formatDay:()=>eV,formatMonthCaption:()=>eQ,formatMonthDropdown:()=>eJ,formatWeekNumber:()=>eK,formatWeekNumberHeader:()=>e0,formatWeekdayName:()=>e1,formatYearCaption:()=>e3,formatYearDropdown:()=>e2});var i={};n.r(i),n.d(i,{labelCaption:()=>e4,labelDay:()=>e8,labelDayButton:()=>e7,labelGrid:()=>e5,labelGridcell:()=>e9,labelMonthDropdown:()=>te,labelNav:()=>e6,labelNext:()=>tt,labelPrevious:()=>tn,labelWeekNumber:()=>to,labelWeekNumberHeader:()=>ta,labelWeekday:()=>tr,labelYearDropdown:()=>ti});var s=n(12115);Symbol.for("constructDateFrom");let l={},u={};function d(e,t){try{let n=(l[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";if(n in u)return u[n];return f(n,n.split(":"))}catch{if(e in u)return u[e];let t=e?.match(c);if(t)return f(e,t.slice(1));return NaN}}let c=/([+-]\d\d):?(\d\d)?/;function f(e,t){let n=+t[0],r=+(t[1]||0);return u[e]=n>0?60*n+r:60*n-r}class h extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(d(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),v(this,NaN),p(this)):this.setTime(Date.now())}static tz(e,...t){return t.length?new h(...t,e):new h(Date.now(),e)}withTimeZone(e){return new h(+this,e)}getTimezoneOffset(){return-d(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),p(this),+this}[Symbol.for("constructDateFrom")](e){return new h(+new Date(e),this.timeZone)}}let m=/^(get|set)(?!UTC)/;function p(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function v(e){let t=d(e.timeZone,e),n=new Date(+e);n.setUTCHours(n.getUTCHours()-1);let r=-new Date(+e).getTimezoneOffset(),o=r- -new Date(+n).getTimezoneOffset(),a=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();o&&a&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+o);let i=r-t;i&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+i);let s=d(e.timeZone,e),l=-new Date(+e).getTimezoneOffset()-s-i;if(s!==t&&l){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);let t=s-d(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!m.test(e))return;let t=e.replace(m,"$1UTC");h.prototype[t]&&(e.startsWith("get")?h.prototype[e]=function(){return this.internal[t]()}:(h.prototype[e]=function(){var e;return Date.prototype[t].apply(this.internal,arguments),e=this,Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),v(e),+this},h.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),p(this),+this}))});class g extends h{static tz(e,...t){return t.length?new g(...t,e):new g(Date.now(),e)}toISOString(){let[e,t,n]=this.tzComponents(),r=`${e}${t}:${n}`;return this.internal.toISOString().slice(0,-1)+r}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[e,t,n,r]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${n} ${t} ${r}`}toTimeString(){var e,t;let n=this.internal.toUTCString().split(" ")[4],[r,o,a]=this.tzComponents();return`${n} GMT${r}${o}${a} (${e=this.timeZone,t=this,new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){let e=this.getTimezoneOffset(),t=String(Math.floor(Math.abs(e)/60)).padStart(2,"0"),n=String(Math.abs(e)%60).padStart(2,"0");return[e>0?"-":"+",t,n]}withTimeZone(e){return new g(+this,e)}[Symbol.for("constructDateFrom")](e){return new g(+new Date(e),this.timeZone)}}var y=n(19072);let w={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function b(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}let M={date:b({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:b({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:b({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},k={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function D(e){return(t,n)=>{let r;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,o=(null==n?void 0:n.width)?String(n.width):t;r=e.formattingValues[o]||e.formattingValues[t]}else{let t=e.defaultWidth,o=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;r=e.values[o]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function N(e){return function(t){let n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.width,a=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],i=t.match(a);if(!i)return null;let s=i[0],l=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(l)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(l,e=>e.test(s)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(l,e=>e.test(s));return n=e.valueCallback?e.valueCallback(u):u,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(s.length)}}}let C={code:"en-US",formatDistance:(e,t,n)=>{let r;let o=w[e];return(r="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:M,formatRelative:(e,t,n,r)=>k[e],localize:{ordinalNumber:(e,t)=>{let n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:D({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:D({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:D({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:D({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:D({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.match(e.matchPattern);if(!r)return null;let o=r[0],a=t.match(e.parsePattern);if(!a)return null;let i=e.valueCallback?e.valueCallback(a[0]):a[0];return{value:i=n.valueCallback?n.valueCallback(i):i,rest:t.slice(o.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:N({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:N({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:N({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:N({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:N({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},O=Symbol.for("constructDateFrom");function x(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&O in e?e[O](t):e instanceof Date?new e.constructor(t):new Date(t)}function S(e,t){return x(t||e,e)}function W(e,t,n){let r=S(e,null==n?void 0:n.in);return isNaN(t)?x((null==n?void 0:n.in)||e,NaN):(t&&r.setDate(r.getDate()+t),r)}function E(e,t,n){let r=S(e,null==n?void 0:n.in);if(isNaN(t))return x((null==n?void 0:n.in)||e,NaN);if(!t)return r;let o=r.getDate(),a=x((null==n?void 0:n.in)||e,r.getTime());return(a.setMonth(r.getMonth()+t+1,0),o>=a.getDate())?a:(r.setFullYear(a.getFullYear(),a.getMonth(),o),r)}function T(e){let t=S(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function P(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let o=x.bind(null,e||n.find(e=>"object"==typeof e));return n.map(o)}function I(e,t){let n=S(e,null==t?void 0:t.in);return n.setHours(0,0,0,0),n}function L(e,t,n){let[r,o]=P(null==n?void 0:n.in,e,t),a=I(r),i=I(o);return Math.round((+a-T(a)-(+i-T(i)))/864e5)}let U={};function _(e,t){var n,r,o,a,i,s,l,u;let d=null!==(u=null!==(l=null!==(s=null!==(i=null==t?void 0:t.weekStartsOn)&&void 0!==i?i:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.weekStartsOn)&&void 0!==s?s:U.weekStartsOn)&&void 0!==l?l:null===(a=U.locale)||void 0===a?void 0:null===(o=a.options)||void 0===o?void 0:o.weekStartsOn)&&void 0!==u?u:0,c=S(e,null==t?void 0:t.in),f=c.getDay();return c.setDate(c.getDate()+((f<d?-7:0)+6-(f-d))),c.setHours(23,59,59,999),c}function Y(e,t){let n=S(e,null==t?void 0:t.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}function F(e,t){var n,r,o,a,i,s,l,u;let d=null!==(u=null!==(l=null!==(s=null!==(i=null==t?void 0:t.weekStartsOn)&&void 0!==i?i:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.weekStartsOn)&&void 0!==s?s:U.weekStartsOn)&&void 0!==l?l:null===(a=U.locale)||void 0===a?void 0:null===(o=a.options)||void 0===o?void 0:o.weekStartsOn)&&void 0!==u?u:0,c=S(e,null==t?void 0:t.in),f=c.getDay();return c.setDate(c.getDate()-(7*(f<d)+f-d)),c.setHours(0,0,0,0),c}function A(e,t){return F(e,{...t,weekStartsOn:1})}function j(e,t){let n=S(e,null==t?void 0:t.in),r=n.getFullYear(),o=x(n,0);o.setFullYear(r+1,0,4),o.setHours(0,0,0,0);let a=A(o),i=x(n,0);i.setFullYear(r,0,4),i.setHours(0,0,0,0);let s=A(i);return n.getTime()>=a.getTime()?r+1:n.getTime()>=s.getTime()?r:r-1}function H(e,t){let n=S(e,null==t?void 0:t.in);return Math.round((+A(n)-+function(e,t){let n=j(e,void 0),r=x(e,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),A(r)}(n))/6048e5)+1}function B(e,t){var n,r,o,a,i,s,l,u;let d=S(e,null==t?void 0:t.in),c=d.getFullYear(),f=null!==(u=null!==(l=null!==(s=null!==(i=null==t?void 0:t.firstWeekContainsDate)&&void 0!==i?i:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.firstWeekContainsDate)&&void 0!==s?s:U.firstWeekContainsDate)&&void 0!==l?l:null===(a=U.locale)||void 0===a?void 0:null===(o=a.options)||void 0===o?void 0:o.firstWeekContainsDate)&&void 0!==u?u:1,h=x((null==t?void 0:t.in)||e,0);h.setFullYear(c+1,0,f),h.setHours(0,0,0,0);let m=F(h,t),p=x((null==t?void 0:t.in)||e,0);p.setFullYear(c,0,f),p.setHours(0,0,0,0);let v=F(p,t);return+d>=+m?c+1:+d>=+v?c:c-1}function R(e,t){let n=S(e,null==t?void 0:t.in);return Math.round((+F(n,t)-+function(e,t){var n,r,o,a,i,s,l,u;let d=null!==(u=null!==(l=null!==(s=null!==(i=null==t?void 0:t.firstWeekContainsDate)&&void 0!==i?i:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.firstWeekContainsDate)&&void 0!==s?s:U.firstWeekContainsDate)&&void 0!==l?l:null===(a=U.locale)||void 0===a?void 0:null===(o=a.options)||void 0===o?void 0:o.firstWeekContainsDate)&&void 0!==u?u:1,c=B(e,t),f=x((null==t?void 0:t.in)||e,0);return f.setFullYear(c,0,d),f.setHours(0,0,0,0),F(f,t)}(n,t))/6048e5)+1}function Z(e,t){let n=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+n}let q={y(e,t){let n=e.getFullYear(),r=n>0?n:1-n;return Z("yy"===t?r%100:r,t.length)},M(e,t){let n=e.getMonth();return"M"===t?String(n+1):Z(n+1,2)},d:(e,t)=>Z(e.getDate(),t.length),a(e,t){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>Z(e.getHours()%12||12,t.length),H:(e,t)=>Z(e.getHours(),t.length),m:(e,t)=>Z(e.getMinutes(),t.length),s:(e,t)=>Z(e.getSeconds(),t.length),S(e,t){let n=t.length;return Z(Math.trunc(e.getMilliseconds()*Math.pow(10,n-3)),t.length)}},z={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},$={G:function(e,t,n){let r=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){let t=e.getFullYear();return n.ordinalNumber(t>0?t:1-t,{unit:"year"})}return q.y(e,t)},Y:function(e,t,n,r){let o=B(e,r),a=o>0?o:1-o;return"YY"===t?Z(a%100,2):"Yo"===t?n.ordinalNumber(a,{unit:"year"}):Z(a,t.length)},R:function(e,t){return Z(j(e),t.length)},u:function(e,t){return Z(e.getFullYear(),t.length)},Q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return Z(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return Z(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){let r=e.getMonth();switch(t){case"M":case"MM":return q.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){let r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return Z(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){let o=R(e,r);return"wo"===t?n.ordinalNumber(o,{unit:"week"}):Z(o,t.length)},I:function(e,t,n){let r=H(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):Z(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):q.d(e,t)},D:function(e,t,n){let r=function(e,t){let n=S(e,void 0);return L(n,Y(n))+1}(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):Z(r,t.length)},E:function(e,t,n){let r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){let o=e.getDay(),a=(o-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(a);case"ee":return Z(a,2);case"eo":return n.ordinalNumber(a,{unit:"day"});case"eee":return n.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(o,{width:"short",context:"formatting"});default:return n.day(o,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){let o=e.getDay(),a=(o-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(a);case"cc":return Z(a,t.length);case"co":return n.ordinalNumber(a,{unit:"day"});case"ccc":return n.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(o,{width:"narrow",context:"standalone"});case"cccccc":return n.day(o,{width:"short",context:"standalone"});default:return n.day(o,{width:"wide",context:"standalone"})}},i:function(e,t,n){let r=e.getDay(),o=0===r?7:r;switch(t){case"i":return String(o);case"ii":return Z(o,t.length);case"io":return n.ordinalNumber(o,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){let r;let o=e.getHours();switch(r=12===o?z.noon:0===o?z.midnight:o/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){let r;let o=e.getHours();switch(r=o>=17?z.evening:o>=12?z.afternoon:o>=4?z.morning:z.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return q.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):q.H(e,t)},K:function(e,t,n){let r=e.getHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):Z(r,t.length)},k:function(e,t,n){let r=e.getHours();return(0===r&&(r=24),"ko"===t)?n.ordinalNumber(r,{unit:"hour"}):Z(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):q.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):q.s(e,t)},S:function(e,t){return q.S(e,t)},X:function(e,t,n){let r=e.getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return X(r);case"XXXX":case"XX":return Q(r);default:return Q(r,":")}},x:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"x":return X(r);case"xxxx":case"xx":return Q(r);default:return Q(r,":")}},O:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+G(r,":");default:return"GMT"+Q(r,":")}},z:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+G(r,":");default:return"GMT"+Q(r,":")}},t:function(e,t,n){return Z(Math.trunc(+e/1e3),t.length)},T:function(e,t,n){return Z(+e,t.length)}};function G(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=e>0?"-":"+",r=Math.abs(e),o=Math.trunc(r/60),a=r%60;return 0===a?n+String(o):n+String(o)+t+Z(a,2)}function X(e,t){return e%60==0?(e>0?"-":"+")+Z(Math.abs(e)/60,2):Q(e,t)}function Q(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=Math.abs(e);return(e>0?"-":"+")+Z(Math.trunc(n/60),2)+t+Z(n%60,2)}let V=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},J=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},K={p:J,P:(e,t)=>{let n;let r=e.match(/(P+)(p+)?/)||[],o=r[1],a=r[2];if(!a)return V(e,t);switch(o){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",V(o,t)).replace("{{time}}",J(a,t))}},ee=/^D+$/,et=/^Y+$/,en=["D","DD","YY","YYYY"];function er(e){return e instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e)}let eo=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ea=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ei=/^'([^]*?)'?$/,es=/''/g,el=/[a-zA-Z]/;function eu(e,t){let n=t.startOfMonth(e),r=n.getDay();return 1===r?n:0===r?t.addDays(n,-6):t.addDays(n,-1*(r-1))}class ed{constructor(e,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?g.tz(this.options.timeZone):new this.Date,this.newDate=(e,t,n)=>this.overrides?.newDate?this.overrides.newDate(e,t,n):this.options.timeZone?new g(e,t,n,this.options.timeZone):new Date(e,t,n),this.addDays=(e,t)=>this.overrides?.addDays?this.overrides.addDays(e,t):W(e,t),this.addMonths=(e,t)=>this.overrides?.addMonths?this.overrides.addMonths(e,t):E(e,t),this.addWeeks=(e,t)=>this.overrides?.addWeeks?this.overrides.addWeeks(e,t):W(e,7*t,void 0),this.addYears=(e,t)=>this.overrides?.addYears?this.overrides.addYears(e,t):E(e,12*t,void 0),this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(e,t):L(e,t),this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(e,t):function(e,t,n){let[r,o]=P(void 0,e,t);return 12*(r.getFullYear()-o.getFullYear())+(r.getMonth()-o.getMonth())}(e,t),this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(e):function(e,t){var n;let{start:r,end:o}=function(e,t){let[n,r]=P(e,t.start,t.end);return{start:n,end:r}}(void 0,e),a=+r>+o,i=a?+r:+o,s=a?o:r;s.setHours(0,0,0,0),s.setDate(1);let l=(n=void 0,void 0!==n)?n:1;if(!l)return[];l<0&&(l=-l,a=!a);let u=[];for(;+s<=i;)u.push(x(r,s)),s.setMonth(s.getMonth()+l);return a?u.reverse():u}(e),this.endOfBroadcastWeek=e=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(e):function(e,t){let n=eu(e,t),r=function(e,t){let n=t.startOfMonth(e),r=n.getDay()>0?n.getDay():7,o=t.addDays(e,-r+1),a=t.addDays(o,34);return t.getMonth(e)===t.getMonth(a)?5:4}(e,t);return t.addDays(n,7*r-1)}(e,this),this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(e):_(e,{weekStartsOn:1}),this.endOfMonth=e=>this.overrides?.endOfMonth?this.overrides.endOfMonth(e):function(e,t){let n=S(e,void 0),r=n.getMonth();return n.setFullYear(n.getFullYear(),r+1,0),n.setHours(23,59,59,999),n}(e),this.endOfWeek=(e,t)=>this.overrides?.endOfWeek?this.overrides.endOfWeek(e,t):_(e,this.options),this.endOfYear=e=>this.overrides?.endOfYear?this.overrides.endOfYear(e):function(e,t){let n=S(e,void 0),r=n.getFullYear();return n.setFullYear(r+1,0,0),n.setHours(23,59,59,999),n}(e),this.format=(e,t,n)=>{let r=this.overrides?.format?this.overrides.format(e,t,this.options):function(e,t,n){var r,o,a,i,s,l,u,d,c,f,h,m,p,v,g,y,w,b;let M=null!==(f=null!==(c=null==n?void 0:n.locale)&&void 0!==c?c:U.locale)&&void 0!==f?f:C,k=null!==(v=null!==(p=null!==(m=null!==(h=null==n?void 0:n.firstWeekContainsDate)&&void 0!==h?h:null==n?void 0:null===(o=n.locale)||void 0===o?void 0:null===(r=o.options)||void 0===r?void 0:r.firstWeekContainsDate)&&void 0!==m?m:U.firstWeekContainsDate)&&void 0!==p?p:null===(i=U.locale)||void 0===i?void 0:null===(a=i.options)||void 0===a?void 0:a.firstWeekContainsDate)&&void 0!==v?v:1,D=null!==(b=null!==(w=null!==(y=null!==(g=null==n?void 0:n.weekStartsOn)&&void 0!==g?g:null==n?void 0:null===(l=n.locale)||void 0===l?void 0:null===(s=l.options)||void 0===s?void 0:s.weekStartsOn)&&void 0!==y?y:U.weekStartsOn)&&void 0!==w?w:null===(d=U.locale)||void 0===d?void 0:null===(u=d.options)||void 0===u?void 0:u.weekStartsOn)&&void 0!==b?b:0,N=S(e,null==n?void 0:n.in);if(!er(N)&&"number"!=typeof N||isNaN(+S(N)))throw RangeError("Invalid time value");let O=t.match(ea).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,K[t])(e,M.formatLong):e}).join("").match(eo).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(ei);return t?t[1].replace(es,"'"):e}(e)};if($[t])return{isToken:!0,value:e};if(t.match(el))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});M.localize.preprocessor&&(O=M.localize.preprocessor(N,O));let x={firstWeekContainsDate:k,weekStartsOn:D,locale:M};return O.map(r=>{if(!r.isToken)return r.value;let o=r.value;return(!(null==n?void 0:n.useAdditionalWeekYearTokens)&&et.test(o)||!(null==n?void 0:n.useAdditionalDayOfYearTokens)&&ee.test(o))&&!function(e,t,n){let r=function(e,t,n){let r="Y"===e[0]?"years":"days of the month";return"Use `".concat(e.toLowerCase(),"` instead of `").concat(e,"` (in `").concat(t,"`) for formatting ").concat(r," to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md")}(e,t,n);if(console.warn(r),en.includes(e))throw RangeError(r)}(o,t,String(e)),(0,$[o[0]])(N,o,M.localize,x)}).join("")}(e,t,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(r):r},this.getISOWeek=e=>this.overrides?.getISOWeek?this.overrides.getISOWeek(e):H(e),this.getMonth=(e,t)=>this.overrides?.getMonth?this.overrides.getMonth(e,this.options):function(e,t){return S(e,null==t?void 0:t.in).getMonth()}(e,this.options),this.getYear=(e,t)=>this.overrides?.getYear?this.overrides.getYear(e,this.options):function(e,t){return S(e,null==t?void 0:t.in).getFullYear()}(e,this.options),this.getWeek=(e,t)=>this.overrides?.getWeek?this.overrides.getWeek(e,this.options):R(e,this.options),this.isAfter=(e,t)=>this.overrides?.isAfter?this.overrides.isAfter(e,t):+S(e)>+S(t),this.isBefore=(e,t)=>this.overrides?.isBefore?this.overrides.isBefore(e,t):+S(e)<+S(t),this.isDate=e=>this.overrides?.isDate?this.overrides.isDate(e):er(e),this.isSameDay=(e,t)=>this.overrides?.isSameDay?this.overrides.isSameDay(e,t):function(e,t,n){let[r,o]=P(void 0,e,t);return+I(r)==+I(o)}(e,t),this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(e,t):function(e,t,n){let[r,o]=P(void 0,e,t);return r.getFullYear()===o.getFullYear()&&r.getMonth()===o.getMonth()}(e,t),this.isSameYear=(e,t)=>this.overrides?.isSameYear?this.overrides.isSameYear(e,t):function(e,t,n){let[r,o]=P(void 0,e,t);return r.getFullYear()===o.getFullYear()}(e,t),this.max=e=>this.overrides?.max?this.overrides.max(e):function(e,t){let n,r;return e.forEach(e=>{r||"object"!=typeof e||(r=x.bind(null,e));let t=S(e,r);(!n||n<t||isNaN(+t))&&(n=t)}),x(r,n||NaN)}(e),this.min=e=>this.overrides?.min?this.overrides.min(e):function(e,t){let n,r;return e.forEach(e=>{r||"object"!=typeof e||(r=x.bind(null,e));let t=S(e,r);(!n||n>t||isNaN(+t))&&(n=t)}),x(r,n||NaN)}(e),this.setMonth=(e,t)=>this.overrides?.setMonth?this.overrides.setMonth(e,t):function(e,t,n){let r=S(e,void 0),o=r.getFullYear(),a=r.getDate(),i=x(e,0);i.setFullYear(o,t,15),i.setHours(0,0,0,0);let s=function(e,t){let n=S(e,void 0),r=n.getFullYear(),o=n.getMonth(),a=x(n,0);return a.setFullYear(r,o+1,0),a.setHours(0,0,0,0),a.getDate()}(i);return r.setMonth(t,Math.min(a,s)),r}(e,t),this.setYear=(e,t)=>this.overrides?.setYear?this.overrides.setYear(e,t):function(e,t,n){let r=S(e,void 0);return isNaN(+r)?x(e,NaN):(r.setFullYear(t),r)}(e,t),this.startOfBroadcastWeek=(e,t)=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(e,this):eu(e,this),this.startOfDay=e=>this.overrides?.startOfDay?this.overrides.startOfDay(e):I(e),this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(e):A(e),this.startOfMonth=e=>this.overrides?.startOfMonth?this.overrides.startOfMonth(e):function(e,t){let n=S(e,void 0);return n.setDate(1),n.setHours(0,0,0,0),n}(e),this.startOfWeek=(e,t)=>this.overrides?.startOfWeek?this.overrides.startOfWeek(e,this.options):F(e,this.options),this.startOfYear=e=>this.overrides?.startOfYear?this.overrides.startOfYear(e):Y(e),this.options={locale:C,...e},this.overrides=t}getDigitMap(){let{numerals:e="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:e}),n={};for(let e=0;e<10;e++)n[e.toString()]=t.format(e);return n}replaceDigits(e){let t=this.getDigitMap();return e.replace(/\d/g,e=>t[e]||e)}formatNumber(e){return this.replaceDigits(e.toString())}}let ec=new ed;function ef(e,t,n=!1,r=ec){let{from:o,to:a}=e,{differenceInCalendarDays:i,isSameDay:s}=r;return o&&a?(0>i(a,o)&&([o,a]=[a,o]),i(t,o)>=+!!n&&i(a,t)>=+!!n):!n&&a?s(a,t):!n&&!!o&&s(o,t)}function eh(e){return!!(e&&"object"==typeof e&&"before"in e&&"after"in e)}function em(e){return!!(e&&"object"==typeof e&&"from"in e)}function ep(e){return!!(e&&"object"==typeof e&&"after"in e)}function ev(e){return!!(e&&"object"==typeof e&&"before"in e)}function eg(e){return!!(e&&"object"==typeof e&&"dayOfWeek"in e)}function ey(e,t){return Array.isArray(e)&&e.every(t.isDate)}function ew(e,t,n=ec){let r=Array.isArray(t)?t:[t],{isSameDay:o,differenceInCalendarDays:a,isAfter:i}=n;return r.some(t=>{if("boolean"==typeof t)return t;if(n.isDate(t))return o(e,t);if(ey(t,n))return t.includes(e);if(em(t))return ef(t,e,!1,n);if(eg(t))return Array.isArray(t.dayOfWeek)?t.dayOfWeek.includes(e.getDay()):t.dayOfWeek===e.getDay();if(eh(t)){let n=a(t.before,e),r=a(t.after,e),o=n>0,s=r<0;return i(t.before,t.after)?s&&o:o||s}return ep(t)?a(e,t.after)>0:ev(t)?a(t.before,e)>0:"function"==typeof t&&t(e)})}function eb(e){return s.createElement("button",{...e})}function eM(e){return s.createElement("span",{...e})}function ek(e){let{size:t=24,orientation:n="left",className:r}=e;return s.createElement("svg",{className:r,width:t,height:t,viewBox:"0 0 24 24"},"up"===n&&s.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===n&&s.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===n&&s.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===n&&s.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function eD(e){let{day:t,modifiers:n,...r}=e;return s.createElement("td",{...r})}function eN(e){let{day:t,modifiers:n,...r}=e,o=s.useRef(null);return s.useEffect(()=>{n.focused&&o.current?.focus()},[n.focused]),s.createElement("button",{ref:o,...r})}function eC(e){let{options:t,className:n,components:r,classNames:o,...a}=e,i=[o[y.UI.Dropdown],n].join(" "),l=t?.find(({value:e})=>e===a.value);return s.createElement("span",{"data-disabled":a.disabled,className:o[y.UI.DropdownRoot]},s.createElement(r.Select,{className:i,...a},t?.map(({value:e,label:t,disabled:n})=>s.createElement(r.Option,{key:e,value:e,disabled:n},t))),s.createElement("span",{className:o[y.UI.CaptionLabel],"aria-hidden":!0},l?.label,s.createElement(r.Chevron,{orientation:"down",size:18,className:o[y.UI.Chevron]})))}function eO(e){return s.createElement("div",{...e})}function ex(e){return s.createElement("div",{...e})}function eS(e){let{calendarMonth:t,displayIndex:n,...r}=e;return s.createElement("div",{...r},e.children)}function eW(e){let{calendarMonth:t,displayIndex:n,...r}=e;return s.createElement("div",{...r})}function eE(e){return s.createElement("table",{...e})}function eT(e){return s.createElement("div",{...e})}let eP=(0,s.createContext)(void 0);function eI(){let e=(0,s.useContext)(eP);if(void 0===e)throw Error("useDayPicker() must be used within a custom component.");return e}function eL(e){let{components:t}=eI();return s.createElement(t.Dropdown,{...e})}function eU(e){let{onPreviousClick:t,onNextClick:n,previousMonth:r,nextMonth:o,...a}=e,{components:i,classNames:l,labels:{labelPrevious:u,labelNext:d}}=eI(),c=(0,s.useCallback)(e=>{o&&n?.(e)},[o,n]),f=(0,s.useCallback)(e=>{r&&t?.(e)},[r,t]);return s.createElement("nav",{...a},s.createElement(i.PreviousMonthButton,{type:"button",className:l[y.UI.PreviousMonthButton],tabIndex:r?void 0:-1,"aria-disabled":!r||void 0,"aria-label":u(r),onClick:f},s.createElement(i.Chevron,{disabled:!r||void 0,className:l[y.UI.Chevron],orientation:"left"})),s.createElement(i.NextMonthButton,{type:"button",className:l[y.UI.NextMonthButton],tabIndex:o?void 0:-1,"aria-disabled":!o||void 0,"aria-label":d(o),onClick:c},s.createElement(i.Chevron,{disabled:!o||void 0,orientation:"right",className:l[y.UI.Chevron]})))}function e_(e){let{components:t}=eI();return s.createElement(t.Button,{...e})}function eY(e){return s.createElement("option",{...e})}function eF(e){let{components:t}=eI();return s.createElement(t.Button,{...e})}function eA(e){let{rootRef:t,...n}=e;return s.createElement("div",{...n,ref:t})}function ej(e){return s.createElement("select",{...e})}function eH(e){let{week:t,...n}=e;return s.createElement("tr",{...n})}function eB(e){return s.createElement("th",{...e})}function eR(e){return s.createElement("thead",{"aria-hidden":!0},s.createElement("tr",{...e}))}function eZ(e){let{week:t,...n}=e;return s.createElement("th",{...n})}function eq(e){return s.createElement("th",{...e})}function ez(e){return s.createElement("tbody",{...e})}function e$(e){let{components:t}=eI();return s.createElement(t.Dropdown,{...e})}var eG=n(50713);function eX(e,t,n){return(n??new ed(t)).format(e,"LLLL y")}let eQ=eX;function eV(e,t,n){return(n??new ed(t)).format(e,"d")}function eJ(e,t=ec){return t.format(e,"LLLL")}function eK(e,t=ec){return e<10?t.formatNumber(`0${e.toLocaleString()}`):t.formatNumber(`${e.toLocaleString()}`)}function e0(){return""}function e1(e,t,n){return(n??new ed(t)).format(e,"cccccc")}function e2(e,t=ec){return t.format(e,"yyyy")}let e3=e2;function e5(e,t,n){return(n??new ed(t)).format(e,"LLLL y")}let e4=e5;function e9(e,t,n,r){let o=(r??new ed(n)).format(e,"PPPP");return t?.today&&(o=`Today, ${o}`),o}function e7(e,t,n,r){let o=(r??new ed(n)).format(e,"PPPP");return t.today&&(o=`Today, ${o}`),t.selected&&(o=`${o}, selected`),o}let e8=e7;function e6(){return""}function te(e){return"Choose the Month"}function tt(e){return"Go to the Next Month"}function tn(e){return"Go to the Previous Month"}function tr(e,t,n){return(n??new ed(t)).format(e,"cccc")}function to(e,t){return`Week ${e}`}function ta(e){return"Week Number"}function ti(e){return"Choose the Year"}let ts=e=>e instanceof HTMLElement?e:null,tl=e=>[...e.querySelectorAll("[data-animated-month]")??[]],tu=e=>ts(e.querySelector("[data-animated-month]")),td=e=>ts(e.querySelector("[data-animated-caption]")),tc=e=>ts(e.querySelector("[data-animated-weeks]")),tf=e=>ts(e.querySelector("[data-animated-nav]")),th=e=>ts(e.querySelector("[data-animated-weekdays]"));function tm(e,t){let{month:n,defaultMonth:r,today:o=t.today(),numberOfMonths:a=1,endMonth:i,startMonth:s}=e,l=n||r||o,{differenceInCalendarMonths:u,addMonths:d,startOfMonth:c}=t;return i&&0>u(i,l)&&(l=d(i,-1*(a-1))),s&&0>u(l,s)&&(l=s),c(l)}class tp{constructor(e,t,n=ec){this.date=e,this.displayMonth=t,this.outside=!!(t&&!n.isSameMonth(e,t)),this.dateLib=n}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class tv{constructor(e,t){this.days=t,this.weekNumber=e}}class tg{constructor(e,t){this.date=e,this.weeks=t}}function ty(e,t){let[n,r]=(0,s.useState)(e);return[void 0===t?n:t,r]}function tw(e){return!e[y.pL.disabled]&&!e[y.pL.hidden]&&!e[y.pL.outside]}function tb(e,t,n=ec){return ef(e,t.from,!1,n)||ef(e,t.to,!1,n)||ef(t,e.from,!1,n)||ef(t,e.to,!1,n)}function tM(e){let t=e;t.timeZone&&((t={...e}).today&&(t.today=new g(t.today,t.timeZone)),t.month&&(t.month=new g(t.month,t.timeZone)),t.defaultMonth&&(t.defaultMonth=new g(t.defaultMonth,t.timeZone)),t.startMonth&&(t.startMonth=new g(t.startMonth,t.timeZone)),t.endMonth&&(t.endMonth=new g(t.endMonth,t.timeZone)),"single"===t.mode&&t.selected?t.selected=new g(t.selected,t.timeZone):"multiple"===t.mode&&t.selected?t.selected=t.selected?.map(e=>new g(e,t.timeZone)):"range"===t.mode&&t.selected&&(t.selected={from:t.selected.from?new g(t.selected.from,t.timeZone):void 0,to:t.selected.to?new g(t.selected.to,t.timeZone):void 0}));let{components:n,formatters:l,labels:u,dateLib:d,locale:c,classNames:f}=(0,s.useMemo)(()=>{var e,n;let r={...C,...t.locale};return{dateLib:new ed({locale:r,weekStartsOn:t.broadcastCalendar?1:t.weekStartsOn,firstWeekContainsDate:t.firstWeekContainsDate,useAdditionalWeekYearTokens:t.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:t.useAdditionalDayOfYearTokens,timeZone:t.timeZone,numerals:t.numerals},t.dateLib),components:(e=t.components,{...o,...e}),formatters:(n=t.formatters,n?.formatMonthCaption&&!n.formatCaption&&(n.formatCaption=n.formatMonthCaption),n?.formatYearCaption&&!n.formatYearDropdown&&(n.formatYearDropdown=n.formatYearCaption),{...a,...n}),labels:{...i,...t.labels},locale:r,classNames:{...(0,eG.a)(),...t.classNames}}},[t.locale,t.broadcastCalendar,t.weekStartsOn,t.firstWeekContainsDate,t.useAdditionalWeekYearTokens,t.useAdditionalDayOfYearTokens,t.timeZone,t.numerals,t.dateLib,t.components,t.formatters,t.labels,t.classNames]),{captionLayout:h,mode:m,navLayout:p,numberOfMonths:v=1,onDayBlur:w,onDayClick:b,onDayFocus:M,onDayKeyDown:k,onDayMouseEnter:D,onDayMouseLeave:N,onNextClick:O,onPrevClick:x,showWeekNumber:S,styles:W}=t,{formatCaption:E,formatDay:T,formatMonthDropdown:P,formatWeekNumber:I,formatWeekNumberHeader:L,formatWeekdayName:U,formatYearDropdown:_}=l,Y=function(e,t){let[n,r]=function(e,t){let{startMonth:n,endMonth:r}=e,{startOfYear:o,startOfDay:a,startOfMonth:i,endOfMonth:s,addYears:l,endOfYear:u,newDate:d,today:c}=t,{fromYear:f,toYear:h,fromMonth:m,toMonth:p}=e;!n&&m&&(n=m),!n&&f&&(n=t.newDate(f,0,1)),!r&&p&&(r=p),!r&&h&&(r=d(h,11,31));let v="dropdown"===e.captionLayout||"dropdown-years"===e.captionLayout;return n?n=i(n):f?n=d(f,0,1):!n&&v&&(n=o(l(e.today??c(),-100))),r?r=s(r):h?r=d(h,11,31):!r&&v&&(r=u(e.today??c())),[n?a(n):n,r?a(r):r]}(e,t),{startOfMonth:o,endOfMonth:a}=t,i=tm(e,t),[l,u]=ty(i,e.month?i:void 0);(0,s.useEffect)(()=>{u(tm(e,t))},[e.timeZone]);let d=function(e,t,n,r){let{numberOfMonths:o=1}=n,a=[];for(let n=0;n<o;n++){let o=r.addMonths(e,n);if(t&&o>t)break;a.push(o)}return a}(l,r,e,t),c=function(e,t,n,r){let o=e[0],a=e[e.length-1],{ISOWeek:i,fixedWeeks:s,broadcastCalendar:l}=n??{},{addDays:u,differenceInCalendarDays:d,differenceInCalendarMonths:c,endOfBroadcastWeek:f,endOfISOWeek:h,endOfMonth:m,endOfWeek:p,isAfter:v,startOfBroadcastWeek:g,startOfISOWeek:y,startOfWeek:w}=r,b=l?g(o,r):i?y(o):w(o),M=d(l?f(a):i?h(m(a)):p(m(a)),b),k=c(a,o)+1,D=[];for(let e=0;e<=M;e++){let n=u(b,e);if(t&&v(n,t))break;D.push(n)}let N=(l?35:42)*k;if(s&&D.length<N){let e=N-D.length;for(let t=0;t<e;t++){let e=u(D[D.length-1],1);D.push(e)}}return D}(d,e.endMonth?a(e.endMonth):void 0,e,t),f=function(e,t,n,r){let{addDays:o,endOfBroadcastWeek:a,endOfISOWeek:i,endOfMonth:s,endOfWeek:l,getISOWeek:u,getWeek:d,startOfBroadcastWeek:c,startOfISOWeek:f,startOfWeek:h}=r,m=e.reduce((e,m)=>{let p=n.broadcastCalendar?c(m,r):n.ISOWeek?f(m):h(m),v=n.broadcastCalendar?a(m):n.ISOWeek?i(s(m)):l(s(m)),g=t.filter(e=>e>=p&&e<=v),y=n.broadcastCalendar?35:42;if(n.fixedWeeks&&g.length<y){let e=t.filter(e=>{let t=y-g.length;return e>v&&e<=o(v,t)});g.push(...e)}let w=g.reduce((e,t)=>{let o=n.ISOWeek?u(t):d(t),a=e.find(e=>e.weekNumber===o),i=new tp(t,m,r);return a?a.days.push(i):e.push(new tv(o,[i])),e},[]),b=new tg(m,w);return e.push(b),e},[]);return n.reverseMonths?m.reverse():m}(d,c,e,t),h=f.reduce((e,t)=>[...e,...t.weeks],[]),m=function(e){let t=[];return e.reduce((e,n)=>[...e,...n.weeks.reduce((e,t)=>[...e,...t.days],t)],t)}(f),p=function(e,t,n,r){if(n.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:a}=n,{startOfMonth:i,addMonths:s,differenceInCalendarMonths:l}=r,u=o?a??1:1,d=i(e);if(!t||!(0>=l(d,t)))return s(d,-u)}(l,n,e,t),v=function(e,t,n,r){if(n.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:a=1}=n,{startOfMonth:i,addMonths:s,differenceInCalendarMonths:l}=r,u=o?a:1,d=i(e);if(!t||!(l(t,e)<a))return s(d,u)}(l,r,e,t),{disableNavigation:g,onMonthChange:y}=e,w=e=>h.some(t=>t.days.some(t=>t.isEqualTo(e))),b=e=>{if(g)return;let t=o(e);n&&t<o(n)&&(t=o(n)),r&&t>o(r)&&(t=o(r)),u(t),y?.(t)};return{months:f,weeks:h,days:m,navStart:n,navEnd:r,previousMonth:p,nextMonth:v,goToMonth:b,goToDay:e=>{!w(e)&&b(e.date)}}}(t,d),{days:F,months:A,navStart:j,navEnd:H,previousMonth:B,nextMonth:R,goToMonth:Z}=Y,q=function(e,t,n){let{disabled:r,hidden:o,modifiers:a,showOutsideDays:i,broadcastCalendar:s,today:l}=t,{isSameDay:u,isSameMonth:d,startOfMonth:c,isBefore:f,endOfMonth:h,isAfter:m}=n,p=t.startMonth&&c(t.startMonth),v=t.endMonth&&h(t.endMonth),g={[y.pL.focused]:[],[y.pL.outside]:[],[y.pL.disabled]:[],[y.pL.hidden]:[],[y.pL.today]:[]},w={};for(let t of e){let{date:e,displayMonth:c}=t,h=!!(c&&!d(e,c)),y=!!(p&&f(e,p)),b=!!(v&&m(e,v)),M=!!(r&&ew(e,r,n)),k=!!(o&&ew(e,o,n))||y||b||!s&&!i&&h||s&&!1===i&&h,D=u(e,l??n.today());h&&g.outside.push(t),M&&g.disabled.push(t),k&&g.hidden.push(t),D&&g.today.push(t),a&&Object.keys(a).forEach(r=>{let o=a?.[r];o&&ew(e,o,n)&&(w[r]?w[r].push(t):w[r]=[t])})}return e=>{let t={[y.pL.focused]:!1,[y.pL.disabled]:!1,[y.pL.hidden]:!1,[y.pL.outside]:!1,[y.pL.today]:!1},n={};for(let n in g){let r=g[n];t[n]=r.some(t=>t===e)}for(let t in w)n[t]=w[t].some(t=>t===e);return{...t,...n}}}(F,t,d),{isSelected:z,select:$,selected:G}=function(e,t){let n=function(e,t){let{selected:n,required:r,onSelect:o}=e,[a,i]=ty(n,o?n:void 0),s=o?n:a,{isSameDay:l}=t;return{selected:s,select:(e,t,n)=>{let a=e;return!r&&s&&s&&l(e,s)&&(a=void 0),o||i(a),o?.(a,e,t,n),a},isSelected:e=>!!s&&l(s,e)}}(e,t),r=function(e,t){let{selected:n,required:r,onSelect:o}=e,[a,i]=ty(n,o?n:void 0),s=o?n:a,{isSameDay:l}=t,u=e=>s?.some(t=>l(t,e))??!1,{min:d,max:c}=e;return{selected:s,select:(e,t,n)=>{let a=[...s??[]];if(u(e)){if(s?.length===d||r&&s?.length===1)return;a=s?.filter(t=>!l(t,e))}else a=s?.length===c?[e]:[...a,e];return o||i(a),o?.(a,e,t,n),a},isSelected:u}}(e,t),o=function(e,t){let{disabled:n,excludeDisabled:r,selected:o,required:a,onSelect:i}=e,[s,l]=ty(o,i?o:void 0),u=i?o:s;return{selected:u,select:(o,s,d)=>{let{min:c,max:f}=e,h=o?function(e,t,n=0,r=0,o=!1,a=ec){let i;let{from:s,to:l}=t||{},{isSameDay:u,isAfter:d,isBefore:c}=a;if(s||l){if(s&&!l)i=u(s,e)?o?{from:s,to:void 0}:void 0:c(e,s)?{from:e,to:s}:{from:s,to:e};else if(s&&l){if(u(s,e)&&u(l,e))i=o?{from:s,to:l}:void 0;else if(u(s,e))i={from:s,to:n>0?void 0:e};else if(u(l,e))i={from:e,to:n>0?void 0:e};else if(c(e,s))i={from:e,to:l};else if(d(e,s))i={from:s,to:e};else if(d(e,l))i={from:s,to:e};else throw Error("Invalid range")}}else i={from:e,to:n>0?void 0:e};if(i?.from&&i?.to){let t=a.differenceInCalendarDays(i.to,i.from);r>0&&t>r?i={from:e,to:void 0}:n>1&&t<n&&(i={from:e,to:void 0})}return i}(o,u,c,f,a,t):void 0;return r&&n&&h?.from&&h.to&&function(e,t,n=ec){let r=Array.isArray(t)?t:[t];if(r.filter(e=>"function"!=typeof e).some(t=>"boolean"==typeof t?t:n.isDate(t)?ef(e,t,!1,n):ey(t,n)?t.some(t=>ef(e,t,!1,n)):em(t)?!!t.from&&!!t.to&&tb(e,{from:t.from,to:t.to},n):eg(t)?function(e,t,n=ec){let r=Array.isArray(t)?t:[t],o=e.from,a=Math.min(n.differenceInCalendarDays(e.to,e.from),6);for(let e=0;e<=a;e++){if(r.includes(o.getDay()))return!0;o=n.addDays(o,1)}return!1}(e,t.dayOfWeek,n):eh(t)?n.isAfter(t.before,t.after)?tb(e,{from:n.addDays(t.after,1),to:n.addDays(t.before,-1)},n):ew(e.from,t,n)||ew(e.to,t,n):!!(ep(t)||ev(t))&&(ew(e.from,t,n)||ew(e.to,t,n))))return!0;let o=r.filter(e=>"function"==typeof e);if(o.length){let t=e.from,r=n.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=r;e++){if(o.some(e=>e(t)))return!0;t=n.addDays(t,1)}}return!1}({from:h.from,to:h.to},n,t)&&(h.from=o,h.to=void 0),i||l(h),i?.(h,o,s,d),h},isSelected:e=>u&&ef(u,e,!1,t)}}(e,t);switch(e.mode){case"single":return n;case"multiple":return r;case"range":return o;default:return}}(t,d)??{},{blur:X,focused:Q,isFocusTarget:V,moveFocus:J,setFocused:K}=function(e,t,n,o,a){let{autoFocus:i}=e,[l,u]=(0,s.useState)(),d=function(e,t,n,o){let a;let i=-1;for(let s of e){let e=t(s);tw(e)&&(e[y.pL.focused]&&i<r.FocusedModifier?(a=s,i=r.FocusedModifier):o?.isEqualTo(s)&&i<r.LastFocused?(a=s,i=r.LastFocused):n(s.date)&&i<r.Selected?(a=s,i=r.Selected):e[y.pL.today]&&i<r.Today&&(a=s,i=r.Today))}return a||(a=e.find(e=>tw(t(e)))),a}(t.days,n,o||(()=>!1),l),[c,f]=(0,s.useState)(i?d:void 0);return{isFocusTarget:e=>!!d?.isEqualTo(e),setFocused:f,focused:c,blur:()=>{u(c),f(void 0)},moveFocus:(n,r)=>{if(!c)return;let o=function e(t,n,r,o,a,i,s,l=0){if(l>365)return;let u=function(e,t,n,r,o,a,i){let{ISOWeek:s,broadcastCalendar:l}=a,{addDays:u,addMonths:d,addWeeks:c,addYears:f,endOfBroadcastWeek:h,endOfISOWeek:m,endOfWeek:p,max:v,min:g,startOfBroadcastWeek:y,startOfISOWeek:w,startOfWeek:b}=i,M=({day:u,week:c,month:d,year:f,startOfWeek:e=>l?y(e,i):s?w(e):b(e),endOfWeek:e=>l?h(e):s?m(e):p(e)})[e](n,"after"===t?1:-1);return"before"===t&&r?M=v([r,M]):"after"===t&&o&&(M=g([o,M])),M}(t,n,r.date,o,a,i,s),d=!!(i.disabled&&ew(u,i.disabled,s)),c=!!(i.hidden&&ew(u,i.hidden,s)),f=new tp(u,u,s);return d||c?e(t,n,f,o,a,i,s,l+1):f}(n,r,c,t.navStart,t.navEnd,e,a);o&&(t.goToDay(o),f(o))}}}(t,Y,q,z??(()=>!1),d),{labelDayButton:ee,labelGridcell:et,labelGrid:en,labelMonthDropdown:er,labelNav:eo,labelPrevious:ea,labelNext:ei,labelWeekday:es,labelWeekNumber:el,labelWeekNumberHeader:eu,labelYearDropdown:eb}=u,eM=(0,s.useMemo)(()=>(function(e,t,n){let r=e.today(),o=t?e.startOfISOWeek(r):e.startOfWeek(r),a=[];for(let t=0;t<7;t++){let n=e.addDays(o,t);a.push(n)}return a})(d,t.ISOWeek),[d,t.ISOWeek]),ek=void 0!==m||void 0!==b,eD=(0,s.useCallback)(()=>{B&&(Z(B),x?.(B))},[B,Z,x]),eN=(0,s.useCallback)(()=>{R&&(Z(R),O?.(R))},[Z,R,O]),eC=(0,s.useCallback)((e,t)=>n=>{n.preventDefault(),n.stopPropagation(),K(e),$?.(e.date,t,n),b?.(e.date,t,n)},[$,b,K]),eO=(0,s.useCallback)((e,t)=>n=>{K(e),M?.(e.date,t,n)},[M,K]),ex=(0,s.useCallback)((e,t)=>n=>{X(),w?.(e.date,t,n)},[X,w]),eS=(0,s.useCallback)((e,n)=>r=>{let o={ArrowLeft:["day","rtl"===t.dir?"after":"before"],ArrowRight:["day","rtl"===t.dir?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[r.shiftKey?"year":"month","before"],PageDown:[r.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[r.key]){r.preventDefault(),r.stopPropagation();let[e,t]=o[r.key];J(e,t)}k?.(e.date,n,r)},[J,k,t.dir]),eW=(0,s.useCallback)((e,t)=>n=>{D?.(e.date,t,n)},[D]),eE=(0,s.useCallback)((e,t)=>n=>{N?.(e.date,t,n)},[N]),eT=(0,s.useCallback)(e=>t=>{let n=Number(t.target.value);Z(d.setMonth(d.startOfMonth(e),n))},[d,Z]),eI=(0,s.useCallback)(e=>t=>{let n=Number(t.target.value);Z(d.setYear(d.startOfMonth(e),n))},[d,Z]),{className:eL,style:eU}=(0,s.useMemo)(()=>({className:[f[y.UI.Root],t.className].filter(Boolean).join(" "),style:{...W?.[y.UI.Root],...t.style}}),[f,t.className,t.style,W]),e_=function(e){let t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0,"data-nav-layout":e.navLayout||void 0};return Object.entries(e).forEach(([e,n])=>{e.startsWith("data-")&&(t[e]=n)}),t}(t),eY=(0,s.useRef)(null);!function(e,t,{classNames:n,months:r,focused:o,dateLib:a}){let i=(0,s.useRef)(null),l=(0,s.useRef)(r),u=(0,s.useRef)(!1);(0,s.useLayoutEffect)(()=>{let s=l.current;if(l.current=r,!t||!e.current||!(e.current instanceof HTMLElement)||0===r.length||0===s.length||r.length!==s.length)return;let d=a.isSameMonth(r[0].date,s[0].date),c=a.isAfter(r[0].date,s[0].date),f=c?n[y.X5.caption_after_enter]:n[y.X5.caption_before_enter],h=c?n[y.X5.weeks_after_enter]:n[y.X5.weeks_before_enter],m=i.current,p=e.current.cloneNode(!0);if(p instanceof HTMLElement?(tl(p).forEach(e=>{if(!(e instanceof HTMLElement))return;let t=tu(e);t&&e.contains(t)&&e.removeChild(t);let n=td(e);n&&n.classList.remove(f);let r=tc(e);r&&r.classList.remove(h)}),i.current=p):i.current=null,u.current||d||o)return;let v=m instanceof HTMLElement?tl(m):[],g=tl(e.current);if(g&&g.every(e=>e instanceof HTMLElement)&&v&&v.every(e=>e instanceof HTMLElement)){u.current=!0;let t=[];e.current.style.isolation="isolate";let r=tf(e.current);r&&(r.style.zIndex="1"),g.forEach((o,a)=>{let i=v[a];if(!i)return;o.style.position="relative",o.style.overflow="hidden";let s=td(o);s&&s.classList.add(f);let l=tc(o);l&&l.classList.add(h);let d=()=>{u.current=!1,e.current&&(e.current.style.isolation=""),r&&(r.style.zIndex=""),s&&s.classList.remove(f),l&&l.classList.remove(h),o.style.position="",o.style.overflow="",o.contains(i)&&o.removeChild(i)};t.push(d),i.style.pointerEvents="none",i.style.position="absolute",i.style.overflow="hidden",i.setAttribute("aria-hidden","true");let m=th(i);m&&(m.style.opacity="0");let p=td(i);p&&(p.classList.add(c?n[y.X5.caption_before_exit]:n[y.X5.caption_after_exit]),p.addEventListener("animationend",d));let g=tc(i);g&&g.classList.add(c?n[y.X5.weeks_before_exit]:n[y.X5.weeks_after_exit]),o.insertBefore(i,o.firstChild)})}})}(eY,!!t.animate,{classNames:f,months:A,focused:Q,dateLib:d});let eF={dayPickerProps:t,selected:G,select:$,isSelected:z,months:A,nextMonth:R,previousMonth:B,goToMonth:Z,getModifiers:q,components:n,classNames:f,styles:W,labels:u,formatters:l};return s.createElement(eP.Provider,{value:eF},s.createElement(n.Root,{rootRef:t.animate?eY:void 0,className:eL,style:eU,dir:t.dir,id:t.id,lang:t.lang,nonce:t.nonce,title:t.title,role:t.role,"aria-label":t["aria-label"],...e_},s.createElement(n.Months,{className:f[y.UI.Months],style:W?.[y.UI.Months]},!t.hideNavigation&&!p&&s.createElement(n.Nav,{"data-animated-nav":t.animate?"true":void 0,className:f[y.UI.Nav],style:W?.[y.UI.Nav],"aria-label":eo(),onPreviousClick:eD,onNextClick:eN,previousMonth:B,nextMonth:R}),A.map((e,r)=>{let o=function(e,t,n,r,o){let{startOfMonth:a,startOfYear:i,endOfYear:s,eachMonthOfInterval:l,getMonth:u}=o;return l({start:i(e),end:s(e)}).map(e=>{let i=r.formatMonthDropdown(e,o);return{value:u(e),label:i,disabled:t&&e<a(t)||n&&e>a(n)||!1}})}(e.date,j,H,l,d),a=function(e,t,n,r){if(!e||!t)return;let{startOfYear:o,endOfYear:a,addYears:i,getYear:s,isBefore:l,isSameYear:u}=r,d=o(e),c=a(t),f=[],h=d;for(;l(h,c)||u(h,c);)f.push(h),h=i(h,1);return f.map(e=>{let t=n.formatYearDropdown(e,r);return{value:s(e),label:t,disabled:!1}})}(j,H,l,d);return s.createElement(n.Month,{"data-animated-month":t.animate?"true":void 0,className:f[y.UI.Month],style:W?.[y.UI.Month],key:r,displayIndex:r,calendarMonth:e},"around"===p&&!t.hideNavigation&&0===r&&s.createElement(n.PreviousMonthButton,{type:"button",className:f[y.UI.PreviousMonthButton],tabIndex:B?void 0:-1,"aria-disabled":!B||void 0,"aria-label":ea(B),onClick:eD,"data-animated-button":t.animate?"true":void 0},s.createElement(n.Chevron,{disabled:!B||void 0,className:f[y.UI.Chevron],orientation:"rtl"===t.dir?"right":"left"})),s.createElement(n.MonthCaption,{"data-animated-caption":t.animate?"true":void 0,className:f[y.UI.MonthCaption],style:W?.[y.UI.MonthCaption],calendarMonth:e,displayIndex:r},h?.startsWith("dropdown")?s.createElement(n.DropdownNav,{className:f[y.UI.Dropdowns],style:W?.[y.UI.Dropdowns]},"dropdown"===h||"dropdown-months"===h?s.createElement(n.MonthsDropdown,{className:f[y.UI.MonthsDropdown],"aria-label":er(),classNames:f,components:n,disabled:!!t.disableNavigation,onChange:eT(e.date),options:o,style:W?.[y.UI.Dropdown],value:d.getMonth(e.date)}):s.createElement("span",null,P(e.date,d)),"dropdown"===h||"dropdown-years"===h?s.createElement(n.YearsDropdown,{className:f[y.UI.YearsDropdown],"aria-label":eb(d.options),classNames:f,components:n,disabled:!!t.disableNavigation,onChange:eI(e.date),options:a,style:W?.[y.UI.Dropdown],value:d.getYear(e.date)}):s.createElement("span",null,_(e.date,d)),s.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},E(e.date,d.options,d))):s.createElement(n.CaptionLabel,{className:f[y.UI.CaptionLabel],role:"status","aria-live":"polite"},E(e.date,d.options,d))),"around"===p&&!t.hideNavigation&&r===v-1&&s.createElement(n.NextMonthButton,{type:"button",className:f[y.UI.NextMonthButton],tabIndex:R?void 0:-1,"aria-disabled":!R||void 0,"aria-label":ei(R),onClick:eN,"data-animated-button":t.animate?"true":void 0},s.createElement(n.Chevron,{disabled:!R||void 0,className:f[y.UI.Chevron],orientation:"rtl"===t.dir?"left":"right"})),r===v-1&&"after"===p&&!t.hideNavigation&&s.createElement(n.Nav,{"data-animated-nav":t.animate?"true":void 0,className:f[y.UI.Nav],style:W?.[y.UI.Nav],"aria-label":eo(),onPreviousClick:eD,onNextClick:eN,previousMonth:B,nextMonth:R}),s.createElement(n.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===m||"range"===m,"aria-label":en(e.date,d.options,d)||void 0,className:f[y.UI.MonthGrid],style:W?.[y.UI.MonthGrid]},!t.hideWeekdays&&s.createElement(n.Weekdays,{"data-animated-weekdays":t.animate?"true":void 0,className:f[y.UI.Weekdays],style:W?.[y.UI.Weekdays]},S&&s.createElement(n.WeekNumberHeader,{"aria-label":eu(d.options),className:f[y.UI.WeekNumberHeader],style:W?.[y.UI.WeekNumberHeader],scope:"col"},L()),eM.map((e,t)=>s.createElement(n.Weekday,{"aria-label":es(e,d.options,d),className:f[y.UI.Weekday],key:t,style:W?.[y.UI.Weekday],scope:"col"},U(e,d.options,d)))),s.createElement(n.Weeks,{"data-animated-weeks":t.animate?"true":void 0,className:f[y.UI.Weeks],style:W?.[y.UI.Weeks]},e.weeks.map((e,r)=>s.createElement(n.Week,{className:f[y.UI.Week],key:e.weekNumber,style:W?.[y.UI.Week],week:e},S&&s.createElement(n.WeekNumber,{week:e,style:W?.[y.UI.WeekNumber],"aria-label":el(e.weekNumber,{locale:c}),className:f[y.UI.WeekNumber],scope:"row",role:"rowheader"},I(e.weekNumber,d)),e.days.map(e=>{let{date:r}=e,o=q(e);if(o[y.pL.focused]=!o.hidden&&!!Q?.isEqualTo(e),o[y.wc.selected]=z?.(r)||o.selected,em(G)){let{from:e,to:t}=G;o[y.wc.range_start]=!!(e&&t&&d.isSameDay(r,e)),o[y.wc.range_end]=!!(e&&t&&d.isSameDay(r,t)),o[y.wc.range_middle]=ef(G,r,!0,d)}let a=function(e,t={},n={}){let r={...t?.[y.UI.Day]};return Object.entries(e).filter(([,e])=>!0===e).forEach(([e])=>{r={...r,...n?.[e]}}),r}(o,W,t.modifiersStyles),i=function(e,t,n={}){return Object.entries(e).filter(([,e])=>!0===e).reduce((e,[r])=>(n[r]?e.push(n[r]):t[y.pL[r]]?e.push(t[y.pL[r]]):t[y.wc[r]]&&e.push(t[y.wc[r]]),e),[t[y.UI.Day]])}(o,f,t.modifiersClassNames),l=ek||o.hidden?void 0:et(r,o,d.options,d);return s.createElement(n.Day,{key:`${d.format(r,"yyyy-MM-dd")}_${d.format(e.displayMonth,"yyyy-MM")}`,day:e,modifiers:o,className:i.join(" "),style:a,role:"gridcell","aria-selected":o.selected||void 0,"aria-label":l,"data-day":d.format(r,"yyyy-MM-dd"),"data-month":e.outside?d.format(r,"yyyy-MM"):void 0,"data-selected":o.selected||void 0,"data-disabled":o.disabled||void 0,"data-hidden":o.hidden||void 0,"data-outside":e.outside||void 0,"data-focused":o.focused||void 0,"data-today":o.today||void 0},!o.hidden&&ek?s.createElement(n.DayButton,{className:f[y.UI.DayButton],style:W?.[y.UI.DayButton],type:"button",day:e,modifiers:o,disabled:o.disabled||void 0,tabIndex:V(e)?0:-1,"aria-label":ee(r,o,d.options,d),onClick:eC(e,o),onBlur:ex(e,o),onFocus:eO(e,o),onKeyDown:eS(e,o),onMouseEnter:eW(e,o),onMouseLeave:eE(e,o)},T(r,d.options,d)):!o.hidden&&T(e.date,d.options,d))}))))))})),t.footer&&s.createElement(n.Footer,{className:f[y.UI.Footer],style:W?.[y.UI.Footer],role:"status","aria-live":"polite"},t.footer)))}!function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"}(r||(r={}))},74126:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])}}]);