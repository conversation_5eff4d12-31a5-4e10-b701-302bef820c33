# Testing the BackendApiUrl Clear Fix

## Test Scenario

This tests that when a user changes the OrchestratorUrl via the UI, the cached BackendApiUrl is properly cleared to force re-discovery.

## What the Fix Does

1. **Before the fix**: When updating config, the ApiServer preserved the old `BackendApiUrl` value even when `OrchestratorUrl` changed, causing the agent to try connecting to the wrong backend.

2. **After the fix**: When `OrchestratorUrl` changes, the ApiServer now sets `BackendApiUrl = null` before saving the configuration, forcing the SignalR client to re-discover the correct backend URL.

## Key Changes Made

### ApiServer.HandleUpdateConfigAsync()
```csharp
// Check if OrchestratorUrl is changing to clear cached BackendApiUrl
var finalOrchestratorUrl = newConfig.OrchestratorUrl ?? currentConfig.OrchestratorUrl;
var shouldClearBackendApiUrl = !string.IsNullOrEmpty(finalOrchestratorUrl) && 
                               finalOrchestratorUrl != currentConfig.OrchestratorUrl;

// Clear BackendApiUrl when URL changes
BackendApiUrl = shouldClearBackendApiUrl ? null : (newConfig.BackendApiUrl ?? currentConfig.BackendApiUrl),
```

## Expected Behavior After Fix

1. User starts with config:
   ```json
   {
     "OrchestratorUrl": "https://cloud.openautomate.io/tenant-a",
     "BackendApiUrl": "https://api.openautomate.io",
     "MachineKey": "key-123"
   }
   ```

2. User changes OrchestratorUrl in UI to: `https://cloud.openautomate.io/tenant-b`

3. After clicking "Connect", config.json should show:
   ```json
   {
     "OrchestratorUrl": "https://cloud.openautomate.io/tenant-b", 
     "BackendApiUrl": null,
     "MachineKey": "key-123"
   }
   ```

4. The SignalR client will then discover the correct backend URL for tenant-b and cache it.

## Testing Instructions

1. Start the Bot Agent service and UI
2. Configure with a valid OrchestratorUrl and MachineKey
3. Click "Connect" and verify connection works
4. Check config.json to see the cached BackendApiUrl
5. Change the OrchestratorUrl to a different tenant
6. Click "Connect" 
7. **Verify**: config.json should now have `"BackendApiUrl": null` or the newly discovered URL for the new tenant

The key improvement is that step 7 will now correctly clear the old cached BackendApiUrl, allowing the agent to discover and connect to the correct backend for the new tenant.