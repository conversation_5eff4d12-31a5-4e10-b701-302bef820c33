[bot]
name = bot1
description = this is my bot
version = 1.0.1

[agent]
enabled = true
host = localhost
port = 8080

[logging]
level = INFO

[folders]
# Base path for bot folders (relative to user's home directory)
# Examples:
#   Documents/openautomatebot  -> ~/Documents/openautomatebot/bot_name/
#   Documents/automationlab    -> ~/Documents/automationlab/bot_name/
#   /absolute/path             -> /absolute/path/bot_name/
base_path = Documents/openautomatebot

# Create these subfolders automatically
create_subfolders = true

# Comma-separated list of subfolder names to create
subfolder_names = input,output,temp,screenshots,logs
