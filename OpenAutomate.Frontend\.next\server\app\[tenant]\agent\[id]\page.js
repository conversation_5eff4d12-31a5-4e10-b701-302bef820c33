(()=>{var e={};e.id=8757,e.ids=[8757],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6863:(e,t,r)=>{Promise.resolve().then(r.bind(r,61967))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41297:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>i});var n=r(65239),s=r(48088),a=r(31369),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let i={children:["",{children:["[tenant]",{children:["agent",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,61967)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\agent\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\agent\\[id]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[tenant]/agent/[id]/page",pathname:"/[tenant]/agent/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},61967:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\[tenant]\\\\agent\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\agent\\[id]\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66695:(e,t,r)=>{Promise.resolve().then(r.bind(r,81148))},81148:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var n=r(60687),s=r(16189),a=r(29523),o=r(44493),d=r(96834),i=r(78122),l=r(28559),c=r(31158),p=r(43210),m=r(39582),u=r(4344),x=r(63676),h=r(31207),g=r(70891),b=r(20140);let j=e=>"Disconnected"===e?"bg-red-100 text-red-600 border-none":"Busy"===e?"bg-yellow-100 text-yellow-600 border-none":"Available"===e?"bg-green-100 text-green-600 border-none":"bg-gray-100 text-gray-600 border-none";function f({id:e}){let t=(0,s.useRouter)(),r=(0,s.useParams)(),f=r?.tenant,N=(0,x.d)(f),{toast:y}=(0,b.d)(),{data:P,error:A,isLoading:w}=(0,h.Ay)(e?g.DC.agentById(e):null,()=>(0,m.Ri)(e)),C=(0,p.useMemo)(()=>P?{...P,botAgentId:P.id}:null,[P]),k=u.$.app.url,_=N[e]?.status??C?.status??"";return w?(0,n.jsx)("div",{className:"flex items-center justify-center h-full py-10",children:(0,n.jsx)("div",{className:"animate-spin text-primary",children:(0,n.jsx)(i.A,{className:"h-10 w-10"})})}):A?(0,n.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,n.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load agent details."}),(0,n.jsx)(a.$,{variant:"outline",className:"mt-2",onClick:()=>window.location.reload(),children:"Retry"})]}):C?(0,n.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,n.jsxs)(o.Zp,{className:"border rounded-md shadow-sm",children:[(0,n.jsxs)(o.aR,{className:"flex items-center justify-between border-b p-4",children:[(0,n.jsxs)(a.$,{variant:"ghost",size:"sm",className:"gap-1",onClick:()=>{t.back()},children:[(0,n.jsx)(l.A,{className:"h-4 w-4"}),"Back"]}),(0,n.jsxs)(a.$,{variant:"outline",size:"sm",className:"gap-1",onClick:()=>{window.location.href="https://openautomate-agent.s3.ap-southeast-1.amazonaws.com/OpenAutomate.BotAgent.Installer.msi"},children:[(0,n.jsx)(c.A,{className:"h-4 w-4"}),"Download Agent"]})]}),(0,n.jsxs)(o.Wu,{className:"p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(v,{label:"Name",children:C.name}),(0,n.jsx)(v,{label:"Machine name",children:C.machineName})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsx)(v,{label:"Status",children:(0,n.jsx)(d.E,{variant:"outline",className:j(_),children:_})})})]}),(0,n.jsxs)("div",{className:"mt-8 pt-6 border-t",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Connection Information"}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4",children:(0,n.jsxs)("div",{className:"bg-muted p-4 rounded-md",children:[(0,n.jsx)("div",{className:"flex justify-between items-center mb-2",children:(0,n.jsx)("span",{className:"text-sm font-medium",children:"Connection URL"})}),(0,n.jsx)("div",{className:"bg-card p-2 rounded border text-sm font-mono overflow-x-auto",children:`${k}/${f}`}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Use this URL to connect your bot agent to the OpenAutomate platform."})]})})]})]})]})}):(0,n.jsx)("div",{children:"Agent not found"})}function v({label:e,children:t}){return(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:e}),(0,n.jsx)("div",{className:"text-base font-medium pb-1 border-b",children:t})]})}function N(){let e=(0,s.useParams)().id;return(0,n.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,n.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,n.jsx)(f,{id:e})]})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7966,5584,5156,4654,6467,1637,6763,519,3210],()=>r(41297));module.exports=n})();