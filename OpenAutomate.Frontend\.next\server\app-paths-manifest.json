{"/api/connection-info/route": "app/api/connection-info/route.js", "/email/verify/route": "app/email/verify/route.js", "/_not-found/page": "app/_not-found/page.js", "/(auth)/tenant-selector/page": "app/(auth)/tenant-selector/page.js", "/(auth)/verification-pending/page": "app/(auth)/verification-pending/page.js", "/email-verified/page": "app/email-verified/page.js", "/page": "app/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(auth)/forgot-password/page": "app/(auth)/forgot-password/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/(auth)/reset-password/page": "app/(auth)/reset-password/page.js", "/(auth)/[tenant]/invitation/accept/page": "app/(auth)/[tenant]/invitation/accept/page.js", "/(systemAdmin)/org-unit-management/[id]/page": "app/(systemAdmin)/org-unit-management/[id]/page.js", "/(systemAdmin)/dashboard/page": "app/(systemAdmin)/dashboard/page.js", "/[tenant]/administration/license/[id]/page": "app/[tenant]/administration/license/[id]/page.js", "/(systemAdmin)/user-management/page": "app/(systemAdmin)/user-management/page.js", "/(systemAdmin)/settings/page": "app/(systemAdmin)/settings/page.js", "/[tenant]/administration/roles/[id]/page": "app/[tenant]/administration/roles/[id]/page.js", "/[tenant]/agent/[id]/page": "app/[tenant]/agent/[id]/page.js", "/[tenant]/asset/page": "app/[tenant]/asset/page.js", "/[tenant]/asset/[id]/page": "app/[tenant]/asset/[id]/page.js", "/[tenant]/dashboard/page": "app/[tenant]/dashboard/page.js", "/(systemAdmin)/org-unit-management/page": "app/(systemAdmin)/org-unit-management/page.js", "/[tenant]/administration/license/page": "app/[tenant]/administration/license/page.js", "/[tenant]/administration/subscription/page": "app/[tenant]/administration/subscription/page.js", "/[tenant]/administration/organizationUnit/page": "app/[tenant]/administration/organizationUnit/page.js", "/[tenant]/administration/users/page": "app/[tenant]/administration/users/page.js", "/[tenant]/administration/roles/page": "app/[tenant]/administration/roles/page.js", "/[tenant]/agent/page": "app/[tenant]/agent/page.js", "/[tenant]/automation/executions/page": "app/[tenant]/automation/executions/page.js", "/[tenant]/automation/package/page": "app/[tenant]/automation/package/page.js", "/[tenant]/automation/package/[id]/page": "app/[tenant]/automation/package/[id]/page.js", "/[tenant]/automation/schedule/page": "app/[tenant]/automation/schedule/page.js", "/[tenant]/profile/page": "app/[tenant]/profile/page.js", "/[tenant]/automation/page": "app/[tenant]/automation/page.js"}