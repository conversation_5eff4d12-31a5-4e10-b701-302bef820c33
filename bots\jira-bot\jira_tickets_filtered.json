[{"Key": "OA-793", "Summary": "(Test) AutomationPackageServiceTests", "Description": "", "Issue Type": "Subtask", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-25 11:04:59", "Updated": "2025-06-25 11:05:13", "Resolution Date": "", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-792", "Summary": "Edit Schedule", "Description": "", "Issue Type": "Story", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-25 00:35:08", "Updated": "2025-06-25 00:36:13", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-791", "Summary": "Delete Schedule", "Description": "", "Issue Type": "Story", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-25 00:35:02", "Updated": "2025-06-25 00:36:11", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-790", "Summary": "[BUG] Fix DateTime conversion error in schedule", "Description": "", "Issue Type": "Bug", "Status": "In Progress", "Priority": "Medium", "Resolution": "", "Created": "2025-06-24 22:38:13", "Updated": "2025-06-24 22:40:02", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-789", "Summary": "(Upskilling) Quartz.NET", "Description": "", "Issue Type": "Task", "Status": "In Progress", "Priority": "Medium", "Resolution": "", "Created": "2025-06-24 19:47:05", "Updated": "2025-06-24 19:53:45", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-788", "Summary": "Unit test - OrganizationUnitUserService", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-24 15:20:52", "Updated": "2025-06-25 10:58:05", "Resolution Date": "2025-06-25 10:58:05", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-787", "Summary": "Unit test - OrganizationUnitUserController", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-24 14:40:09", "Updated": "2025-06-25 10:58:14", "Resolution Date": "2025-06-25 10:58:14", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-786", "Summary": "Unit test - EmailTestControllerTest", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-24 09:22:40", "Updated": "2025-06-25 10:58:47", "Resolution Date": "2025-06-25 10:58:47", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-785", "Summary": "Unit test - CustomControllerBaseTest", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-24 09:21:13", "Updated": "2025-06-25 10:58:44", "Resolution Date": "2025-06-25 10:58:44", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-784", "Summary": "Unit test - AuthorControllerTest", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-23 20:18:58", "Updated": "2025-06-25 10:58:32", "Resolution Date": "2025-06-25 10:58:32", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-783", "Summary": "Test - OrganizationUnitUser", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-23 20:08:24", "Updated": "2025-06-25 10:58:19", "Resolution Date": "2025-06-25 10:58:19", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-781", "Summary": "Test - Unit test Custom + Email", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-23 19:56:51", "Updated": "2025-06-25 10:58:50", "Resolution Date": "2025-06-25 10:58:50", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-780", "Summary": "(Test) - Unit test Authorization", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-23 19:54:35", "Updated": "2025-06-25 10:58:37", "Resolution Date": "2025-06-25 10:58:37", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-779", "Summary": "(Bug) Deploy TypeScript lint issue", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Frontend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Issue Type": "Bug", "Status": "In Progress", "Priority": "Medium", "Resolution": "", "Created": "2025-06-23 00:24:52", "Updated": "2025-06-23 14:21:04", "Resolution Date": "", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-778", "Summary": "(FE) Create Schedule Integration", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-22 17:12:09", "Updated": "2025-06-25 00:30:03", "Resolution Date": "2025-06-25 00:30:03", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-777", "Summary": "(Docs) Unification 15-Week plan, jira, weekly report", "Description": "", "Issue Type": "Task", "Status": "Review", "Priority": "Medium", "Resolution": "", "Created": "2025-06-21 17:00:32", "Updated": "2025-06-25 00:21:04", "Resolution Date": "", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-776", "Summary": "(Docs) Process Design Document for Bot demo", "Description": "", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-21 04:22:12", "Updated": "2025-06-21 04:22:13", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-775", "Summary": "(Docs) Business Workflow", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-21 04:21:34", "Updated": "2025-06-21 21:00:51", "Resolution Date": "2025-06-21 21:00:51", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-774", "Summary": "(Test) AutomationPackageControllerTests", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'codeBlock', 'content': [{'type': 'text', 'text': '#### AutomationPackageController Tests\\n**File**: `OpenAutomate.API.Tests/ControllerTests/AutomationPackageControllerTests.cs`\\n**Priority**: 🔴 HIGH (Core automation functionality)\\n\\n**Test Cases**:\\n```csharp\\n// Package CRUD\\n- CreatePackage_WithValidData_ReturnsCreated\\n- CreatePackage_WithInvalidData_ReturnsBadRequest\\n- CreatePackage_WithoutAuth_ReturnsUnauthorized\\n- GetPackage_WithValidId_ReturnsPackage\\n- GetPackage_WithInvalidId_ReturnsNotFound\\n- GetPackage_CrossTenant_ReturnsNotFound\\n- GetAllPackages_FiltersByTenant_ReturnsCorrectPackages\\n- UpdatePackage_WithValidData_ReturnsUpdated\\n- UpdatePackage_CrossTenant_ReturnsNotFound\\n- DeletePackage_WithValidId_ReturnsNoContent\\n- DeletePackage_WithActiveExecutions_ReturnsBadRequest\\n\\n// Version Management\\n- UploadVersion_WithValidFile_ReturnsCreated\\n- UploadVersion_WithInvalidFile_ReturnsBadRequest\\n- UploadVersion_WithDuplicateVersion_ReturnsConflict\\n- DownloadVersion_WithValidVersion_ReturnsFile\\n- DownloadVersion_WithInvalidVersion_ReturnsNotFound\\n- DownloadVersion_CrossTenant_ReturnsNotFound\\n- DeleteVersion_WithValidVersion_ReturnsNoContent\\n- DeleteVersion_WithActiveExecutions_ReturnsBadRequest\\n\\n// Error Handling\\n- CreatePackage_WhenServiceThrows_ReturnsInternalServerError\\n- UploadVersion_WhenStorageUnavailable_ReturnsServiceUnavailable\\n```'}]}]}", "Issue Type": "Subtask", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-20 16:36:49", "Updated": "2025-06-21 23:03:32", "Resolution Date": "", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-773", "Summary": "(Test) Unit Test - AutomationPackage", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaGroup', 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'e0ba5c70-ef34-43aa-8251-1a281a1deac8', 'collection': ''}}]}, {'type': 'paragraph'}]}", "Issue Type": "Task", "Status": "In Progress", "Priority": "Medium", "Resolution": "", "Created": "2025-06-20 16:36:05", "Updated": "2025-06-25 11:03:23", "Resolution Date": "", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-772", "Summary": "(Test) CronExpressionServiceTests", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'codeBlock', 'content': [{'type': 'text', 'text': '**File**: `OpenAutomate.Core.Tests/ServiceTests/CronExpressionServiceTests.cs`\\n**Priority**: 🔴 HIGH (Critical for scheduling validation)\\n\\n**Test Cases**:\\n```csharp\\n// Cron Validation\\n- ValidateCronExpression_WithValidExpression_ReturnsTrue\\n- ValidateCronExpression_WithInvalidExpression_ReturnsFalse\\n- ValidateCronExpression_WithNullExpression_ReturnsFalse\\n- ValidateCronExpression_WithEmptyExpression_ReturnsFalse\\n\\n// Next Execution Calculation\\n- GetNextExecutionTime_WithValidCron_ReturnsCorrectTime\\n- GetNextExecutionTime_WithInvalidCron_ThrowsException\\n- GetNextExecutionTimes_WithValidCron_ReturnsMultipleTimes\\n- GetNextExecutionTimes_WithCount_ReturnsCorrectCount\\n\\n// Cron Description\\n- GetCronDescription_WithValidExpression_ReturnsDescription\\n- GetCronDescription_WithInvalidExpression_ThrowsException\\n```'}]}]}", "Issue Type": "Subtask", "Status": "Review", "Priority": "Medium", "Resolution": "", "Created": "2025-06-20 16:34:35", "Updated": "2025-06-21 22:33:13", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-771", "Summary": "(Test) ScheduleServiceTests", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'codeBlock', 'content': [{'type': 'text', 'text': '**File**: `OpenAutomate.Core.Tests/ServiceTests/ScheduleServiceTests.cs`\\n**Priority**: 🔴 HIGH (Quartz.NET integration critical for capstone)\\n\\n**Test Cases**:\\n```csharp\\n// Schedule CRUD\\n- CreateScheduleAsync_WithValidData_ReturnsSchedule\\n- CreateScheduleAsync_WithInvalidCron_ThrowsException\\n- GetScheduleByIdAsync_WithValidId_ReturnsSchedule\\n- GetScheduleByIdAsync_CrossTenant_ReturnsNull\\n- UpdateScheduleAsync_WithValidData_UpdatesSuccessfully\\n- UpdateScheduleAsync_CrossTenant_ThrowsException\\n- DeleteScheduleAsync_WithValidId_DeletesSuccessfully\\n- DeleteScheduleAsync_CrossTenant_ThrowsException\\n\\n// Schedule Management\\n- GetSchedulesAsync_FiltersByTenant_ReturnsCorrectSchedules\\n- EnableScheduleAsync_WithValidId_EnablesSuccessfully\\n- DisableScheduleAsync_WithValidId_DisablesSuccessfully\\n- GetNextExecutionTimeAsync_WithValidCron_ReturnsCorrectTime\\n- GetNextExecutionTimeAsync_WithInvalidCron_ThrowsException\\n\\n// Quartz Integration\\n- ScheduleJobAsync_WithValidSchedule_CreatesQuartzJob\\n- UnscheduleJobAsync_WithValidSchedule_RemovesQuartzJob\\n- UpdateQuartzJobAsync_WithModifiedSchedule_UpdatesJob\\n```'}]}]}", "Issue Type": "Subtask", "Status": "Review", "Priority": "Medium", "Resolution": "", "Created": "2025-06-20 16:33:28", "Updated": "2025-06-21 22:33:10", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-770", "Summary": "(Bug) Edit Calendar Component Shadcn", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Adjust the text color to ensure proper visibility and contrast in dark mode.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Replace the Vietnamese word '}, {'type': 'text', 'text': '\"Tháng\"', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' with its English equivalent (e.g., \"Month\") throughout the relevant components or UI sections.'}]}]}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '67240c73-b3cf-4b1a-af75-7ae8cfd08e8c', 'alt': 'image-20250619-182652.png', 'collection': '', 'height': 350, 'width': 259}}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-20 01:25:52", "Updated": "2025-06-21 23:00:18", "Resolution Date": "2025-06-21 23:00:17", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-769", "Summary": "(FE) Modify theme color dark mode", "Description": "", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-18 21:28:12", "Updated": "2025-06-18 21:28:12", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-768", "Summary": "(Test) Unit test SchedulesController", "Description": "", "Issue Type": "Subtask", "Status": "Review", "Priority": "Medium", "Resolution": "", "Created": "2025-06-17 20:57:51", "Updated": "2025-06-20 16:32:16", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-766", "Summary": "(Python) Bot template work with execution id", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-17 20:31:05", "Updated": "2025-06-19 19:21:35", "Resolution Date": "2025-06-19 19:21:35", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-765", "Summary": "(Test) Unit Test - Scheduling", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaGroup', 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'e7760093-b59a-48fa-a5cb-65784a7be8dc', 'collection': ''}}]}, {'type': 'paragraph'}]}", "Issue Type": "Task", "Status": "Review", "Priority": "Medium", "Resolution": "", "Created": "2025-06-17 18:40:17", "Updated": "2025-06-21 23:00:28", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-764", "Summary": "Create a basic schedule", "Description": "", "Issue Type": "Story", "Status": "Review", "Priority": "Medium", "Resolution": "", "Created": "2025-06-17 18:25:51", "Updated": "2025-06-25 00:32:10", "Resolution Date": "", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-763", "Summary": "(FE) (Bug) User management API returns 403 for OWNER role", "Description": "", "Issue Type": "Subtask", "Status": "In Progress", "Priority": "Medium", "Resolution": "", "Created": "2025-06-17 16:19:03", "Updated": "2025-06-17 16:19:11", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-762", "Summary": "View Logs for each execution completed", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-17 14:50:05", "Updated": "2025-06-21 23:00:03", "Resolution Date": "2025-06-21 23:00:03", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-761", "Summary": "(Bug) User management API returns 403 for OWNER role", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-15 17:50:33", "Updated": "2025-06-25 00:37:20", "Resolution Date": "2025-06-17 18:26:44", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-760", "Summary": "(Bug) Roles page layout inconsistent", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Chỉnh layout cho thống nhất vs các tab khác nhé'}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '54e25560-b267-4c3b-a5d4-1ab8aacbc815', 'alt': 'image-20250614-121132.png', 'collection': '', 'height': 517, 'width': 1527}}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-14 19:10:16", "Updated": "2025-06-17 00:19:36", "Resolution Date": "2025-06-17 00:19:36", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-759", "Summary": "(BE) Bug Form Login issue", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-14 10:46:43", "Updated": "2025-06-17 00:21:47", "Resolution Date": "2025-06-17 00:21:47", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-758", "Summary": "(FE) Bug Form Login issue", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-14 10:06:31", "Updated": "2025-06-17 00:21:45", "Resolution Date": "2025-06-17 00:21:45", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-756", "Summary": "Execution Status Update", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-14 00:39:37", "Updated": "2025-06-17 00:19:40", "Resolution Date": "2025-06-17 00:19:40", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-755", "Summary": "Resend email verification", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'User Story'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Title:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' Resend Email Verification'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': \" registered user who hasn't verified their email address  \"}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'I want', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' to be able to request a new verification email  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'So that', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': \" I can complete my account verification if I didn't receive the original email or if it expired\"}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Description'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'This feature allows authenticated users to resend their email verification if they haven\\'t received the original verification email or if the verification token has expired. The user can trigger this action through a \"Resend Email Verification\" button/link in the application.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a user is logged in and their email is not yet verified  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they click \"Resend Email Verification\"  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a new verification email should be sent to their registered email address'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a user is logged in and their email is already verified  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they attempt to resend verification  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they should receive a message \"Email is already verified\"'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' an unauthenticated user  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they attempt to access the resend verification endpoint  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they should receive an \"Unauthorized\" response'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Implementation'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The backend implementation is already complete in EmailVerificationController.cs:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Endpoint:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'POST /api/email/resend', 'marks': [{'type': 'code'}]}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Authentication:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' Required (Bearer token)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Response Codes:', 'marks': [{'type': 'strong'}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '200 OK: Verification email sent successfully'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '400 Bad Request: Email already verified or failed to send'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '401 Unauthorized: User not authenticated'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '404 Not Found: User not found'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '500 Internal Server Error: Server error'}]}]}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Security & Validation'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Validates user authentication token from JWT claims'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Checks if user exists in the system'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Prevents sending verification emails to already verified accounts'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive logging for audit purposes'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Proper error handling for all edge cases'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Frontend Requirements'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add \"Resend Email Verification\" button/link in user dashboard or profile'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Show success/error messages based on API response'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Disable button after successful send to prevent spam'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Display appropriate UI for already verified users'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'API Response Examples'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Success Response (200):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"Verification email sent\"\\n}'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Already Verified (400):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"Email is already verified\"\\n}'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User Not Found (404):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"User not found\"\\n}'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Definition of Done'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Backend API endpoint implemented and tested ✓ (Already complete)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Frontend UI component for resend button implemented'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Unit tests written and passing'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Integration tests covering happy path and error scenarios'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User acceptance testing completed'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Code reviewed and approved'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Documentation updated'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Story Points: 2'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Priority: Medium'}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-14 00:30:33", "Updated": "2025-06-25 00:13:09", "Resolution Date": "2025-06-25 00:13:09", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-754", "Summary": "(Docs) Draw Usecase Diagram", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-14 00:26:11", "Updated": "2025-06-18 20:42:31", "Resolution Date": "2025-06-18 20:42:30", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-753", "Summary": "(Bug) Form Login issue", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'orderedList', 'attrs': {'order': 1}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Đặt lại vị trí Forgot password xuống dưới password.'}]}]}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': '      UX khó chịu thực sự'}]}, {'type': 'mediaSingle', 'attrs': {'width': 50, 'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '57e539ae-7ad8-4362-9b2d-17932cce96ee', 'alt': 'image-20250613-133550.png', 'collection': '', 'height': 526, 'width': 837}}]}, {'type': 'orderedList', 'attrs': {'order': 2}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Chỉnh lại message, sai mật khẩu thì báo sai mật khẩu (có thể chỉnh cả BE và FE)'}]}]}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'bb5ae47b-328f-4007-9682-da0aa72910c0', 'alt': 'image-20250613-133936.png', 'collection': '', 'height': 873, 'width': 909}}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-13 20:34:58", "Updated": "2025-06-17 00:19:51", "Resolution Date": "2025-06-17 00:19:51", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-752", "Summary": "(FE)Resend email verification", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Frontend Requirements'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add \"Resend Email Verification\" button/link in user dashboard or profile'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Show success/error messages based on API response'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Disable button after successful send to prevent spam'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Display appropriate UI for already verified users'}]}]}]}]}", "Issue Type": "Subtask", "Status": "In Progress", "Priority": "Medium", "Resolution": "", "Created": "2025-06-13 03:23:35", "Updated": "2025-06-14 00:37:53", "Resolution Date": "", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-751", "Summary": "(Bug) Roles are not isolated", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-12 01:58:05", "Updated": "2025-06-14 00:44:52", "Resolution Date": "2025-06-13 20:44:40", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-750", "Summary": "(Bug) Schedule - Calendar UI issue", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Frontend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': '![Image]('}, {'type': 'text', 'text': 'https://github.com/user-attachments/assets/05008f7f-bfdf-424f-833c-dbaae91376dc', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/user-attachments/assets/05008f7f-bfdf-424f-833c-dbaae91376dc'}}]}, {'type': 'text', 'text': ')'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-11 22:01:06", "Updated": "2025-06-16 00:26:02", "Resolution Date": "2025-06-16 00:26:01", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-749", "Summary": "BotAgentConnectionController", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Connection management'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Machine key validation'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:27:34", "Updated": "2025-06-13 20:40:46", "Resolution Date": "2025-06-13 20:40:46", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-748", "Summary": "BotAgentAssetController", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Asset access authorization'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Machine key authentication'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:27:07", "Updated": "2025-06-13 20:40:43", "Resolution Date": "2025-06-13 20:40:43", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-747", "Summary": "EmailVerificationController", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Email verification flow'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Token validation'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:25:21", "Updated": "2025-06-13 20:40:39", "Resolution Date": "2025-06-13 20:40:39", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-746", "Summary": "OrganizationUnitInvitationController test", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Invitation CRUD'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Invitation acceptance flow'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:24:50", "Updated": "2025-06-13 20:40:41", "Resolution Date": "2025-06-13 20:40:41", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-745", "Summary": "(Test) Execution Unit Test - API", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:23:35", "Updated": "2025-06-14 00:45:15", "Resolution Date": "2025-06-13 20:40:48", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-744", "Summary": "IOrganizationUnitService test", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Multi-tenant isolation tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Invitation management tests'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:19:06", "Updated": "2025-06-17 00:22:20", "Resolution Date": "2025-06-17 00:22:20", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-743", "Summary": "IExecutionService test", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:16:13", "Updated": "2025-06-17 00:22:18", "Resolution Date": "2025-06-17 00:22:18", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-742", "Summary": "IAssetService test", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAssetValueForBotAgentAsync (security testing)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Asset encryption/decryption tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Authorization validation tests'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:14:49", "Updated": "2025-06-17 00:22:16", "Resolution Date": "2025-06-17 00:22:16", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-741", "Summary": "IAutomationPackageService", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:13:03", "Updated": "2025-06-17 00:22:15", "Resolution Date": "2025-06-17 00:22:15", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-740", "Summary": "Domain Entity tests", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'OrganizationUnitInvitation', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' entity tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'PasswordResetToken', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' entity tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Entity validation tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Entity relationship constraint tests'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:12:39", "Updated": "2025-06-17 00:22:13", "Resolution Date": "2025-06-17 00:22:13", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-739", "Summary": "(Test) Execution Unit Test - Core", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Core project'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 19:12:05", "Updated": "2025-06-16 00:26:14", "Resolution Date": "2025-06-16 00:26:13", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-738", "Summary": "(BE)Resend email verification", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'User Story'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Title:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' Resend Email Verification'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': \" registered user who hasn't verified their email address  \"}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'I want', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' to be able to request a new verification email  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'So that', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': \" I can complete my account verification if I didn't receive the original email or if it expired\"}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Description'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'This feature allows authenticated users to resend their email verification if they haven\\'t received the original verification email or if the verification token has expired. The user can trigger this action through a \"Resend Email Verification\" button/link in the application.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a user is logged in and their email is not yet verified  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they click \"Resend Email Verification\"  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a new verification email should be sent to their registered email address'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a user is logged in and their email is already verified  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they attempt to resend verification  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they should receive a message \"Email is already verified\"'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' an unauthenticated user  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they attempt to access the resend verification endpoint  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they should receive an \"Unauthorized\" response'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Implementation'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The backend implementation is already complete in EmailVerificationController.cs:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Endpoint:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'POST /api/email/resend', 'marks': [{'type': 'code'}]}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Authentication:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' Required (Bearer token)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Response Codes:', 'marks': [{'type': 'strong'}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '200 OK: Verification email sent successfully'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '400 Bad Request: Email already verified or failed to send'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '401 Unauthorized: User not authenticated'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '404 Not Found: User not found'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '500 Internal Server Error: Server error'}]}]}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Security & Validation'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Validates user authentication token from JWT claims'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Checks if user exists in the system'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Prevents sending verification emails to already verified accounts'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive logging for audit purposes'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Proper error handling for all edge cases'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Frontend Requirements'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add \"Resend Email Verification\" button/link in user dashboard or profile'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Show success/error messages based on API response'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Disable button after successful send to prevent spam'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Display appropriate UI for already verified users'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'API Response Examples'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Success Response (200):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"Verification email sent\"\\n}'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Already Verified (400):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"Email is already verified\"\\n}'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User Not Found (404):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"User not found\"\\n}'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Definition of Done'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Backend API endpoint implemented and tested ✓ (Already complete)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Frontend UI component for resend button implemented'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Unit tests written and passing'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Integration tests covering happy path and error scenarios'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User acceptance testing completed'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Code reviewed and approved'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Documentation updated'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Story Points: 2'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Priority: Medium'}]}]}", "Issue Type": "Subtask", "Status": "In Progress", "Priority": "Medium", "Resolution": "", "Created": "2025-06-10 17:37:24", "Updated": "2025-06-21 17:11:32", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-735", "Summary": "(Agent) Update Agent Installer to include executor and new UI", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-10 11:05:28", "Updated": "2025-06-12 23:52:54", "Resolution Date": "2025-06-12 23:52:54", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-734", "Summary": "(Epic) DevOps and Infrastructure", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Development operations including CI/CD pipelines, deployment automation, logging enhancement, and infrastructure management. Covers build automation, deployment processes, and monitoring setup.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:41", "Updated": "2025-06-09 18:54:41", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-733", "Summary": "(Epic) System Administration", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'System-level administration features for platform management including user management, organization unit oversight, and system monitoring. Covers admin dashboard, user management, and system-wide controls.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:41", "Updated": "2025-06-09 18:54:41", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-732", "Summary": "(Epic) User Management and Authentication", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Complete user management system including registration, password reset, profile management, and authentication flows. Covers user CRUD operations, password management, and authentication UI.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:40", "Updated": "2025-06-09 18:54:40", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-731", "Summary": "(Epic) Public Website and Guest Experience", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Public-facing website with guest UI, landing pages, and marketing content. Covers splash screens, hero sections, about us, contact pages, and multi-language support.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:40", "Updated": "2025-06-09 18:54:40", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-730", "Summary": "(Epic) Testing and Quality Assurance", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive testing infrastructure including unit tests, integration tests, and CI/CD pipeline setup. Covers controller tests, repository tests, infrastructure layer testing, and automated testing workflows.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:39", "Updated": "2025-06-09 18:54:39", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-729", "Summary": "(Epic) Automation Execution Engine", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Python-based automation execution system including script execution, template management, and execution tracking. Covers executor application, Python templates, and execution monitoring.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:39", "Updated": "2025-06-09 18:54:39", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-728", "Summary": "(Epic) Frontend Core Infrastructure", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Core frontend application infrastructure including UI layout, components, authentication integration, and real-time updates. Covers React application setup, component library, routing, and SignalR client integration.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:39", "Updated": "2025-06-09 18:54:39", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-727", "Summary": "(Epic) Asset Management System", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive automation asset management including creation, editing, deletion, retrieval, and agent assignment. Covers asset CRUD operations, encryption, OData APIs, and agent-asset relationships.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:38", "Updated": "2025-06-09 18:54:38", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-726", "Summary": "(Epic) Bot Agent Management System", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Complete bot agent lifecycle management including registration, connection, status monitoring, and real-time communication. Covers agent creation, Windows service implementation, WPF UI, SignalR infrastructure, and agent status tracking.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:38", "Updated": "2025-06-09 18:54:38", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-725", "Summary": "(Epic) Core Platform Foundation", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Foundational platform components including authentication, multi-tenant architecture, project setup, and core infrastructure. Covers user login, organization unit creation, multi-tenant data architecture, project setup, and base architecture patterns.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-09 18:54:37", "Updated": "2025-06-09 18:54:37", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-722", "Summary": "(FE) OU owner - assign role to user in OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-09 14:04:00", "Updated": "2025-06-10 22:29:19", "Resolution Date": "2025-06-10 22:29:19", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-721", "Summary": "(BE) OU owner - assign role to user in OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-09 14:03:40", "Updated": "2025-06-10 22:29:17", "Resolution Date": "2025-06-10 22:29:17", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-720", "Summary": "(Docs) Create \"How to use Agent\" for docsites", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-09 01:29:17", "Updated": "2025-06-13 20:44:36", "Resolution Date": "2025-06-13 20:44:36", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-718", "Summary": "(Docs) Create \"About Agent\" for docsite", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-09 01:28:13", "Updated": "2025-06-13 20:44:35", "Resolution Date": "2025-06-13 20:44:35", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-716", "Summary": "(Agent) Change the UI of Agent App", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-09 00:48:09", "Updated": "2025-06-16 00:26:35", "Resolution Date": "2025-06-16 00:26:35", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-715", "Summary": "Deployment with vercel (publicSite)", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Deployment URL: '}, {'type': 'inlineCard', 'attrs': {'url': 'https://open-automate-public-site.vercel.app/'}}, {'type': 'text', 'text': '  '}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-08 22:11:11", "Updated": "2025-06-09 10:17:10", "Resolution Date": "2025-06-08 22:11:27", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-714", "Summary": "Executions View History", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-08 01:29:02", "Updated": "2025-06-25 00:12:36", "Resolution Date": "2025-06-19 17:30:47", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-711", "Summary": "Could not view the executions page", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Frontend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': '![Image]('}, {'type': 'text', 'text': 'https://github.com/user-attachments/assets/d96ce5e9-3e59-4bc6-8726-7eb5ababc5dd', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/user-attachments/assets/d96ce5e9-3e59-4bc6-8726-7eb5ababc5dd'}}]}, {'type': 'text', 'text': ')'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 23:40:40", "Updated": "2025-06-09 00:47:17", "Resolution Date": "2025-06-09 00:47:17", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-698", "Summary": "(FE) Implement loading states and error boundaries", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add comprehensive loading states, error boundaries, and graceful error handling throughout the application.'}]}]}", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:50", "Updated": "2025-06-22 22:12:38", "Resolution Date": "", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-661", "Summary": "(FE) Create platform-wide analytics dashboard", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a system admin, I want platform-wide analytics so I can understand usage patterns and make informed decisions.'}]}]}", "Issue Type": "Task", "Status": "In Progress", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:38", "Updated": "2025-06-17 14:54:41", "Resolution Date": "", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-650", "Summary": "(FE) Create system admin dashboard", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a system admin, I want a comprehensive dashboard so I can monitor and manage the entire platform effectively.'}]}]}", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:34", "Updated": "2025-06-25 00:33:18", "Resolution Date": "", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-642", "Summary": "(FE) Create personal profile management", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a user, I want to manage my personal profile so I can keep my information up to date and customize my experience.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:17:31", "Updated": "2025-06-24 19:51:30", "Resolution Date": "2025-06-24 19:51:30", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-608", "Summary": "(BE) Quartz.NET Integration for Scheduling", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add notification actions (mark as read, delete) and bulk operations for managing multiple notifications at once.'}]}]}", "Issue Type": "Subtask", "Status": "Review", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:20", "Updated": "2025-06-25 00:30:48", "Resolution Date": "", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-607", "Summary": "(BE) API - Delete Schedule", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement filtering by notification type, date, read status, and search functionality for finding specific notifications.'}]}]}", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:19", "Updated": "2025-06-25 00:36:06", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-606", "Summary": "(BE) API - Edit Schedule", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create notification list component with pagination, infinite scroll, and efficient rendering for large notification counts.'}]}]}", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:19", "Updated": "2025-06-25 00:35:56", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-605", "Summary": "(BE) API - Create Schedule", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a user, I want a notification center so I can see all my notifications in one place and manage them easily.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:17:18", "Updated": "2025-06-25 00:29:58", "Resolution Date": "2025-06-25 00:29:58", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-604", "Summary": "(BE) Schedule Management API - View Schedules", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build API endpoints for retrieving, marking as read, and managing in-app notifications.'}]}]}", "Issue Type": "Subtask", "Status": "Review", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:18", "Updated": "2025-06-25 00:34:09", "Resolution Date": "", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-603", "Summary": "(FE) Delete Schedule", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement notification persistence with automatic cleanup policies and read/unread status management.'}]}]}", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:18", "Updated": "2025-06-25 00:35:31", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-602", "Summary": "(FE) Edit Schedule", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create real-time notification delivery using SignalR with connection management and offline message queuing.'}]}]}", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:17", "Updated": "2025-06-25 00:35:41", "Resolution Date": "", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-601", "Summary": "(FE) Create Schedule", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design data models for in-app notifications including content, metadata, and user interaction tracking.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:17:17", "Updated": "2025-06-25 00:30:02", "Resolution Date": "2025-06-25 00:30:02", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-572", "Summary": "(FE) Create PDF report export", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build PDF export functionality for formatted reports with charts, tables, and professional layout.'}]}]}", "Issue Type": "Task", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:17:06", "Updated": "2025-06-22 22:13:25", "Resolution Date": "", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-523", "Summary": "(FE) Add view logs functionality", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement log export capabilities in various formats (TXT, CSV, JSON) for external analysis and reporting.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:49", "Updated": "2025-06-21 23:00:01", "Resolution Date": "2025-06-21 23:00:01", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-518", "Summary": "(FE) Implement SignalR client connection for Execution Status", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Setup SignalR client connection with automatic reconnection and connection state management.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:47", "Updated": "2025-06-16 00:27:15", "Resolution Date": "2025-06-16 00:27:15", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-516", "Summary": "(Agent) Agent Executor Integration and ExecutionId Flow", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create automated log retention policies and cleanup processes to manage storage and maintain performance.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:46", "Updated": "2025-06-19 19:21:37", "Resolution Date": "2025-06-19 19:21:37", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-515", "Summary": "(BE) Database Schema and S3 Log Storage Service Implementation", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement API endpoints for retrieving logs with search, filtering, and pagination capabilities.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:46", "Updated": "2025-06-19 19:21:34", "Resolution Date": "2025-06-19 19:21:34", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-514", "Summary": "(BE) Logs Upload Execution Controller API Endpoints Implementation", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create structured logging service with different log levels, categorization, and efficient storage mechanisms.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:46", "Updated": "2025-06-19 19:21:38", "Resolution Date": "2025-06-19 19:21:38", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-511", "Summary": "(BE) Implement execution status broadcasting", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create service to broadcast execution status changes, progress updates, and completion events to connected clients.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:45", "Updated": "2025-06-16 00:27:07", "Resolution Date": "2025-06-16 00:27:07", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-509", "Summary": "(BE) Add execution history API endpoints", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement API endpoints to retrieve execution history with filtering, pagination, and detailed execution information.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:44", "Updated": "2025-06-19 17:30:44", "Resolution Date": "2025-06-19 17:30:44", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-508", "Summary": "(Agent) Executor and Service to update status to SignalRHub", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create service to track and record execution events, status changes, and completion results for scheduled jobs.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:43", "Updated": "2025-06-16 00:27:12", "Resolution Date": "2025-06-16 00:27:12", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-507", "Summary": "(BE) Design execution history data model", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create data models for storing schedule execution history including status, timestamps, and execution metadata.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:43", "Updated": "2025-06-19 17:30:42", "Resolution Date": "2025-06-19 17:30:42", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-506", "Summary": "(FE) Create schedule edit modal", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a user, I want to edit existing schedules so I can modify timing or parameters without recreating them.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:43", "Updated": "2025-06-21 12:21:33", "Resolution Date": "2025-06-21 12:21:33", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-504", "Summary": "(FE) Implement schedule filtering and search", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add filtering capabilities by status, schedule type, automation, and search functionality for schedule management.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:42", "Updated": "2025-06-25 00:29:59", "Resolution Date": "2025-06-25 00:29:59", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-503", "Summary": "(FE) Create schedule list view component", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a user, I want to view all my scheduled executions in a list so I can see their status and next run times.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:42", "Updated": "2025-06-25 00:29:57", "Resolution Date": "2025-06-25 00:29:57", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-502", "Summary": "(FE) Add recurring pattern selection UI", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement UI components for selecting recurring patterns (daily, weekly, monthly) with visual preview of next execution times.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:41", "Updated": "2025-06-21 23:00:12", "Resolution Date": "2025-06-21 23:00:12", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-500", "Summary": "(FE) Implement date/time picker components", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create reusable date/time picker components with timezone support and validation for schedule creation.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:41", "Updated": "2025-06-21 12:23:27", "Resolution Date": "2025-06-21 12:23:27", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-498", "Summary": "(BE) Create recurring schedule management API", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement CRUD operations for recurring schedules including update, delete, and pause/resume functionality.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:40", "Updated": "2025-06-25 00:29:55", "Resolution Date": "2025-06-25 00:29:55", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-497", "Summary": "(BE) Add cron expression support", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement cron expression parsing, validation, and scheduling with support for complex time patterns.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:40", "Updated": "2025-06-25 00:28:12", "Resolution Date": "2025-06-25 00:28:12", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-496", "Summary": "(BE) Implement recurring schedule patterns", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create support for daily, weekly, monthly recurring patterns with configurable intervals and end dates.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:39", "Updated": "2025-06-25 00:30:01", "Resolution Date": "2025-06-25 00:30:01", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-495", "Summary": "(BE) Add schedule validation and conflict detection", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement validation logic for schedule parameters and detect scheduling conflicts or resource constraints.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:39", "Updated": "2025-06-25 00:28:11", "Resolution Date": "2025-06-25 00:28:11", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-494", "Summary": "(BE) Implement one-time schedule API", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create API endpoints for scheduling one-time automation executions with specific date/time and execution parameters.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:38", "Updated": "2025-06-25 00:28:09", "Resolution Date": "2025-06-25 00:28:09", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-493", "Summary": "(BE) Create schedule model and DTOs", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design and implement data models for scheduled executions including schedule configuration, execution parameters, and validation rules.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:38", "Updated": "2025-06-25 00:28:08", "Resolution Date": "2025-06-25 00:28:08", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-492", "Summary": "(BE) Implement basic job management service", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create service layer for basic job operations including start, stop, pause, resume scheduler and job lifecycle management.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:38", "Updated": "2025-06-25 00:28:06", "Resolution Date": "2025-06-25 00:28:06", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-491", "Summary": "(BE) Create job store configuration", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configure Quartz job store with database persistence, connection pooling, and clustering support for production environment.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:37", "Updated": "2025-06-25 00:28:05", "Resolution Date": "2025-06-25 00:28:05", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-490", "Summary": "(BE) Setup Quartz.NET infrastructure", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Install and configure Quartz.NET package, setup basic scheduler configuration, and create database schema for job persistence.'}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-07 03:16:37", "Updated": "2025-06-25 00:28:03", "Resolution Date": "2025-06-25 00:28:03", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-489", "Summary": "(Epic) Platform Polish and Production Readiness", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Final platform polish including performance optimization, security hardening, accessibility, documentation, and testing.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:37", "Updated": "2025-06-07 03:16:37", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-488", "Summary": "(Epic) Python Bot Development and Marketplace", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Complete Python bot development ecosystem with templates, documentation, tutorials, and marketplace foundation.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:36", "Updated": "2025-06-07 03:16:36", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-487", "Summary": "(Epic) Account Management and System Administration", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Personal account management and comprehensive system administration features for platform management.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:36", "Updated": "2025-06-07 03:16:36", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-486", "Summary": "(Epic) Payment and Credit System", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Complete payment system with LemonSqueezy integration, credit management, trial system, and billing dashboard.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:36", "Updated": "2025-06-07 03:16:36", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-485", "Summary": "(Epic) Multi-Channel Notification System", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive notification system with email, in-app, and push notifications with user preference management.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:35", "Updated": "2025-06-07 03:16:35", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-483", "Summary": "(Epic) OU Analytics and Reporting", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive analytics system with OU-specific metrics, automated reporting, and data export capabilities.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:35", "Updated": "2025-06-07 03:16:35", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-482", "Summary": "(Epic) OU-Based Authorization System", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive permission system with fine-grained access control, role management, and hierarchical OU permissions.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:34", "Updated": "2025-06-07 03:16:34", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-481", "Summary": "(Epic) Execution Monitoring and Logging", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive execution monitoring system with real-time tracking, detailed logging, performance metrics, and user notifications.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:34", "Updated": "2025-06-07 03:16:34", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-480", "Summary": "(Epic) Job Scheduling System", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement comprehensive job scheduling system using Quartz.NET with UI for creating, managing, and monitoring scheduled automation executions.'}]}]}", "Issue Type": "Epic", "Status": "To Do", "Priority": "Medium", "Resolution": "", "Created": "2025-06-07 03:16:33", "Updated": "2025-06-07 03:16:33", "Resolution Date": "", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-335", "Summary": "(FE) List of invitation in OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-05 11:14:28", "Updated": "2025-06-10 22:26:39", "Resolution Date": "2025-06-10 22:26:39", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-334", "Summary": "(BE) API list of invitation in OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-05 11:14:01", "Updated": "2025-06-10 22:26:32", "Resolution Date": "2025-06-10 22:26:32", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-333", "Summary": "(FE) remove user from OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-04 21:27:29", "Updated": "2025-06-10 22:29:37", "Resolution Date": "2025-06-10 22:29:37", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-332", "Summary": "(BE) API remove user from OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-04 21:27:13", "Updated": "2025-06-10 22:29:34", "Resolution Date": "2025-06-10 22:29:34", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-331", "Summary": "Remove init loading effect (publicSite)", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-03 22:03:54", "Updated": "2025-06-07 02:23:01", "Resolution Date": "2025-06-05 19:48:03", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-330", "Summary": "basic SEO public site", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-03 21:19:29", "Updated": "2025-06-09 14:53:38", "Resolution Date": "2025-06-09 14:53:37", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-329", "Summary": "Public Site Animation (FE)", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-03 18:31:03", "Updated": "2025-06-17 00:19:44", "Resolution Date": "2025-06-17 00:19:44", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-328", "Summary": "Pagination & filter for Executions", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-03 18:28:51", "Updated": "2025-06-14 00:27:44", "Resolution Date": "2025-06-13 20:44:45", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-327", "Summary": "Pagination & filter for Packages", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-03 18:27:42", "Updated": "2025-06-14 00:27:50", "Resolution Date": "2025-06-13 20:44:43", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-325", "Summary": "(FE) OU owner - view list of users in OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-02 16:16:19", "Updated": "2025-06-17 00:22:35", "Resolution Date": "2025-06-17 00:22:35", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-324", "Summary": "(BE) API OU owner - view list of users in OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-02 15:36:25", "Updated": "2025-06-17 00:22:32", "Resolution Date": "2025-06-17 00:22:32", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-323", "Summary": "Execution run from Orchestrator (BE API)", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-02 10:30:31", "Updated": "2025-06-08 01:24:57", "Resolution Date": "2025-06-08 01:24:57", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-322", "Summary": "Execution run from Orchestrator (API Integration)", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-02 10:29:52", "Updated": "2025-06-08 01:24:59", "Resolution Date": "2025-06-08 01:24:59", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-321", "Summary": "Execution run from orchestrator", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-06-02 10:28:26", "Updated": "2025-06-08 01:24:54", "Resolution Date": "2025-06-08 01:24:54", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-319", "Summary": "Draw the Product Overview Diagram", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Report 3 - mục 1. Product Overview'}, {'type': 'hardBreak'}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'b7306687-c495-4766-ba81-5bea3557d4aa', 'alt': 'image-20250530-163218.png', 'collection': '', 'height': 1219, 'width': 2887}}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'dùng '}, {'type': 'text', 'text': 'draw.io', 'marks': [{'type': 'link', 'attrs': {'href': 'http://draw.io'}}]}, {'type': 'text', 'text': ' hoặc phần mềm gì đó cloud để có thể chỉnh sửa chung được.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-30 23:29:51", "Updated": "2025-06-08 01:28:02", "Resolution Date": "2025-06-08 01:28:01", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-318", "Summary": "(Docs) Make docsite be better UI", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'OpenAutomate Documentation – OpenAutomate Documentation', 'marks': [{'type': 'link', 'attrs': {'href': 'https://docs.openautomate.me/'}}]}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'make it be better'}, {'type': 'hardBreak'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'chủ yếu là trang chủ. '}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'sửa bố cục và logo lại cho đẹp'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'để ý  tránh làm mất chức năng search'}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'b67502ed-ad06-45f1-bf33-0b1a94e38302', 'alt': 'image-20250529-153420.png', 'collection': '', 'height': 1504, 'width': 3792}}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-29 22:31:50", "Updated": "2025-06-14 00:46:46", "Resolution Date": "2025-06-12 14:36:06", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-317", "Summary": "deploy DocsSite to vercel", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-29 22:07:29", "Updated": "2025-06-07 02:31:43", "Resolution Date": "2025-06-02 10:17:17", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-316", "Summary": "OU owner - view list of roles in OU", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-29 21:47:42", "Updated": "2025-06-09 14:53:19", "Resolution Date": "2025-06-09 14:53:19", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-315", "Summary": "OU owner - view list of invitation in OU", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-29 21:44:39", "Updated": "2025-06-10 22:26:45", "Resolution Date": "2025-06-10 22:26:45", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-314", "Summary": "OU owner - view list of users in OU", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-29 21:44:05", "Updated": "2025-06-10 18:29:45", "Resolution Date": "2025-06-10 18:29:44", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-313", "Summary": "OU owner - assign resource to role in OU", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 19:05:46", "Updated": "2025-06-17 00:19:50", "Resolution Date": "2025-06-17 00:19:49", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-312", "Summary": "OU owner - create role in OU", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 19:05:31", "Updated": "2025-06-10 18:29:57", "Resolution Date": "2025-06-10 18:29:56", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-311", "Summary": "OU owner - assign role to user in OU", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 19:04:48", "Updated": "2025-06-10 22:29:23", "Resolution Date": "2025-06-10 22:29:23", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-310", "Summary": "Remove user from OU", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 19:04:02", "Updated": "2025-06-14 00:28:45", "Resolution Date": "2025-06-10 22:29:41", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-309", "Summary": "(BE) Implement Payment Integration (LemonSqueezy)", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 13:55:37", "Updated": "2025-06-09 20:40:57", "Resolution Date": "2025-06-03 00:33:22", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-308", "Summary": "(BE) Implement System Admin for OU/User Management", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 13:53:34", "Updated": "2025-06-09 20:40:44", "Resolution Date": "2025-06-03 00:33:21", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-307", "Summary": "(BE) Implement Personal Account Management", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 13:07:21", "Updated": "2025-06-09 20:39:26", "Resolution Date": "2025-06-02 10:18:15", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-305", "Summary": "(BE) Implement Notifications", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 05:04:31", "Updated": "2025-06-09 20:39:00", "Resolution Date": "2025-06-02 10:17:04", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-304", "Summary": "(BE) Implement AI Chatbot for Documentation", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 05:03:06", "Updated": "2025-06-09 20:38:48", "Resolution Date": "2025-06-02 10:17:11", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-303", "Summary": "(BE) Implement Analytics per OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 05:00:08", "Updated": "2025-06-09 20:38:35", "Resolution Date": "2025-06-02 10:17:15", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-302", "Summary": "(BE) Implement OU-based Authorization", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 04:58:45", "Updated": "2025-06-09 20:38:22", "Resolution Date": "2025-06-02 10:17:13", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-301", "Summary": "(BE) Implement Execution Logs", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-25 04:57:23", "Updated": "2025-06-09 20:38:10", "Resolution Date": "2025-06-02 10:18:14", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-300", "Summary": "(BE) Implement Execution Monitoring", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-24 21:02:08", "Updated": "2025-06-09 20:37:57", "Resolution Date": "2025-05-25 20:09:49", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-299", "Summary": "(BE) API Agent Edit", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-23 21:09:36", "Updated": "2025-06-07 02:46:17", "Resolution Date": "2025-06-02 10:22:36", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-298", "Summary": "(BE) API for Agent Delete", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-23 17:11:39", "Updated": "2025-06-07 02:46:44", "Resolution Date": "2025-06-02 10:22:26", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-297", "Summary": "bug could not create/get ou list", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-23 12:35:30", "Updated": "2025-05-23 21:27:45", "Resolution Date": "2025-05-23 21:27:44", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-296", "Summary": "Refactor code (BE) - reset password", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'bcee701e-9248-417f-b71e-df798bdd2002', 'alt': 'image-20250522-184651.png', 'collection': '', 'height': 529, 'width': 1417}}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-23 01:46:39", "Updated": "2025-05-23 21:27:08", "Resolution Date": "2025-05-23 21:27:08", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-295", "Summary": "OU Owner - invite user to OU", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-22 22:40:19", "Updated": "2025-06-14 00:45:27", "Resolution Date": "2025-06-10 18:29:34", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-294", "Summary": "Agent View Detail", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-22 19:13:52", "Updated": "2025-05-25 13:09:04", "Resolution Date": "2025-05-25 13:09:04", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-293", "Summary": "Agent <PERSON><PERSON>", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-22 19:12:34", "Updated": "2025-05-29 22:37:45", "Resolution Date": "2025-05-29 22:37:45", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-292", "Summary": "Agent Edit ", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-22 19:12:26", "Updated": "2025-06-02 10:17:12", "Resolution Date": "2025-06-02 10:17:12", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-291", "Summary": "Change Deploy server", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-22 12:59:49", "Updated": "2025-05-23 21:27:58", "Resolution Date": "2025-05-23 21:27:57", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-290", "Summary": "<PERSON><PERSON><PERSON> For Backend", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '25e51c58-2948-4605-8075-d55ac850d791', 'alt': 'image-20250521-162316.png', 'collection': '', 'height': 1373, 'width': 1886}}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Backend nó hay lỗi tenant context, để thuận tiện cho việc debug thì a triển khai cái này.'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'khi code frontend thì ae nhớ note lại cái endpoint nào gây lỗi để tạo issue fix nhé.'}, {'type': 'hardBreak'}, {'type': 'mention', 'attrs': {'id': '712020:97c81d0a-1388-491a-878b-9389a0050aed', 'text': '@Kh<PERSON>h <PERSON>ng', 'accessLevel': '', 'localId': '0281ca5f-ce0d-417d-894a-aeac4ed40ef1'}}, {'type': 'text', 'text': ' '}, {'type': 'mention', 'attrs': {'id': '712020:105a75f3-cc45-4601-baaf-56b6d40c9716', 'text': '@vunlhde160548', 'accessLevel': '', 'localId': '07b943a6-6fee-4117-a476-8e8ae760d1f5'}}, {'type': 'text', 'text': ' '}, {'type': 'mention', 'attrs': {'id': '712020:a5d63575-296a-4a0d-9060-d4b8b9751863', 'text': '@Phan Hoang Nhat (K17 DN)', 'accessLevel': '', 'localId': '7b54039f-4f8a-4714-9cba-c33956046a81'}}, {'type': 'text', 'text': ' '}, {'type': 'mention', 'attrs': {'id': '712020:9bb57e18-5571-4637-987d-93bb3bca0428', 'text': '@Huỳnh Đức Chính', 'accessLevel': '', 'localId': '59ef68f9-7ea2-45fa-a424-f16ec7c24626'}}, {'type': 'text', 'text': ' '}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-21 23:16:45", "Updated": "2025-05-23 21:27:36", "Resolution Date": "2025-05-23 21:27:36", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-289", "Summary": "Support Multi Language for PublicSite", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-21 22:05:49", "Updated": "2025-06-04 23:50:51", "Resolution Date": "2025-06-04 23:50:51", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-288", "Summary": "Modify Asset UI", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '41672d81-fec0-42e1-8772-b28b5330abc0', 'alt': 'image-20250521-115928.png', 'collection': '', 'height': 916, 'width': 1644}}]}, {'type': 'orderedList', 'attrs': {'order': 1}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Dùng màu đang ko hợp lí, tốt hơn hết là không dùng màu mè gì cả, nhìn sẽ gây hiểu nhầm.'}]}]}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '03c1eead-9004-426c-b74c-e1ef04fe0b45', 'alt': 'image-20250521-121125.png', 'collection': '', 'height': 802, 'width': 1201}}]}, {'type': 'orderedList', 'attrs': {'order': 2}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Agent ko bắt buộc, có quyền rỗng, cho phép user tạo asset trước rồi sau này add Agent sau'}]}]}]}, {'type': 'paragraph', 'content': [{'type': 'mention', 'attrs': {'id': '712020:97c81d0a-1388-491a-878b-9389a0050aed', 'text': '@Khánh Hưng', 'accessLevel': '', 'localId': '056c40a2-3d0a-4467-b8a1-21da01d59db5'}}, {'type': 'text', 'text': ' '}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-21 18:58:13", "Updated": "2025-05-23 00:29:08", "Resolution Date": "2025-05-23 00:29:08", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-286", "Summary": "System Admin Dashboard page (FE) ", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '<PERSON>à<PERSON> một trang tách biệt hoàn toàn so với phần tenant.'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': '<PERSON><PERSON> các mục để admin quản lý: quản lý user, quản lý organization unit, quản lý Agents,…'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Lên mạng tham khảo các Design và bắt đầu thiết kế nhé.'}]}, {'type': 'paragraph'}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Note: C<PERSON>i này là dành cho System Admin (quản lý tất cả), nó khác với tenant owner.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-20 02:07:05", "Updated": "2025-06-09 10:16:45", "Resolution Date": "2025-06-04 16:25:30", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-284", "Summary": "(FE) UI to reset password", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-20 00:11:08", "Updated": "2025-06-07 02:49:42", "Resolution Date": "2025-06-02 10:21:47", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-283", "Summary": "(BE) API to reset password", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-20 00:10:55", "Updated": "2025-06-07 02:49:51", "Resolution Date": "2025-06-02 10:21:45", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-282", "Summary": "User - Reset password", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-20 00:09:46", "Updated": "2025-05-29 22:38:00", "Resolution Date": "2025-05-29 22:38:00", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-281", "Summary": "UI forgot password", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-19 23:37:00", "Updated": "2025-06-07 02:50:00", "Resolution Date": "2025-05-25 23:10:00", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-278", "Summary": "Setup Docs Site Project/Repo", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-19 14:02:46", "Updated": "2025-05-23 00:29:12", "Resolution Date": "2025-05-23 00:29:12", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-276", "Summary": "sonarqube test coverage not calculated", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'fix the sonarqube pipeline '}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-19 13:37:29", "Updated": "2025-05-20 02:58:02", "Resolution Date": "2025-05-20 02:58:02", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-274", "Summary": "SignalR client for frontend to update status real-time", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-18 22:33:06", "Updated": "2025-06-07 02:50:07", "Resolution Date": "2025-05-19 14:04:56", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-273", "Summary": "Frontend reverse proxy connection request from agent to backend server", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-18 22:32:38", "Updated": "2025-06-07 02:50:16", "Resolution Date": "2025-05-18 22:33:09", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-271", "Summary": "Asset Frontend Code Enhancement", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'tối ưu code frontend lại cho sạch đẹp'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-18 17:11:54", "Updated": "2025-05-25 13:17:08", "Resolution Date": "2025-05-20 02:58:24", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-270", "Summary": "AssetRepository Test Failed", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '53e8f953-61cd-4cf5-8dac-78b0791bfecd', 'alt': 'image-20250518-100420.png', 'collection': '', 'height': 1064, 'width': 1840}}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'fix để chạy đc pass hết'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-18 17:03:55", "Updated": "2025-06-07 02:50:25", "Resolution Date": "2025-05-19 14:29:38", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-268", "Summary": "Standadize agent status", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.BotAgent'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Status to be: Available, Busy, Disconnected'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-18 16:27:05", "Updated": "2025-06-07 02:50:33", "Resolution Date": "2025-06-07 01:37:05", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-266", "Summary": "Product Introduction (Report 1)", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-17 22:45:16", "Updated": "2025-06-07 02:54:22", "Resolution Date": "2025-05-17 22:45:37", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-265", "Summary": "i18n_Support multiple languages ", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-17 18:29:40", "Updated": "2025-06-07 02:54:30", "Resolution Date": "2025-05-24 20:59:16", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-264", "Summary": "Ui Administration", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-17 18:28:34", "Updated": "2025-05-29 17:17:52", "Resolution Date": "2025-05-29 17:17:52", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-263", "Summary": "Ui Automation", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-17 18:28:15", "Updated": "2025-06-07 01:53:57", "Resolution Date": "2025-05-24 20:56:21", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-262", "Summary": "Bug url agent details and asset details", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Frontend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'the url is wrong tenant'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': '![Image]('}, {'type': 'text', 'text': 'https://github.com/user-attachments/assets/dff2bbfd-91f6-485e-9577-042a892b616c', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/user-attachments/assets/dff2bbfd-91f6-485e-9577-042a892b616c'}}]}, {'type': 'text', 'text': ')'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-17 16:52:55", "Updated": "2025-06-07 02:54:42", "Resolution Date": "2025-06-07 01:38:08", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-261", "Summary": "fix domain-test-asset", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-17 16:08:12", "Updated": "2025-06-07 02:54:53", "Resolution Date": "2025-05-17 16:27:40", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "vunlhde160548", "Reporter Email": ""}, {"Key": "OA-260", "Summary": "API OData for Asset", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-16 15:40:50", "Updated": "2025-06-07 02:55:01", "Resolution Date": "2025-05-16 21:30:15", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-259", "Summary": "Asset for agent to retrieve", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-15 13:54:05", "Updated": "2025-06-07 02:55:10", "Resolution Date": "2025-05-17 16:25:27", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-257", "Summary": "Contact Us Page ", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-14 18:12:24", "Updated": "2025-06-07 02:55:56", "Resolution Date": "2025-05-16 23:01:33", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-256", "Summary": "About Us Page", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-14 18:11:28", "Updated": "2025-05-15 14:42:09", "Resolution Date": "2025-05-15 14:42:09", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-255", "Summary": "Public site Guest UI", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-14 18:10:30", "Updated": "2025-06-09 19:21:18", "Resolution Date": "2025-05-17 16:25:48", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-254", "Summary": "UI Agent", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-13 21:49:44", "Updated": "2025-06-07 02:03:11", "Resolution Date": "2025-05-17 18:28:20", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-253", "Summary": "Set up OData, and API OData for BotAgent", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-13 21:40:06", "Updated": "2025-06-07 02:02:55", "Resolution Date": "2025-06-07 01:20:58", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-251", "Summary": "fix Asset encrypt", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-13 01:56:52", "Updated": "2025-06-07 02:02:30", "Resolution Date": "2025-05-13 02:12:58", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-240", "Summary": "(BE) API for Agent Status Updates", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Repository:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' AssetRepository.cs ('}, {'type': 'text', 'text': 'OpenAutomate.Infrastructure/Repositories/AssetRepository.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' AssetRepositoryTests.cs ('}, {'type': 'text', 'text': 'OpenAutomate.Infrastructure.Tests/Repositories/AssetRepositoryTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddAsset'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAssetById'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'UpdateAsset'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'DeleteAsset'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Any custom queries'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public method is tested for both valid and invalid input.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use in-memory database for integration tests.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use AAA pattern.'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 22:10:47", "Updated": "2025-06-09 20:37:12", "Resolution Date": "2025-05-17 14:42:20", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-239", "Summary": "(BE) API for Agent Registration", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Repository:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' UserRepository.cs ('}, {'type': 'text', 'text': 'OpenAutomate.Infrastructure/Repositories/UserRepository.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' UserRepositoryTests.cs ('}, {'type': 'text', 'text': 'OpenAutomate.Infrastructure.Tests/Repositories/UserRepositoryTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddUser'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetUserById'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'UpdateUser'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'DeleteUser'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Any custom queries'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public method has at least one test for success and one for error path.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use in-memory database for integration tests.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use AAA pattern.'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 22:10:30", "Updated": "2025-06-09 20:37:00", "Resolution Date": "2025-05-17 14:42:18", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-238", "Summary": "(BE) API for Asset Deletion", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'OpenAutomate.Docs/testing/InfrastructureLayerTestingGuide.md at main · OpenAutomateOrg/OpenAutomate.Docs', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/OpenAutomateOrg/OpenAutomate.Docs/blob/main/testing/InfrastructureLayerTestingGuide.md'}}]}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 22:10:14", "Updated": "2025-06-09 20:36:47", "Resolution Date": "2025-05-17 14:42:23", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-237", "Summary": "(BE) API for Asset Update", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' AdminController.cs ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/AdminController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' AdminControllerTests.cs ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/ControllerTests/AdminControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '(List each public action in AdminController, e.g.)'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAdmins'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddAdmin'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'RemoveAdmin'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'UpdateAdmin'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '(…add all public actions found in the controller)'}]}]}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public action has at least one test for success and one for error path (e.g., unauthorized, validation error, not found, exception).'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All dependencies (e.g., IAdminService, ILogger<AdminController>) are mocked.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Tests follow the Arrange-Act-Assert (AAA) pattern.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test method names follow '}, {'type': 'text', 'text': '{ActionName}_{Condition}_{ExpectedResult}', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' convention.'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 21:55:24", "Updated": "2025-06-09 20:36:34", "Resolution Date": "2025-05-15 22:47:23", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-236", "Summary": "(BE) API for Asset Retrieval", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Mục tiêu: '}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User tạo được 1 Asset Với Name, Key - Value.'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Và gán được Agent vào cho Asset đó.'}]}]}]}, {'type': 'paragraph'}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Business là: Sau này những Agent đ<PERSON> được gán thì mới có quyền get Asset về.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 21:42:40", "Updated": "2025-06-09 20:36:21", "Resolution Date": "2025-05-15 22:27:25", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-235", "Summary": "(BE) API for Asset Creation", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 21:41:57", "Updated": "2025-06-07 02:02:11", "Resolution Date": "2025-05-12 21:43:01", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-233", "Summary": "(Docs) Draw Screen Flow & Function Description (Software Requirement Specification)", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 21:35:58", "Updated": "2025-06-09 20:35:56", "Resolution Date": "2025-06-08 01:28:03", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-232", "Summary": "(BE) OrganizationUnitController", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'OrganizationUnitController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/OrganizationUnitController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'OrganizationUnitControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/OrganizationUnitControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetOrganizationUnits', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddOrganizationUnit', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All public actions tested for both success and failure.'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 13:10:29", "Updated": "2025-06-07 21:32:35", "Resolution Date": "2025-05-12 21:07:02", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-231", "Summary": "(Testing) AssetController", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'AssetController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/AssetController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'AssetControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/AssetControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAssets', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddAsset', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All public actions tested for main scenarios.'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 13:10:19", "Updated": "2025-06-09 20:35:31", "Resolution Date": "2025-05-12 20:49:52", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-230", "Summary": "(Testing) BotAgentController", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'BotAgentController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/BotAgentController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'BotAgentControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/BotAgentControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'RegisterAgent', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAgentStatus', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Success and error paths are covered.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All dependencies are mocked.'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 13:10:12", "Updated": "2025-06-09 20:35:18", "Resolution Date": "2025-05-12 21:07:09", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-229", "Summary": "(Testing) AuthenController", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'AuthenController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/AuthenController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'AuthenControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/AuthenControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Login', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Logout', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'RefreshToken', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public action is tested for both valid and invalid input.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Mock all dependencies.'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 13:10:06", "Updated": "2025-06-09 20:35:05", "Resolution Date": "2025-05-12 20:49:49", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-228", "Summary": "(Testing) UserController", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'UserController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/UserController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'UserControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/UserControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'UpdateUserInfo', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'ChangePassword', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public action has at least one test for success and one for error path.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All dependencies ('}, {'type': 'text', 'text': 'IUserService', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ', '}, {'type': 'text', 'text': 'ILogger<UserController>', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ') are mocked.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use AAA pattern.'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 13:09:54", "Updated": "2025-06-09 20:34:53", "Resolution Date": "2025-05-12 13:27:37", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-227", "Summary": "(Testing) Write Unit Tests for Controllers", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 13:07:41", "Updated": "2025-06-09 20:33:41", "Resolution Date": "2025-05-15 22:47:29", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-226", "Summary": "(FE) Update Features Section Animation", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-12 02:21:12", "Updated": "2025-06-09 20:33:29", "Resolution Date": "2025-05-13 03:52:47", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-223", "Summary": "(BE) API to Create OU", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-10 23:33:30", "Updated": "2025-06-09 20:33:16", "Resolution Date": "2025-05-10 23:33:41", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-221", "Summary": "(Agent) API Client to Communicate with Service", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-10 21:57:00", "Updated": "2025-06-09 20:33:04", "Resolution Date": "2025-05-12 22:59:32", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-220", "Summary": "(Agent) Create Basic UI for Agent", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-10 21:15:43", "Updated": "2025-06-09 20:32:52", "Resolution Date": "2025-05-12 22:59:28", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-219", "Summary": "(Testing) Unit Test API for Admin to Edit User Info", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-10 20:38:36", "Updated": "2025-06-09 20:31:02", "Resolution Date": "2025-05-17 14:15:11", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-218", "Summary": "(Testing) Unit Test API for Admin View All Users", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-10 20:37:50", "Updated": "2025-06-09 20:30:49", "Resolution Date": "2025-05-17 14:13:53", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-217", "Summary": "(FE) Update Hero Section Animation", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-09 10:08:10", "Updated": "2025-06-09 20:30:36", "Resolution Date": "2025-05-11 01:38:19", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-216", "Summary": "(FE) Create Splash Screen with Animation AnimeJS", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-09 08:45:11", "Updated": "2025-06-09 20:30:24", "Resolution Date": "2025-05-11 01:22:55", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-215", "Summary": "(FE) Update Animation for Guest UI", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-09 08:40:35", "Updated": "2025-06-09 20:30:11", "Resolution Date": "2025-05-18 16:04:55", "Assignee": "<PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-214", "Summary": "(Agent) Implement SignalR Client for Agent Service", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-08 12:39:53", "Updated": "2025-06-09 20:29:58", "Resolution Date": "2025-05-12 17:48:41", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-213", "Summary": "(BE) Implement BotAgent Hub for Backend", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-08 12:39:23", "Updated": "2025-06-09 20:29:46", "Resolution Date": "2025-05-12 17:48:39", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-212", "Summary": "(BE) API for Admin to Delete User", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-05 23:35:29", "Updated": "2025-06-09 20:29:33", "Resolution Date": "2025-05-12 11:09:45", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-211", "Summary": "(BE) API for Admin to Edit User Info", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-05 23:34:13", "Updated": "2025-06-09 20:29:20", "Resolution Date": "2025-05-09 21:07:37", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-210", "Summary": "(BE) API for Admin to View All Users", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-05 23:32:25", "Updated": "2025-06-09 20:29:08", "Resolution Date": "2025-05-07 13:51:50", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-208", "Summary": "(BE) APIs for Admin to Manage Users", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-05 23:31:15", "Updated": "2025-06-09 20:26:17", "Resolution Date": "2025-05-17 14:15:15", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-205", "Summary": "(FE) Update UI", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-05 22:31:07", "Updated": "2025-06-09 20:22:24", "Resolution Date": "2025-05-28 22:25:23", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-204", "Summary": "(BE) Tenant Routing", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-04 00:56:42", "Updated": "2025-06-09 20:22:12", "Resolution Date": "2025-05-10 23:32:19", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-203", "Summary": "(DevOps) CD for Frontend", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-02 15:19:50", "Updated": "2025-06-09 20:21:59", "Resolution Date": "2025-05-05 23:26:50", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-202", "Summary": "(DevOps) CD for Backend", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-02 15:19:37", "Updated": "2025-06-09 20:21:47", "Resolution Date": "2025-05-05 23:26:49", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-201", "Summary": "(DevOps) CD Pipeline", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-02 15:19:20", "Updated": "2025-06-09 20:21:34", "Resolution Date": "2025-05-05 23:26:55", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-199", "Summary": "fix test coverage", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-05-01 19:09:27", "Updated": "2025-05-01 20:17:27", "Resolution Date": "2025-05-01 20:17:27", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-196", "Summary": "Core Layer Test - Function in TokenService", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-30 09:46:59", "Updated": "2025-05-17 14:13:11", "Resolution Date": "2025-05-17 14:13:11", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-194", "Summary": "(BE) API for Authentication", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-30 03:02:59", "Updated": "2025-04-30 03:05:17", "Resolution Date": "2025-04-30 03:05:17", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-193", "Summary": "(FE) UI and Integration for Organization Unit General Management", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-30 03:00:02", "Updated": "2025-05-10 23:32:25", "Resolution Date": "2025-05-10 23:32:25", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-192", "Summary": "(BE) API for Organization Unit General Managemenmt", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-30 02:59:14", "Updated": "2025-04-30 16:25:31", "Resolution Date": "2025-04-30 16:25:31", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-191", "Summary": "(Task) Core Layer Test - Domain-Test", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'tạo children subtask tương tự, ghi rõ function nào mình test. '}, {'type': 'mention', 'attrs': {'id': '712020:105a75f3-cc45-4601-baaf-56b6d40c9716', 'text': '@vunlhde160548', 'accessLevel': '', 'localId': 'fbe14687-3a5c-477b-8cdb-fbd5ac66f296'}}, {'type': 'text', 'text': ' '}, {'type': 'mention', 'attrs': {'id': '712020:97c81d0a-1388-491a-878b-9389a0050aed', 'text': '@Khánh Hưng', 'accessLevel': '', 'localId': '279667d0-f43b-4359-9163-c8f28c9bd39d'}}, {'type': 'text', 'text': ' '}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-30 02:49:37", "Updated": "2025-06-07 19:27:33", "Resolution Date": "2025-05-17 14:40:12", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-189", "Summary": "Unit test Core ServiceTests", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-29 09:42:40", "Updated": "2025-05-17 14:40:17", "Resolution Date": "2025-05-17 14:40:17", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-188", "Summary": "API code returns a list of OUs after login", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-28 16:13:27", "Updated": "2025-04-29 09:43:04", "Resolution Date": "2025-04-29 09:43:04", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-177", "Summary": "update revoke token", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-26 02:25:34", "Updated": "2025-04-27 04:12:25", "Resolution Date": "2025-04-27 04:12:25", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-176", "Summary": "Map Register API with UI", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-26 00:57:54", "Updated": "2025-04-27 04:14:43", "Resolution Date": "2025-04-27 04:14:43", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON><PERSON>", "Reporter Email": ""}, {"Key": "OA-172", "Summary": "Create modal UI", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 22:45:19", "Updated": "2025-06-07 02:00:58", "Resolution Date": "2025-05-08 12:05:41", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-171", "Summary": "Setup pipeline SonarQube Scan for frontend develop branch", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 22:44:58", "Updated": "2025-04-25 11:08:31", "Resolution Date": "2025-04-25 11:08:31", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-165", "Summary": "Possible null reference return", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Possible null reference return. · Issue #66 · OpenAutomateOrg/OpenAutomate.Backend', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/OpenAutomateOrg/OpenAutomate.Backend/issues/66'}}]}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 18:18:07", "Updated": "2025-06-07 02:05:32", "Resolution Date": "2025-04-26 02:29:56", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-161", "Summary": "Non-nullable property must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Check sonar và fix bug'}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 18:17:52", "Updated": "2025-06-07 02:05:23", "Resolution Date": "2025-04-25 13:28:48", "Assignee": "vunlhde160548", "Assignee Email": "", "Reporter": "Automation for Jira", "Reporter Email": ""}, {"Key": "OA-160", "Summary": "Possible null reference return", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'e1b45b36-56a9-4e78-94ce-74c3d526e10c', 'alt': 'image-20250424-111333.png', 'collection': '', 'height': 428, 'width': 837}}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 18:13:41", "Updated": "2025-06-07 02:04:58", "Resolution Date": "2025-04-27 04:12:33", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-159", "Summary": " async method lacks 'await' operators", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '78d1d413-80f5-4b7c-9d42-6d7ede7c1c72', 'alt': 'image-20250424-111215.png', 'collection': '', 'height': 414, 'width': 833}}]}]}", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 18:12:19", "Updated": "2025-06-07 02:04:21", "Resolution Date": "2025-04-27 04:12:35", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-147", "Summary": "Map Login API with UI", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 00:06:23", "Updated": "2025-06-07 02:00:49", "Resolution Date": "2025-04-26 01:09:35", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-146", "Summary": "Create Protected Routes", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement route protection'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add tenant resolution logic'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create tenant context provider'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 00:06:20", "Updated": "2025-06-07 02:00:37", "Resolution Date": "2025-04-27 04:14:32", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-145", "Summary": "Implement Auth Context Provider", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create Auth provider component'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement token storage strategy'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add authentication state management'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 00:06:16", "Updated": "2025-06-07 02:00:23", "Resolution Date": "2025-04-27 04:14:31", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-144", "Summary": "Create Authentication UI Components", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design and implement login form'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create registration form'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add password reset functionality'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 00:06:12", "Updated": "2025-06-07 02:00:14", "Resolution Date": "2025-04-27 04:14:29", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-143", "Summary": "Implement Tenant Management API", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create organization CRUD endpoints'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement tenant slug validation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add tenant administrator role management'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 00:03:53", "Updated": "2025-04-25 11:09:29", "Resolution Date": "2025-04-25 11:09:29", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-142", "Summary": "Create Core Domain Entities", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement base entity with tenant ID'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create repository pattern with tenant context'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build unit of work pattern'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 00:03:49", "Updated": "2025-04-25 11:09:28", "Resolution Date": "2025-04-25 11:09:28", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-141", "Summary": "Implement EF Core Global Query Filters", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up tenant-based filtering'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create tenant resolution middleware'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement tenant context service'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-24 00:03:45", "Updated": "2025-04-25 11:09:27", "Resolution Date": "2025-04-25 11:09:27", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-140", "Summary": "Send Email Confirm User Registration", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:56:25", "Updated": "2025-06-07 01:58:55", "Resolution Date": "2025-04-27 04:13:29", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-139", "Summary": "Send Email Welcome When Registration Successfully", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:56:20", "Updated": "2025-06-07 01:58:46", "Resolution Date": "2025-04-27 04:13:31", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-138", "Summary": "Authority APIs for organization unit", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:56:13", "Updated": "2025-06-07 01:58:11", "Resolution Date": "2025-04-26 02:31:20", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-137", "Summary": "Create Auth API Endpoints", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement registration endpoint'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement login endpoint'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement token refresh endpoint'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:56:10", "Updated": "2025-06-07 01:58:03", "Resolution Date": "2025-04-26 02:31:17", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-136", "Summary": "Implement JWT Authentication", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create token generation service'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement refresh token mechanism'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configure HTTP-only cookies'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:56:07", "Updated": "2025-06-07 01:57:55", "Resolution Date": "2025-04-26 02:31:15", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-135", "Summary": "Design Auth Domain Model", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create user and organization entities'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement role-based authorization model'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design database schema for auth'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:56:03", "Updated": "2025-06-07 01:57:48", "Resolution Date": "2025-04-26 02:31:13", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-134", "Summary": "Setup pipeline SonarQube Code Scan for backend develop branch", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:51:09", "Updated": "2025-04-23 23:52:13", "Resolution Date": "2025-04-23 23:52:13", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-133", "Summary": "Setup SonarQube", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:51:04", "Updated": "2025-04-23 23:52:12", "Resolution Date": "2025-04-23 23:52:12", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-132", "Summary": "Jira + Github Issue Sync", "Description": "", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:36:49", "Updated": "2025-04-24 16:00:30", "Resolution Date": "2025-04-24 16:00:30", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-131", "Summary": "Set up Email Service", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AWS SES.'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'all information should be in config/ appsetting for dynamic changes, not hard code.'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Cloud Email Sending Service - Amazon Simple Email Service - AWS', 'marks': [{'type': 'link', 'attrs': {'href': 'https://aws.amazon.com/ses/'}}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:36:38", "Updated": "2025-04-24 22:48:11", "Resolution Date": "2025-04-24 22:48:11", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-130", "Summary": "(Agent) Initialize Bot Agent Repository", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up .NET Core Windows Service project'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up WPF client application project'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configure build pipeline'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:36:31", "Updated": "2025-06-09 20:21:22", "Resolution Date": "2025-04-24 22:48:05", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-129", "Summary": "(FE) Initialize Frontend Repository", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up Next.js 14 with TypeScript and App Router'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configure linting and formatting'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Establish component structure'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:36:13", "Updated": "2025-06-09 20:21:09", "Resolution Date": "2025-04-24 22:48:03", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-128", "Summary": "(BE) Initialize Backend Repository", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create '}, {'type': 'inlineCard', 'attrs': {'url': 'http://ASP.NET'}}, {'type': 'text', 'text': '  Core 8 project structure with clean architecture'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement multi-tenant architecture patterns'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up dependency injection'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:35:54", "Updated": "2025-06-09 20:20:57", "Resolution Date": "2025-04-24 22:47:59", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-127", "Summary": "(Bug) Use Model Binding Instead of Raw Request Data", "Description": "", "Issue Type": "Bug", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-23 23:24:30", "Updated": "2025-06-07 19:19:01", "Resolution Date": "2025-04-24 18:13:46", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-118", "Summary": "(FE) Create Table UI", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Table UI and Detail Page'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-22 16:05:21", "Updated": "2025-06-09 19:22:45", "Resolution Date": "2025-05-04 23:12:35", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-117", "Summary": "(FE) Create Tabs UI", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-22 16:04:25", "Updated": "2025-06-09 18:51:06", "Resolution Date": "2025-04-26 15:21:14", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-72", "Summary": "(FE) Create Dashboard UI", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build dashboard layout'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement placeholder widgets'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add basic responsive grid'}]}]}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-17 14:50:41", "Updated": "2025-06-09 19:22:18", "Resolution Date": "2025-04-24 15:59:25", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-70", "Summary": "(FE) Design Application Layout", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create main application shell'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement responsive navigation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design sidebar and header components'}]}]}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-17 14:50:05", "Updated": "2025-06-09 19:22:48", "Resolution Date": "2025-04-23 16:37:52", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-69", "Summary": "(BE) Create Agent Management API", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build CRUD operations for agents'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement agent filtering and search'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add agent status updates'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-17 14:48:53", "Updated": "2025-06-09 20:19:42", "Resolution Date": "2025-05-08 12:41:17", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-68", "Summary": "(BE) Implement Agent Registration", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create agent registration endpoint'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add agent authentication'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement tenant association'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-17 14:48:21", "Updated": "2025-06-09 19:39:49", "Resolution Date": "2025-05-08 12:41:14", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-67", "Summary": "(BE) Design Bot Agent Model", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create bot agent entity'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design agent status tracking system'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add heartbeat mechanism'}]}]}]}]}", "Issue Type": "Subtask", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-17 14:47:45", "Updated": "2025-06-09 19:39:34", "Resolution Date": "2025-05-08 12:41:13", "Assignee": "", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-60", "Summary": "(FE) Authentication Integration and UI", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create authentication UI and context providers for frontend.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-17 14:38:01", "Updated": "2025-06-07 02:01:24", "Resolution Date": "2025-04-30 02:56:02", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-55", "Summary": "(Task) Project Setup and Base Architecture", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up project repositories, infrastructure, and establish core architecture patterns.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-17 14:26:10", "Updated": "2025-06-07 14:30:51", "Resolution Date": "2025-04-24 22:48:12", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-53", "Summary": "(Story) Bot Agent Creation & Connection via Orchestrator", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As an organization unit member, I want to create and connect a new bot agent so that I can execute automation tasks on my infrastructure and manage it from the central platform.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': \"I can access a bot agent creation form from the organization's dashboard\"}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can provide a name and optional description for the bot agent'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Upon creation, the system generates a secure machine key for authentication'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I am presented with clear installation instructions and the machine key'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can download the bot agent installer package directly from the UI'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can run the installer on my target machine and provide the machine key during setup'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The bot agent automatically connects to the central platform using the provided key'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': \"I can see the bot agent's connection status in real-time on the dashboard\"}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The bot agent appears in the list of available agents with its status (online/offline)'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can manage which assets the bot agent has access to'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The bot agent maintains a secure connection with the platform through SignalR'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The bot agent regularly sends heartbeat signals to confirm its online status'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Notes:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement in BotAgentController with proper tenant isolation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use RandomNumberGenerator for cryptographically secure machine key generation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Store machine key with proper hashing for verification'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create AssetBotAgent relationships for asset access control'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use SignalR hub for real-time status communication'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement heartbeat tracking in the database'}]}]}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-14 14:49:26", "Updated": "2025-06-07 14:30:25", "Resolution Date": "2025-05-17 16:24:44", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-52", "Summary": "(Story) Asset Creation via Orchestrator", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-14 14:46:48", "Updated": "2025-06-07 14:30:07", "Resolution Date": "2025-05-17 16:25:38", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-35", "Summary": "(Task) Bugs & Enhancement in Sprint 1", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:14:40", "Updated": "2025-06-07 14:29:53", "Resolution Date": "2025-05-05 23:27:31", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-34", "Summary": "(DevOps) CI Pipeline Setup", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:14:36", "Updated": "2025-06-09 19:38:06", "Resolution Date": "2025-04-25 11:08:37", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-31", "Summary": "(FE) Core UI Layout & Components", "Description": "", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:14:15", "Updated": "2025-06-09 19:37:57", "Resolution Date": "2025-05-05 22:28:11", "Assignee": "<PERSON><PERSON> (K17 DN)", "Assignee Email": "", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-30", "Summary": "(Story) Python-based Automation Template", "Description": "", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:14:11", "Updated": "2025-06-07 14:05:26", "Resolution Date": "2025-06-02 10:17:07", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-29", "Summary": "(FE) Agent Screen UI", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph'}, {'type': 'paragraph', 'content': [{'type': 'mention', 'attrs': {'id': '712020:105a75f3-cc45-4601-baaf-56b6d40c9716', 'text': '@vunlhde160548', 'accessLevel': '', 'localId': 'bd47ea4b-197e-4ab8-bbe0-6ed999b4a49e'}}, {'type': 'text', 'text': '  xem code backend, tham khảo giao diện Agent  '}, {'type': 'text', 'text': 'http://open-bot.live/', 'marks': [{'type': 'link', 'attrs': {'href': 'http://open-bot.live/'}}]}, {'type': 'text', 'text': ' sau đó tích hợp API vào'}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '415871c5-c657-4824-86ef-b00205b13dee', 'alt': 'image-20250512-154923.png', 'collection': '', 'height': 759, 'width': 751}}]}, {'type': 'paragraph'}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Output là:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'User tạo được 1 Agent.'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Cái Agent Key, là lúc tạo xong nó mới popup lên và chỉ hiện 1 lần (như kiểu API key), sau khi đóng popup thì Agent Key ko hiện nữa, có thể hiện thị là: ******00c1'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:14:05", "Updated": "2025-06-07 02:01:09", "Resolution Date": "2025-05-17 16:24:51", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON> (K17 DN)", "Reporter Email": ""}, {"Key": "OA-25", "Summary": "(Story) Asset Retrieve Windows Application", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Integrate real-time updates into the frontend.'}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:13:40", "Updated": "2025-06-07 14:05:15", "Resolution Date": "2025-05-19 14:05:19", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-24", "Summary": "(Story) Agent Executor Application", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Develop the Python script execution engine for the bot agent.'}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:13:36", "Updated": "2025-06-08 01:24:37", "Resolution Date": "2025-06-08 01:24:37", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-23", "Summary": "(BE) SignalR Infrastructure", "Description": "{'type': 'doc', 'version': 1, 'content': []}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:13:32", "Updated": "2025-06-07 13:28:48", "Resolution Date": "2025-05-12 17:48:47", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-22", "Summary": "(Story) Bot Agent WPF UI for Configuration", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a machine operator or automation administrator', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ', I want to be able to configure and monitor my OpenAutomate Bot Agent through a user-friendly interface, so that I can easily connect my machine to the OpenAutomate platform and manage its connection status without requiring technical expertise.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria'}]}, {'type': 'orderedList', 'attrs': {'order': 1}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to see my machine name automatically detected when I launch the application'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to configure required credentials (Server URL and Agent Key) through clear input fields'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to connect and disconnect from the platform with simple button clicks'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to see a clear visual indication of my current connection status'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want my configuration settings to be saved automatically between application restarts'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to set my logging level preference (Debug, Info, Warning, Error)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to receive helpful error messages if connection issues occur'}]}]}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:13:28", "Updated": "2025-06-07 13:27:33", "Resolution Date": "2025-05-12 13:28:54", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-20", "Summary": "(Story) Agent Status Real-time Updates", "Description": "{'type': 'doc', 'version': 1, 'content': []}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:13:15", "Updated": "2025-06-07 13:27:20", "Resolution Date": "2025-05-19 14:05:01", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-19", "Summary": "(Story) Asset Delete", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design and implement the package management UI in the frontend.'}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:13:07", "Updated": "2025-06-07 13:13:02", "Resolution Date": "2025-05-23 00:28:55", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-18", "Summary": "(Story) Bot Agent Windows Service Connection", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As an automation administrator', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ', I want to install and manage a Windows service for the OpenAutomate Bot Agent, so that it runs automatically on system startup, maintains connection to the OpenAutomate platform, and executes automation tasks without requiring manual intervention or an open user interface.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria'}]}, {'type': 'orderedList', 'attrs': {'order': 1}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want the Bot Agent to run as a Windows service that starts automatically with the operating system'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want the service to establish connection to the OpenAutomate platform using saved configuration'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want the service to automatically reconnect if the connection is lost'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to be able to start, stop, and restart the service through standard Windows service controls'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Notes'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Windows Service implementation using .NET'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configuration shared with UI application'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Robust error handling and reconnection logic'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Event logging with rotating file storage'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'SignalR for real-time communication with platform'}]}]}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:13:01", "Updated": "2025-06-07 13:12:37", "Resolution Date": "2025-05-12 13:28:47", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-17", "Summary": "(Story) Asset Edit", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Develop the automation package system for storing and managing automation scripts.'}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:12:50", "Updated": "2025-06-07 13:09:08", "Resolution Date": "2025-05-23 00:28:52", "Assignee": "<PERSON><PERSON><PERSON><PERSON>", "Assignee Email": "", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-16", "Summary": "(Story) OU Creation", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As an authenticated user, I want to create a new organization unit so that I can establish a dedicated tenant space for my team or department with proper isolation of automation assets and processes.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can access an organization unit creation form from the dashboard after logging in'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can specify a name for the organization unit which will automatically generate a URL slug'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The system validates the name and slug for uniqueness across the platform'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I receive clear error messages if validation fails'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The system automatically creates default authority roles (OWNER, MANAGER, DEVELOPER, USER) for the new organization unit'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I am automatically assigned the OWNER role for the newly created organization unit'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can optionally invite other users during the creation process'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Upon successful creation, the system provides a confirmation with the generated tenant URL'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The new organization unit is immediately accessible via its tenant URL path'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Data created within this organization unit is properly isolated from other tenants'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Notes:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement in OrganizationUnitController with standard authentication check'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use TenantQueryFilterService to ensure proper data isolation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Generate slugs using normalized form of the organization name (lowercase, hyphens for spaces)'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Ensure proper database indexes for OrganizationUnitId fields'}]}]}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:12:45", "Updated": "2025-06-09 19:36:06", "Resolution Date": "2025-05-12 13:29:22", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-15", "Summary": "(BE) Bot Agent Model & Connection", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement bot agent registration and management system.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:12:38", "Updated": "2025-06-07 01:57:40", "Resolution Date": "2025-05-10 23:37:24", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-14", "Summary": "(BE) Multi-Tenant Data Architecture Implementation", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build the core multi-tenant data architecture to support organization isolation.'}]}]}", "Issue Type": "Task", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:12:28", "Updated": "2025-06-07 13:08:45", "Resolution Date": "2025-04-25 11:09:33", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}, {"Key": "OA-13", "Summary": "(Story) User Login to Platform", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement secure authentication system supporting JWT tokens and refresh tokens.'}]}]}", "Issue Type": "Story", "Status": "Done", "Priority": "Medium", "Resolution": "Done", "Created": "2025-04-05 11:12:22", "Updated": "2025-06-07 13:08:35", "Resolution Date": "2025-04-27 04:13:36", "Assignee": "<PERSON><PERSON>", "Assignee Email": "<EMAIL>", "Reporter": "<PERSON><PERSON>", "Reporter Email": "<EMAIL>"}]