"""
Data Service Module

Handles Excel data processing for the certificate notification bot.
Provides clean interfaces for reading training data and identifying eligible students.
"""

import logging
import pandas as pd
from datetime import datetime, date
from typing import Dict, Any, List, Optional
from pathlib import Path


class DataService:
    """
    Service class for handling Excel data operations.
    
    This service:
    - Reads training data from Excel files with proper Vietnamese text encoding
    - Validates data structure and required columns
    - Filters eligible students based on criteria
    - Processes and formats student data
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the data service.
        
        Args:
            logger: Optional logger instance
        """
        self.logger = logger or logging.getLogger(__name__)
        self.today = date.today()
        
        # Required columns for processing
        self.required_columns = [
            "Kết quả (ĐẠT/KHÔNG ĐẠT)",
            "Thời gian gửi chứng chỉ", 
            "<PERSON><PERSON><PERSON> cấ<PERSON> chứng chỉ",
            "Họ và tên",
            "Tên khóa học",
            "Thời gian bắt đầu đào tạo",
            "Thời gian kết thúc đào tạo",
            "Mã nhân viên",
            "Email"
        ]
        
        self.logger.info("Data service initialized with Vietnamese text support")
    
    def read_training_data(self, excel_file_path: str) -> pd.DataFrame:
        """
        Read training data from Excel file with proper Vietnamese text encoding.
        
        Args:
            excel_file_path: Path to the Excel file
            
        Returns:
            DataFrame with training data (headers from row 3)
            
        Raises:
            FileNotFoundError: If Excel file doesn't exist
            ValueError: If required columns are missing
            Exception: If file cannot be read
        """
        try:
            excel_path = Path(excel_file_path)
            if not excel_path.exists():
                raise FileNotFoundError(f"Excel file not found: {excel_file_path}")
            
            # Read Excel file with headers at row 3 (index 2) and proper encoding for Vietnamese
            self.logger.info(f"Reading Excel file with Vietnamese text support: {excel_file_path}")
            
            # Use engine='openpyxl' for better Unicode support
            df = pd.read_excel(excel_file_path, header=2, engine='openpyxl')
            
            # Ensure all text columns are properly decoded as UTF-8
            for col in df.columns:
                if df[col].dtype == 'object':  # Text columns
                    df[col] = df[col].astype(str).apply(self._ensure_utf8_text)
            
            self.logger.info(f"Loaded {len(df)} records from Excel file")
            self.logger.info(f"Columns: {list(df.columns)}")
            
            # Validate required columns
            self._validate_columns(df)
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error reading Excel file: {str(e)}")
            raise
    
    def _ensure_utf8_text(self, text) -> str:
        """
        Ensure text is properly handled for Vietnamese characters.
        
        Args:
            text: Input text
            
        Returns:
            Properly encoded text string
        """
        if pd.isna(text) or text == 'nan':
            return ''
        
        try:
            text_str = str(text)
            # Ensure proper UTF-8 encoding
            if isinstance(text_str, bytes):
                return text_str.decode('utf-8')
            else:
                # Test if the string can be properly encoded/decoded
                return text_str.encode('utf-8').decode('utf-8')
        except (UnicodeDecodeError, UnicodeEncodeError):
            # Fallback for problematic characters
            return str(text).encode('utf-8', errors='replace').decode('utf-8')
    
    def _validate_columns(self, df: pd.DataFrame):
        """
        Validate that all required columns are present in the DataFrame.
        
        Args:
            df: DataFrame to validate
            
        Raises:
            ValueError: If required columns are missing
        """
        missing_columns = [col for col in self.required_columns if col not in df.columns]
        if missing_columns:
            error_msg = f"Missing required columns: {missing_columns}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        self.logger.info("All required columns are present")
    
    def find_eligible_students(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Find students eligible for certificate notification.
        
        Eligibility Criteria:
        - "Kết quả (ĐẠT/KHÔNG ĐẠT)" = "Đạt"
        - "Thời gian gửi chứng chỉ" is null/empty
        - "Ngày cấp chứng chỉ" = today
        
        Args:
            df: DataFrame with training data
            
        Returns:
            DataFrame with eligible students
        """
        try:
            self.logger.info(f"Filtering students for date: {self.today}")
            
            # Convert date column to datetime for comparison
            df = df.copy()  # Work with a copy to avoid modifying original
            df['Ngày cấp chứng chỉ'] = pd.to_datetime(df['Ngày cấp chứng chỉ'], errors='coerce').dt.date
            
            # Filter conditions
            conditions = (
                (df["Kết quả (ĐẠT/KHÔNG ĐẠT)"].str.strip().str.upper() == "ĐẠT") &
                (df["Thời gian gửi chứng chỉ"].isna() | (df["Thời gian gửi chứng chỉ"] == "")) &
                (df["Ngày cấp chứng chỉ"] == self.today)
            )
            
            eligible_students = df[conditions].copy()
            
            self.logger.info(f"Found {len(eligible_students)} eligible students for certificate notification")
            
            if len(eligible_students) > 0:
                self.logger.info("Eligible students:")
                for idx, row in eligible_students.iterrows():
                    email = row.get('Email', 'No email')
                    self.logger.info(f"  - {row['Họ và tên']} ({row['Mã nhân viên']}) - {row['Tên khóa học']} - {email}")
            else:
                self.logger.info("No students are eligible for certificate notification today")
            
            return eligible_students
            
        except Exception as e:
            self.logger.error(f"Error finding eligible students: {str(e)}")
            raise
    
    def prepare_student_data(self, student_row: pd.Series) -> Dict[str, Any]:
        """
        Prepare student data for certificate generation with proper Vietnamese text handling.
        
        Args:
            student_row: Pandas Series with student data
            
        Returns:
            Dictionary with formatted student data for certificate template
        """
        try:
            # Format training period
            training_period = self._format_training_period(
                student_row["Thời gian bắt đầu đào tạo"],
                student_row["Thời gian kết thúc đào tạo"]
            )
            
            # Format certificate date
            cert_date_str = self._format_certificate_date(student_row["Ngày cấp chứng chỉ"])
            
            # Ensure all text fields are properly encoded for Vietnamese
            student_data = {
                "TEN_UNG_VIEN": self._ensure_utf8_text(student_row["Họ và tên"]).strip(),
                "TEN_KHOA_HOC": self._ensure_utf8_text(student_row["Tên khóa học"]).strip(),
                "THOI_GIAN_DAO_TAO": training_period,
                "NGAY_CAP_CHUNG_CHI": cert_date_str,
                "MA_NHAN_VIEN": str(student_row["Mã nhân viên"]).strip(),
                "EMAIL": str(student_row["Email"]).strip()
            }
            
            self.logger.debug(f"Prepared Vietnamese data for student: {student_data['TEN_UNG_VIEN']}")
            return student_data
            
        except Exception as e:
            self.logger.error(f"Error preparing student data: {str(e)}")
            raise
    
    def _format_training_period(self, start_date, end_date) -> str:
        """
        Format training period from start and end dates.
        
        Args:
            start_date: Training start date
            end_date: Training end date
            
        Returns:
            Formatted training period string
        """
        try:
            # Handle different date formats
            if pd.notna(start_date) and pd.notna(end_date):
                if isinstance(start_date, str):
                    start_date = pd.to_datetime(start_date, errors='coerce')
                if isinstance(end_date, str):
                    end_date = pd.to_datetime(end_date, errors='coerce')
                
                if pd.notna(start_date) and pd.notna(end_date):
                    return f"{start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}"
            
            return "N/A"
            
        except Exception:
            return "N/A"
    
    def _format_certificate_date(self, cert_date) -> str:
        """
        Format certificate date.
        
        Args:
            cert_date: Certificate date
            
        Returns:
            Formatted certificate date string
        """
        try:
            if pd.notna(cert_date):
                if isinstance(cert_date, str):
                    cert_date = pd.to_datetime(cert_date, errors='coerce')
                if pd.notna(cert_date):
                    return cert_date.strftime('%d/%m/%Y')
            
            return self.today.strftime('%d/%m/%Y')
            
        except Exception:
            return self.today.strftime('%d/%m/%Y')
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get summary statistics about the training data.
        
        Args:
            df: DataFrame with training data
            
        Returns:
            Dictionary with summary information
        """
        try:
            # Convert date column for analysis
            df_copy = df.copy()
            df_copy['Ngày cấp chứng chỉ'] = pd.to_datetime(df_copy['Ngày cấp chứng chỉ'], errors='coerce').dt.date
            
            summary = {
                'total_records': len(df),
                'students_passed': len(df[df["Kết quả (ĐẠT/KHÔNG ĐẠT)"].str.strip().str.upper() == "ĐẠT"]),
                'students_failed': len(df[df["Kết quả (ĐẠT/KHÔNG ĐẠT)"].str.strip().str.upper() == "KHÔNG ĐẠT"]),
                'notifications_sent': len(df[df["Thời gian gửi chứng chỉ"].notna() & (df["Thời gian gửi chứng chỉ"] != "")]),
                'certificates_due_today': len(df[df_copy["Ngày cấp chứng chỉ"] == self.today]),
                'unique_courses': df["Tên khóa học"].nunique(),
                'date_range': {
                    'earliest_cert_date': df_copy["Ngày cấp chứng chỉ"].min(),
                    'latest_cert_date': df_copy["Ngày cấp chứng chỉ"].max()
                }
            }
            
            self.logger.info(f"Data summary: {summary['total_records']} total records, "
                           f"{summary['students_passed']} passed, "
                           f"{summary['certificates_due_today']} certificates due today")
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error generating data summary: {str(e)}")
            return {}
    
    def validate_student_data(self, student_data: Dict[str, Any]) -> bool:
        """
        Validate that student data has all required fields.
        
        Args:
            student_data: Student data dictionary
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ['TEN_UNG_VIEN', 'TEN_KHOA_HOC', 'MA_NHAN_VIEN', 'NGAY_CAP_CHUNG_CHI', 'EMAIL']
        
        for field in required_fields:
            if not student_data.get(field) or str(student_data[field]).strip() == '':
                self.logger.warning(f"Missing or empty required field: {field}")
                return False
        
        # Additional email validation
        email = student_data.get('EMAIL', '').strip()
        if email and '@' not in email:
            self.logger.warning(f"Invalid email format: {email}")
            return False
        
        return True


def test_data_service():
    """Test function for the data service."""
    try:
        # Initialize data service
        data_service = DataService()
        
        print("✅ Data service initialized successfully")
        print(f"📅 Processing date: {data_service.today}")
        print(f"📋 Required columns: {len(data_service.required_columns)}")
        
        # Test student data validation
        test_student = {
            "TEN_UNG_VIEN": "Test Student",
            "TEN_KHOA_HOC": "Test Course",
            "MA_NHAN_VIEN": "TEST001",
            "NGAY_CAP_CHUNG_CHI": "16/01/2024",
            "EMAIL": "<EMAIL>"
        }
        
        if data_service.validate_student_data(test_student):
            print("✅ Student data validation works")
        else:
            print("❌ Student data validation failed")
            
        print("📊 Data service ready for Excel processing")
        
    except Exception as e:
        print(f"❌ Error testing data service: {str(e)}")


if __name__ == "__main__":
    test_data_service() 