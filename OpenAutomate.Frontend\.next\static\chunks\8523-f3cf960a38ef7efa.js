"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8523],{45503:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(12115);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},46002:(e,t,r)=>{r.d(t,{UC:()=>e_,In:()=>eD,q7:()=>eA,VF:()=>eB,p4:()=>eV,ZL:()=>eM,bL:()=>ek,wn:()=>eH,PP:()=>eO,l9:()=>eT,WT:()=>eI,LM:()=>eL});var n=r(12115),l=r(47650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}function i(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var a=r(60671);function u(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=u(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():u(e[t],null)}}}}function d(...e){return n.useCallback(s(...e),e)}var c=r(95155),p=n.createContext(void 0),f=r(7166),v=r(92293),h=r(12307),m=r(52496),g=r(13227),w=r(962);function y(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var o;let e,i;let a=(o=r,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(u.ref=t?s(t,a):a),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,i=n.Children.toArray(l),a=i.find(b);if(a){let e=a.props.children,l=i.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,c.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var x=Symbol("radix.slottable");function b(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===x}var S=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=y(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(l?r:t,{...o,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),C=r(12640),j=globalThis?.document?n.useLayoutEffect:()=>{},R=r(45503),E=r(99853),N=r(38168),P=r(93795),k=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],I="Select",[D,M,_]=(0,a.N)(I),[L,A]=function(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return l.scopeName=e,[function(t,l){let o=n.createContext(l),i=r.length;r=[...r,l];let a=t=>{let{scope:r,children:l,...a}=t,u=r?.[e]?.[i]||o,s=n.useMemo(()=>a,Object.values(a));return(0,c.jsx)(u.Provider,{value:s,children:l})};return a.displayName=t+"Provider",[a,function(r,a){let u=a?.[e]?.[i]||o,s=n.useContext(u);if(s)return s;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(l,...t)]}(I,[_,g.Bk]),V=(0,g.Bk)(),[B,O]=L(I),[H,W]=L(I),F=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:i,value:a,defaultValue:u,onValueChange:s,dir:d,name:f,autoComplete:v,disabled:h,required:w,form:y}=e,x=V(t),[b,S]=n.useState(null),[j,R]=n.useState(null),[E,N]=n.useState(!1),P=function(e){let t=n.useContext(p);return e||t||"ltr"}(d),[k,T]=(0,C.i)({prop:l,defaultProp:null!=o&&o,onChange:i,caller:I}),[M,_]=(0,C.i)({prop:a,defaultProp:u,onChange:s,caller:I}),L=n.useRef(null),A=!b||y||!!b.closest("form"),[O,W]=n.useState(new Set),F=Array.from(O).map(e=>e.props.value).join(";");return(0,c.jsx)(g.bL,{...x,children:(0,c.jsxs)(B,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:j,onValueNodeChange:R,valueNodeHasChildren:E,onValueNodeHasChildrenChange:N,contentId:(0,m.B)(),value:M,onValueChange:_,open:k,onOpenChange:T,dir:P,triggerPointerDownPosRef:L,disabled:h,children:[(0,c.jsx)(D.Provider,{scope:t,children:(0,c.jsx)(H,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{W(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),A?(0,c.jsxs)(eR,{"aria-hidden":!0,required:w,tabIndex:-1,name:f,autoComplete:v,value:M,onChange:e=>_(e.target.value),disabled:h,form:y,children:[void 0===M?(0,c.jsx)("option",{value:""}):null,Array.from(O)]},F):null]})})};F.displayName=I;var K="SelectTrigger",$=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,a=V(r),u=O(K,r),s=u.disabled||l,p=d(t,u.onTriggerChange),f=M(r),v=n.useRef("touch"),[h,m,w]=eN(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eP(t,e,r);void 0!==n&&u.onValueChange(n.value)}),y=e=>{s||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,c.jsx)(g.Mz,{asChild:!0,...a,children:(0,c.jsx)(S.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":eE(u.value)?"":void 0,...o,ref:p,onClick:i(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==v.current&&y(e)}),onPointerDown:i(o.onPointerDown,e=>{v.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:i(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(y(),e.preventDefault())})})})});$.displayName=K;var U="SelectValue",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:i="",...a}=e,u=O(U,r),{onValueNodeHasChildrenChange:s}=u,p=void 0!==o,f=d(t,u.onValueNodeChange);return j(()=>{s(p)},[s,p]),(0,c.jsx)(S.span,{...a,ref:f,style:{pointerEvents:"none"},children:eE(u.value)?(0,c.jsx)(c.Fragment,{children:i}):o})});z.displayName=U;var q=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,c.jsx)(S.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});q.displayName="SelectIcon";var Z=e=>(0,c.jsx)(w.Z,{asChild:!0,...e});Z.displayName="SelectPortal";var Q="SelectContent",X=n.forwardRef((e,t)=>{let r=O(Q,e.__scopeSelect),[o,i]=n.useState();return(j(()=>{i(new DocumentFragment)},[]),r.open)?(0,c.jsx)(ee,{...e,ref:t}):o?l.createPortal((0,c.jsx)(Y,{scope:e.__scopeSelect,children:(0,c.jsx)(D.Slot,{scope:e.__scopeSelect,children:(0,c.jsx)("div",{children:e.children})})}),o):null});X.displayName=Q;var[Y,G]=L(Q),J=y("SelectContent.RemoveScroll"),ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:u,side:s,sideOffset:p,align:m,alignOffset:g,arrowPadding:w,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:C,...j}=e,R=O(Q,r),[E,k]=n.useState(null),[T,I]=n.useState(null),D=d(t,e=>k(e)),[_,L]=n.useState(null),[A,V]=n.useState(null),B=M(r),[H,W]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(E)return(0,N.Eq)(E)},[E]),(0,v.Oh)();let K=n.useCallback(e=>{let[t,...r]=B().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&T&&(T.scrollTop=0),r===n&&T&&(T.scrollTop=T.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[B,T]),$=n.useCallback(()=>K([_,E]),[K,_,E]);n.useEffect(()=>{H&&$()},[H,$]);let{onOpenChange:U,triggerPointerDownPosRef:z}=R;n.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=z.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=z.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():E.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[E,U,z]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[q,Z]=eN(e=>{let t=B().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eP(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),X=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&(L(e),n&&(F.current=!0))},[R.value]),G=n.useCallback(()=>null==E?void 0:E.focus(),[E]),ee=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&V(e)},[R.value]),en="popper"===l?er:et,el=en===er?{side:s,sideOffset:p,align:m,alignOffset:g,arrowPadding:w,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:C}:{};return(0,c.jsx)(Y,{scope:r,content:E,viewport:T,onViewportChange:I,itemRefCallback:X,selectedItem:_,onItemLeave:G,itemTextRefCallback:ee,focusSelectedItem:$,selectedItemText:A,position:l,isPositioned:H,searchRef:q,children:(0,c.jsx)(P.A,{as:J,allowPinchZoom:!0,children:(0,c.jsx)(h.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:i(o,e=>{var t;null===(t=R.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,c.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,c.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>W(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:i(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});ee.displayName="SelectContentImpl";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...i}=e,a=O(Q,r),u=G(Q,r),[s,p]=n.useState(null),[f,v]=n.useState(null),h=d(t,e=>v(e)),m=M(r),g=n.useRef(!1),w=n.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:b,focusSelectedItem:C}=u,R=n.useCallback(()=>{if(a.trigger&&a.valueNode&&s&&f&&y&&x&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==a.dir){let l=n.left-t.left,i=r.left-l,a=e.left-i,u=e.width+a,d=Math.max(u,t.width),c=o(i,[10,Math.max(10,window.innerWidth-10-d)]);s.style.minWidth=u+"px",s.style.left=c+"px"}else{let l=t.right-n.right,i=window.innerWidth-r.right-l,a=window.innerWidth-e.right-i,u=e.width+a,d=Math.max(u,t.width),c=o(i,[10,Math.max(10,window.innerWidth-10-d)]);s.style.minWidth=u+"px",s.style.right=c+"px"}let i=m(),u=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(f),p=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),h=parseInt(c.borderBottomWidth,10),w=p+v+d+parseInt(c.paddingBottom,10)+h,S=Math.min(5*x.offsetHeight,w),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),E=e.top+e.height/2-10,N=x.offsetHeight/2,P=p+v+(x.offsetTop+N);if(P<=E){let e=i.length>0&&x===i[i.length-1].ref.current;s.style.bottom="0px";let t=Math.max(u-E,N+(e?R:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+h);s.style.height=P+t+"px"}else{let e=i.length>0&&x===i[0].ref.current;s.style.top="0px";let t=Math.max(E,p+y.offsetTop+(e?j:0)+N);s.style.height=t+(w-P)+"px",y.scrollTop=P-E+y.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=S+"px",s.style.maxHeight=u+"px",null==l||l(),requestAnimationFrame(()=>g.current=!0)}},[m,a.trigger,a.valueNode,s,f,y,x,b,a.dir,l]);j(()=>R(),[R]);let[E,N]=n.useState();j(()=>{f&&N(window.getComputedStyle(f).zIndex)},[f]);let P=n.useCallback(e=>{e&&!0===w.current&&(R(),null==C||C(),w.current=!1)},[R,C]);return(0,c.jsx)(en,{scope:r,contentWrapper:s,shouldExpandOnScrollRef:g,onScrollButtonChange:P,children:(0,c.jsx)("div",{ref:p,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,c.jsx)(S.div,{...i,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});et.displayName="SelectItemAlignedPosition";var er=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,i=V(r);return(0,c.jsx)(g.UC,{...i,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});er.displayName="SelectPopperPosition";var[en,el]=L(Q,{}),eo="SelectViewport",ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,a=G(eo,r),u=el(eo,r),s=d(t,a.onViewportChange),p=n.useRef(0);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,c.jsx)(D.Slot,{scope:r,children:(0,c.jsx)(S.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:i(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(p.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,i=Math.min(n,o),a=o-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}p.current=t.scrollTop})})})]})});ei.displayName=eo;var ea="SelectGroup",[eu,es]=L(ea);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,m.B)();return(0,c.jsx)(eu,{scope:r,id:l,children:(0,c.jsx)(S.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=ea;var ed="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=es(ed,r);return(0,c.jsx)(S.div,{id:l.id,...n,ref:t})}).displayName=ed;var ec="SelectItem",[ep,ef]=L(ec),ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:a,...u}=e,s=O(ec,r),p=G(ec,r),f=s.value===l,[v,h]=n.useState(null!=a?a:""),[g,w]=n.useState(!1),y=d(t,e=>{var t;return null===(t=p.itemRefCallback)||void 0===t?void 0:t.call(p,e,l,o)}),x=(0,m.B)(),b=n.useRef("touch"),C=()=>{o||(s.onValueChange(l),s.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,c.jsx)(ep,{scope:r,value:l,disabled:o,textId:x,isSelected:f,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,c.jsx)(D.ItemSlot,{scope:r,value:l,disabled:o,textValue:v,children:(0,c.jsx)(S.div,{role:"option","aria-labelledby":x,"data-highlighted":g?"":void 0,"aria-selected":f&&g,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:y,onFocus:i(u.onFocus,()=>w(!0)),onBlur:i(u.onBlur,()=>w(!1)),onClick:i(u.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:i(u.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:i(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:i(u.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null===(t=p.onItemLeave)||void 0===t||t.call(p)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:i(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=p.onItemLeave)||void 0===t||t.call(p)}}),onKeyDown:i(u.onKeyDown,e=>{var t;((null===(t=p.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(T.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ev.displayName=ec;var eh="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:i,...a}=e,u=O(eh,r),s=G(eh,r),p=ef(eh,r),f=W(eh,r),[v,h]=n.useState(null),m=d(t,e=>h(e),p.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,p.value,p.disabled)}),g=null==v?void 0:v.textContent,w=n.useMemo(()=>(0,c.jsx)("option",{value:p.value,disabled:p.disabled,children:g},p.value),[p.disabled,p.value,g]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=f;return j(()=>(y(w),()=>x(w)),[y,x,w]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(S.span,{id:p.textId,...a,ref:m}),p.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(a.children,u.valueNode):null]})});em.displayName=eh;var eg="SelectItemIndicator",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(eg,r).isSelected?(0,c.jsx)(S.span,{"aria-hidden":!0,...n,ref:t}):null});ew.displayName=eg;var ey="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=G(ey,e.__scopeSelect),l=el(ey,e.__scopeSelect),[o,i]=n.useState(!1),a=d(t,l.onScrollButtonChange);return j(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,c.jsx)(eC,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=ey;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=G(eb,e.__scopeSelect),l=el(eb,e.__scopeSelect),[o,i]=n.useState(!1),a=d(t,l.onScrollButtonChange);return j(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,c.jsx)(eC,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,a=G("SelectScrollButton",r),u=n.useRef(null),s=M(r),d=n.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),j(()=>{var e;let t=s().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[s]),(0,c.jsx)(S.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:i(o.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(l,50))}),onPointerMove:i(o.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===u.current&&(u.current=window.setInterval(l,50))}),onPointerLeave:i(o.onPointerLeave,()=>{d()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,c.jsx)(S.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var ej="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=V(r),o=O(ej,r),i=G(ej,r);return o.open&&"popper"===i.position?(0,c.jsx)(g.i3,{...l,...n,ref:t}):null}).displayName=ej;var eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,i=n.useRef(null),a=d(t,i),u=(0,R.Z)(l);return n.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[u,l]),(0,c.jsx)(S.select,{...o,style:{...E.Qg,...o.style},ref:a,defaultValue:l})});function eE(e){return""===e||void 0===e}function eN(e){let t=function(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),i=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,i]}function eP(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=e,l=Math.max(i,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(a=a.filter(e=>e!==r));let u=a.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return u!==r?u:void 0}eR.displayName="SelectBubbleInput";var ek=F,eT=$,eI=z,eD=q,eM=Z,e_=X,eL=ei,eA=ev,eV=em,eB=ew,eO=ex,eH=eS},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},99853:(e,t,r)=>{r.d(t,{bL:()=>c,Qg:()=>s});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(47650);var o=r(95155),i=Symbol("radix.slottable");function a(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var i;let e,a;let u=(i=r,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(s.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}(t,u):u),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...i}=e,u=n.Children.toArray(l),s=u.find(a);if(s){let e=s.props.children,l=u.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...i,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?r:t,{...i,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),s=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),d=n.forwardRef((e,t)=>(0,o.jsx)(u.span,{...e,ref:t,style:{...s,...e.style}}));d.displayName="VisuallyHidden";var c=d}}]);