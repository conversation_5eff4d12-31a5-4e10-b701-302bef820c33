"use strict";exports.id=1637,exports.ids=[1637],exports.modules={22918:(e,t,o)=>{o.d(t,{$:()=>Q});let n=[0,2e3,1e4,3e4,null];class r{constructor(e){this._retryDelays=void 0!==e?[...e,null]:n}nextRetryDelayInMilliseconds(e){return this._retryDelays[e.previousRetryCount]}}class s{}s.Authorization="Authorization",s.<PERSON>="<PERSON>ie";class i{constructor(e,t,o){this.statusCode=e,this.statusText=t,this.content=o}}class a{get(e,t){return this.send({...t,method:"GET",url:e})}post(e,t){return this.send({...t,method:"POST",url:e})}delete(e,t){return this.send({...t,method:"DELETE",url:e})}getCookieString(e){return""}}class c extends a{constructor(e,t){super(),this._innerClient=e,this._accessTokenFactory=t}async send(e){let t=!0;this._accessTokenFactory&&(!this._accessToken||e.url&&e.url.indexOf("/negotiate?")>0)&&(t=!1,this._accessToken=await this._accessTokenFactory()),this._setAuthorizationHeader(e);let o=await this._innerClient.send(e);return t&&401===o.statusCode&&this._accessTokenFactory?(this._accessToken=await this._accessTokenFactory(),this._setAuthorizationHeader(e),await this._innerClient.send(e)):o}_setAuthorizationHeader(e){e.headers||(e.headers={}),this._accessToken?e.headers[s.Authorization]=`Bearer ${this._accessToken}`:this._accessTokenFactory&&e.headers[s.Authorization]&&delete e.headers[s.Authorization]}getCookieString(e){return this._innerClient.getCookieString(e)}}class l extends Error{constructor(e,t){let o=new.target.prototype;super(`${e}: Status code '${t}'`),this.statusCode=t,this.__proto__=o}}class h extends Error{constructor(e="A timeout occurred."){let t=new.target.prototype;super(e),this.__proto__=t}}class g extends Error{constructor(e="An abort occurred."){let t=new.target.prototype;super(e),this.__proto__=t}}class u extends Error{constructor(e,t){let o=new.target.prototype;super(e),this.transport=t,this.errorType="UnsupportedTransportError",this.__proto__=o}}class d extends Error{constructor(e,t){let o=new.target.prototype;super(e),this.transport=t,this.errorType="DisabledTransportError",this.__proto__=o}}class _ extends Error{constructor(e,t){let o=new.target.prototype;super(e),this.transport=t,this.errorType="FailedToStartTransportError",this.__proto__=o}}class p extends Error{constructor(e){let t=new.target.prototype;super(e),this.errorType="FailedToNegotiateWithServerError",this.__proto__=t}}class f extends Error{constructor(e,t){let o=new.target.prototype;super(e),this.innerErrors=t,this.__proto__=o}}var m,w,b=o(97011);class v{constructor(){}log(e,t){}}v.instance=new v;class y{static isRequired(e,t){if(null==e)throw Error(`The '${t}' argument is required.`)}static isNotEmpty(e,t){if(!e||e.match(/^\s*$/))throw Error(`The '${t}' argument should not be empty.`)}static isIn(e,t,o){if(!(e in t))throw Error(`Unknown ${o} value: ${e}.`)}}class S{static get isBrowser(){return!S.isNode&&"object"==typeof window&&"object"==typeof window.document}static get isWebWorker(){return!S.isNode&&"object"==typeof self&&"importScripts"in self}static get isReactNative(){return!S.isNode&&"object"==typeof window&&void 0===window.document}static get isNode(){return"undefined"!=typeof process&&process.release&&"node"===process.release.name}}function C(e,t){let o="";return $(e)?(o=`Binary data of length ${e.byteLength}`,t&&(o+=`. Content: '${function(e){let t=new Uint8Array(e),o="";return t.forEach(e=>{let t=e<16?"0":"";o+=`0x${t}${e.toString(16)} `}),o.substr(0,o.length-1)}(e)}'`)):"string"==typeof e&&(o=`String data of length ${e.length}`,t&&(o+=`. Content: '${e}'`)),o}function $(e){return e&&"undefined"!=typeof ArrayBuffer&&(e instanceof ArrayBuffer||e.constructor&&"ArrayBuffer"===e.constructor.name)}async function k(e,t,o,n,r,s){let i={},[a,c]=T();i[a]=c,e.log(b.$.Trace,`(${t} transport) sending data. ${C(r,s.logMessageContent)}.`);let l=$(r)?"arraybuffer":"text",h=await o.post(n,{content:r,headers:{...i,...s.headers},responseType:l,timeout:s.timeout,withCredentials:s.withCredentials});e.log(b.$.Trace,`(${t} transport) request complete. Response status: ${h.statusCode}.`)}class E{constructor(e,t){this._subject=e,this._observer=t}dispose(){let e=this._subject.observers.indexOf(this._observer);e>-1&&this._subject.observers.splice(e,1),0===this._subject.observers.length&&this._subject.cancelCallback&&this._subject.cancelCallback().catch(e=>{})}}class I{constructor(e){this._minLevel=e,this.out=console}log(e,t){if(e>=this._minLevel){let o=`[${new Date().toISOString()}] ${b.$[e]}: ${t}`;switch(e){case b.$.Critical:case b.$.Error:this.out.error(o);break;case b.$.Warning:this.out.warn(o);break;case b.$.Information:this.out.info(o);break;default:this.out.log(o)}}}}function T(){let e="X-SignalR-User-Agent";return S.isNode&&(e="User-Agent"),[e,function(e,t,o,n){let r="Microsoft SignalR/",s=e.split(".");return r+=`${s[0]}.${s[1]} (${e}; `,t&&""!==t?r+=`${t}; `:r+="Unknown OS; ",r+=`${o}`,n?r+=`; ${n}`:r+="; Unknown Runtime Version",r+=")"}("8.0.7",function(){if(!S.isNode)return"";switch(process.platform){case"win32":return"Windows NT";case"darwin":return"macOS";case"linux":return"Linux";default:return process.platform}}(),S.isNode?"NodeJS":"Browser",function(){if(S.isNode)return process.versions.node}())]}function P(e){return e.stack?e.stack:e.message?e.message:`${e}`}class R extends a{constructor(e){if(super(),this._logger=e,"undefined"==typeof fetch||S.isNode){let e=require;this._jar=new(e("tough-cookie")).CookieJar,"undefined"==typeof fetch?this._fetchType=e("node-fetch"):this._fetchType=fetch,this._fetchType=e("fetch-cookie")(this._fetchType,this._jar)}else this._fetchType=fetch.bind(function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw Error("could not find global")}());if("undefined"==typeof AbortController){let e=require;this._abortControllerType=e("abort-controller")}else this._abortControllerType=AbortController}async send(e){let t,o;if(e.abortSignal&&e.abortSignal.aborted)throw new g;if(!e.method)throw Error("No method defined.");if(!e.url)throw Error("No url defined.");let n=new this._abortControllerType;e.abortSignal&&(e.abortSignal.onabort=()=>{n.abort(),t=new g});let r=null;e.timeout&&(r=setTimeout(()=>{n.abort(),this._logger.log(b.$.Warning,"Timeout from HTTP request."),t=new h},e.timeout)),""===e.content&&(e.content=void 0),e.content&&(e.headers=e.headers||{},$(e.content)?e.headers["Content-Type"]="application/octet-stream":e.headers["Content-Type"]="text/plain;charset=UTF-8");try{o=await this._fetchType(e.url,{body:e.content,cache:"no-cache",credentials:!0===e.withCredentials?"include":"same-origin",headers:{"X-Requested-With":"XMLHttpRequest",...e.headers},method:e.method,mode:"cors",redirect:"follow",signal:n.signal})}catch(e){if(t)throw t;throw this._logger.log(b.$.Warning,`Error from HTTP request. ${e}.`),e}finally{r&&clearTimeout(r),e.abortSignal&&(e.abortSignal.onabort=null)}if(!o.ok)throw new l(await D(o,"text")||o.statusText,o.status);let s=D(o,e.responseType),a=await s;return new i(o.status,o.statusText,a)}getCookieString(e){let t="";return S.isNode&&this._jar&&this._jar.getCookies(e,(e,o)=>t=o.join("; ")),t}}function D(e,t){let o;switch(t){case"arraybuffer":o=e.arrayBuffer();break;case"text":default:o=e.text();break;case"blob":case"document":case"json":throw Error(`${t} is not supported.`)}return o}class x extends a{constructor(e){super(),this._logger=e}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new g):e.method?e.url?new Promise((t,o)=>{let n=new XMLHttpRequest;n.open(e.method,e.url,!0),n.withCredentials=void 0===e.withCredentials||e.withCredentials,n.setRequestHeader("X-Requested-With","XMLHttpRequest"),""===e.content&&(e.content=void 0),e.content&&($(e.content)?n.setRequestHeader("Content-Type","application/octet-stream"):n.setRequestHeader("Content-Type","text/plain;charset=UTF-8"));let r=e.headers;r&&Object.keys(r).forEach(e=>{n.setRequestHeader(e,r[e])}),e.responseType&&(n.responseType=e.responseType),e.abortSignal&&(e.abortSignal.onabort=()=>{n.abort(),o(new g)}),e.timeout&&(n.timeout=e.timeout),n.onload=()=>{e.abortSignal&&(e.abortSignal.onabort=null),n.status>=200&&n.status<300?t(new i(n.status,n.statusText,n.response||n.responseText)):o(new l(n.response||n.responseText||n.statusText,n.status))},n.onerror=()=>{this._logger.log(b.$.Warning,`Error from HTTP request. ${n.status}: ${n.statusText}.`),o(new l(n.statusText,n.status))},n.ontimeout=()=>{this._logger.log(b.$.Warning,"Timeout from HTTP request."),o(new h)},n.send(e.content)}):Promise.reject(Error("No url defined.")):Promise.reject(Error("No method defined."))}}class M extends a{constructor(e){if(super(),"undefined"!=typeof fetch||S.isNode)this._httpClient=new R(e);else if("undefined"!=typeof XMLHttpRequest)this._httpClient=new x(e);else throw Error("No usable HttpClient found.")}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new g):e.method?e.url?this._httpClient.send(e):Promise.reject(Error("No url defined.")):Promise.reject(Error("No method defined."))}getCookieString(e){return this._httpClient.getCookieString(e)}}var q=o(52974);class H{constructor(){this._isAborted=!1,this.onabort=null}abort(){!this._isAborted&&(this._isAborted=!0,this.onabort&&this.onabort())}get signal(){return this}get aborted(){return this._isAborted}}class A{get pollAborted(){return this._pollAbort.aborted}constructor(e,t,o){this._httpClient=e,this._logger=t,this._pollAbort=new H,this._options=o,this._running=!1,this.onreceive=null,this.onclose=null}async connect(e,t){if(y.isRequired(e,"url"),y.isRequired(t,"transferFormat"),y.isIn(t,q.B,"transferFormat"),this._url=e,this._logger.log(b.$.Trace,"(LongPolling transport) Connecting."),t===q.B.Binary&&"undefined"!=typeof XMLHttpRequest&&"string"!=typeof new XMLHttpRequest().responseType)throw Error("Binary protocols over XmlHttpRequest not implementing advanced features are not supported.");let[o,n]=T(),r={[o]:n,...this._options.headers},s={abortSignal:this._pollAbort.signal,headers:r,timeout:1e5,withCredentials:this._options.withCredentials};t===q.B.Binary&&(s.responseType="arraybuffer");let i=`${e}&_=${Date.now()}`;this._logger.log(b.$.Trace,`(LongPolling transport) polling: ${i}.`);let a=await this._httpClient.get(i,s);200!==a.statusCode?(this._logger.log(b.$.Error,`(LongPolling transport) Unexpected response code: ${a.statusCode}.`),this._closeError=new l(a.statusText||"",a.statusCode),this._running=!1):this._running=!0,this._receiving=this._poll(this._url,s)}async _poll(e,t){try{for(;this._running;)try{let o=`${e}&_=${Date.now()}`;this._logger.log(b.$.Trace,`(LongPolling transport) polling: ${o}.`);let n=await this._httpClient.get(o,t);204===n.statusCode?(this._logger.log(b.$.Information,"(LongPolling transport) Poll terminated by server."),this._running=!1):200!==n.statusCode?(this._logger.log(b.$.Error,`(LongPolling transport) Unexpected response code: ${n.statusCode}.`),this._closeError=new l(n.statusText||"",n.statusCode),this._running=!1):n.content?(this._logger.log(b.$.Trace,`(LongPolling transport) data received. ${C(n.content,this._options.logMessageContent)}.`),this.onreceive&&this.onreceive(n.content)):this._logger.log(b.$.Trace,"(LongPolling transport) Poll timed out, reissuing.")}catch(e){this._running?e instanceof h?this._logger.log(b.$.Trace,"(LongPolling transport) Poll timed out, reissuing."):(this._closeError=e,this._running=!1):this._logger.log(b.$.Trace,`(LongPolling transport) Poll errored after shutdown: ${e.message}`)}}finally{this._logger.log(b.$.Trace,"(LongPolling transport) Polling complete."),this.pollAborted||this._raiseOnClose()}}async send(e){return this._running?k(this._logger,"LongPolling",this._httpClient,this._url,e,this._options):Promise.reject(Error("Cannot send until the transport is connected"))}async stop(){this._logger.log(b.$.Trace,"(LongPolling transport) Stopping polling."),this._running=!1,this._pollAbort.abort();try{let e;await this._receiving,this._logger.log(b.$.Trace,`(LongPolling transport) sending DELETE request to ${this._url}.`);let t={},[o,n]=T();t[o]=n;let r={headers:{...t,...this._options.headers},timeout:this._options.timeout,withCredentials:this._options.withCredentials};try{await this._httpClient.delete(this._url,r)}catch(t){e=t}e?e instanceof l&&(404===e.statusCode?this._logger.log(b.$.Trace,"(LongPolling transport) A 404 response was returned from sending a DELETE request."):this._logger.log(b.$.Trace,`(LongPolling transport) Error sending a DELETE request: ${e}`)):this._logger.log(b.$.Trace,"(LongPolling transport) DELETE request accepted.")}finally{this._logger.log(b.$.Trace,"(LongPolling transport) Stop finished."),this._raiseOnClose()}}_raiseOnClose(){if(this.onclose){let e="(LongPolling transport) Firing onclose event.";this._closeError&&(e+=" Error: "+this._closeError),this._logger.log(b.$.Trace,e),this.onclose(this._closeError)}}}class B{constructor(e,t,o,n){this._httpClient=e,this._accessToken=t,this._logger=o,this._options=n,this.onreceive=null,this.onclose=null}async connect(e,t){return y.isRequired(e,"url"),y.isRequired(t,"transferFormat"),y.isIn(t,q.B,"transferFormat"),this._logger.log(b.$.Trace,"(SSE transport) Connecting."),this._url=e,this._accessToken&&(e+=(0>e.indexOf("?")?"?":"&")+`access_token=${encodeURIComponent(this._accessToken)}`),new Promise((o,n)=>{let r,s=!1;if(t!==q.B.Text){n(Error("The Server-Sent Events transport only supports the 'Text' transfer format"));return}if(S.isBrowser||S.isWebWorker)r=new this._options.EventSource(e,{withCredentials:this._options.withCredentials});else{let t=this._httpClient.getCookieString(e),o={};o.Cookie=t;let[n,s]=T();o[n]=s,r=new this._options.EventSource(e,{withCredentials:this._options.withCredentials,headers:{...o,...this._options.headers}})}try{r.onmessage=e=>{if(this.onreceive)try{this._logger.log(b.$.Trace,`(SSE transport) data received. ${C(e.data,this._options.logMessageContent)}.`),this.onreceive(e.data)}catch(e){this._close(e);return}},r.onerror=e=>{s?this._close():n(Error("EventSource failed to connect. The connection could not be found on the server, either the connection ID is not present on the server, or a proxy is refusing/buffering the connection. If you have multiple servers check that sticky sessions are enabled."))},r.onopen=()=>{this._logger.log(b.$.Information,`SSE connected to ${this._url}`),this._eventSource=r,s=!0,o()}}catch(e){n(e);return}})}async send(e){return this._eventSource?k(this._logger,"SSE",this._httpClient,this._url,e,this._options):Promise.reject(Error("Cannot send until the transport is connected"))}stop(){return this._close(),Promise.resolve()}_close(e){this._eventSource&&(this._eventSource.close(),this._eventSource=void 0,this.onclose&&this.onclose(e))}}class N{constructor(e,t,o,n,r,s){this._logger=o,this._accessTokenFactory=t,this._logMessageContent=n,this._webSocketConstructor=r,this._httpClient=e,this.onreceive=null,this.onclose=null,this._headers=s}async connect(e,t){let o;return y.isRequired(e,"url"),y.isRequired(t,"transferFormat"),y.isIn(t,q.B,"transferFormat"),this._logger.log(b.$.Trace,"(WebSockets transport) Connecting."),this._accessTokenFactory&&(o=await this._accessTokenFactory()),new Promise((n,r)=>{let i;e=e.replace(/^http/,"ws");let a=this._httpClient.getCookieString(e),c=!1;if(S.isNode||S.isReactNative){let t={},[n,r]=T();t[n]=r,o&&(t[s.Authorization]=`Bearer ${o}`),a&&(t[s.Cookie]=a),i=new this._webSocketConstructor(e,void 0,{headers:{...t,...this._headers}})}else o&&(e+=(0>e.indexOf("?")?"?":"&")+`access_token=${encodeURIComponent(o)}`);i||(i=new this._webSocketConstructor(e)),t===q.B.Binary&&(i.binaryType="arraybuffer"),i.onopen=t=>{this._logger.log(b.$.Information,`WebSocket connected to ${e}.`),this._webSocket=i,c=!0,n()},i.onerror=e=>{let t=null;t="undefined"!=typeof ErrorEvent&&e instanceof ErrorEvent?e.error:"There was an error with the transport",this._logger.log(b.$.Information,`(WebSockets transport) ${t}.`)},i.onmessage=e=>{if(this._logger.log(b.$.Trace,`(WebSockets transport) data received. ${C(e.data,this._logMessageContent)}.`),this.onreceive)try{this.onreceive(e.data)}catch(e){this._close(e);return}},i.onclose=e=>{if(c)this._close(e);else{let t=null;r(Error("undefined"!=typeof ErrorEvent&&e instanceof ErrorEvent?e.error:"WebSocket failed to connect. The connection could not be found on the server, either the endpoint may not be a SignalR endpoint, the connection ID is not present on the server, or there is a proxy blocking WebSockets. If you have multiple servers check that sticky sessions are enabled."))}}})}send(e){return this._webSocket&&this._webSocket.readyState===this._webSocketConstructor.OPEN?(this._logger.log(b.$.Trace,`(WebSockets transport) sending data. ${C(e,this._logMessageContent)}.`),this._webSocket.send(e),Promise.resolve()):Promise.reject("WebSocket is not in the OPEN state")}stop(){return this._webSocket&&this._close(void 0),Promise.resolve()}_close(e){this._webSocket&&(this._webSocket.onclose=()=>{},this._webSocket.onmessage=()=>{},this._webSocket.onerror=()=>{},this._webSocket.close(),this._webSocket=void 0),this._logger.log(b.$.Trace,"(WebSockets transport) socket closed."),this.onclose&&(this._isCloseEvent(e)&&(!1===e.wasClean||1e3!==e.code)?this.onclose(Error(`WebSocket closed with status code: ${e.code} (${e.reason||"no reason given"}).`)):e instanceof Error?this.onclose(e):this.onclose())}_isCloseEvent(e){return e&&"boolean"==typeof e.wasClean&&"number"==typeof e.code}}class W{constructor(e,t={}){if(this._stopPromiseResolver=()=>{},this.features={},this._negotiateVersion=1,y.isRequired(e,"url"),this._logger=function(e){return void 0===e?new I(b.$.Information):null===e?v.instance:void 0!==e.log?e:new I(e)}(t.logger),this.baseUrl=this._resolveUrl(e),(t=t||{}).logMessageContent=void 0!==t.logMessageContent&&t.logMessageContent,"boolean"==typeof t.withCredentials||void 0===t.withCredentials)t.withCredentials=void 0===t.withCredentials||t.withCredentials;else throw Error("withCredentials option was not a 'boolean' or 'undefined' value");t.timeout=void 0===t.timeout?1e5:t.timeout;let o=null,n=null;if(S.isNode){let e=require;o=e("ws"),n=e("eventsource")}S.isNode||"undefined"==typeof WebSocket||t.WebSocket?S.isNode&&!t.WebSocket&&o&&(t.WebSocket=o):t.WebSocket=WebSocket,S.isNode||"undefined"==typeof EventSource||t.EventSource?S.isNode&&!t.EventSource&&void 0!==n&&(t.EventSource=n):t.EventSource=EventSource,this._httpClient=new c(t.httpClient||new M(this._logger),t.accessTokenFactory),this._connectionState="Disconnected",this._connectionStarted=!1,this._options=t,this.onreceive=null,this.onclose=null}async start(e){if(e=e||q.B.Binary,y.isIn(e,q.B,"transferFormat"),this._logger.log(b.$.Debug,`Starting connection with transfer format '${q.B[e]}'.`),"Disconnected"!==this._connectionState)return Promise.reject(Error("Cannot start an HttpConnection that is not in the 'Disconnected' state."));if(this._connectionState="Connecting",this._startInternalPromise=this._startInternal(e),await this._startInternalPromise,"Disconnecting"===this._connectionState){let e="Failed to start the HttpConnection before stop() was called.";return this._logger.log(b.$.Error,e),await this._stopPromise,Promise.reject(new g(e))}if("Connected"!==this._connectionState){let e="HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!";return this._logger.log(b.$.Error,e),Promise.reject(new g(e))}this._connectionStarted=!0}send(e){return"Connected"!==this._connectionState?Promise.reject(Error("Cannot send data if the connection is not in the 'Connected' State.")):(this._sendQueue||(this._sendQueue=new j(this.transport)),this._sendQueue.send(e))}async stop(e){return"Disconnected"===this._connectionState?(this._logger.log(b.$.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnected state.`),Promise.resolve()):"Disconnecting"===this._connectionState?(this._logger.log(b.$.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnecting state.`),this._stopPromise):void(this._connectionState="Disconnecting",this._stopPromise=new Promise(e=>{this._stopPromiseResolver=e}),await this._stopInternal(e),await this._stopPromise)}async _stopInternal(e){this._stopError=e;try{await this._startInternalPromise}catch(e){}if(this.transport){try{await this.transport.stop()}catch(e){this._logger.log(b.$.Error,`HttpConnection.transport.stop() threw error '${e}'.`),this._stopConnection()}this.transport=void 0}else this._logger.log(b.$.Debug,"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.")}async _startInternal(e){let t=this.baseUrl;this._accessTokenFactory=this._options.accessTokenFactory,this._httpClient._accessTokenFactory=this._accessTokenFactory;try{if(this._options.skipNegotiation){if(this._options.transport===q.w.WebSockets)this.transport=this._constructTransport(q.w.WebSockets),await this._startTransport(t,e);else throw Error("Negotiation can only be skipped when using the WebSocket transport directly.")}else{let o=null,n=0;do{if(o=await this._getNegotiationResponse(t),"Disconnecting"===this._connectionState||"Disconnected"===this._connectionState)throw new g("The connection was stopped during negotiation.");if(o.error)throw Error(o.error);if(o.ProtocolVersion)throw Error("Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.");if(o.url&&(t=o.url),o.accessToken){let e=o.accessToken;this._accessTokenFactory=()=>e,this._httpClient._accessToken=e,this._httpClient._accessTokenFactory=void 0}n++}while(o.url&&n<100);if(100===n&&o.url)throw Error("Negotiate redirection limit exceeded.");await this._createTransport(t,this._options.transport,o,e)}this.transport instanceof A&&(this.features.inherentKeepAlive=!0),"Connecting"===this._connectionState&&(this._logger.log(b.$.Debug,"The HttpConnection connected successfully."),this._connectionState="Connected")}catch(e){return this._logger.log(b.$.Error,"Failed to start the connection: "+e),this._connectionState="Disconnected",this.transport=void 0,this._stopPromiseResolver(),Promise.reject(e)}}async _getNegotiationResponse(e){let t={},[o,n]=T();t[o]=n;let r=this._resolveNegotiateUrl(e);this._logger.log(b.$.Debug,`Sending negotiation request: ${r}.`);try{let e=await this._httpClient.post(r,{content:"",headers:{...t,...this._options.headers},timeout:this._options.timeout,withCredentials:this._options.withCredentials});if(200!==e.statusCode)return Promise.reject(Error(`Unexpected status code returned from negotiate '${e.statusCode}'`));let o=JSON.parse(e.content);if((!o.negotiateVersion||o.negotiateVersion<1)&&(o.connectionToken=o.connectionId),o.useStatefulReconnect&&!0!==this._options._useStatefulReconnect)return Promise.reject(new p("Client didn't negotiate Stateful Reconnect but the server did."));return o}catch(t){let e="Failed to complete negotiation with the server: "+t;return t instanceof l&&404===t.statusCode&&(e+=" Either this is not a SignalR endpoint or there is a proxy blocking the connection."),this._logger.log(b.$.Error,e),Promise.reject(new p(e))}}_createConnectUrl(e,t){return t?e+(-1===e.indexOf("?")?"?":"&")+`id=${t}`:e}async _createTransport(e,t,o,n){let r=this._createConnectUrl(e,o.connectionToken);if(this._isITransport(t)){this._logger.log(b.$.Debug,"Connection was provided an instance of ITransport, using that directly."),this.transport=t,await this._startTransport(r,n),this.connectionId=o.connectionId;return}let s=[],i=o.availableTransports||[],a=o;for(let o of i){let i=this._resolveTransportOrError(o,t,n,(null==a?void 0:a.useStatefulReconnect)===!0);if(i instanceof Error)s.push(`${o.transport} failed:`),s.push(i);else if(this._isITransport(i)){if(this.transport=i,!a){try{a=await this._getNegotiationResponse(e)}catch(e){return Promise.reject(e)}r=this._createConnectUrl(e,a.connectionToken)}try{await this._startTransport(r,n),this.connectionId=a.connectionId;return}catch(e){if(this._logger.log(b.$.Error,`Failed to start the transport '${o.transport}': ${e}`),a=void 0,s.push(new _(`${o.transport} failed: ${e}`,q.w[o.transport])),"Connecting"!==this._connectionState){let e="Failed to select transport before stop() was called.";return this._logger.log(b.$.Debug,e),Promise.reject(new g(e))}}}}return s.length>0?Promise.reject(new f(`Unable to connect to the server with any of the available transports. ${s.join(" ")}`,s)):Promise.reject(Error("None of the transports supported by the client are supported by the server."))}_constructTransport(e){switch(e){case q.w.WebSockets:if(!this._options.WebSocket)throw Error("'WebSocket' is not supported in your environment.");return new N(this._httpClient,this._accessTokenFactory,this._logger,this._options.logMessageContent,this._options.WebSocket,this._options.headers||{});case q.w.ServerSentEvents:if(!this._options.EventSource)throw Error("'EventSource' is not supported in your environment.");return new B(this._httpClient,this._httpClient._accessToken,this._logger,this._options);case q.w.LongPolling:return new A(this._httpClient,this._logger,this._options);default:throw Error(`Unknown transport: ${e}.`)}}_startTransport(e,t){return this.transport.onreceive=this.onreceive,this.features.reconnect?this.transport.onclose=async o=>{let n=!1;if(this.features.reconnect)try{this.features.disconnected(),await this.transport.connect(e,t),await this.features.resend()}catch{n=!0}else{this._stopConnection(o);return}n&&this._stopConnection(o)}:this.transport.onclose=e=>this._stopConnection(e),this.transport.connect(e,t)}_resolveTransportOrError(e,t,o,n){var r,s;let i=q.w[e.transport];if(null==i)return this._logger.log(b.$.Debug,`Skipping transport '${e.transport}' because it is not supported by this client.`),Error(`Skipping transport '${e.transport}' because it is not supported by this client.`);if(r=t,s=i,r&&(s&r)==0)return this._logger.log(b.$.Debug,`Skipping transport '${q.w[i]}' because it was disabled by the client.`),new d(`'${q.w[i]}' is disabled by the client.`,i);if(!(e.transferFormats.map(e=>q.B[e]).indexOf(o)>=0))return this._logger.log(b.$.Debug,`Skipping transport '${q.w[i]}' because it does not support the requested transfer format '${q.B[o]}'.`),Error(`'${q.w[i]}' does not support ${q.B[o]}.`);if(i===q.w.WebSockets&&!this._options.WebSocket||i===q.w.ServerSentEvents&&!this._options.EventSource)return this._logger.log(b.$.Debug,`Skipping transport '${q.w[i]}' because it is not supported in your environment.'`),new u(`'${q.w[i]}' is not supported in your environment.`,i);this._logger.log(b.$.Debug,`Selecting transport '${q.w[i]}'.`);try{return this.features.reconnect=i===q.w.WebSockets?n:void 0,this._constructTransport(i)}catch(e){return e}}_isITransport(e){return e&&"object"==typeof e&&"connect"in e}_stopConnection(e){if(this._logger.log(b.$.Debug,`HttpConnection.stopConnection(${e}) called while in state ${this._connectionState}.`),this.transport=void 0,e=this._stopError||e,this._stopError=void 0,"Disconnected"===this._connectionState){this._logger.log(b.$.Debug,`Call to HttpConnection.stopConnection(${e}) was ignored because the connection is already in the disconnected state.`);return}if("Connecting"===this._connectionState)throw this._logger.log(b.$.Warning,`Call to HttpConnection.stopConnection(${e}) was ignored because the connection is still in the connecting state.`),Error(`HttpConnection.stopConnection(${e}) was called while the connection is still in the connecting state.`);if("Disconnecting"===this._connectionState&&this._stopPromiseResolver(),e?this._logger.log(b.$.Error,`Connection disconnected with error '${e}'.`):this._logger.log(b.$.Information,"Connection disconnected."),this._sendQueue&&(this._sendQueue.stop().catch(e=>{this._logger.log(b.$.Error,`TransportSendQueue.stop() threw error '${e}'.`)}),this._sendQueue=void 0),this.connectionId=void 0,this._connectionState="Disconnected",this._connectionStarted){this._connectionStarted=!1;try{this.onclose&&this.onclose(e)}catch(t){this._logger.log(b.$.Error,`HttpConnection.onclose(${e}) threw error '${t}'.`)}}}_resolveUrl(e){if(0===e.lastIndexOf("https://",0)||0===e.lastIndexOf("http://",0))return e;if(!S.isBrowser)throw Error(`Cannot resolve '${e}'.`);let t=window.document.createElement("a");return t.href=e,this._logger.log(b.$.Information,`Normalizing '${e}' to '${t.href}'.`),t.href}_resolveNegotiateUrl(e){let t=new URL(e);t.pathname.endsWith("/")?t.pathname+="negotiate":t.pathname+="/negotiate";let o=new URLSearchParams(t.searchParams);return o.has("negotiateVersion")||o.append("negotiateVersion",this._negotiateVersion.toString()),o.has("useStatefulReconnect")?"true"===o.get("useStatefulReconnect")&&(this._options._useStatefulReconnect=!0):!0===this._options._useStatefulReconnect&&o.append("useStatefulReconnect","true"),t.search=o.toString(),t.toString()}}class j{constructor(e){this._transport=e,this._buffer=[],this._executing=!0,this._sendBufferedData=new L,this._transportResult=new L,this._sendLoopPromise=this._sendLoop()}send(e){return this._bufferData(e),this._transportResult||(this._transportResult=new L),this._transportResult.promise}stop(){return this._executing=!1,this._sendBufferedData.resolve(),this._sendLoopPromise}_bufferData(e){if(this._buffer.length&&typeof this._buffer[0]!=typeof e)throw Error(`Expected data to be of type ${typeof this._buffer} but was of type ${typeof e}`);this._buffer.push(e),this._sendBufferedData.resolve()}async _sendLoop(){for(;;){if(await this._sendBufferedData.promise,!this._executing){this._transportResult&&this._transportResult.reject("Connection stopped.");break}this._sendBufferedData=new L;let e=this._transportResult;this._transportResult=void 0;let t="string"==typeof this._buffer[0]?this._buffer.join(""):j._concatBuffers(this._buffer);this._buffer.length=0;try{await this._transport.send(t),e.resolve()}catch(t){e.reject(t)}}}static _concatBuffers(e){let t=new Uint8Array(e.map(e=>e.byteLength).reduce((e,t)=>e+t)),o=0;for(let n of e)t.set(new Uint8Array(n),o),o+=n.byteLength;return t.buffer}}class L{constructor(){this.promise=new Promise((e,t)=>([this._resolver,this._rejecter]=[e,t]))}resolve(){this._resolver()}reject(e){this._rejecter(e)}}class F{static write(e){return`${e}${F.RecordSeparator}`}static parse(e){if(e[e.length-1]!==F.RecordSeparator)throw Error("Message is incomplete.");let t=e.split(F.RecordSeparator);return t.pop(),t}}F.RecordSeparatorCode=30,F.RecordSeparator=String.fromCharCode(F.RecordSeparatorCode);class U{writeHandshakeRequest(e){return F.write(JSON.stringify(e))}parseHandshakeResponse(e){let t,o;if($(e)){let n=new Uint8Array(e),r=n.indexOf(F.RecordSeparatorCode);if(-1===r)throw Error("Message is incomplete.");let s=r+1;t=String.fromCharCode.apply(null,Array.prototype.slice.call(n.slice(0,s))),o=n.byteLength>s?n.slice(s).buffer:null}else{let n=e.indexOf(F.RecordSeparator);if(-1===n)throw Error("Message is incomplete.");let r=n+1;t=e.substring(0,r),o=e.length>r?e.substring(r):null}let n=JSON.parse(F.parse(t)[0]);if(n.type)throw Error("Expected a handshake response from the server.");return[o,n]}}!function(e){e[e.Invocation=1]="Invocation",e[e.StreamItem=2]="StreamItem",e[e.Completion=3]="Completion",e[e.StreamInvocation=4]="StreamInvocation",e[e.CancelInvocation=5]="CancelInvocation",e[e.Ping=6]="Ping",e[e.Close=7]="Close",e[e.Ack=8]="Ack",e[e.Sequence=9]="Sequence"}(m||(m={}));class O{constructor(){this.observers=[]}next(e){for(let t of this.observers)t.next(e)}error(e){for(let t of this.observers)t.error&&t.error(e)}complete(){for(let e of this.observers)e.complete&&e.complete()}subscribe(e){return this.observers.push(e),new E(this,e)}}class z{constructor(e,t,o){this._bufferSize=1e5,this._messages=[],this._totalMessageCount=0,this._waitForSequenceMessage=!1,this._nextReceivingSequenceId=1,this._latestReceivedSequenceId=0,this._bufferedByteCount=0,this._reconnectInProgress=!1,this._protocol=e,this._connection=t,this._bufferSize=o}async _send(e){let t=this._protocol.writeMessage(e),o=Promise.resolve();if(this._isInvocationMessage(e)){this._totalMessageCount++;let e=()=>{},n=()=>{};$(t)?this._bufferedByteCount+=t.byteLength:this._bufferedByteCount+=t.length,this._bufferedByteCount>=this._bufferSize&&(o=new Promise((t,o)=>{e=t,n=o})),this._messages.push(new K(t,this._totalMessageCount,e,n))}try{this._reconnectInProgress||await this._connection.send(t)}catch{this._disconnected()}await o}_ack(e){let t=-1;for(let o=0;o<this._messages.length;o++){let n=this._messages[o];if(n._id<=e.sequenceId)t=o,$(n._message)?this._bufferedByteCount-=n._message.byteLength:this._bufferedByteCount-=n._message.length,n._resolver();else if(this._bufferedByteCount<this._bufferSize)n._resolver();else break}-1!==t&&(this._messages=this._messages.slice(t+1))}_shouldProcessMessage(e){if(this._waitForSequenceMessage)return e.type===m.Sequence&&(this._waitForSequenceMessage=!1,!0);if(!this._isInvocationMessage(e))return!0;let t=this._nextReceivingSequenceId;return(this._nextReceivingSequenceId++,t<=this._latestReceivedSequenceId)?(t===this._latestReceivedSequenceId&&this._ackTimer(),!1):(this._latestReceivedSequenceId=t,this._ackTimer(),!0)}_resetSequence(e){if(e.sequenceId>this._nextReceivingSequenceId){this._connection.stop(Error("Sequence ID greater than amount of messages we've received."));return}this._nextReceivingSequenceId=e.sequenceId}_disconnected(){this._reconnectInProgress=!0,this._waitForSequenceMessage=!0}async _resend(){let e=0!==this._messages.length?this._messages[0]._id:this._totalMessageCount+1;for(let t of(await this._connection.send(this._protocol.writeMessage({type:m.Sequence,sequenceId:e})),this._messages))await this._connection.send(t._message);this._reconnectInProgress=!1}_dispose(e){for(let t of(null!=e||(e=Error("Unable to reconnect to server.")),this._messages))t._rejector(e)}_isInvocationMessage(e){switch(e.type){case m.Invocation:case m.StreamItem:case m.Completion:case m.StreamInvocation:case m.CancelInvocation:return!0;case m.Close:case m.Sequence:case m.Ping:case m.Ack:return!1}}_ackTimer(){void 0===this._ackTimerHandle&&(this._ackTimerHandle=setTimeout(async()=>{try{this._reconnectInProgress||await this._connection.send(this._protocol.writeMessage({type:m.Ack,sequenceId:this._latestReceivedSequenceId}))}catch{}clearTimeout(this._ackTimerHandle),this._ackTimerHandle=void 0},1e3))}}class K{constructor(e,t,o,n){this._message=e,this._id=t,this._resolver=o,this._rejector=n}}!function(e){e.Disconnected="Disconnected",e.Connecting="Connecting",e.Connected="Connected",e.Disconnecting="Disconnecting",e.Reconnecting="Reconnecting"}(w||(w={}));class X{static create(e,t,o,n,r,s,i){return new X(e,t,o,n,r,s,i)}constructor(e,t,o,n,r,s,i){this._nextKeepAlive=0,this._freezeEventListener=()=>{this._logger.log(b.$.Warning,"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep")},y.isRequired(e,"connection"),y.isRequired(t,"logger"),y.isRequired(o,"protocol"),this.serverTimeoutInMilliseconds=null!=r?r:3e4,this.keepAliveIntervalInMilliseconds=null!=s?s:15e3,this._statefulReconnectBufferSize=null!=i?i:1e5,this._logger=t,this._protocol=o,this.connection=e,this._reconnectPolicy=n,this._handshakeProtocol=new U,this.connection.onreceive=e=>this._processIncomingData(e),this.connection.onclose=e=>this._connectionClosed(e),this._callbacks={},this._methods={},this._closedCallbacks=[],this._reconnectingCallbacks=[],this._reconnectedCallbacks=[],this._invocationId=0,this._receivedHandshakeResponse=!1,this._connectionState=w.Disconnected,this._connectionStarted=!1,this._cachedPingMessage=this._protocol.writeMessage({type:m.Ping})}get state(){return this._connectionState}get connectionId(){return this.connection&&this.connection.connectionId||null}get baseUrl(){return this.connection.baseUrl||""}set baseUrl(e){if(this._connectionState!==w.Disconnected&&this._connectionState!==w.Reconnecting)throw Error("The HubConnection must be in the Disconnected or Reconnecting state to change the url.");if(!e)throw Error("The HubConnection url must be a valid url.");this.connection.baseUrl=e}start(){return this._startPromise=this._startWithStateTransitions(),this._startPromise}async _startWithStateTransitions(){if(this._connectionState!==w.Disconnected)return Promise.reject(Error("Cannot start a HubConnection that is not in the 'Disconnected' state."));this._connectionState=w.Connecting,this._logger.log(b.$.Debug,"Starting HubConnection.");try{await this._startInternal(),S.isBrowser&&window.document.addEventListener("freeze",this._freezeEventListener),this._connectionState=w.Connected,this._connectionStarted=!0,this._logger.log(b.$.Debug,"HubConnection connected successfully.")}catch(e){return this._connectionState=w.Disconnected,this._logger.log(b.$.Debug,`HubConnection failed to start successfully because of error '${e}'.`),Promise.reject(e)}}async _startInternal(){this._stopDuringStartError=void 0,this._receivedHandshakeResponse=!1;let e=new Promise((e,t)=>{this._handshakeResolver=e,this._handshakeRejecter=t});await this.connection.start(this._protocol.transferFormat);try{let t=this._protocol.version;this.connection.features.reconnect||(t=1);let o={protocol:this._protocol.name,version:t};if(this._logger.log(b.$.Debug,"Sending handshake request."),await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(o)),this._logger.log(b.$.Information,`Using HubProtocol '${this._protocol.name}'.`),this._cleanupTimeout(),this._resetTimeoutPeriod(),this._resetKeepAliveInterval(),await e,this._stopDuringStartError)throw this._stopDuringStartError;this.connection.features.reconnect&&(this._messageBuffer=new z(this._protocol,this.connection,this._statefulReconnectBufferSize),this.connection.features.disconnected=this._messageBuffer._disconnected.bind(this._messageBuffer),this.connection.features.resend=()=>{if(this._messageBuffer)return this._messageBuffer._resend()}),this.connection.features.inherentKeepAlive||await this._sendMessage(this._cachedPingMessage)}catch(e){throw this._logger.log(b.$.Debug,`Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`),this._cleanupTimeout(),this._cleanupPingTimer(),await this.connection.stop(e),e}}async stop(){let e=this._startPromise;this.connection.features.reconnect=!1,this._stopPromise=this._stopInternal(),await this._stopPromise;try{await e}catch(e){}}_stopInternal(e){if(this._connectionState===w.Disconnected)return this._logger.log(b.$.Debug,`Call to HubConnection.stop(${e}) ignored because it is already in the disconnected state.`),Promise.resolve();if(this._connectionState===w.Disconnecting)return this._logger.log(b.$.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnecting state.`),this._stopPromise;let t=this._connectionState;return(this._connectionState=w.Disconnecting,this._logger.log(b.$.Debug,"Stopping HubConnection."),this._reconnectDelayHandle)?(this._logger.log(b.$.Debug,"Connection stopped during reconnect delay. Done reconnecting."),clearTimeout(this._reconnectDelayHandle),this._reconnectDelayHandle=void 0,this._completeClose(),Promise.resolve()):(t===w.Connected&&this._sendCloseMessage(),this._cleanupTimeout(),this._cleanupPingTimer(),this._stopDuringStartError=e||new g("The connection was stopped before the hub handshake could complete."),this.connection.stop(e))}async _sendCloseMessage(){try{await this._sendWithProtocol(this._createCloseMessage())}catch{}}stream(e,...t){let o;let[n,r]=this._replaceStreamingParams(t),s=this._createStreamInvocation(e,t,r),i=new O;return i.cancelCallback=()=>{let e=this._createCancelInvocation(s.invocationId);return delete this._callbacks[s.invocationId],o.then(()=>this._sendWithProtocol(e))},this._callbacks[s.invocationId]=(e,t)=>{if(t){i.error(t);return}e&&(e.type===m.Completion?e.error?i.error(Error(e.error)):i.complete():i.next(e.item))},o=this._sendWithProtocol(s).catch(e=>{i.error(e),delete this._callbacks[s.invocationId]}),this._launchStreams(n,o),i}_sendMessage(e){return this._resetKeepAliveInterval(),this.connection.send(e)}_sendWithProtocol(e){return this._messageBuffer?this._messageBuffer._send(e):this._sendMessage(this._protocol.writeMessage(e))}send(e,...t){let[o,n]=this._replaceStreamingParams(t),r=this._sendWithProtocol(this._createInvocation(e,t,!0,n));return this._launchStreams(o,r),r}invoke(e,...t){let[o,n]=this._replaceStreamingParams(t),r=this._createInvocation(e,t,!1,n);return new Promise((e,t)=>{this._callbacks[r.invocationId]=(o,n)=>{if(n){t(n);return}o&&(o.type===m.Completion?o.error?t(Error(o.error)):e(o.result):t(Error(`Unexpected message type: ${o.type}`)))};let n=this._sendWithProtocol(r).catch(e=>{t(e),delete this._callbacks[r.invocationId]});this._launchStreams(o,n)})}on(e,t){if(e&&t)e=e.toLowerCase(),this._methods[e]||(this._methods[e]=[]),-1===this._methods[e].indexOf(t)&&this._methods[e].push(t)}off(e,t){if(!e)return;e=e.toLowerCase();let o=this._methods[e];if(o){if(t){let n=o.indexOf(t);-1!==n&&(o.splice(n,1),0===o.length&&delete this._methods[e])}else delete this._methods[e]}}onclose(e){e&&this._closedCallbacks.push(e)}onreconnecting(e){e&&this._reconnectingCallbacks.push(e)}onreconnected(e){e&&this._reconnectedCallbacks.push(e)}_processIncomingData(e){if(this._cleanupTimeout(),this._receivedHandshakeResponse||(e=this._processHandshakeResponse(e),this._receivedHandshakeResponse=!0),e){for(let t of this._protocol.parseMessages(e,this._logger))if(!this._messageBuffer||this._messageBuffer._shouldProcessMessage(t))switch(t.type){case m.Invocation:this._invokeClientMethod(t).catch(e=>{this._logger.log(b.$.Error,`Invoke client method threw error: ${P(e)}`)});break;case m.StreamItem:case m.Completion:{let e=this._callbacks[t.invocationId];if(e){t.type===m.Completion&&delete this._callbacks[t.invocationId];try{e(t)}catch(e){this._logger.log(b.$.Error,`Stream callback threw error: ${P(e)}`)}}break}case m.Ping:break;case m.Close:{this._logger.log(b.$.Information,"Close message received from server.");let e=t.error?Error("Server returned an error on close: "+t.error):void 0;!0===t.allowReconnect?this.connection.stop(e):this._stopPromise=this._stopInternal(e);break}case m.Ack:this._messageBuffer&&this._messageBuffer._ack(t);break;case m.Sequence:this._messageBuffer&&this._messageBuffer._resetSequence(t);break;default:this._logger.log(b.$.Warning,`Invalid message type: ${t.type}.`)}}this._resetTimeoutPeriod()}_processHandshakeResponse(e){let t,o;try{[o,t]=this._handshakeProtocol.parseHandshakeResponse(e)}catch(o){let e="Error parsing handshake response: "+o;this._logger.log(b.$.Error,e);let t=Error(e);throw this._handshakeRejecter(t),t}if(t.error){let e="Server returned handshake error: "+t.error;this._logger.log(b.$.Error,e);let o=Error(e);throw this._handshakeRejecter(o),o}return this._logger.log(b.$.Debug,"Server handshake complete."),this._handshakeResolver(),o}_resetKeepAliveInterval(){!this.connection.features.inherentKeepAlive&&(this._nextKeepAlive=new Date().getTime()+this.keepAliveIntervalInMilliseconds,this._cleanupPingTimer())}_resetTimeoutPeriod(){if((!this.connection.features||!this.connection.features.inherentKeepAlive)&&(this._timeoutHandle=setTimeout(()=>this.serverTimeout(),this.serverTimeoutInMilliseconds),void 0===this._pingServerHandle)){let e=this._nextKeepAlive-new Date().getTime();e<0&&(e=0),this._pingServerHandle=setTimeout(async()=>{if(this._connectionState===w.Connected)try{await this._sendMessage(this._cachedPingMessage)}catch{this._cleanupPingTimer()}},e)}}serverTimeout(){this.connection.stop(Error("Server timeout elapsed without receiving a message from the server."))}async _invokeClientMethod(e){let t,o,n;let r=e.target.toLowerCase(),s=this._methods[r];if(!s){this._logger.log(b.$.Warning,`No client method with the name '${r}' found.`),e.invocationId&&(this._logger.log(b.$.Warning,`No result given for '${r}' method and invocation ID '${e.invocationId}'.`),await this._sendWithProtocol(this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null)));return}let i=s.slice(),a=!!e.invocationId;for(let s of i)try{let i=t;t=await s.apply(this,e.arguments),a&&t&&i&&(this._logger.log(b.$.Error,`Multiple results provided for '${r}'. Sending error to server.`),n=this._createCompletionMessage(e.invocationId,"Client provided multiple results.",null)),o=void 0}catch(e){o=e,this._logger.log(b.$.Error,`A callback for the method '${r}' threw error '${e}'.`)}n?await this._sendWithProtocol(n):a?(o?n=this._createCompletionMessage(e.invocationId,`${o}`,null):void 0!==t?n=this._createCompletionMessage(e.invocationId,null,t):(this._logger.log(b.$.Warning,`No result given for '${r}' method and invocation ID '${e.invocationId}'.`),n=this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null)),await this._sendWithProtocol(n)):t&&this._logger.log(b.$.Error,`Result given for '${r}' method but server is not expecting a result.`)}_connectionClosed(e){this._logger.log(b.$.Debug,`HubConnection.connectionClosed(${e}) called while in state ${this._connectionState}.`),this._stopDuringStartError=this._stopDuringStartError||e||new g("The underlying connection was closed before the hub handshake could complete."),this._handshakeResolver&&this._handshakeResolver(),this._cancelCallbacksWithError(e||Error("Invocation canceled due to the underlying connection being closed.")),this._cleanupTimeout(),this._cleanupPingTimer(),this._connectionState===w.Disconnecting?this._completeClose(e):this._connectionState===w.Connected&&this._reconnectPolicy?this._reconnect(e):this._connectionState===w.Connected&&this._completeClose(e)}_completeClose(e){if(this._connectionStarted){this._connectionState=w.Disconnected,this._connectionStarted=!1,this._messageBuffer&&(this._messageBuffer._dispose(null!=e?e:Error("Connection closed.")),this._messageBuffer=void 0),S.isBrowser&&window.document.removeEventListener("freeze",this._freezeEventListener);try{this._closedCallbacks.forEach(t=>t.apply(this,[e]))}catch(t){this._logger.log(b.$.Error,`An onclose callback called with error '${e}' threw error '${t}'.`)}}}async _reconnect(e){let t=Date.now(),o=0,n=void 0!==e?e:Error("Attempting to reconnect due to a unknown error."),r=this._getNextRetryDelay(o++,0,n);if(null===r){this._logger.log(b.$.Debug,"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt."),this._completeClose(e);return}if(this._connectionState=w.Reconnecting,e?this._logger.log(b.$.Information,`Connection reconnecting because of error '${e}'.`):this._logger.log(b.$.Information,"Connection reconnecting."),0!==this._reconnectingCallbacks.length){try{this._reconnectingCallbacks.forEach(t=>t.apply(this,[e]))}catch(t){this._logger.log(b.$.Error,`An onreconnecting callback called with error '${e}' threw error '${t}'.`)}if(this._connectionState!==w.Reconnecting){this._logger.log(b.$.Debug,"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.");return}}for(;null!==r;){if(this._logger.log(b.$.Information,`Reconnect attempt number ${o} will start in ${r} ms.`),await new Promise(e=>{this._reconnectDelayHandle=setTimeout(e,r)}),this._reconnectDelayHandle=void 0,this._connectionState!==w.Reconnecting){this._logger.log(b.$.Debug,"Connection left the reconnecting state during reconnect delay. Done reconnecting.");return}try{if(await this._startInternal(),this._connectionState=w.Connected,this._logger.log(b.$.Information,"HubConnection reconnected successfully."),0!==this._reconnectedCallbacks.length)try{this._reconnectedCallbacks.forEach(e=>e.apply(this,[this.connection.connectionId]))}catch(e){this._logger.log(b.$.Error,`An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`)}return}catch(e){if(this._logger.log(b.$.Information,`Reconnect attempt failed because of error '${e}'.`),this._connectionState!==w.Reconnecting){this._logger.log(b.$.Debug,`Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`),this._connectionState===w.Disconnecting&&this._completeClose();return}n=e instanceof Error?e:Error(e.toString()),r=this._getNextRetryDelay(o++,Date.now()-t,n)}}this._logger.log(b.$.Information,`Reconnect retries have been exhausted after ${Date.now()-t} ms and ${o} failed attempts. Connection disconnecting.`),this._completeClose()}_getNextRetryDelay(e,t,o){try{return this._reconnectPolicy.nextRetryDelayInMilliseconds({elapsedMilliseconds:t,previousRetryCount:e,retryReason:o})}catch(o){return this._logger.log(b.$.Error,`IRetryPolicy.nextRetryDelayInMilliseconds(${e}, ${t}) threw error '${o}'.`),null}}_cancelCallbacksWithError(e){let t=this._callbacks;this._callbacks={},Object.keys(t).forEach(o=>{let n=t[o];try{n(null,e)}catch(t){this._logger.log(b.$.Error,`Stream 'error' callback called with '${e}' threw error: ${P(t)}`)}})}_cleanupPingTimer(){this._pingServerHandle&&(clearTimeout(this._pingServerHandle),this._pingServerHandle=void 0)}_cleanupTimeout(){this._timeoutHandle&&clearTimeout(this._timeoutHandle)}_createInvocation(e,t,o,n){if(o)return 0!==n.length?{arguments:t,streamIds:n,target:e,type:m.Invocation}:{arguments:t,target:e,type:m.Invocation};{let o=this._invocationId;return(this._invocationId++,0!==n.length)?{arguments:t,invocationId:o.toString(),streamIds:n,target:e,type:m.Invocation}:{arguments:t,invocationId:o.toString(),target:e,type:m.Invocation}}}_launchStreams(e,t){if(0!==e.length)for(let o in t||(t=Promise.resolve()),e)e[o].subscribe({complete:()=>{t=t.then(()=>this._sendWithProtocol(this._createCompletionMessage(o)))},error:e=>{let n;n=e instanceof Error?e.message:e&&e.toString?e.toString():"Unknown error",t=t.then(()=>this._sendWithProtocol(this._createCompletionMessage(o,n)))},next:e=>{t=t.then(()=>this._sendWithProtocol(this._createStreamItemMessage(o,e)))}})}_replaceStreamingParams(e){let t=[],o=[];for(let n=0;n<e.length;n++){let r=e[n];if(this._isObservable(r)){let s=this._invocationId;this._invocationId++,t[s]=r,o.push(s.toString()),e.splice(n,1)}}return[t,o]}_isObservable(e){return e&&e.subscribe&&"function"==typeof e.subscribe}_createStreamInvocation(e,t,o){let n=this._invocationId;return(this._invocationId++,0!==o.length)?{arguments:t,invocationId:n.toString(),streamIds:o,target:e,type:m.StreamInvocation}:{arguments:t,invocationId:n.toString(),target:e,type:m.StreamInvocation}}_createCancelInvocation(e){return{invocationId:e,type:m.CancelInvocation}}_createStreamItemMessage(e,t){return{invocationId:e,item:t,type:m.StreamItem}}_createCompletionMessage(e,t,o){return t?{error:t,invocationId:e,type:m.Completion}:{invocationId:e,result:o,type:m.Completion}}_createCloseMessage(){return{type:m.Close}}}class J{constructor(){this.name="json",this.version=2,this.transferFormat=q.B.Text}parseMessages(e,t){if("string"!=typeof e)throw Error("Invalid input for JSON hub protocol. Expected a string.");if(!e)return[];null===t&&(t=v.instance);let o=F.parse(e),n=[];for(let e of o){let o=JSON.parse(e);if("number"!=typeof o.type)throw Error("Invalid payload.");switch(o.type){case m.Invocation:this._isInvocationMessage(o);break;case m.StreamItem:this._isStreamItemMessage(o);break;case m.Completion:this._isCompletionMessage(o);break;case m.Ping:case m.Close:break;case m.Ack:this._isAckMessage(o);break;case m.Sequence:this._isSequenceMessage(o);break;default:t.log(b.$.Information,"Unknown message type '"+o.type+"' ignored.");continue}n.push(o)}return n}writeMessage(e){return F.write(JSON.stringify(e))}_isInvocationMessage(e){this._assertNotEmptyString(e.target,"Invalid payload for Invocation message."),void 0!==e.invocationId&&this._assertNotEmptyString(e.invocationId,"Invalid payload for Invocation message.")}_isStreamItemMessage(e){if(this._assertNotEmptyString(e.invocationId,"Invalid payload for StreamItem message."),void 0===e.item)throw Error("Invalid payload for StreamItem message.")}_isCompletionMessage(e){if(e.result&&e.error)throw Error("Invalid payload for Completion message.");!e.result&&e.error&&this._assertNotEmptyString(e.error,"Invalid payload for Completion message."),this._assertNotEmptyString(e.invocationId,"Invalid payload for Completion message.")}_isAckMessage(e){if("number"!=typeof e.sequenceId)throw Error("Invalid SequenceId for Ack message.")}_isSequenceMessage(e){if("number"!=typeof e.sequenceId)throw Error("Invalid SequenceId for Sequence message.")}_assertNotEmptyString(e,t){if("string"!=typeof e||""===e)throw Error(t)}}let V={trace:b.$.Trace,debug:b.$.Debug,info:b.$.Information,information:b.$.Information,warn:b.$.Warning,warning:b.$.Warning,error:b.$.Error,critical:b.$.Critical,none:b.$.None};class Q{configureLogging(e){if(y.isRequired(e,"logging"),void 0!==e.log)this.logger=e;else if("string"==typeof e){let t=function(e){let t=V[e.toLowerCase()];if(void 0!==t)return t;throw Error(`Unknown log level: ${e}`)}(e);this.logger=new I(t)}else this.logger=new I(e);return this}withUrl(e,t){return y.isRequired(e,"url"),y.isNotEmpty(e,"url"),this.url=e,"object"==typeof t?this.httpConnectionOptions={...this.httpConnectionOptions,...t}:this.httpConnectionOptions={...this.httpConnectionOptions,transport:t},this}withHubProtocol(e){return y.isRequired(e,"protocol"),this.protocol=e,this}withAutomaticReconnect(e){if(this.reconnectPolicy)throw Error("A reconnectPolicy has already been set.");return e?Array.isArray(e)?this.reconnectPolicy=new r(e):this.reconnectPolicy=e:this.reconnectPolicy=new r,this}withServerTimeout(e){return y.isRequired(e,"milliseconds"),this._serverTimeoutInMilliseconds=e,this}withKeepAliveInterval(e){return y.isRequired(e,"milliseconds"),this._keepAliveIntervalInMilliseconds=e,this}withStatefulReconnect(e){return void 0===this.httpConnectionOptions&&(this.httpConnectionOptions={}),this.httpConnectionOptions._useStatefulReconnect=!0,this._statefulReconnectBufferSize=null==e?void 0:e.bufferSize,this}build(){let e=this.httpConnectionOptions||{};if(void 0===e.logger&&(e.logger=this.logger),!this.url)throw Error("The 'HubConnectionBuilder.withUrl' method must be called before building the connection.");let t=new W(this.url,e);return X.create(t,this.logger||v.instance,this.protocol||new J,this.reconnectPolicy,this._serverTimeoutInMilliseconds,this._keepAliveIntervalInMilliseconds,this._statefulReconnectBufferSize)}}},31158:(e,t,o)=>{o.d(t,{A:()=>n});let n=(0,o(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},52974:(e,t,o)=>{var n,r;o.d(t,{B:()=>r,w:()=>n}),function(e){e[e.None=0]="None",e[e.WebSockets=1]="WebSockets",e[e.ServerSentEvents=2]="ServerSentEvents",e[e.LongPolling=4]="LongPolling"}(n||(n={})),function(e){e[e.Text=1]="Text",e[e.Binary=2]="Binary"}(r||(r={}))},97011:(e,t,o)=>{var n;o.d(t,{$:()=>n}),function(e){e[e.Trace=0]="Trace",e[e.Debug=1]="Debug",e[e.Information=2]="Information",e[e.Warning=3]="Warning",e[e.Error=4]="Error",e[e.Critical=5]="Critical",e[e.None=6]="None"}(n||(n={}))}};