{"Version": 1, "WorkspaceRootPath": "G:\\CapstoneProject\\OpenAutomate.Backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|g:\\capstoneproject\\openautomate.backend\\openautomate.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|g:\\capstoneproject\\openautomate.backend\\openautomate.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|g:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\automationpackagecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\automationpackagecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|g:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\subscriptionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\subscriptionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0192DCE8-78C1-4EF7-872F-3E4973797A91}|OpenAutomate.API.Tests\\OpenAutomate.API.Tests.csproj|g:\\capstoneproject\\openautomate.backend\\openautomate.api.tests\\controllertests\\assetcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0192DCE8-78C1-4EF7-872F-3E4973797A91}|OpenAutomate.API.Tests\\OpenAutomate.API.Tests.csproj|solutionrelative:openautomate.api.tests\\controllertests\\assetcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "AutomationPackageController.cs", "DocumentMoniker": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AutomationPackageController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\AutomationPackageController.cs", "ToolTip": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AutomationPackageController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\AutomationPackageController.cs", "ViewState": "AgIAABYBAAAAAAAAAAAkwCwBAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T07:03:15.237Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.Development.json", "DocumentMoniker": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\appsettings.Development.json", "RelativeDocumentMoniker": "OpenAutomate.API\\appsettings.Development.json", "ToolTip": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\appsettings.Development.json", "RelativeToolTip": "OpenAutomate.API\\appsettings.Development.json", "ViewState": "AgIAAEQAAAAAAAAAAAAAAFMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-30T06:37:20.918Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "SubscriptionService.cs", "DocumentMoniker": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\SubscriptionService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\SubscriptionService.cs", "ToolTip": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\SubscriptionService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\SubscriptionService.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAMkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T10:29:01.937Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AssetControllerTests.cs", "DocumentMoniker": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API.Tests\\ControllerTests\\AssetControllerTests.cs", "RelativeDocumentMoniker": "OpenAutomate.API.Tests\\ControllerTests\\AssetControllerTests.cs", "ToolTip": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API.Tests\\ControllerTests\\AssetControllerTests.cs", "RelativeToolTip": "OpenAutomate.API.Tests\\ControllerTests\\AssetControllerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T08:39:45.312Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.Production.json", "DocumentMoniker": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\appsettings.Production.json", "RelativeDocumentMoniker": "OpenAutomate.API\\appsettings.Production.json", "ToolTip": "G:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\appsettings.Production.json", "RelativeToolTip": "OpenAutomate.API\\appsettings.Production.json", "ViewState": "AgIAAEQAAAAAAAAAAAAAAC8AAACxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-06T13:19:08.217Z", "EditorCaption": ""}]}]}]}