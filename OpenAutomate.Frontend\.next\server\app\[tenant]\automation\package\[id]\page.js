(()=>{var e={};e.id=4930,e.ids=[4930],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13239:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var s=a(65239),r=a(48088),n=a(31369),i=a(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let o={children:["",{children:["[tenant]",{children:["automation",{children:["package",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94078)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\automation\\package\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\automation\\package\\[id]\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[tenant]/automation/package/[id]/page",pathname:"/[tenant]/automation/package/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},16023:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},19080:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},31599:(e,t,a)=>{"use strict";a.d(t,{c:()=>o});var s=a(43210),r=a(39989),n=a(16189),i=a(31207),l=a(70891);function o(){let e=(0,n.useRouter)(),{data:t,error:a,isLoading:o,mutate:d}=(0,i.Ay)(l.DC.organizationUnits(),()=>r.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:o,error:a?"Failed to fetch organization units. Please try again later.":null,refresh:d,selectOrganizationUnit:(0,s.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},37337:(e,t,a)=>{"use strict";a.d(t,{AW:()=>c,Cb:()=>u,QQ:()=>o,ae:()=>n,jm:()=>d,oy:()=>l,s9:()=>i});var s=a(51787);let r=()=>"default",n=async e=>{let t=r();return await s.F.get(`${t}/api/packages/${e}`)},i=async()=>{let e=r();return await s.F.get(`${e}/api/packages`)},l=async e=>{let t=r(),a=new FormData;return a.append("file",e.file),e.name&&a.append("name",e.name),e.description&&a.append("description",e.description),e.version&&a.append("version",e.version),await s.F.post(`${t}/api/packages/upload`,a)},o=async(e,t)=>{let a=r();return await s.F.get(`${a}/api/packages/${e}/versions/${t}/download`)},d=async e=>{let t=r();await s.F.delete(`${t}/api/packages/${e}`)},c=async(e,t)=>{let a=r();await s.F.delete(`${a}/api/packages/${e}/versions/${t}`)},u=async e=>{let t=r(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(e),n=`${t}/odata/AutomationPackages`;a&&(n+=`?${a}`),console.log("OData query endpoint:",n);try{let e=await s.F.get(n);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} packages from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log(`Found array property "${a}" in response`);let s=e[a],r=e["@odata.count"];return{value:s,"@odata.count":"number"==typeof r?r:s.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},39989:(e,t,a)=>{"use strict";a.d(t,{K:()=>r});var s=a(51787);let r={getMyOrganizationUnits:async()=>await s.F.get("/api/ou/my-ous"),getBySlug:async e=>await s.F.get(`/api/ou/slug/${e}`),getById:async e=>await s.F.get(`/api/ou/${e}`),create:async e=>await s.F.post("/api/ou/create",e),update:async(e,t)=>await s.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await s.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await s.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await s.F.get(`/api/ou/${e}/deletion-status`)}},40228:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},57337:(e,t,a)=>{"use strict";a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\automation\\\\package\\\\package-detail.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\automation\\package\\package-detail.tsx","default")},61018:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64656:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var s=a(37413),r=a(48974),n=a(31057),i=a(50417),l=a(92588),o=a(61018),d=a(2505);function c({children:e}){return(0,s.jsx)(o.TenantGuard,{children:(0,s.jsx)(d.ChatProvider,{children:(0,s.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,s.jsxs)(i.SidebarProvider,{className:"flex flex-col",children:[(0,s.jsx)(n.SiteHeader,{}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)(r.AppSidebar,{}),(0,s.jsx)(i.SidebarInset,{children:(0,s.jsx)(l.SearchProvider,{children:(0,s.jsx)("main",{className:"",children:e})})})]})]})})})})}},68117:(e,t,a)=>{"use strict";a.d(t,{default:()=>A});var s=a(60687),r=a(96834),n=a(29523),i=a(44493),l=a(35950),o=a(91821),d=a(20140),c=a(43210),u=a(31207),m=a(70891),p=a(16189),x=a(41862),h=a(28559),v=a(88233),g=a(19080),f=a(40228),y=a(10022),j=a(16023);let b=(0,a(62688).A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var N=a(31158),k=a(37337),w=a(59321);function A(){let e=(0,p.useParams)(),t=(0,p.useRouter)(),{toast:a}=(0,d.d)(),A=e.id,{data:P,error:$,isLoading:F,mutate:C}=(0,u.Ay)(A?m.DC.packageById(A):null,()=>(0,k.ae)(A)),[D,M]=(0,c.useState)(null),O=async e=>{try{M(e.versionNumber);let t=await (0,k.QQ)(A,e.versionNumber);window.open(t.downloadUrl,"_blank"),a({title:"Download Started",description:`Downloading ${e.fileName}`,variant:"default"})}catch(e){console.error("Error downloading package:",e),a((0,w.m4)(e))}finally{M(null)}},G=async e=>{if(confirm(`Are you sure you want to delete version ${e.versionNumber}?`))try{await (0,k.AW)(A,e.versionNumber),C(),a({title:"Version Deleted",description:`Version ${e.versionNumber} has been deleted successfully`,variant:"default"})}catch(e){console.error("Error deleting version:",e),a((0,w.m4)(e))}},z=async()=>{if(confirm("Are you sure you want to delete this package and all its versions?"))try{await (0,k.jm)(A),a({title:"Package Deleted",description:`Package "${P?.name}" has been deleted successfully`,variant:"default"}),t.back()}catch(e){console.error("Error deleting package:",e),a((0,w.m4)(e))}},R=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},S=e=>new Date(e).toLocaleString();if(F)return(0,s.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.A,{className:"h-6 w-6 animate-spin"}),(0,s.jsx)("span",{children:"Loading package details..."})]})});if($&&!P)return(0,s.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,s.jsx)(o.Fc,{className:"max-w-md",children:(0,s.jsxs)(o.TN,{children:[(0,m.IS)($)??"Package not found",(0,s.jsx)(n.$,{variant:"outline",size:"sm",className:"ml-2",onClick:()=>C(),children:"Retry"})]})})});if(!P)return(0,s.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,s.jsx)(o.Fc,{className:"max-w-md",children:(0,s.jsxs)(o.TN,{children:["Package not found",(0,s.jsx)(n.$,{variant:"outline",size:"sm",className:"ml-2",onClick:()=>C(),children:"Retry"})]})})});let _=[...P.versions].sort((e,t)=>new Date(t.uploadedAt).getTime()-new Date(e.uploadedAt).getTime());return(0,s.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:()=>t.back(),children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:P.name}),(0,s.jsx)("p",{className:"text-muted-foreground",children:P.description})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(r.E,{variant:P.isActive?"default":"secondary",children:P.isActive?"Active":"Inactive"}),(0,s.jsxs)(n.$,{variant:"destructive",onClick:z,children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Delete Package"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Package Information"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Name"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:P.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Description"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:P.description})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Created"}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-1"}),S(P.createdAt)]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Total Versions"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:P.versions.length})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Status"}),(0,s.jsx)(r.E,{variant:P.isActive?"default":"secondary",children:P.isActive?"Active":"Inactive"})]})]})]})}),(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Package Versions"]}),(0,s.jsxs)(n.$,{size:"sm",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Upload New Version"]})]})}),(0,s.jsx)(i.Wu,{children:0===_.length?(0,s.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No versions uploaded yet"}):(0,s.jsx)("div",{className:"space-y-4",children:_.map((e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(r.E,{variant:0===t?"default":"secondary",children:["v",e.versionNumber]}),0===t&&(0,s.jsx)(r.E,{variant:"outline",children:"Latest"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.fileName})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"text-right text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center text-muted-foreground",children:[(0,s.jsx)(b,{className:"h-4 w-4 mr-1"}),R(e.fileSize)]}),(0,s.jsxs)("div",{className:"flex items-center text-muted-foreground",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-1"}),S(e.uploadedAt)]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>O(e),disabled:D===e.versionNumber,children:D===e.versionNumber?(0,s.jsx)(x.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(N.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{size:"sm",variant:"destructive",onClick:()=>G(e),children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]})]})]}),t<_.length-1&&(0,s.jsx)(l.w,{className:"my-2"})]},e.id))})})]})})]})]})}},69231:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>o});var s=a(60687),r=a(43210),n=a(16189),i=a(31599),l=a(31568);function o({children:e}){let{tenant:t}=(0,n.useParams)();(0,n.useRouter)();let{isAuthenticated:a,isLoading:o}=(0,l.A)(),{organizationUnits:d,isLoading:c}=(0,i.c)(),[u,m]=(0,r.useState)(!0);return o||c||u?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,s.jsx)(s.Fragment,{children:e})}},72826:(e,t,a)=>{Promise.resolve().then(a.bind(a,69231)),Promise.resolve().then(a.bind(a,83847)),Promise.resolve().then(a.bind(a,78526)),Promise.resolve().then(a.bind(a,97597)),Promise.resolve().then(a.bind(a,98641)),Promise.resolve().then(a.bind(a,80110))},83442:(e,t,a)=>{Promise.resolve().then(a.bind(a,61018)),Promise.resolve().then(a.bind(a,2505)),Promise.resolve().then(a.bind(a,92588)),Promise.resolve().then(a.bind(a,48974)),Promise.resolve().then(a.bind(a,31057)),Promise.resolve().then(a.bind(a,50417))},84427:(e,t,a)=>{Promise.resolve().then(a.bind(a,68117))},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},90003:(e,t,a)=>{Promise.resolve().then(a.bind(a,57337))},91821:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>o,TN:()=>c,XL:()=>d});var s=a(60687),r=a(43210),n=a(24224),i=a(36966);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),o=r.forwardRef(({className:e,variant:t,...a},r)=>(0,s.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(l({variant:t}),e),...a}));o.displayName="Alert";let d=r.forwardRef(({className:e,children:t,...a},r)=>(t||console.warn("AlertTitle must have content for accessibility"),(0,s.jsx)("h5",{ref:r,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...a,children:t})));d.displayName="AlertTitle";let c=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));c.displayName="AlertDescription"},94078:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i,metadata:()=>n});var s=a(37413),r=a(57337);let n={title:"Package Details",description:"View automation package details and versions"};function i(){return(0,s.jsx)("div",{className:"h-full",children:(0,s.jsx)(r.default,{})})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,7966,5584,5156,4654,6467,6763,519],()=>a(13239));module.exports=s})();