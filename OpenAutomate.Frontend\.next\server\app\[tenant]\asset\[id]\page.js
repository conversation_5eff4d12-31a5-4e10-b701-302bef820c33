(()=>{var e={};e.id=1922,e.ids=[1922],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8888:(e,t,s)=>{Promise.resolve().then(s.bind(s,72504))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},27590:(e,t,s)=>{"use strict";function r(e,t={}){let{dateStyle:s="medium",timeStyle:a="short",fallback:n="N/A",customFormat:i,locale:o="en-US"}=t;if(!e)return n;try{let t;if("string"==typeof e){let s=e;s.endsWith("Z")||s.includes("+")||s.includes("-",10)||(s=s.replace(/(\.\d+)?$/,"$1Z"),console.debug(`Corrected UTC date format: ${e} -> ${s}`)),t=new Date(s)}else t=e;if(isNaN(t.getTime()))return console.warn(`Invalid date provided to formatUtcToLocal: ${e}`),n;if(i)return function(e,t){let s=e.getFullYear(),r=e.getMonth()+1,a=e.getDate(),n=e.getHours(),i=e.getMinutes(),o={yyyy:s.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][r-1],dd:a.toString().padStart(2,"0"),h:(n%12||12).toString(),mm:i.toString().padStart(2,"0"),a:n>=12?"PM":"AM"},l=t;return Object.entries(o).forEach(([e,t])=>{l=l.replace(RegExp(e,"g"),t)}),l}(t,i);return new Intl.DateTimeFormat(o,{dateStyle:s,timeStyle:a}).format(t)}catch(t){return console.error(`Error formatting date ${e}:`,t),n}}s.d(t,{Ej:()=>r})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31599:(e,t,s)=>{"use strict";s.d(t,{c:()=>l});var r=s(43210),a=s(39989),n=s(16189),i=s(31207),o=s(70891);function l(){let e=(0,n.useRouter)(),{data:t,error:s,isLoading:l,mutate:d}=(0,i.Ay)(o.DC.organizationUnits(),()=>a.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:l,error:s?"Failed to fetch organization units. Please try again later.":null,refresh:d,selectOrganizationUnit:(0,r.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},39989:(e,t,s)=>{"use strict";s.d(t,{K:()=>a});var r=s(51787);let a={getMyOrganizationUnits:async()=>await r.F.get("/api/ou/my-ous"),getBySlug:async e=>await r.F.get(`/api/ou/slug/${e}`),getById:async e=>await r.F.get(`/api/ou/${e}`),create:async e=>await r.F.post("/api/ou/create",e),update:async(e,t)=>await r.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await r.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await r.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await r.F.get(`/api/ou/${e}/deletion-status`)}},48340:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\[tenant]\\\\asset\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\asset\\[id]\\page.tsx","default")},61018:(e,t,s)=>{"use strict";s.d(t,{TenantGuard:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64656:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(37413),a=s(48974),n=s(31057),i=s(50417),o=s(92588),l=s(61018),d=s(2505);function c({children:e}){return(0,r.jsx)(l.TenantGuard,{children:(0,r.jsx)(d.ChatProvider,{children:(0,r.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,r.jsxs)(i.SidebarProvider,{className:"flex flex-col",children:[(0,r.jsx)(n.SiteHeader,{}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)(a.AppSidebar,{}),(0,r.jsx)(i.SidebarInset,{children:(0,r.jsx)(o.SearchProvider,{children:(0,r.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,s)=>{"use strict";s.d(t,{TenantGuard:()=>l});var r=s(60687),a=s(43210),n=s(16189),i=s(31599),o=s(31568);function l({children:e}){let{tenant:t}=(0,n.useParams)();(0,n.useRouter)();let{isAuthenticated:s,isLoading:l}=(0,o.A)(),{organizationUnits:d,isLoading:c}=(0,i.c)(),[u,p]=(0,a.useState)(!0);return l||c||u?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,r.jsx)(r.Fragment,{children:e})}},71769:(e,t,s)=>{"use strict";s.d(t,{$o:()=>n,Lm:()=>l,NH:()=>o,deleteAsset:()=>d,gT:()=>i,j0:()=>u,mK:()=>p,qi:()=>c});var r=s(51787);let a=()=>"default",n=async e=>{let t=a();return r.F.post(`${t}/api/assets`,e)},i=async(e,t,s)=>{let n=a(),i=await r.F.put(`${n}/api/assets/${e}`,t);return await r.F.put(`${n}/api/assets/${e}/bot-agents`,{botAgentIds:s}),i},o=async()=>{let e=a();return await r.F.get(`${e}/api/assets`)},l=async e=>{let t=a(),s=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(([e,s])=>{null!=s&&t.append(e,String(s))}),t.toString()}(e),n=`${t}/odata/Assets`;s&&(n+=`?${s}`),console.log("OData query endpoint:",n);try{let e=await r.F.get(n);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} items from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":e["@odata.count"]??e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let s=t[0];console.log(`Found array property "${s}" in response`);let r=e[s],a=e["@odata.count"];return{value:r,"@odata.count":("number"==typeof a?a:void 0)??r.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching assets with OData:",e),{value:[]}}},d=async e=>{let t=a();await r.F.delete(`${t}/api/assets/${e}`)},c=async e=>{let t=a();return r.F.get(`${t}/api/assets/${e}`)},u=async e=>{let t=a();return r.F.get(`${t}/api/assets/${e}/bot-agents`)},p=async()=>{let e=a();return r.F.get(`${e}/api/agents`)}},72504:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(60687),a=s(16189),n=s(29523),i=s(44493),o=s(12597),l=s(13861),d=s(99891),c=s(28559),u=s(19959),p=s(10022),m=s(58869),x=s(43210),h=s(31207),g=s(70891),f=s(20140),b=s(71769),v=s(27590);let j=({asset:e,showSecret:t,onToggleSecret:s})=>0===e.type?(0,r.jsx)("div",{className:"text-base font-semibold border-b pb-1",children:e.value??"-"}):(0,r.jsxs)("div",{className:"flex items-center gap-2 border-b pb-1",children:[(0,r.jsx)("span",{className:"text-base font-semibold",children:t?e.value??"-":"••••••••"}),(0,r.jsx)("button",{type:"button",className:"ml-2 text-gray-500 hover:text-primary",onClick:s,"aria-label":t?"Hide secret":"Show secret",children:t?(0,r.jsx)(o.A,{className:"w-4 h-4"}):(0,r.jsx)(l.A,{className:"w-4 h-4"})})]}),y=({agents:e})=>(e?.length??0)===0?(0,r.jsx)("div",{className:"h-[100px] flex items-center justify-center text-muted-foreground",children:"No authorized agents."}):(0,r.jsx)("div",{className:"overflow-x-auto rounded-lg border",children:(0,r.jsxs)("table",{className:"min-w-full text-sm",children:[(0,r.jsx)("thead",{className:"bg-muted",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"border px-3 py-2 text-left",children:"Name"}),(0,r.jsx)("th",{className:"border px-3 py-2 text-left",children:"Machine Name"})]})}),(0,r.jsx)("tbody",{children:e?.map(e=>r.jsxs("tr",{className:"hover:bg-accent/30 transition",children:[r.jsx("td",{className:"border px-3 py-2",children:e.name}),r.jsx("td",{className:"border px-3 py-2",children:e.machineName})]},e.id))})]})});function N({id:e}){let t=(0,a.useRouter)(),{toast:s}=(0,f.d)(),{data:o,error:l,isLoading:N}=(0,h.Ay)(e?g.DC.assetById(e):null,()=>(0,b.qi)(e)),{data:A,error:P,isLoading:w}=(0,h.Ay)(e?g.DC.assetAgents(e):null,()=>(0,b.j0)(e)),[C,$]=(0,x.useState)(!1),F=l??P;return N||w?(0,r.jsx)("div",{children:"Loading..."}):F&&!o?(0,r.jsx)("div",{className:"text-red-500",children:(0,g.IS)(F)}):o?(0,r.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,r.jsxs)(i.Zp,{className:"border rounded-xl shadow-lg",children:[(0,r.jsxs)(i.aR,{className:"flex items-center justify-between border-b p-6 rounded-t-xl",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"w-6 h-6 text-primary"}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"Asset Detail"})]}),(0,r.jsxs)(n.$,{variant:"ghost",size:"sm",className:"gap-1",onClick:()=>{t.back()},children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),"Back"]})]}),(0,r.jsxs)(i.Wu,{className:"p-8 space-y-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mb-1",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"})," Key"]}),(0,r.jsx)("div",{className:"text-base font-semibold border-b pb-1",children:o.key})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mb-1",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"})," Description"]}),(0,r.jsx)("div",{className:"text-base font-semibold border-b pb-1",children:o.description})]})," ",(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mb-1",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"})," Value"]}),(0,r.jsx)(j,{asset:o,showSecret:C,onToggleSecret:()=>$(e=>!e)})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mb-1",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"})," Type"]}),(0,r.jsx)("span",{children:0===o.type?"String":"Secret"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mb-1 mt-4",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"})," Created At"]}),(0,r.jsx)("div",{className:"text-base font-semibold border-b pb-1",children:(0,v.Ej)(o.createdAt,{dateStyle:"medium",timeStyle:void 0,fallback:"-"})})]})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[" ",(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-primary"}),(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Authorized Agents"})]}),(0,r.jsx)(y,{agents:A})]})]})]})}):(0,r.jsx)("div",{children:"Asset not found"})}function A(){let e=(0,a.useParams)().id;return(0,r.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,r.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,r.jsx)(N,{id:e})]})}},72826:(e,t,s)=>{Promise.resolve().then(s.bind(s,69231)),Promise.resolve().then(s.bind(s,83847)),Promise.resolve().then(s.bind(s,78526)),Promise.resolve().then(s.bind(s,97597)),Promise.resolve().then(s.bind(s,98641)),Promise.resolve().then(s.bind(s,80110))},83442:(e,t,s)=>{Promise.resolve().then(s.bind(s,61018)),Promise.resolve().then(s.bind(s,2505)),Promise.resolve().then(s.bind(s,92588)),Promise.resolve().then(s.bind(s,48974)),Promise.resolve().then(s.bind(s,31057)),Promise.resolve().then(s.bind(s,50417))},91799:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var r=s(65239),a=s(48088),n=s(31369),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let l={children:["",{children:["[tenant]",{children:["asset",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,48340)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\asset\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\asset\\[id]\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[tenant]/asset/[id]/page",pathname:"/[tenant]/asset/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},99160:(e,t,s)=>{Promise.resolve().then(s.bind(s,48340))},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7966,5584,5156,4654,6467,6763,519],()=>s(91799));module.exports=r})();