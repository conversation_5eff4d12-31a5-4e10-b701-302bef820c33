(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7068],{3711:(e,t,a)=>{"use strict";a.d(t,{ChartPieLabel:()=>h});var n=a(95155),r=a(33109),o=a(8782),s=a(34e3),l=a(49171),c=a(70449),i=a(34953),u=a(12115),d=a(66695),g=a(81499);let p={executions:{label:"Executions"},running:{label:"Running",color:"hsl(var(--chart-1))"},pending:{label:"Pending",color:"hsl(var(--chart-2))"},completed:{label:"Completed",color:"hsl(var(--chart-3))"},failed:{label:"Failed",color:"hsl(var(--chart-4))"}};function h(){var e;let{data:t}=(0,i.Ay)(c.DC.executionsWithOData({$count:!0,$top:1e3}),()=>(0,l.bF)({$count:!0,$top:1e3})),a=(0,u.useMemo)(()=>{if(!(null==t?void 0:t.value))return[{status:"running",count:0,fill:"hsl(var(--chart-1))"},{status:"pending",count:0,fill:"hsl(var(--chart-2))"},{status:"completed",count:0,fill:"hsl(var(--chart-3))"},{status:"failed",count:0,fill:"hsl(var(--chart-4))"}];let e=t.value,a={running:0,pending:0,completed:0,failed:0};return e.forEach(e=>{let t=e.status.toLowerCase();"running"===t?a.running++:"pending"===t?a.pending++:"completed"===t?a.completed++:"failed"===t&&a.failed++}),[{status:"running",count:a.running,fill:"hsl(var(--chart-1))"},{status:"pending",count:a.pending,fill:"hsl(var(--chart-2))"},{status:"completed",count:a.completed,fill:"hsl(var(--chart-3))"},{status:"failed",count:a.failed,fill:"hsl(var(--chart-4))"}].filter(e=>e.count>0)},[t]),h=a.reduce((e,t)=>e+t.count,0),m=(null===(e=a.find(e=>"completed"===e.status))||void 0===e?void 0:e.count)||0,f=h>0?(m/h*100).toFixed(1):"0";return(0,n.jsx)("div",{className:"  grid grid-cols-1 gap-4  dark:*:data-[slot=card]:bg-neutral-900",children:(0,n.jsxs)(d.Zp,{className:"flex flex-col  h-full flex-1",children:[(0,n.jsxs)(d.aR,{className:"items-center pb-0",children:[(0,n.jsx)(d.ZB,{children:"Execution Status"}),(0,n.jsx)(d.BT,{children:"Real-time execution distribution"})]}),(0,n.jsx)(d.Wu,{className:"flex-1 pb-0 ",children:(0,n.jsx)(g.at,{config:p,className:"mx-auto aspect-square max-h-[350px]",children:(0,n.jsxs)(o.r,{children:[(0,n.jsx)(g.II,{content:(0,n.jsx)(g.Nt,{hideLabel:!0})}),(0,n.jsx)(s.F,{data:a,dataKey:"count",label:!0,nameKey:"status"})]})})}),(0,n.jsx)(d.wL,{className:"flex-col gap-2 text-sm",children:(0,n.jsxs)("div",{className:"flex items-center gap-2 font-medium leading-none",children:[f,"% success rate (",h," total) ",(0,n.jsx)(r.A,{className:"h-7 w-4"})]})})]})})}},19040:(e,t,a)=>{"use strict";a.d(t,{Bx:()=>o,Gg:()=>l,H1:()=>s});var n=a(7283);function r(){{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return console.log("Current tenant:",e[1]),e[1]}return console.log("Using default tenant"),"default"}let o=async e=>{let t=r(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&t.append(a,String(n))}),t.toString()}(e),o="".concat(t,"/odata/OrganizationUnitUsers");return a&&(o+="?".concat(a)),function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:{value:[]}}(await n.F.get(o))},s={getUsers:async e=>(await n.F.get("".concat(e,"/api/ou/users"))).users,getRolesInOrganizationUnit:async e=>n.F.get("".concat(e,"/api/ou/users/roles")),assignRolesBulk:async(e,t)=>{let a=r(),o=t.map(e=>e.trim()),s="".concat(a,"/api/author/user/").concat(e,"/assign-multiple-roles");try{return await n.F.post(s,{authorityIds:o})}catch(e){throw console.error("Error assigning roles:",e),e}}},l=async e=>{let t=r();await n.F.delete("".concat(t,"/api/ou/users/").concat(e))}},32771:(e,t,a)=>{"use strict";a.d(t,{AW:()=>u,Cb:()=>d,QQ:()=>c,ae:()=>o,jm:()=>i,oy:()=>l,s9:()=>s});var n=a(7283);let r=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},o=async e=>{let t=r();return await n.F.get("".concat(t,"/api/packages/").concat(e))},s=async()=>{let e=r();return await n.F.get("".concat(e,"/api/packages"))},l=async e=>{let t=r(),a=new FormData;return a.append("file",e.file),e.name&&a.append("name",e.name),e.description&&a.append("description",e.description),e.version&&a.append("version",e.version),await n.F.post("".concat(t,"/api/packages/upload"),a)},c=async(e,t)=>{let a=r();return await n.F.get("".concat(a,"/api/packages/").concat(e,"/versions/").concat(t,"/download"))},i=async e=>{let t=r();await n.F.delete("".concat(t,"/api/packages/").concat(e))},u=async(e,t)=>{let a=r();await n.F.delete("".concat(a,"/api/packages/").concat(e,"/versions/").concat(t))},d=async e=>{let t=r(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&"$count"!==a&&t.append(a,String(n))}),t.toString()}(e),o="".concat(t,"/odata/AutomationPackages");a&&(o+="?".concat(a)),console.log("OData query endpoint:",o);try{let e=await n.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," packages from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log('Found array property "'.concat(a,'" in response'));let n=e[a],r=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof r?r:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},36725:(e,t,a)=>{"use strict";a.d(t,{StatisticalStatus:()=>d});var n=a(95155),r=a(81284),o=a(66695),s=a(49171),l=a(70449),c=a(34953),i=a(12115);let u=e=>{switch(e){case"Running":return"border-blue-500 text-blue-700 bg-blue-50 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-700";case"Pending":return"border-amber-500 text-amber-700 bg-amber-50 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-700";case"Completed":return"border-emerald-500 text-emerald-700 bg-emerald-50 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-700";case"Failed":return"border-red-500 text-red-700 bg-red-50 dark:bg-red-950 dark:text-red-300 dark:border-red-700";default:return"border-gray-500 text-gray-700 bg-gray-50 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-700"}};function d(){let{data:e}=(0,c.Ay)(l.DC.executionsWithOData({$count:!0,$top:1e3}),()=>(0,s.bF)({$count:!0,$top:1e3})),t=(0,i.useMemo)(()=>{if(!(null==e?void 0:e.value))return[{label:"Running",count:0},{label:"Pending",count:0},{label:"Completed",count:0},{label:"Failed",count:0}];let t=e.value,a={Running:0,Pending:0,Completed:0,Failed:0};return t.forEach(e=>{let t=e.status;"Running"===t?a.Running++:"Pending"===t?a.Pending++:"Completed"===t?a.Completed++:"Failed"===t&&a.Failed++}),[{label:"Running",count:a.Running},{label:"Pending",count:a.Pending},{label:"Completed",count:a.Completed},{label:"Failed",count:a.Failed}]},[e]);return(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4 *:data-[slot=card]:bg-white   dark:*:data-[slot=card]:bg-neutral-900",children:(0,n.jsxs)(o.Zp,{className:"flex flex-col h-full flex-1",children:[(0,n.jsx)(o.aR,{className:"items-center pb-4",children:(0,n.jsxs)(o.ZB,{className:"flex items-center justify-between text-lg font-medium w-full",children:[(0,n.jsx)("span",{children:"Execution Status"}),(0,n.jsx)(r.A,{className:"w-5 h-5 text-muted-foreground"})]})}),(0,n.jsx)(o.Wu,{className:"flex-1 pb-4 ",children:(0,n.jsx)("div",{className:"grid grid-cols-2 gap-3",children:t.map(e=>(0,n.jsx)(o.Zp,{className:"border-0 shadow-sm hover:shadow-md transition-shadow duration-200 ".concat(u(e.label)),children:(0,n.jsxs)(o.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-3",children:[(0,n.jsx)("div",{className:"px-3 py-1.5 rounded-full text-xs font-semibold uppercase tracking-wide ",children:e.label}),(0,n.jsx)("div",{className:"text-3xl font-bold text-foreground",children:e.count.toLocaleString()}),(0,n.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.label.toLowerCase()," jobs"]})]})},e.label))})})]})})}},45611:(e,t,a)=>{"use strict";a.d(t,{SectionCards:()=>g});var n=a(95155),r=a(66695),o=a(86490),s=a(19040),l=a(81053),c=a(66094),i=a(32771),u=a(34953),d=a(70449);function g(){var e,t,a,g,p;let{data:h}=(0,u.Ay)(d.DC.organizationUnits(),()=>(0,s.Bx)({$count:!0,$top:1})),{data:m}=(0,u.Ay)(d.DC.agentsWithOData({$count:!0,$top:1}),()=>(0,o.dT)({$count:!0,$top:1})),{data:f}=(0,u.Ay)(d.DC.assetsWithOData({$count:!0,$top:1}),()=>(0,l.Lm)({$count:!0,$top:1})),{data:x}=(0,u.Ay)(d.DC.schedulesWithOData({$count:!0,$top:1}),()=>(0,c.ye)({$count:!0,$top:1})),{data:v}=(0,u.Ay)(d.DC.packagesWithOData({$count:!0,$top:1}),()=>(0,i.Cb)({$count:!0,$top:1})),y=null!==(e=null==h?void 0:h["@odata.count"])&&void 0!==e?e:0,b=null!==(t=null==m?void 0:m["@odata.count"])&&void 0!==t?t:0,w=null!==(a=null==f?void 0:f["@odata.count"])&&void 0!==a?a:0,j=null!==(g=null==x?void 0:x["@odata.count"])&&void 0!==g?g:0,k=null!==(p=null==v?void 0:v["@odata.count"])&&void 0!==p?p:0;return(0,n.jsxs)("div",{className:" *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-5 grid grid-cols-1 gap-4 *:data-[slot=card]:bg-white  dark:*:data-[slot=card]:bg-neutral-900 ",children:[(0,n.jsxs)(r.Zp,{className:"@container/card",children:[(0,n.jsxs)(r.aR,{className:"relative",children:[(0,n.jsx)(r.ZB,{children:"Users"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(r.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:y.toLocaleString()})})]}),(0,n.jsx)(r.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:" Total organization users"})})]}),(0,n.jsxs)(r.Zp,{className:"@container/card",children:[(0,n.jsxs)(r.aR,{className:"relative",children:[(0,n.jsx)(r.ZB,{children:"Agents"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(r.BT,{className:"@[250px]/card:text-3xl text-orange-600  text-2xl font-semibold tabular-nums",children:b.toLocaleString()})})]}),(0,n.jsx)(r.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:" Total active agents"})})]}),(0,n.jsxs)(r.Zp,{className:"@container/card",children:[(0,n.jsxs)(r.aR,{className:"relative",children:[(0,n.jsx)(r.ZB,{children:"Assets"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(r.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:w.toLocaleString()})})]}),(0,n.jsx)(r.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Resources under control"})})]}),(0,n.jsxs)(r.Zp,{className:"@container/card",children:[(0,n.jsxs)(r.aR,{className:"relative",children:[(0,n.jsx)(r.ZB,{children:"Schedules"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(r.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:j.toLocaleString()})})]}),(0,n.jsx)(r.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Scheduled tasks"})})]}),(0,n.jsxs)(r.Zp,{className:"@container/card",children:[(0,n.jsxs)(r.aR,{className:"relative",children:[(0,n.jsx)(r.ZB,{children:"Packages"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(r.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:k.toLocaleString()})})]}),(0,n.jsx)(r.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Automation packages"})})]})]})}},47366:(e,t,a)=>{Promise.resolve().then(a.bind(a,3711)),Promise.resolve().then(a.bind(a,45611)),Promise.resolve().then(a.bind(a,36725))},49171:(e,t,a)=>{"use strict";a.d(t,{RT:()=>o,bF:()=>l,iX:()=>i,vH:()=>s});var n=a(7283);let r=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},o=async e=>{let t=r();return await n.F.post("".concat(t,"/api/executions/trigger"),e)},s=async()=>{let e=r();try{return await n.F.get("".concat(e,"/api/executions"))}catch(e){return console.error("Error fetching all executions:",e),[]}},l=async e=>{let t=r(),a={...e};(void 0===a.$top||a.$top<=0)&&(a.$top=10);let o=new Date().getTime(),s=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&"$count"!==a&&t.append(a,String(n))}),t.toString()}(a),l="".concat(t,"/odata/Executions");s?l+="?".concat(s,"&_t=").concat(o):l+="?_t=".concat(o),console.log("Fetching executions with endpoint: ".concat(l)),console.log("Page: ".concat(a.$skip?a.$skip/a.$top+1:1,", Size: ").concat(a.$top));try{let e=await n.F.get(l),t=function(e){return e&&"object"==typeof e?{value:Array.isArray(e.value)?e.value:[],"@odata.count":"number"==typeof e["@odata.count"]?e["@odata.count"]:void 0,"@odata.nextLink":"string"==typeof e["@odata.nextLink"]?e["@odata.nextLink"]:void 0}:{value:[]}}(e);return a.$top&&t.value.length>a.$top&&(console.warn("OData returned ".concat(t.value.length," items but only ").concat(a.$top," were requested. Trimming results.")),t.value=t.value.slice(0,a.$top)),console.log("Received ".concat(t.value.length," executions from OData")),t}catch(e){return console.error("Error fetching executions with OData:",e),{value:[]}}},c=async e=>{let t=r();return await n.F.get("".concat(t,"/api/executions/").concat(e,"/logs/download"))},i=async(e,t)=>{try{let{downloadUrl:a}=await c(e),n=t||"execution_".concat(e,"_logs.log");try{let e=await fetch(a,{method:"GET",headers:{Accept:"application/octet-stream"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.blob(),r=window.URL.createObjectURL(t),o=document.createElement("a");o.href=r,o.download=n,o.style.display="none",document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(r)}catch(t){console.warn("Fetch method failed, falling back to direct link:",t);let e=document.createElement("a");e.href=a,e.download=n,e.target="_blank",e.rel="noopener noreferrer",e.style.display="none",document.body.appendChild(e),e.click(),document.body.removeChild(e)}}catch(e){throw console.error("Error downloading execution logs:",e),e}}},66094:(e,t,a)=>{"use strict";a.d(t,{Fs:()=>i,H4:()=>d,MK:()=>h,Os:()=>l,V5:()=>r,VD:()=>u,g8:()=>g,po:()=>p,sF:()=>s,ye:()=>c});var n=a(7283),r=function(e){return e.Once="Once",e.Minutes="Minutes",e.Hourly="Hourly",e.Daily="Daily",e.Weekly="Weekly",e.Monthly="Monthly",e.Advanced="Advanced",e}({});let o=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},s=async e=>{let t=o();return await n.F.post("".concat(t,"/api/schedules"),e)},l=async()=>{let e=o();try{return await n.F.get("".concat(e,"/api/schedules"))}catch(e){return console.error("Error fetching all schedules:",e),[]}},c=async e=>{let t=o(),a={...e};(void 0===a.$top||a.$top<=0)&&(a.$top=10);let r=new Date().getTime(),s=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&"$count"!==a&&t.append(a,String(n))}),t.toString()}(a),l="".concat(t,"/odata/Schedules");s?l+="?".concat(s,"&_t=").concat(r):l+="?_t=".concat(r),console.log("Fetching schedules with endpoint: ".concat(l)),console.log("Page: ".concat(a.$skip?a.$skip/a.$top+1:1,", Size: ").concat(a.$top));try{let e=await n.F.get(l),t=function(e){return e&&"object"==typeof e?{value:Array.isArray(e.value)?e.value:[],"@odata.count":"number"==typeof e["@odata.count"]?e["@odata.count"]:void 0,"@odata.nextLink":"string"==typeof e["@odata.nextLink"]?e["@odata.nextLink"]:void 0}:{value:[]}}(e);return a.$top&&t.value.length>a.$top&&(console.warn("OData returned ".concat(t.value.length," items but only ").concat(a.$top," were requested. Trimming results.")),t.value=t.value.slice(0,a.$top)),console.log("Received ".concat(t.value.length," schedules from OData")),t}catch(e){return console.error("Error fetching schedules with OData:",e),{value:[]}}},i=async(e,t)=>{let a=o();return await n.F.put("".concat(a,"/api/schedules/").concat(e),t)},u=async e=>{let t=o();await n.F.delete("".concat(t,"/api/schedules/").concat(e))},d=async e=>{let t=o();return await n.F.post("".concat(t,"/api/schedules/").concat(e,"/enable"))},g=async e=>{let t=o();return await n.F.post("".concat(t,"/api/schedules/").concat(e,"/disable"))},p=e=>{if(!e)return"Not scheduled";try{let t=new Date(e);if(isNaN(t.getTime()))return"Invalid date";return new Intl.DateTimeFormat("en-US",{dateStyle:"medium",timeStyle:"short"}).format(t)}catch(e){return"Invalid date"}},h=e=>{switch(e){case"Once":return"Once";case"Minutes":return"Every few minutes";case"Hourly":return"Hourly";case"Daily":return"Daily";case"Weekly":return"Weekly";case"Monthly":return"Monthly";case"Advanced":return"Custom (Cron)";default:return"Unknown"}}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>i,ZB:()=>l,Zp:()=>o,aR:()=>s,wL:()=>u});var n=a(95155);a(12115);var r=a(36928);function o(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function s(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function c(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function u(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},70449:(e,t,a)=>{"use strict";a.d(t,{DC:()=>l,EJ:()=>s,IS:()=>c,bb:()=>o});var n=a(7283),r=a(15874);function o(){return{fetcher:e=>(0,n.fetchApi)(e),onError:function(e){(0,r.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let s={fetcher:e=>(0,n.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},l={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},c=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},81053:(e,t,a)=>{"use strict";a.d(t,{$o:()=>o,Lm:()=>c,NH:()=>l,deleteAsset:()=>i,gT:()=>s,j0:()=>d,mK:()=>g,qi:()=>u});var n=a(7283);let r=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},o=async e=>{let t=r();return n.F.post("".concat(t,"/api/assets"),e)},s=async(e,t,a)=>{let o=r(),s=await n.F.put("".concat(o,"/api/assets/").concat(e),t);return await n.F.put("".concat(o,"/api/assets/").concat(e,"/bot-agents"),{botAgentIds:a}),s},l=async()=>{let e=r();return await n.F.get("".concat(e,"/api/assets"))},c=async e=>{let t=r(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&t.append(a,String(n))}),t.toString()}(e),o="".concat(t,"/odata/Assets");a&&(o+="?".concat(a)),console.log("OData query endpoint:",o);try{let e=await n.F.get(o);return console.log("Raw OData response:",e),function(e){var t,a;if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," items from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":null!==(t=e["@odata.count"])&&void 0!==t?t:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let n=t[0];console.log('Found array property "'.concat(n,'" in response'));let r=e[n],o=e["@odata.count"];return{value:r,"@odata.count":null!==(a="number"==typeof o?o:void 0)&&void 0!==a?a:r.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching assets with OData:",e),{value:[]}}},i=async e=>{let t=r();await n.F.delete("".concat(t,"/api/assets/").concat(e))},u=async e=>{let t=r();return n.F.get("".concat(t,"/api/assets/").concat(e))},d=async e=>{let t=r();return n.F.get("".concat(t,"/api/assets/").concat(e,"/bot-agents"))},g=async()=>{let e=r();return n.F.get("".concat(e,"/api/agents"))}},81499:(e,t,a)=>{"use strict";a.d(t,{II:()=>p,Nt:()=>h,at:()=>d});var n=a(95155),r=a(12115),o=a(83540),s=a(94517),l=a(24026),c=a(36928);let i={light:"",dark:".dark"},u=r.createContext(null);function d(e){let{id:t,className:a,children:s,config:l,...i}=e,d=r.useId(),p="chart-".concat(t||d.replace(/:/g,""));return(0,n.jsx)(u.Provider,{value:{config:l},children:(0,n.jsxs)("div",{"data-slot":"chart","data-chart":p,className:(0,c.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",a),...i,children:[(0,n.jsx)(g,{id:p,config:l}),(0,n.jsx)(o.u,{children:s})]})})}let g=e=>{let{id:t,config:a}=e,r=Object.entries(a).filter(e=>{let[,t]=e;return t.theme||t.color});return r.length?(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(i).map(e=>{let[a,n]=e;return"\n".concat(n," [data-chart=").concat(t,"] {\n").concat(r.map(e=>{var t;let[n,r]=e,o=(null===(t=r.theme)||void 0===t?void 0:t[a])||r.color;return o?"  --color-".concat(n,": ").concat(o,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},p=s.m;function h(e){let{active:t,payload:a,className:o,indicator:s="dot",hideLabel:l=!1,hideIndicator:i=!1,label:d,labelFormatter:g,labelClassName:p,formatter:h,color:f,nameKey:x,labelKey:v}=e,{config:y}=function(){let e=r.useContext(u);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),b=r.useMemo(()=>{var e;if(l||!(null==a?void 0:a.length))return null;let[t]=a,r="".concat(v||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),o=m(y,t,r),s=v||"string"!=typeof d?null==o?void 0:o.label:(null===(e=y[d])||void 0===e?void 0:e.label)||d;return g?(0,n.jsx)("div",{className:(0,c.cn)("font-medium",p),children:g(s,a)}):s?(0,n.jsx)("div",{className:(0,c.cn)("font-medium",p),children:s}):null},[d,g,a,l,p,y,v]);if(!t||!(null==a?void 0:a.length))return null;let w=1===a.length&&"dot"!==s;return(0,n.jsxs)("div",{className:(0,c.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",o),children:[w?null:b,(0,n.jsx)("div",{className:"grid gap-1.5",children:a.map((e,t)=>{let a="".concat(x||e.name||e.dataKey||"value"),r=m(y,e,a),o=f||e.payload.fill||e.color;return(0,n.jsx)("div",{className:(0,c.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===s&&"items-center"),children:h&&(null==e?void 0:e.value)!==void 0&&e.name?h(e.value,e.name,e,t,e.payload):(0,n.jsxs)(n.Fragment,{children:[(null==r?void 0:r.icon)?(0,n.jsx)(r.icon,{}):!i&&(0,n.jsx)("div",{className:(0,c.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===s,"w-1":"line"===s,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===s,"my-0.5":w&&"dashed"===s}),style:{"--color-bg":o,"--color-border":o}}),(0,n.jsxs)("div",{className:(0,c.cn)("flex flex-1 justify-between leading-none",w?"items-end":"items-center"),children:[(0,n.jsxs)("div",{className:"grid gap-1.5",children:[w?b:null,(0,n.jsx)("span",{className:"text-muted-foreground",children:(null==r?void 0:r.label)||e.name})]}),e.value&&(0,n.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function m(e,t,a){if("object"!=typeof t||null===t)return;let n="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,r=a;return a in t&&"string"==typeof t[a]?r=t[a]:n&&a in n&&"string"==typeof n[a]&&(r=n[a]),r in e?e[r]:e[a]}l.s},86490:(e,t,a)=>{"use strict";a.d(t,{NA:()=>l,Qk:()=>u,Ri:()=>s,dT:()=>c,kz:()=>i,xR:()=>o});var n=a(7283);let r=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},o=async e=>{let t=r();return await n.F.post("".concat(t,"/api/agents/create"),e)},s=async e=>{let t=r();return await n.F.get("".concat(t,"/api/agents/").concat(e))},l=async()=>{let e=r();return await n.F.get("".concat(e,"/api/agents"))},c=async e=>{let t=r(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&"$count"!==a&&t.append(a,String(n))}),t.toString()}(e),o="".concat(t,"/odata/BotAgents");a&&(o+="?".concat(a)),console.log("OData query endpoint:",o);try{let e=await n.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," items from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log('Found array property "'.concat(a,'" in response'));let n=e[a],r=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof r?r:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching agents with OData:",e),{value:[]}}},i=async e=>{let t=r();await n.F.delete("".concat(t,"/api/agents/").concat(e))},u=async(e,t)=>{let a=r();return await n.F.put("".concat(a,"/api/agents/").concat(e),t)}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,1559,7827,4727,8441,1684,7358],()=>t(47366)),_N_E=e.O()}]);