(()=>{var e={};e.id=7830,e.ids=[7830],e.modules={115:(e,t,s)=>{Promise.resolve().then(s.bind(s,26850))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>u,gC:()=>p,l6:()=>l,yv:()=>c});var r=s(60687);s(43210);var a=s(69875),n=s(78272),i=s(13964),o=s(3589),d=s(36966);function l({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function m({className:e,size:t="default",children:s,...i}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...n}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,r.jsx)(x,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(h,{})]})})}function u({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"size-4"})})}function h({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26850:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\[tenant]\\\\administration\\\\roles\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\roles\\[id]\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37067:(e,t,s)=>{Promise.resolve().then(s.bind(s,96044))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},91215:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),n=s(31369),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["[tenant]",{children:["administration",{children:["roles",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,26850)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\roles\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\roles\\[id]\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[tenant]/administration/roles/[id]/page",pathname:"/[tenant]/administration/roles/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96044:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),a=s(16189),n=s(29523),i=s(44493),o=s(96834),d=s(28559),l=s(63143),c=s(88233),m=s(43210),p=s(31207),u=s(70891),x=s(6091),h=s(20140),v=s(59321),f=s(73437),j=s(80391);function g({id:e}){let t=(0,a.useRouter)(),{toast:s}=(0,h.d)(),{data:g,error:y,isLoading:N,mutate:A}=(0,p.Ay)(u.DC.roleById(e),()=>x.eV.getRoleById(e)),[P,w]=(0,m.useState)(!1),C=()=>{t.back()},k=()=>{w(!0)},_=async()=>{if(g)try{await x.eV.deleteRole(g.id),s({title:"Success",description:"Role deleted successfully."}),t.back()}catch(e){console.error("Failed to delete role:",e),s((0,v.m4)(e))}},z=async(e=!1)=>{w(!1),e&&await A()};return N?(0,r.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"})})}):g?(0,r.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,r.jsxs)(i.Zp,{className:"border rounded-md shadow-sm",children:[(0,r.jsxs)(i.aR,{className:"flex flex-row items-center justify-between border-b p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(n.$,{variant:"ghost",size:"sm",className:"gap-1",onClick:C,children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"Back"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.ZB,{className:"text-xl",children:g.name}),g.isSystemAuthority&&(0,r.jsx)(o.E,{variant:"secondary",className:"text-xs",children:"System Role"})]})]}),!g.isSystemAuthority&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:k,children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:_,className:"text-destructive hover:text-destructive",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]}),(0,r.jsxs)(i.Wu,{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(b,{label:"Role Name",children:g.name}),(0,r.jsx)(b,{label:"Description",children:g.description||"No description provided"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(b,{label:"Type",children:(0,r.jsx)(o.E,{variant:g.isSystemAuthority?"secondary":"outline",children:g.isSystemAuthority?"System Role":"Custom Role"})}),(0,r.jsx)(b,{label:"Created",children:(0,f.GP)(new Date(g.createdAt),"PPP")}),g.updatedAt&&(0,r.jsx)(b,{label:"Last Updated",children:(0,f.GP)(new Date(g.updatedAt),"PPP")})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Permissions"}),g.permissions&&g.permissions.length>0?(0,r.jsx)("div",{className:"grid gap-3",children:g.permissions.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-muted/30",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"font-medium",children:e.resourceName}),(0,r.jsx)(o.E,{variant:"outline",children:(0,x.gO)(e.permission)})]}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Level ",e.permission]})]},e.resourceName))}):(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground border rounded-lg border-dashed",children:[(0,r.jsx)("p",{children:"No permissions assigned to this role."}),!g.isSystemAuthority&&(0,r.jsx)(n.$,{variant:"outline",onClick:k,className:"mt-2",children:"Add Permissions"})]})]})})]})]}),(0,r.jsx)(j.I,{isOpen:P,onClose:z,editingRole:{id:g.id,name:g.name,description:g.description,isSystemAuthority:g.isSystemAuthority,createdAt:g.createdAt,updatedAt:g.updatedAt,permissions:g.permissions}},g.id)]}):(0,r.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,r.jsx)(i.Zp,{className:"border rounded-md shadow-sm",children:(0,r.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Role not found."}),(0,r.jsxs)(n.$,{variant:"outline",onClick:C,className:"mt-4",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Back to Roles"]})]})})})}function b({label:e,children:t}){return(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:e}),(0,r.jsx)("div",{className:"text-base font-medium pb-1 border-b",children:t})]})}function y(){let e=(0,a.useParams)().id;return(0,r.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,r.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,r.jsx)(g,{id:e})]})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7966,5584,5156,4654,6467,1694,3437,6763,519,7288],()=>s(91215));module.exports=r})();