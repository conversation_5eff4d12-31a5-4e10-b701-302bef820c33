"""
Certificate Generation Module

This module provides functionality to generate certificates from Word templates
using docxtpl library with proper Vietnamese text encoding support. It replaces 
placeholders/bookmarks in the template with actual student data and can output 
in both DOCX and PDF formats.

Usage:
    from tasks.create_cer import CertificateGenerator

    generator = CertificateGenerator("template.docx")
    # Generate PDF certificate (default)
    generator.generate_certificate({
        "student_name": "Nguyễn Văn A",
        "course_name": "Lập trình Python",
        "completion_date": "2024-01-15"
    }, "output.pdf", "pdf")

    # Generate DOCX certificate
    generator.generate_certificate({
        "student_name": "Nguyễn Văn A",
        "course_name": "<PERSON>ập trình Python",
        "completion_date": "2024-01-15"
    }, "output.docx", "docx")
"""

import logging
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

try:
    from docxtpl import DocxTemplate
except ImportError:
    raise ImportError("docxtpl is required. Install it with: pip install docxtpl")

try:
    from docx2pdf import convert
except ImportError:
    raise ImportError("docx2pdf is required for PDF output. Install it with: pip install docx2pdf")


class CertificateGenerator:
    """
    A class to generate certificates from Word templates using docxtpl with Vietnamese text support.

    This class handles loading Word templates, replacing placeholders with
    student data (including Vietnamese text), and saving the generated certificates.
    """

    def __init__(self, template_path: str):
        """
        Initialize the certificate generator with a template file.

        Args:
            template_path (str): Path to the Word template file (.docx)

        Raises:
            FileNotFoundError: If the template file doesn't exist
            ValueError: If the template file is not a .docx file
        """
        self.template_path = Path(template_path)
        self.logger = logging.getLogger(__name__)

        # Validate template file
        if not self.template_path.exists():
            raise FileNotFoundError(f"Template file not found: {template_path}")

        if self.template_path.suffix.lower() != '.docx':
            raise ValueError("Template file must be a .docx file")

        self.logger.info(f"Certificate generator initialized with template: {template_path}")

    def _ensure_utf8_encoding(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ensure all text data is properly encoded for Vietnamese characters.
        
        Args:
            data: Dictionary with student data
            
        Returns:
            Dictionary with properly encoded text
        """
        encoded_data = {}
        
        for key, value in data.items():
            if isinstance(value, str):
                try:
                    # Ensure proper UTF-8 encoding
                    if isinstance(value, bytes):
                        encoded_data[key] = value.decode('utf-8')
                    else:
                        # Test encoding/decoding
                        encoded_data[key] = value.encode('utf-8').decode('utf-8')
                except (UnicodeDecodeError, UnicodeEncodeError) as e:
                    self.logger.warning(f"Encoding issue with field {key}: {e}")
                    # Fallback
                    encoded_data[key] = str(value).encode('utf-8', errors='replace').decode('utf-8')
            else:
                encoded_data[key] = value
        
        return encoded_data

    def generate_certificate(self, student_data: Dict[str, Any], output_path: str, output_format: str = "pdf") -> bool:
        try:
            # Load the template
            self.logger.info(f"Loading template: {self.template_path}")
            doc = DocxTemplate(self.template_path)

            # Ensure proper encoding for Vietnamese text
            encoded_data = self._ensure_utf8_encoding(student_data)

            # Add current date if not provided
            if 'current_date' not in encoded_data:
                encoded_data['current_date'] = datetime.now().strftime("%d/%m/%Y")

            # Log the data being used (without sensitive information)
            student_name = encoded_data.get('TEN_UNG_VIEN', encoded_data.get('student_name', 'Unknown'))
            self.logger.info(f"Generating certificate with Vietnamese text for student: {student_name}")

            # Render the template with properly encoded student data
            doc.render(encoded_data)

            # Ensure output directory exists
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Determine output format and handle accordingly
            output_format = output_format.lower()

            if output_format == "pdf":
                # For PDF output, first save as DOCX then convert to PDF
                import tempfile
                import os

                # Create a temporary DOCX file
                with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                    temp_docx_path = temp_file.name

                doc.save(temp_docx_path)

                # Convert DOCX to PDF
                pdf_path = output_path.with_suffix('.pdf')
                convert(temp_docx_path, str(pdf_path))

                # Clean up temporary DOCX file
                os.unlink(temp_docx_path)

                self.logger.info(f"Certificate with Vietnamese text generated successfully as PDF: {pdf_path}")
            else:
                # For DOCX output (default behavior)
                docx_path = output_path.with_suffix('.docx')
                doc.save(docx_path)
                self.logger.info(f"Certificate with Vietnamese text generated successfully as DOCX: {docx_path}")

            return True

        except Exception as e:
            self.logger.error(f"Error generating certificate with Vietnamese text: {str(e)}")
            return False

    def generate_multiple_certificates(self, students_data: list, output_directory: str,
                                     filename_template: str = "{TEN_UNG_VIEN}_certificate.pdf",
                                     output_format: str = "pdf") -> Dict[str, bool]:
        results = {}
        output_dir = Path(output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)

        for student_data in students_data:
            try:
                # Ensure proper encoding
                encoded_data = self._ensure_utf8_encoding(student_data)
                
                # Generate filename from template - handle Vietnamese characters
                try:
                    filename = filename_template.format(**encoded_data)
                except KeyError:
                    # Fallback if template keys don't match
                    student_name = encoded_data.get('TEN_UNG_VIEN', encoded_data.get('student_name', 'Unknown'))
                    filename = f"{student_name}_certificate.pdf"
                
                # Sanitize filename for Vietnamese characters
                filename = self._sanitize_filename(filename)

                output_path = output_dir / filename

                success = self.generate_certificate(encoded_data, str(output_path), output_format)
                student_name = encoded_data.get('TEN_UNG_VIEN', encoded_data.get('student_name', 'Unknown'))
                results[student_name] = success

            except Exception as e:
                student_name = student_data.get('TEN_UNG_VIEN', student_data.get('student_name', 'Unknown'))
                self.logger.error(f"Error processing student {student_name}: {str(e)}")
                results[student_name] = False

        return results

    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename to be filesystem-safe while preserving Vietnamese characters.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        import re
        
        # Remove or replace characters that are problematic for filenames
        # Keep Vietnamese characters but remove filesystem-unsafe chars
        clean_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        clean_name = re.sub(r'\s+', '_', clean_name)  # Replace spaces with underscores
        clean_name = clean_name.strip('._')  # Remove leading/trailing dots and underscores
        
        # Limit length
        if len(clean_name) > 100:
            clean_name = clean_name[:100]
        
        return clean_name if clean_name else 'certificate.pdf'

    def validate_template(self) -> Dict[str, Any]:
        """
        Validate the template and extract information about available placeholders.

        Returns:
            Dict[str, Any]: Information about the template including any detected placeholders
        """
        try:
            # Try to load the template to validate it
            DocxTemplate(self.template_path)

            # Basic template info
            info = {
                "template_path": str(self.template_path),
                "file_size": self.template_path.stat().st_size,
                "is_valid": True,
                "error": None
            }

            self.logger.info("Template validation successful")
            return info

        except Exception as e:
            self.logger.error(f"Template validation failed: {str(e)}")
            return {
                "template_path": str(self.template_path),
                "is_valid": False,
                "error": str(e)
            }


def create_sample_template_guide():
    """
    Print a guide for creating Word templates compatible with docxtpl.
    """
    guide = """
    CERTIFICATE TEMPLATE CREATION GUIDE
    ===================================

    To create a Word template for certificate generation:

    1. Create a new Word document (.docx)
    2. Design your certificate layout with text, images, borders, etc.
    3. Insert placeholders using Jinja2 syntax: {{ placeholder_name }}

    Common placeholders:
    - {{ student_name }} - Student's full name
    - {{ course_name }} - Name of the course
    - {{ completion_date }} - Date of completion
    - {{ instructor_name }} - Instructor's name
    - {{ grade }} - Grade or score
    - {{ certificate_id }} - Unique certificate ID
    - {{ current_date }} - Current date (auto-generated)

    Example template text:
    "This is to certify that {{ student_name }} has successfully completed
    the course {{ course_name }} on {{ completion_date }} with a grade of {{ grade }}."

    4. Save the document as a .docx file
    5. Use this file as your template with CertificateGenerator

    Note: Make sure to use double curly braces {{ }} around placeholder names.
    """
    print(guide)


# Example usage function
def example_usage():
    """
    Example of how to use the CertificateGenerator class.
    """
    # Sample student data
    student_data = {
        "TEN_UNG_VIEN": "Hoai Nguyen",
        "TEN_KHOA_HOC": "Python Programming Fundamentals",
        "THOI_GIAN_DAO_TAO": "January 15, 2024",
        "NGAY_CAP_CHUNG_CHI": "February 15, 2024",
        "MA_NHAN_VIEN": "71429"
    }

    try:
        # Initialize generator with template
        generator = CertificateGenerator("template/Template_1.docx")

        # Generate single certificate as PDF
        success = generator.generate_certificate(student_data, f"certificates/{student_data['MA_NHAN_VIEN']}_certificate.pdf", "pdf")

        if success:
            print("Certificate generated successfully!")
        else:
            print("Failed to generate certificate.")

    except FileNotFoundError:
        print("Template file not found. Please create a certificate template first.")
        create_sample_template_guide()
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    # Run example when script is executed directly
    example_usage()