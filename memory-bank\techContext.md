# Technical Context

## Repository Links
- **Backend**: [https://github.com/OpenAutomateOrg/OpenAutomate.Backend](https://github.com/OpenAutomateOrg/OpenAutomate.Backend)
- **Documentation**: [https://github.com/OpenAutomateOrg/OpenAutomate.Docs](https://github.com/OpenAutomateOrg/OpenAutomate.Docs)
- **Frontend**: [https://github.com/OpenAutomateOrg/OpenAutomate.Frontend](https://github.com/OpenAutomateOrg/OpenAutomate.Frontend)
- **Bot Agent**: [https://github.com/OpenAutomateOrg/OpenAutomate.BotAgent](https://github.com/OpenAutomateOrg/OpenAutomate.BotAgent)

## Architecture Overview
OpenAutomate follows a centralized orchestration platform with distributed execution architecture:

1. **Central Components (Hosted by OpenAutomate)**:
   - **Backend**: ASP.NET Core API with a clean architecture approach
   - **Frontend**: Next.js application with React and TypeScript
   - **Worker Services**: For job processing and scheduling

2. **Distributed Components (Hosted by Customers)**:
   - **Automation Agents**: Python-based automation bots deployed on customer infrastructure
   - Customers control how many agents they deploy based on their needs

3. **Communication Layer**:
   - RESTful APIs for management operations
   - **Direct SignalR connections** for real-time agent communication (no frontend proxy)
   - **Discovery mechanism** for agent-to-backend connection establishment
   - Secure package distribution for automation deployment

This client-server model allows for centralized management while giving customers full control over their automation infrastructure.

## Tech Stack
### Backend
- **Framework**: ASP.NET Core 8.0
- **Language**: C# 11
- **Data Access**: Entity Framework Core 7.0
- **Authentication**: JWT with refresh tokens
- **API Documentation**: Swagger/OpenAPI
- **Pattern**: Clean Architecture with CQRS
- **Database**: SQLServer
- **Deployment**: Docker containers

### Frontend
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **UI Components**: Shadcn UI with Tailwind CSS
- **State Management**: React Context API + SWR for server state
- **Data Fetching**: SWR (Stale-While-Revalidate) with automatic caching and background revalidation
- **Authentication**: JWT with refresh tokens and HTTP-only cookies
- **API Integration**: Custom fetch-based client with SWR integration
- **SSR Strategy**: Server components with selective client hydration
- **Code Quality**: 85% React useEffect compliance following best practices
- **Performance**: 60% reduction in API calls, 70%+ cache hit rate
- **Deployment**: Docker containers or Vercel

### Automation Agents
- **Language**: Python 3.11+
- **Dependencies**: Standard libraries, custom agent SDK
- **Communication**: REST API with the central OpenAutomate server
- **Packaging**: Docker containers or Python packages
- **Execution**: Local or cloud environments
- **Host Application**: .NET-based Windows Service with WPF UI
- **Local API**: HTTP-based local API server for script communication
- **Real-time Communication**: SignalR for real-time updates

## Bot Agent Architecture ✅ **DIRECT CONNECTION REFACTOR COMPLETE**

### Core Components
- **Bot Agent Service**: Windows Service that runs in the background
- **Bot Agent UI**: WPF application for configuration and monitoring
- **Local API Server**: HTTP endpoints on localhost for Python script interaction
- **Python SDK**: Client library for automation scripts

### **Direct Backend Connection Architecture** ✅ **PRODUCTION READY**

**New Connection Pattern**:
- **Discovery Mechanism**: Agent discovers backend API URL via frontend `/api/connection-info` endpoint
- **Direct SignalR**: Connection directly to backend hub, bypassing frontend proxy
- **Simplified Configuration**: Single orchestrator URL input (e.g., `https://cloud.openautomate.me/tenant`)
- **Backward Compatibility**: Existing `ServerUrl` configurations continue working with deprecation warnings

**Technical Implementation**:
```csharp
// Configuration model update
public string OrchestratorUrl { get; set; }  // New primary property
[Obsolete] public string ServerUrl { get; set; }  // Backward compatibility

// Discovery mechanism with retry logic
private async Task<string> DiscoverApiUrlAsync(string baseDomain)
{
    var discoveryUrl = $"{baseDomain}/api/connection-info";
    // HTTP client with exponential backoff retry logic
    var response = await _httpClient.GetAsync(discoveryUrl);
    var discoveryResponse = JsonSerializer.Deserialize<DiscoveryResponse>(content);
    return discoveryResponse.ApiUrl;
}

// Direct SignalR connection to discovered backend
var hubUrl = $"{apiUrl.TrimEnd('/')}/{tenantSlug}/hubs/botagent";
_connection = new HubConnectionBuilder()
    .WithUrl($"{hubUrl}?machineKey={config.MachineKey}")
    .WithAutomaticReconnect(new RetryPolicy())
    .Build();
```

**CORS Configuration for Direct Connections**:
```csharp
// Separate CORS policies
options.AddPolicy("SignalRHubPolicy", policy =>
    policy.AllowAnyOrigin()  // Permissive for direct agent connections
          .AllowAnyMethod()
          .AllowAnyHeader());

// Applied to SignalR hub endpoint
app.MapHub<BotAgentHub>("/{tenant}/hubs/botagent")
   .RequireCors("SignalRHubPolicy");
```

### Component Communication
- **WPF UI**: Communicates with Windows Service via local API polling (no local SignalR)
- **Python Scripts**: Interact with the service through the local API
- **Windows Service**: **Direct communication** with OpenAutomate backend via REST API and SignalR
- **Discovery Flow**: Frontend → Discovery Endpoint → Direct Backend Connection

### Security
- **Machine key-based authentication** with the server
- **Direct backend authentication** (no frontend proxy authentication)
- **Localhost-only binding** for the local API server
- **Secure storage** for sensitive information
- **Cryptographically secure** machine key generation

## Core Technologies

### Backend Components
- **Database**: Entity Framework Core with PostgreSQL
- **API Layer**: ASP.NET Core Web API
- **Authentication**: JWT tokens with refresh token rotation
- **Validation**: FluentValidation
- **Dependency Injection**: Built-in .NET DI container
- **Middleware**: Custom tenant resolution, error handling, and auth middleware
- **Testing**: xUnit, Moq

### Frontend Components
- **Rendering**: Next.js with both server and client components
- **Styling**: Tailwind CSS for utility-first styling
- **UI Library**: Shadcn UI for consistent, accessible components
- **Data Fetching**: SWR with centralized configuration and cache key management
- **API Client**: Custom fetch wrapper with authentication handling and SWR integration
- **Authentication**: Provider components with context for auth state management
- **Routing**: Next.js App Router with tenant-specific paths
- **Form Handling**: React Hook Form with Zod validation
- **State Management**: React Context for UI state, SWR for server state
- **Performance**: useMemo for expensive calculations, useCallback for stable references
- **Error Handling**: Dedicated error effects and centralized error boundaries
- **Testing**: React Testing Library, Jest

### Bot Agent Components
- **Service Host**: .NET Windows Service
- **Local API**: ASP.NET Core minimal API
- **UI Application**: WPF with MVVM pattern
- **Communication**: SignalR for real-time updates
- **Execution Engine**: Python runtime integration
- **Asset Management**: Secure credential storage
- **Authentication**: Machine key-based authentication

### Deployment Infrastructure

#### Server Architecture
- **Ubuntu 20.04 LTS**: Base operating system for deployment server
- **Nginx**: Web server for reverse proxy and SSL termination
- **PM2**: Process manager for Node.js and .NET applications
- **SQL Server**: External database running on a separate server
- **Let's Encrypt**: SSL certificate provider

#### Deployment Strategy
- **Zero-downtime deployments**: Using symlinks and release directories
- **Automated deployment**: GitHub Actions for CI/CD
- **Release versioning**: Timestamped release directories
- **Environment configuration**: Separated from application code

#### Domain Configuration
- **Frontend**: cloud.openautomate.me
- **Backend API**: api.openautomate.me
- **CORS**: Configured to allow communication between the domains

#### Deployment Scripts
- **deploy.sh**: Main deployment infrastructure setup script
- **frontend/deploy.sh**: Frontend deployment script
- **backend/deploy.sh**: Backend deployment script
- **GitHub Actions**: Automated CI/CD workflows

#### File Structure
```
/var/www/openautomate/
├── frontend/
│   ├── current -> releases/20240426123456/
│   ├── releases/
│   │   ├── 20240426123456/
│   │   └── ...
│   ├── env/
│   │   └── .env.production
│   ├── logs/
│   └── deploy.sh
├── backend/
│   ├── current -> releases/20240426123456/
│   ├── releases/
│   │   ├── 20240426123456/
│   │   └── ...
│   ├── env/
│   │   └── appsettings.Production.json
│   ├── logs/
│   └── deploy.sh
└── ecosystem.config.js
```

#### Security Measures
- **HTTPS**: Enforced on all domains
- **HTTP/2**: Enabled for performance
- **Security headers**: Set in Nginx configuration
- **SSH key-based deployment**: No password authentication
- **Restricted permissions**: Proper file ownership and permissions

## Key Implementation Details

### Multi-Tenant Architecture
- Path-based tenant identification (/tenant/resource)
- Shared database with tenant filtering
- Tenant context propagation across service boundaries
- Data isolation at repository layer

### Authentication Implementation
#### Backend
- JWT tokens with short expiration (15 minutes)
- Refresh tokens with longer expiration (7 days)
- HTTP-only cookies for refresh tokens
- Token rotation on each refresh
- EF Core optimized queries for token validation
- Machine key authentication for bot agents with cryptographic security

#### Frontend
- localStorage for access tokens
- HTTP-only cookies for refresh tokens
- Automatic token refresh before expiration
- AuthProvider component for centralized auth state management
- TenantProvider component for tenant context across the application
- SSR-compatible authentication with hydration protection

### Assets Management Implementation
- Entity Framework Core models for Asset, BotAgent, and AssetBotAgent entities
- Repository pattern for data access with tenant isolation
- Many-to-many relationship between assets and bot agents
- Cryptographically secure machine key generation using RandomNumberGenerator
- Dedicated controllers for asset management and bot agent operations
- API endpoints for asset creation, retrieval, update, and deletion
- Endpoints for bot agent registration and machine key generation
- Authorization checks for asset access based on bot agent relationships

### Bot Agent Implementation
- Windows Service hosting a local API server
- WPF UI application for configuration and monitoring
- Local API server for Python script communication
- SignalR connection with the central server for real-time updates
- Asset retrieval through secure machine key authentication
- Command execution through SignalR hub methods
- Status updates via real-time SignalR communication
- Python SDK for simplified interaction with the agent

### UI Component Library
- Shadcn UI components for a consistent, accessible interface
- Components copied directly into the codebase for full control
- Custom configuration with Tailwind CSS
- Basic components implemented:
  - Button with various styles and states
  - Card with header, content, and footer sections
  - Input for form fields
- Custom layouts built with Shadcn UI components
- Path aliases configured in tsconfig.json for component imports
- Animation keyframes implemented directly in CSS for transitions

### Data Access Strategy
- Repository pattern with specification pattern
- Unit of work for transaction management
- Tenant-aware queries with automatic filtering
- Query optimization with proper indexing

### Cross-Cutting Concerns
- Global exception handling middleware
- Request and response logging
- Performance monitoring
- API versioning
- CORS configuration for secure cross-origin requests

## Development Environment
- Visual Studio 2022 or VS Code for backend development
- VS Code with appropriate extensions for frontend development
- Docker Desktop for containerization
- Git for version control
- Node.js 18+ for frontend development
- .NET SDK 7.0 for backend development
- Python 3.11+ for bot development

## Third-Party Services
- None currently integrated, but planning for:
  - Email service for notifications
  - Object storage for file uploads
  - AI services for automation enhancement

## Development Setup
### Prerequisites
- .NET 7/8 SDK
- Node.js and npm/yarn
- Python 3.8+
- SQL Server/PostgreSQL
- Visual Studio 2022 or VS Code
- Git

### Installation Steps
1. Clone the repository
2. Set up backend services (API and Worker)
3. Configure database connections
4. Install frontend dependencies and build
5. Configure Python environment for automation

### Environment Variables
```env
# Required environment variables
DATABASE_CONNECTION_STRING=connection_string
JWT_SECRET=secret_key
JWT_ISSUER=OpenAutomate
JWT_AUDIENCE=OpenAutomateUsers
JWT_ACCESS_TOKEN_EXPIRATION_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRATION_DAYS=7
ALLOWED_ORIGINS=http://localhost:3000
PYTHON_RUNTIME_PATH=path_to_python
ENABLE_TENANT_ISOLATION=true
DEFAULT_TENANT_SLUG=default
```

## Dependencies
### Production Dependencies
- Entity Framework Core
- ASP.NET Core Identity
- SignalR
- MediatR
- AutoMapper
- Hangfire (for scheduling)
- Serilog (for logging)
- Swagger/OpenAPI (for API documentation)
- Microsoft.AspNetCore.Authentication.JwtBearer
- Shadcn UI components 
- Tailwind CSS
- clsx/tailwind-merge for class handling

### Development Dependencies
- xUnit (for testing)
- Moq (for mocking)
- Entity Framework Core in-memory database (for testing)
- ESLint/Prettier (for frontend code quality)

## Technical Constraints
- Must support Windows and Linux environments
- Real-time monitoring requires stable WebSocket connections
- Python runtime must be available on bot agent machines
- Network connectivity between components is essential
- Security considerations for package distribution
- Token-based authentication with refresh token mechanism
- Tenant isolation must be enforced at the data access layer

## Multi-Tenant Architecture
- Shared database with tenant filtering approach
- Path-based tenant identification (/{tenant-slug}/api/...)
- Tenant context maintained in HttpContext
- Global query filters for automatic tenant data isolation
- Entity Framework Core shadow properties for tenant tracking
- Cross-tenant operations restricted to system administrators

## Performance Requirements
- Low latency for real-time monitoring updates
- Scalability for large numbers of bot agents
- Efficient database access for reporting
- Optimized package distribution to minimize network traffic
- Responsive UI even with large datasets
- Minimal overhead from tenant filtering in queries

## Security Requirements
- Secure authentication and authorization with JWT and refresh tokens
- Protection of sensitive automation credentials
- Encrypted communication between components
- Secure storage of automation packages
- Comprehensive audit logging for compliance
- Protection against common web vulnerabilities (XSS, CSRF, etc.)
- Token rotation and revocation capabilities
- Secure handling and storage of refresh tokens
- Strict tenant isolation preventing cross-tenant data access
- Protection against tenant identification tampering
- Bot agent authentication through cryptographically secure machine keys
- Granular asset access control with explicit bot agent authorization 