"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3003],{11832:(e,t,a)=>{a.d(t,{i:()=>i});var r=a(95155),n=a(18289),o=a(47330),s=a(30285),l=a(44838);function i(e){let{table:t}=e;return(0,r.jsxs)(l.rI,{children:[(0,r.jsx)(n.ty,{asChild:!0,children:(0,r.jsxs)(s.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,r.jsx)(o.A,{}),"View"]})}),(0,r.jsxs)(l.SQ,{align:"end",className:"w-[150px]",children:[(0,r.jsx)(l.lp,{children:"Toggle columns"}),(0,r.jsx)(l.mB,{}),t.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,r.jsx)(l.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},12187:(e,t,a)=>{function r(e){if(!e)return"An unexpected error occurred";if("string"==typeof e)return e;if("object"==typeof e&&null!==e&&"status"in e&&"message"in e&&"number"==typeof e.status&&"string"==typeof e.message)return 0===e.status?e.message||"Network error. Please check your connection.":function(e){var t;if(e.details){let t=function(e){try{var t,a;let r=JSON.parse(e);return null!==(a=null!==(t=r.error)&&void 0!==t?t:r.message)&&void 0!==a?a:null}catch(e){return null}}(e.details);return t||e.details}return null!==(t=e.message)&&void 0!==t?t:"An error occurred"}(e);if(e instanceof Error)return e.message.includes("Failed to fetch")||e.message.includes("NetworkError")?"Network error. Please check your connection.":e.message;let t=function(e){if(null!==e&&"object"==typeof e&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response){let t=e.response.data;if("object"==typeof t&&null!==t){if("message"in t&&"string"==typeof t.message)return t.message;if("error"in t&&"string"==typeof t.error)return t.error}if("string"==typeof t)return t}return null}(e);if(null!==t)return t;if("object"==typeof e&&null!==e){let t=void 0!==e.error&&null!==e.error&&"string"==typeof e.error?e.error:void 0!==e.message&&null!==e.message&&"string"==typeof e.message?e.message:null;if(null!==t)return t}return"An unexpected error occurred"}function n(e){return{title:"Error",description:r(e),variant:function(e){if("object"==typeof e&&null!==e&&"status"in e)return e.status<400?"default":"destructive";if("string"==typeof e){let t=e.toLowerCase();if(t.includes("warning")||t.includes("info"))return"default"}return"destructive"}(e)}}a.d(t,{PE:()=>r,m4:()=>n})},14636:(e,t,a)=>{a.d(t,{AM:()=>s,Wv:()=>l,hl:()=>i});var r=a(95155);a(12115);var n=a(65141),o=a(36928);function s(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"popover",...t})}function l(e){let{...t}=e;return(0,r.jsx)(n.l9,{"data-slot":"popover-trigger",...t})}function i(e){let{className:t,align:a="center",sideOffset:s=4,...l}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{"data-slot":"popover-content",align:a,sideOffset:s,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...l})})}},15426:(e,t,a)=>{a.d(t,{Ej:()=>r});function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{dateStyle:a="medium",timeStyle:r="short",fallback:n="N/A",customFormat:o,locale:s=navigator.language||"en-US"}=t;if(!e)return n;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug("Corrected UTC date format: ".concat(e," -> ").concat(a))),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn("Invalid date provided to formatUtcToLocal: ".concat(e)),n;if(o)return function(e,t){let a=e.getFullYear(),r=e.getMonth()+1,n=e.getDate(),o=e.getHours(),s=e.getMinutes(),l={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][r-1],dd:n.toString().padStart(2,"0"),h:(o%12||12).toString(),mm:s.toString().padStart(2,"0"),a:o>=12?"PM":"AM"},i=t;return Object.entries(l).forEach(e=>{let[t,a]=e;i=i.replace(RegExp(t,"g"),a)}),i}(t,o);return new Intl.DateTimeFormat(s,{dateStyle:a,timeStyle:r}).format(t)}catch(t){return console.error("Error formatting date ".concat(e,":"),t),n}}},32771:(e,t,a)=>{a.d(t,{AW:()=>c,Cb:()=>u,QQ:()=>i,ae:()=>o,jm:()=>d,oy:()=>l,s9:()=>s});var r=a(7283);let n=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},o=async e=>{let t=n();return await r.F.get("".concat(t,"/api/packages/").concat(e))},s=async()=>{let e=n();return await r.F.get("".concat(e,"/api/packages"))},l=async e=>{let t=n(),a=new FormData;return a.append("file",e.file),e.name&&a.append("name",e.name),e.description&&a.append("description",e.description),e.version&&a.append("version",e.version),await r.F.post("".concat(t,"/api/packages/upload"),a)},i=async(e,t)=>{let a=n();return await r.F.get("".concat(a,"/api/packages/").concat(e,"/versions/").concat(t,"/download"))},d=async e=>{let t=n();await r.F.delete("".concat(t,"/api/packages/").concat(e))},c=async(e,t)=>{let a=n();await r.F.delete("".concat(a,"/api/packages/").concat(e,"/versions/").concat(t))},u=async e=>{let t=n(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,r]=e;null!=r&&"$count"!==a&&t.append(a,String(r))}),t.toString()}(e),o="".concat(t,"/odata/AutomationPackages");a&&(o+="?".concat(a)),console.log("OData query endpoint:",o);try{let e=await r.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," packages from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log('Found array property "'.concat(a,'" in response'));let r=e[a],n=e["@odata.count"];return{value:r,"@odata.count":"number"==typeof n?n:r.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},54165:(e,t,a)=>{a.d(t,{Cf:()=>p,Es:()=>m,HM:()=>u,L3:()=>y,c7:()=>f,lG:()=>i,rr:()=>v,zM:()=>d});var r=a(95155),n=a(12115),o=a(59096),s=a(54416),l=a(36928);let i=o.bL,d=o.l9,c=o.ZL,u=o.bm,g=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(o.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",a),...n})});g.displayName=o.hJ.displayName;let p=n.forwardRef((e,t)=>{let{className:a,children:n,...i}=e;return(0,r.jsxs)(c,{children:[(0,r.jsx)(g,{}),(0,r.jsxs)(o.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",a),...i,children:[n,(0,r.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(s.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});p.displayName=o.UC.displayName;let f=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};f.displayName="DialogHeader";let m=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};m.displayName="DialogFooter";let y=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(o.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",a),...n})});y.displayName=o.hE.displayName;let v=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(o.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...n})});v.displayName=o.VY.displayName},62668:(e,t,a)=>{a.d(t,{z:()=>o});var r=a(35695),n=a(12115);function o(){let e=(0,r.useRouter)(),t=(0,r.useSearchParams)(),a=(0,n.useCallback)(e=>{let a=new URLSearchParams(t.toString());return Object.entries(e).forEach(e=>{let[t,r]=e;null===r?a.delete(t):a.set(t,r)}),a.toString()},[t]),o=(0,n.useCallback)((t,r)=>{let n=a(r);e.push("".concat(t,"?").concat(n),{scroll:!1})},[a,e]);return{createQueryString:a,updateUrl:o}}},70449:(e,t,a)=>{a.d(t,{DC:()=>l,EJ:()=>s,IS:()=>i,bb:()=>o});var r=a(7283),n=a(15874);function o(){return{fetcher:e=>(0,r.fetchApi)(e),onError:function(e){(0,n.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let s={fetcher:e=>(0,r.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},l={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},i=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},85511:(e,t,a)=>{a.d(t,{V:()=>m});var r=a(15933),n=a(95155),o=a(12115),s=a(42355),l=a(13052),i=a(66474),d=a(50713),c=a(65477),u=a(36928),g=a(30285);function p(){let e=(0,r._)(["rtl:**:[.rdp-button_next>svg]:rotate-180"],["rtl:**:[.rdp-button\\_next>svg]:rotate-180"]);return p=function(){return e},e}function f(){let e=(0,r._)(["rtl:**:[.rdp-button_previous>svg]:rotate-180"],["rtl:**:[.rdp-button\\_previous>svg]:rotate-180"]);return f=function(){return e},e}function m(e){let{className:t,classNames:a,showOutsideDays:r=!0,captionLayout:o="label",buttonVariant:m="ghost",formatters:v,components:h,...x}=e,b=(0,d.a)();return(0,n.jsx)(c.h,{showOutsideDays:r,className:(0,u.cn)("bg-background group/calendar p-3 [--cell-size:--spacing(8)] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent",String.raw(p()),String.raw(f()),t),captionLayout:o,formatters:{formatMonthDropdown:e=>e.toLocaleString("en",{month:"short"}),...v},classNames:{root:(0,u.cn)("w-fit",b.root),months:(0,u.cn)("flex gap-4 flex-col md:flex-row relative",b.months),month:(0,u.cn)("flex flex-col w-full gap-4",b.month),nav:(0,u.cn)("flex items-center gap-1 w-full absolute top-0 inset-x-0 justify-between",b.nav),button_previous:(0,u.cn)((0,g.r)({variant:m}),"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none",b.button_previous),button_next:(0,u.cn)((0,g.r)({variant:m}),"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none",b.button_next),month_caption:(0,u.cn)("flex items-center justify-center h-(--cell-size) w-full px-(--cell-size)",b.month_caption),dropdowns:(0,u.cn)("w-full flex items-center text-sm font-medium justify-center h-(--cell-size) gap-1.5 text-foreground dark:text-foreground",b.dropdowns),dropdown_root:(0,u.cn)("relative has-focus:border-ring border border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] rounded-md bg-popover text-popover-foreground dark:bg-popover dark:text-popover-foreground [&>select]:bg-popover [&>select]:text-popover-foreground dark:[&>select]:bg-popover dark:[&>select]:text-popover-foreground",b.dropdown_root),dropdown:(0,u.cn)("absolute inset-0 opacity-0 bg-popover text-popover-foreground dark:bg-popover dark:text-popover-foreground",b.dropdown),caption_label:(0,u.cn)("select-none font-medium","label"===o?"text-sm":"rounded-md pl-2 pr-1 flex items-center gap-1 text-sm h-8 [&>svg]:text-muted-foreground [&>svg]:size-3.5",b.caption_label),table:"w-full border-collapse",weekdays:(0,u.cn)("flex",b.weekdays),weekday:(0,u.cn)("text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] select-none",b.weekday),week:(0,u.cn)("flex w-full mt-2",b.week),week_number_header:(0,u.cn)("select-none w-(--cell-size)",b.week_number_header),week_number:(0,u.cn)("text-[0.8rem] select-none text-muted-foreground",b.week_number),day:(0,u.cn)("relative w-full h-full p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md group/day aspect-square select-none",b.day),range_start:(0,u.cn)("rounded-l-md bg-accent",b.range_start),range_middle:(0,u.cn)("rounded-none",b.range_middle),range_end:(0,u.cn)("rounded-r-md bg-accent",b.range_end),today:(0,u.cn)("bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none",b.today),outside:(0,u.cn)("text-muted-foreground aria-selected:text-muted-foreground",b.outside),disabled:(0,u.cn)("text-muted-foreground opacity-50",b.disabled),hidden:(0,u.cn)("invisible",b.hidden),...a},components:{Root:e=>{let{className:t,rootRef:a,...r}=e;return(0,n.jsx)("div",{"data-slot":"calendar",ref:a,className:(0,u.cn)(t),...r})},Chevron:e=>{let{className:t,orientation:a,...r}=e;return"left"===a?(0,n.jsx)(s.A,{className:(0,u.cn)("size-4",t),...r}):"right"===a?(0,n.jsx)(l.A,{className:(0,u.cn)("size-4",t),...r}):(0,n.jsx)(i.A,{className:(0,u.cn)("size-4",t),...r})},DayButton:y,WeekNumber:e=>{let{children:t,...a}=e;return(0,n.jsx)("td",{...a,children:(0,n.jsx)("div",{className:"flex size-(--cell-size) items-center justify-center text-center",children:t})})},...h},...x})}function y(e){let{className:t,day:a,modifiers:r,...s}=e,l=(0,d.a)(),i=o.useRef(null);return o.useEffect(()=>{var e;r.focused&&(null===(e=i.current)||void 0===e||e.focus())},[r.focused]),(0,n.jsx)(g.$,{ref:i,variant:"ghost",size:"icon","data-day":a.date.toLocaleDateString(),"data-selected-single":r.selected&&!r.range_start&&!r.range_end&&!r.range_middle,"data-range-start":r.range_start,"data-range-end":r.range_end,"data-range-middle":r.range_middle,className:(0,u.cn)("data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 dark:hover:text-accent-foreground flex aspect-square size-auto w-full min-w-(--cell-size) flex-col gap-1 leading-none font-normal group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] data-[range-end=true]:rounded-md data-[range-end=true]:rounded-r-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md data-[range-start=true]:rounded-l-md [&>span]:text-xs [&>span]:opacity-70",l.day,t),...s})}},86490:(e,t,a)=>{a.d(t,{NA:()=>l,Qk:()=>c,Ri:()=>s,dT:()=>i,kz:()=>d,xR:()=>o});var r=a(7283);let n=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},o=async e=>{let t=n();return await r.F.post("".concat(t,"/api/agents/create"),e)},s=async e=>{let t=n();return await r.F.get("".concat(t,"/api/agents/").concat(e))},l=async()=>{let e=n();return await r.F.get("".concat(e,"/api/agents"))},i=async e=>{let t=n(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,r]=e;null!=r&&"$count"!==a&&t.append(a,String(r))}),t.toString()}(e),o="".concat(t,"/odata/BotAgents");a&&(o+="?".concat(a)),console.log("OData query endpoint:",o);try{let e=await r.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," items from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log('Found array property "'.concat(a,'" in response'));let r=e[a],n=e["@odata.count"];return{value:r,"@odata.count":"number"==typeof n?n:r.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching agents with OData:",e),{value:[]}}},d=async e=>{let t=n();await r.F.delete("".concat(t,"/api/agents/").concat(e))},c=async(e,t)=>{let a=n();return await r.F.put("".concat(a,"/api/agents/").concat(e),t)}},88262:(e,t,a)=>{a.d(t,{$:()=>n,d:()=>o});var r=a(12115);let n=(0,r.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function o(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}}}]);