(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2509],{45995:(e,t,a)=>{"use strict";a.d(t,{i:()=>r});var s=a(7283);let r={getAllUsers:async()=>(0,s.fetchApi)("api/admin/user/get-all",{method:"GET"}),getUserById:async e=>(0,s.fetchApi)("api/admin/user/detail/".concat(e),{method:"GET"}),updateUserInfo:async(e,t)=>(0,s.fetchApi)("api/admin/user/update-detail/".concat(e),{method:"PUT",body:JSON.stringify(t)}),changeUserPassword:async(e,t)=>(0,s.fetchApi)("api/admin/user/change-password/".concat(e),{method:"POST",body:JSON.stringify(t)}),getAllOrganizationUnits:async()=>await s.F.get("/api/admin/organization-unit/get-all"),deleteOrganizationUnit:async e=>{await s.F.delete("/api/admin/organization-unit/".concat(e))}}},54908:(e,t,a)=>{"use strict";a.d(t,{default:()=>y});var s=a(95155),r=a(66695);function n(e){let{totalUsers:t=0,totalOrganizationUnits:a=0}=e;return(0,s.jsxs)("div",{className:"*:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-3 grid grid-cols-1 gap-4 *:data-[slot=card]:bg-white dark:*:data-[slot=card]:bg-neutral-900",children:[(0,s.jsxs)(r.Zp,{className:"@container/card",children:[(0,s.jsxs)(r.aR,{className:"relative",children:[(0,s.jsx)(r.ZB,{children:"Users"}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(r.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:t.toLocaleString()})})]}),(0,s.jsx)(r.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,s.jsx)("div",{className:"text-muted-foreground",children:"Total organization users"})})]}),(0,s.jsxs)(r.Zp,{className:"@container/card",children:[(0,s.jsxs)(r.aR,{className:"relative",children:[(0,s.jsx)(r.ZB,{children:"Agents"}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(r.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:a.toLocaleString()})})]}),(0,s.jsx)(r.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,s.jsx)("div",{className:"text-muted-foreground",children:"Total active agents"})})]}),(0,s.jsxs)(r.Zp,{className:"@container/card",children:[(0,s.jsxs)(r.aR,{className:"relative",children:[(0,s.jsx)(r.ZB,{children:"Total Billing"}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(r.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:"0"})})]}),(0,s.jsx)(r.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,s.jsx)("div",{className:"text-muted-foreground",children:"Resources under control"})})]})]})}var l=a(12115),i=a(6224),o=a(94754),c=a(96025),d=a(83394),u=a(81499);let m={views:{label:"System Statistics"},users:{label:"Users",color:"var(--chart-1)"},organizationUnits:{label:"Organization Units",color:"var(--chart-2)"}};function x(e){let{users:t=[],organizationUnits:a=[]}=e,[n,x]=l.useState("users"),h=l.useMemo(()=>{let e=new Date,s=Array.from({length:30},(t,a)=>{let s=new Date(e);return s.setDate(s.getDate()-(29-a)),s.toISOString().split("T")[0]}),r=t.reduce(e=>{let t=Math.floor(50*s.length),a=s[t];return e[a]=(e[a]||0)+1,e},{}),n=a.reduce((e,t)=>{let a;try{if(t.createdAt){let e=t.createdAt.includes(".")?t.createdAt.split(".")[0]+"Z":t.createdAt;a=new Date(e).toISOString().split("T")[0]}else a=new Date().toISOString().split("T")[0]}catch(e){a=new Date().toISOString().split("T")[0]}return s.includes(a)&&(e[a]=(e[a]||0)+1),e},{});return s.map(e=>({date:e,users:r[e]||0,organizationUnits:n[e]||0}))},[t,a]),g=l.useMemo(()=>({users:t.length,organizationUnits:a.length}),[t,a]);return(0,s.jsxs)(r.Zp,{className:"py-0 dark:*:data-[slot=card]:bg-neutral-900 ",children:[(0,s.jsxs)(r.aR,{className:"flex flex-col items-stretch border-b !p-0 sm:flex-row",children:[(0,s.jsxs)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 pt-4 pb-3 sm:!py-0",children:[(0,s.jsx)(r.ZB,{children:"System Statistics - Interactive"}),(0,s.jsx)(r.BT,{children:"Showing system data for the last 30 days"})]}),(0,s.jsx)("div",{className:"flex",children:["users","organizationUnits"].map(e=>(0,s.jsxs)("button",{"data-active":n===e,className:"data-[active=true]:bg-muted/50 relative z-30 flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6",onClick:()=>x(e),children:[(0,s.jsx)("span",{className:"text-muted-foreground text-xs",children:m[e].label}),(0,s.jsx)("span",{className:"text-lg text-orange-600 leading-none font-bold sm:text-3xl",children:g[e].toLocaleString()})]},e))})]}),(0,s.jsx)(r.Wu,{className:"px-2 sm:p-6",children:(0,s.jsx)(u.at,{config:m,className:"aspect-auto h-[250px] w-full",children:(0,s.jsxs)(i.E,{accessibilityLayer:!0,data:h,margin:{left:12,right:12},children:[(0,s.jsx)(o.d,{vertical:!1}),(0,s.jsx)(c.W,{dataKey:"date",tickLine:!1,axisLine:!1,tickMargin:8,minTickGap:32,tickFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"})}),(0,s.jsx)(u.II,{content:(0,s.jsx)(u.Nt,{className:"w-[150px]",nameKey:"views",labelFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})})}),(0,s.jsx)(d.y,{dataKey:n,fill:"var(--color-".concat(n,")")})]})})})]})}var h=a(33109);let g=[{month:"January",desktop:186,mobile:80},{month:"February",desktop:305,mobile:200},{month:"March",desktop:237,mobile:120},{month:"April",desktop:73,mobile:190},{month:"May",desktop:209,mobile:130},{month:"June",desktop:214,mobile:140}],f={desktop:{label:"Desktop",color:"var(--chart-1)"},mobile:{label:"Mobile",color:"var(--chart-2)"}};function p(){return(0,s.jsxs)(r.Zp,{className:"dark:*:data-[slot=card]:bg-neutral-900 ",children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Bar Chart - Multiple"}),(0,s.jsx)(r.BT,{children:"January - June 2024"})]}),(0,s.jsx)(r.Wu,{children:(0,s.jsx)(u.at,{config:f,className:"aspect-auto h-[250px] w-full",children:(0,s.jsxs)(i.E,{accessibilityLayer:!0,data:g,children:[(0,s.jsx)(o.d,{vertical:!1}),(0,s.jsx)(c.W,{dataKey:"month",tickLine:!1,tickMargin:10,axisLine:!1,tickFormatter:e=>e.slice(0,3)}),(0,s.jsx)(u.II,{cursor:!1,content:(0,s.jsx)(u.Nt,{indicator:"dashed"})}),(0,s.jsx)(d.y,{dataKey:"desktop",fill:"var(--color-desktop)",radius:4}),(0,s.jsx)(d.y,{dataKey:"mobile",fill:"var(--color-mobile)",radius:4})]})})}),(0,s.jsxs)(r.wL,{className:"flex-col items-start gap-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex gap-2 leading-none font-medium",children:["Trending up by 5.2% this month ",(0,s.jsx)(h.A,{className:"h-4 w-4"})]}),(0,s.jsx)("div",{className:"text-muted-foreground leading-none",children:"Showing total visitors for the last 6 months"})]})]})}var v=a(45995),j=a(70449),b=a(34953);function y(){var e,t;let{data:a}=(0,b.Ay)(j.DC.adminUsers(),()=>v.i.getAllUsers()),{data:r}=(0,b.Ay)(j.DC.adminAllOrganizationUnits(),()=>v.i.getAllOrganizationUnits()),l=null!==(e=null==a?void 0:a.length)&&void 0!==e?e:0,i=null!==(t=null==r?void 0:r.length)&&void 0!==t?t:0;return(0,s.jsx)("div",{className:"flex flex-1 flex-col bg-muted/20 min-h-screen",children:(0,s.jsxs)("div",{className:"@container/main flex flex-1 flex-col gap-4 p-2 sm:p-4 lg:p-6 dark:bg-black/60",children:[(0,s.jsx)("div",{className:"rounded-xl",children:(0,s.jsx)(n,{totalUsers:l,totalOrganizationUnits:i})}),(0,s.jsx)("div",{className:"@container/main flex flex-1 flex-col gap-4 p-2 sm:p-4 lg:p-0 dark:bg-black/60",children:(0,s.jsx)(x,{users:a,organizationUnits:r})}),(0,s.jsx)("div",{className:"@container/main flex flex-1 flex-col gap-4 p-2 sm:p-4 lg:p-0 dark:bg-black/60",children:(0,s.jsx)(p,{})})]})})}},66090:(e,t,a)=>{Promise.resolve().then(a.bind(a,54908))},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>d});var s=a(95155);a(12115);var r=a(36928);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},70449:(e,t,a)=>{"use strict";a.d(t,{DC:()=>i,EJ:()=>l,IS:()=>o,bb:()=>n});var s=a(7283),r=a(15874);function n(){return{fetcher:e=>(0,s.fetchApi)(e),onError:function(e){(0,r.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let l={fetcher:e=>(0,s.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},i={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},o=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},81499:(e,t,a)=>{"use strict";a.d(t,{II:()=>x,Nt:()=>h,at:()=>u});var s=a(95155),r=a(12115),n=a(83540),l=a(94517),i=a(24026),o=a(36928);let c={light:"",dark:".dark"},d=r.createContext(null);function u(e){let{id:t,className:a,children:l,config:i,...c}=e,u=r.useId(),x="chart-".concat(t||u.replace(/:/g,""));return(0,s.jsx)(d.Provider,{value:{config:i},children:(0,s.jsxs)("div",{"data-slot":"chart","data-chart":x,className:(0,o.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",a),...c,children:[(0,s.jsx)(m,{id:x,config:i}),(0,s.jsx)(n.u,{children:l})]})})}let m=e=>{let{id:t,config:a}=e,r=Object.entries(a).filter(e=>{let[,t]=e;return t.theme||t.color});return r.length?(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(c).map(e=>{let[a,s]=e;return"\n".concat(s," [data-chart=").concat(t,"] {\n").concat(r.map(e=>{var t;let[s,r]=e,n=(null===(t=r.theme)||void 0===t?void 0:t[a])||r.color;return n?"  --color-".concat(s,": ").concat(n,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},x=l.m;function h(e){let{active:t,payload:a,className:n,indicator:l="dot",hideLabel:i=!1,hideIndicator:c=!1,label:u,labelFormatter:m,labelClassName:x,formatter:h,color:f,nameKey:p,labelKey:v}=e,{config:j}=function(){let e=r.useContext(d);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),b=r.useMemo(()=>{var e;if(i||!(null==a?void 0:a.length))return null;let[t]=a,r="".concat(v||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),n=g(j,t,r),l=v||"string"!=typeof u?null==n?void 0:n.label:(null===(e=j[u])||void 0===e?void 0:e.label)||u;return m?(0,s.jsx)("div",{className:(0,o.cn)("font-medium",x),children:m(l,a)}):l?(0,s.jsx)("div",{className:(0,o.cn)("font-medium",x),children:l}):null},[u,m,a,i,x,j,v]);if(!t||!(null==a?void 0:a.length))return null;let y=1===a.length&&"dot"!==l;return(0,s.jsxs)("div",{className:(0,o.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",n),children:[y?null:b,(0,s.jsx)("div",{className:"grid gap-1.5",children:a.map((e,t)=>{let a="".concat(p||e.name||e.dataKey||"value"),r=g(j,e,a),n=f||e.payload.fill||e.color;return(0,s.jsx)("div",{className:(0,o.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===l&&"items-center"),children:h&&(null==e?void 0:e.value)!==void 0&&e.name?h(e.value,e.name,e,t,e.payload):(0,s.jsxs)(s.Fragment,{children:[(null==r?void 0:r.icon)?(0,s.jsx)(r.icon,{}):!c&&(0,s.jsx)("div",{className:(0,o.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===l,"w-1":"line"===l,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===l,"my-0.5":y&&"dashed"===l}),style:{"--color-bg":n,"--color-border":n}}),(0,s.jsxs)("div",{className:(0,o.cn)("flex flex-1 justify-between leading-none",y?"items-end":"items-center"),children:[(0,s.jsxs)("div",{className:"grid gap-1.5",children:[y?b:null,(0,s.jsx)("span",{className:"text-muted-foreground",children:(null==r?void 0:r.label)||e.name})]}),e.value&&(0,s.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function g(e,t,a){if("object"!=typeof t||null===t)return;let s="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,r=a;return a in t&&"string"==typeof t[a]?r=t[a]:s&&a in s&&"string"==typeof s[a]&&(r=s[a]),r in e?e[r]:e[a]}i.s}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,1559,8155,4727,8441,1684,7358],()=>t(66090)),_N_E=e.O()}]);