(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8993],{5623:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},11832:(e,t,a)=>{"use strict";a.d(t,{i:()=>c});var s=a(95155),n=a(18289),l=a(47330),r=a(30285),i=a(44838);function c(e){let{table:t}=e;return(0,s.jsxs)(i.rI,{children:[(0,s.jsx)(n.ty,{asChild:!0,children:(0,s.jsxs)(r.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,s.jsx)(l.A,{}),"View"]})}),(0,s.jsxs)(i.SQ,{align:"end",className:"w-[150px]",children:[(0,s.jsx)(i.lp,{children:"Toggle columns"}),(0,s.jsx)(i.mB,{}),t.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,s.jsx)(i.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},15426:(e,t,a)=>{"use strict";function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{dateStyle:a="medium",timeStyle:s="short",fallback:n="N/A",customFormat:l,locale:r=navigator.language||"en-US"}=t;if(!e)return n;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug("Corrected UTC date format: ".concat(e," -> ").concat(a))),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn("Invalid date provided to formatUtcToLocal: ".concat(e)),n;if(l)return function(e,t){let a=e.getFullYear(),s=e.getMonth()+1,n=e.getDate(),l=e.getHours(),r=e.getMinutes(),i={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][s-1],dd:n.toString().padStart(2,"0"),h:(l%12||12).toString(),mm:r.toString().padStart(2,"0"),a:l>=12?"PM":"AM"},c=t;return Object.entries(i).forEach(e=>{let[t,a]=e;c=c.replace(RegExp(t,"g"),a)}),c}(t,l);return new Intl.DateTimeFormat(r,{dateStyle:a,timeStyle:s}).format(t)}catch(t){return console.error("Error formatting date ".concat(e,":"),t),n}}a.d(t,{Ej:()=>s})},24265:(e,t,a)=>{"use strict";a.d(t,{b:()=>d});var s=a(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}a(47650);var l=a(95155),r=Symbol("radix.slottable");function i(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===r}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:a,...l}=e;if(s.isValidElement(a)){var r;let e,i;let c=(r=a,(i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?r.ref:(i=(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?r.props.ref:r.props.ref||r.ref),o=function(e,t){let a={...t};for(let s in t){let n=e[s],l=t[s];/^on[A-Z]/.test(s)?n&&l?a[s]=(...e)=>{l(...e),n(...e)}:n&&(a[s]=n):"style"===s?a[s]={...n,...l}:"className"===s&&(a[s]=[n,l].filter(Boolean).join(" "))}return{...e,...a}}(l,a.props);return a.type!==s.Fragment&&(o.ref=t?function(...e){return t=>{let a=!1,s=e.map(e=>{let s=n(e,t);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let t=0;t<s.length;t++){let a=s[t];"function"==typeof a?a():n(e[t],null)}}}}(t,c):c),s.cloneElement(a,o)}return s.Children.count(a)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=s.forwardRef((e,a)=>{let{children:n,...r}=e,c=s.Children.toArray(n),o=c.find(i);if(o){let e=o.props.children,n=c.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...r,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...r,ref:a,children:n})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),r=s.forwardRef((e,s)=>{let{asChild:n,...r}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(n?a:t,{...r,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),o=s.forwardRef((e,t)=>(0,l.jsx)(c.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var d=o},28830:(e,t,a)=>{Promise.resolve().then(a.bind(a,85691))},47330:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},49103:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>g,Es:()=>x,HM:()=>u,L3:()=>p,c7:()=>h,lG:()=>c,rr:()=>f,zM:()=>o});var s=a(95155),n=a(12115),l=a(59096),r=a(54416),i=a(36928);let c=l.bL,o=l.l9,d=l.ZL,u=l.bm,m=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",a),...n})});m.displayName=l.hJ.displayName;let g=n.forwardRef((e,t)=>{let{className:a,children:n,...c}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(m,{}),(0,s.jsxs)(l.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",a),...c,children:[n,(0,s.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(r.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});g.displayName=l.UC.displayName;let h=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};h.displayName="DialogHeader";let x=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};x.displayName="DialogFooter";let p=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...n})});p.displayName=l.hE.displayName;let f=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...n})});f.displayName=l.VY.displayName},62668:(e,t,a)=>{"use strict";a.d(t,{z:()=>l});var s=a(35695),n=a(12115);function l(){let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),a=(0,n.useCallback)(e=>{let a=new URLSearchParams(t.toString());return Object.entries(e).forEach(e=>{let[t,s]=e;null===s?a.delete(t):a.set(t,s)}),a.toString()},[t]),l=(0,n.useCallback)((t,s)=>{let n=a(s);e.push("".concat(t,"?").concat(n),{scroll:!1})},[a,e]);return{createQueryString:a,updateUrl:l}}},66932:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},74126:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var s=a(95155);a(12115);var n=a(24265),l=a(36928);function r(e){let{className:t,...a}=e;return(0,s.jsx)(n.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},85691:(e,t,a)=>{"use strict";a.d(t,{default:()=>U});var s=a(95155),n=a(91788),l=a(49103),r=a(30285),i=a(47262),c=a(87570),o=a(12115),d=a(5623),u=a(89917),m=a(74126),g=a(54165),h=a(86490),x=a(44838),p=a(13717),f=a(85339);let y=(0,a(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var j=a(54416),v=a(5196),b=a(84616),N=a(62523),w=a(85057);function S(e){let{isOpen:t,onClose:a,mode:n,agent:i,onSuccess:c}=e,[d,u]=(0,o.useState)((null==i?void 0:i.name)||""),[m,x]=(0,o.useState)((null==i?void 0:i.machineName)||""),[S,C]=(0,o.useState)(!1),[k,A]=(0,o.useState)(null),[M,z]=(0,o.useState)(null),[R,I]=(0,o.useState)(!1),[E,F]=(0,o.useState)("");(0,o.useEffect)(()=>{i&&(u(i.name),x(i.machineName))},[i]);let P="edit"===n,D=()=>P?!!(d!==(null==i?void 0:i.name)&&d.trim()||m!==(null==i?void 0:i.machineName)&&m.trim())||(A("Please change at least one field to update."),!1):!!(d.trim()&&m.trim())||(A("Please fill in all required fields"),!1),O=e=>{let t="Failed to create agent. Please try again.";return"object"==typeof e&&null!==e&&("message"in e&&(t=String(e.message)),"status"in e&&(t+=" (Status: ".concat(e.status,")")),"details"in e&&(t+=" - ".concat(e.details)),console.error("Error details:",JSON.stringify(e,null,2))),t},V=async()=>{if(D()){C(!0),A(null);try{let e;if(P){let t=await (0,h.Ri)(i.id);if("Disconnected"!==t.status){F('You can only edit an agent when its status is "Disconnected".'),I(!0),C(!1);return}e=await (0,h.Qk)(i.id,{name:d!==i.name?d:void 0,machineName:m!==i.machineName?m:void 0})}else e=await (0,h.xR)({name:d,machineName:m});z(e),c&&c(e)}catch(e){A(O(e))}finally{C(!1)}}},$=e=>{navigator.clipboard.writeText(e),console.log("Copied to clipboard")},L=()=>{u(""),x(""),A(null),z(null)},T=()=>{L(),a()};return(0,s.jsx)(g.lG,{open:t,onOpenChange:T,children:(0,s.jsxs)(g.Cf,{className:"sm:max-w-[500px] p-0 max-h-[85vh] flex flex-col",onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsxs)(g.c7,{className:"flex items-center gap-2 p-6 pb-2 border-b",children:[P?(0,s.jsx)(p.A,{className:"w-5 h-5 text-primary"}):(0,s.jsx)(l.A,{className:"w-5 h-5 text-primary"}),(0,s.jsx)(g.L3,{className:"text-xl font-bold",children:P?"Edit Agent":"Create a new Agent"})]}),M?(0,s.jsxs)("div",{className:"space-y-4 px-6 py-4",children:[(0,s.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-md text-sm",children:[(0,s.jsx)("p",{className:"font-medium text-green-800 dark:text-green-300 mb-2",children:"Agent created successfully!"}),(0,s.jsx)("p",{className:"text-green-700 dark:text-green-400 mb-4",children:"Please copy the machine key below. It will only be shown once."})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("label",{htmlFor:"machine-key",className:"block text-sm font-medium",children:"Machine Key"}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(N.p,{id:"machine-key",value:M.machineKey,readOnly:!0,className:"flex-1 bg-muted font-mono text-xs"}),(0,s.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>$(M.machineKey),className:"ml-2",children:(0,s.jsx)(y,{className:"h-4 w-4"})})]})]})]}):(0,s.jsxs)("form",{className:"space-y-4 px-6 py-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(w.J,{htmlFor:"name",className:"flex items-center gap-1 mb-2",children:["Name",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(N.p,{id:"name",value:d,onChange:e=>u(e.target.value),disabled:S,className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20 mb-2",autoComplete:"off",spellCheck:"false",onFocus:e=>{setTimeout(()=>{let t=e.target.value.length;e.target.setSelectionRange(t,t)},0)}}),k&&k.includes("Name")&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mt-1",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),k]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(w.J,{htmlFor:"machine-name",className:"flex items-center gap-1 mb-2",children:["Machine name",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(N.p,{id:"machine-name",value:m,onChange:e=>x(e.target.value),disabled:S,className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20 mt-2",autoComplete:"off",spellCheck:"false"}),k&&k.includes("Machine")&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mt-1",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),k]})]}),k&&!k.includes("Name")&&!k.includes("Machine")&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mt-1",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),k]})]}),(0,s.jsxs)(g.Es,{className:"p-6 pt-4 border-t bg-background z-10 flex justify-end gap-2",children:[(0,s.jsxs)(r.$,{variant:"outline",onClick:T,disabled:S,className:"flex items-center gap-1",children:[(0,s.jsx)(j.A,{className:"w-4 h-4"})," ",M?"Close":"Cancel"]}),!M&&(0,s.jsxs)(r.$,{onClick:V,disabled:S,className:"flex items-center gap-1",children:[P?(0,s.jsx)(v.A,{className:"w-4 h-4"}):(0,s.jsx)(b.A,{className:"w-4 h-4"}),P?"Save Changes":"Add Agent"]})]}),(0,s.jsx)(g.lG,{open:R,onOpenChange:I,children:(0,s.jsxs)(g.Cf,{children:[(0,s.jsx)(g.c7,{children:(0,s.jsx)(g.L3,{children:"Error"})}),(0,s.jsx)("div",{children:E}),(0,s.jsx)(g.Es,{children:(0,s.jsx)(r.$,{onClick:()=>I(!1),children:"OK"})})]})})]})})}function C(e){let{row:t,onRefresh:a}=e,[n,l]=(0,o.useState)(!1),[i,c]=(0,o.useState)(!1),[p,f]=(0,o.useState)(""),[y,j]=(0,o.useState)(!1),[v,b]=(0,o.useState)(!1),[N,w]=(0,o.useState)(null),C=async e=>{if(e&&e.stopPropagation(),"Disconnected"!==t.original.status){f('You can only edit an agent when its status is "Disconnected".'),c(!0);return}try{let e=await (0,h.Ri)(t.original.id);w(e),b(!0)}catch(e){f("Failed to fetch agent details."),c(!0)}},k=async()=>{j(!0);try{"Disconnected"===t.original.status?(await (0,h.kz)(t.original.id),l(!1),a&&a()):(l(!1),f('You can only delete an agent when its status is "Disconnected".'),c(!0))}catch(e){l(!1),e instanceof Error?f(e.message):f("Failed to delete agent."),c(!0)}finally{j(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(x.rI,{children:[(0,s.jsx)(x.ty,{asChild:!0,children:(0,s.jsxs)(r.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,s.jsxs)(x.SQ,{align:"start",className:"w-[160px]",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)(x._2,{onClick:C,children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Edit"})]}),(0,s.jsx)(x.mB,{}),(0,s.jsxs)(x._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e&&e.stopPropagation(),l(!0)},children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Delete"})]})]})]}),(0,s.jsx)(S,{isOpen:v,onClose:()=>{b(!1),w(null)},mode:"edit",agent:N,onSuccess:()=>{b(!1),w(null),a&&a()}}),(0,s.jsx)(g.lG,{open:n,onOpenChange:l,children:(0,s.jsxs)(g.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(g.c7,{children:(0,s.jsx)(g.L3,{children:"Confirm Delete"})}),(0,s.jsxs)("div",{children:["Are you sure you want to delete agent ",(0,s.jsx)("b",{children:t.original.name}),"?"]}),(0,s.jsxs)(g.Es,{children:[(0,s.jsx)(r.$,{variant:"outline",onClick:()=>l(!1),disabled:y,children:"Cancel"}),(0,s.jsx)(r.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:k,disabled:y,children:"Delete"})]})]})}),(0,s.jsx)(g.lG,{open:i,onOpenChange:c,children:(0,s.jsxs)(g.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(g.c7,{children:(0,s.jsx)(g.L3,{children:"Error"})}),(0,s.jsx)("div",{children:p}),(0,s.jsx)(g.Es,{children:(0,s.jsx)(r.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:()=>c(!1),children:"OK"})})]})})]})}var k=a(15426);let A=e=>[{id:"select",header:e=>{let{table:t}=e;return(0,s.jsx)(i.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e;return(0,s.jsx)(i.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]"})},enableSorting:!1,enableHiding:!1},{id:"actions",header:e=>{let{column:t}=e;return(0,s.jsx)(c.w,{column:t,title:"Actions"})},cell:t=>{let{row:a}=t;return(0,s.jsx)(C,{row:a,onRefresh:e})}},{accessorKey:"name",header:e=>{let{column:t}=e;return(0,s.jsx)(c.w,{column:t,title:"Name"})},cell:e=>{let{row:t}=e;return(0,s.jsx)("div",{className:"flex space-x-2",children:(0,s.jsx)("span",{className:"max-w-[500px] truncate font-medium",children:t.getValue("name")})})}},{accessorKey:"machineName",header:e=>{let{column:t}=e;return(0,s.jsx)(c.w,{column:t,title:"Machine Name"})},cell:e=>{let{row:t}=e;return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:t.getValue("machineName")})})}},{accessorKey:"status",header:e=>{let{column:t}=e;return(0,s.jsx)(c.w,{column:t,title:"Status"})},cell:e=>{let{row:t}=e,a=String(t.getValue("status")),n="";switch(a){case"Connected":case"Available":n="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";break;case"Busy":n="bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400";break;case"Disconnected":case"Offline":n="bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";break;default:n="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400"}return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(n),children:a})})}},{accessorKey:"lastConnected",header:e=>{let{column:t}=e;return(0,s.jsx)(c.w,{column:t,title:"Last Connected"})},cell:e=>{let{row:t}=e,a=t.getValue("lastConnected"),n="string"==typeof a?a:a?String(a):null,l=(0,k.Ej)(n,{fallback:"Never"});return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:l})})}}];var M=a(54333),z=a(55594),R=a(35695),I=a(47924),E=a(51154),F=a(66932),P=a(11832),D=a(59409),O=a(26126);function V(e){var t,a;let{table:n,statuses:l,onSearch:i,onStatusChange:c,searchValue:d="",isFiltering:u=!1,isPending:m=!1}=e,g=n.getState().columnFilters.length>0,h=n.getState().columnFilters.length,x=(0,o.useRef)(null),p=(0,o.useRef)(null);(0,o.useEffect)(()=>{document.activeElement!==x.current&&null!==p.current&&x.current&&(x.current.focus(),null!==p.current&&x.current.setSelectionRange(p.current,p.current))},[m,u]);let f=e=>{if(x.current&&(p.current=x.current.selectionStart),i)i(e);else{var t;null===(t=n.getColumn("name"))||void 0===t||t.setFilterValue(e)}};return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,s.jsx)(I.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(N.p,{ref:x,placeholder:"Search by Name or Machine Name...",value:d,onChange:e=>f(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:u,onFocus:()=>{x.current&&(p.current=x.current.selectionStart)}}),u&&(0,s.jsx)(E.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!u&&""!==d&&(0,s.jsx)(j.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>f("")})]}),n.getColumn("status")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(D.l6,{onValueChange:e=>{if(c)c(e);else{var t;null===(t=n.getColumn("status"))||void 0===t||t.setFilterValue("all"===e?"":e)}},value:(null===(t=n.getColumn("status"))||void 0===t?void 0:t.getFilterValue())||"all",disabled:u||m,children:[(0,s.jsx)(D.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(F.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(D.yv,{placeholder:"Filter status"}),(null===(a=n.getColumn("status"))||void 0===a?void 0:a.getFilterValue())&&(0,s.jsx)(O.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(D.gC,{children:[(0,s.jsx)(D.eb,{value:"all",children:"All Statuses"}),l.map(e=>(0,s.jsx)(D.eb,{value:e.value,children:e.label},e.value))]})]})}),h>0&&(0,s.jsxs)(O.E,{variant:"secondary",className:"rounded-sm px-1",children:[h," active ",1===h?"filter":"filters"]}),g&&(0,s.jsxs)(r.$,{variant:"ghost",onClick:()=>{n.resetColumnFilters(),i&&i("")},className:"h-8 px-2 lg:px-3",disabled:u,children:["Reset",(0,s.jsx)(j.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(P.i,{table:n})]})}var $=a(36268),L=a(11032),T=a(62668),_=a(29797),H=a(75420),J=a(34953),K=a(70449),B=a(88262);function U(){var e;let t=(0,R.useRouter)(),a=(0,R.usePathname)(),i=(0,R.useSearchParams)(),{updateUrl:c}=(0,T.z)(),{toast:d}=(0,B.d)(),[u,m]=(0,o.useState)(!1),[g,x]=(0,o.useState)("create"),[p,f]=(0,o.useState)({}),[y,j]=(0,o.useState)({}),[v,b]=(0,o.useState)(0),N=(0,o.useRef)(0),[w,C]=(0,o.useState)(!1),[k,z]=(0,o.useState)(!1),[I,E]=(0,o.useState)(!1),F=(0,o.useRef)(null),P=(0,o.useRef)(!0),[D,O]=(0,o.useState)(()=>{let e=[],t=i.get("name");t&&e.push({id:"name",value:t});let a=i.get("status");return a&&e.push({id:"status",value:a}),e}),[U,W]=(0,o.useState)(()=>{let e=i.get("sort"),t=i.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[q,Y]=(0,o.useState)(()=>{let e=i.get("page"),t=i.get("size");return{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:t?parseInt(t):10}}),[G,Q]=(0,o.useState)(null!==(e=i.get("name"))&&void 0!==e?e:""),Z=a.split("/")[1],X=(0,o.useMemo)(()=>{let e={$top:q.pageSize,$skip:q.pageIndex*q.pageSize,$count:!0};if(U.length>0&&(e.$orderby=U.map(e=>"".concat(e.id," ").concat(e.desc?"desc":"asc")).join(",")),D.length>0){let t=D.filter(e=>"string"!=typeof e.value||""!==e.value).map(e=>{let t=e.id,a=e.value;return"string"==typeof a?"name"===t&&a?"(contains(tolower(name), '".concat(a.toLowerCase(),"') or contains(tolower(machineName), '").concat(a.toLowerCase(),"'))"):"contains(tolower(".concat(t,"), '").concat(a.toLowerCase(),"')"):Array.isArray(a)?a.map(e=>"".concat(t," eq '").concat(e,"'")).join(" or "):""}).filter(Boolean);t.length>0&&(e.$filter=t.join(" and "))}return e},[q,U,D]),{data:ee,error:et,isLoading:ea,mutate:es}=(0,J.Ay)(K.DC.agentsWithOData(X),()=>(0,h.dT)(X)),en=(0,o.useMemo)(()=>(null==ee?void 0:ee.value)?ee.value.map(e=>({...e,botAgentId:e.id})):[],[ee]);(0,o.useEffect)(()=>{et&&(console.error("Failed to load agents:",et),d({title:"Error",description:"Failed to load agents. Please try again.",variant:"destructive"}))},[et,d]),(0,o.useEffect)(()=>{if(!ee)return;if("number"==typeof ee["@odata.count"]){b(ee["@odata.count"]),N.current=ee["@odata.count"],E(!0);return}if(!Array.isArray(ee.value))return;let e=q.pageIndex*q.pageSize+ee.value.length;e>N.current&&(b(e),N.current=e),ee.value.length===q.pageSize&&0===q.pageIndex&&(b(e+1),N.current=e+1),E(!1)},[ee,q.pageIndex,q.pageSize]),(0,o.useEffect)(()=>{if((null==ee?void 0:ee.value)&&0===ee.value.length&&N.current>0&&q.pageIndex>0){let e=Math.max(1,Math.ceil(N.current/q.pageSize));q.pageIndex>=e&&(Y(e=>({...e,pageIndex:0})),c(a,{page:"1"}))}},[ee,q.pageIndex,q.pageSize,N,c,a]);let el=(0,o.useCallback)(async()=>{C(!1),z(!1),await es()},[es]),er=(0,H.d)(Z);(0,o.useEffect)(()=>{let e=console.error;return console.error=function(){for(var t=arguments.length,a=Array(t),s=0;s<t;s++)a[s]=arguments[s];if(a.length>0&&"string"==typeof a[0]&&(a[0].includes("SignalR")||a[0].includes("connection")||a[0].includes("Connection")||a[0].includes("Failed to start"))){console.debug("[Suppressed]",...a);return}e(...a)},()=>{console.error=e}},[]),(0,o.useEffect)(()=>{if(P.current){P.current=!1;let e=i.get("page"),t=i.get("size");e&&t||c(a,{page:null!=e?e:"1",size:null!=t?t:"10"})}},[i,c,a]);let ei=e=>e+1,ec=(e,t)=>Math.max(1,Math.ceil(e/t)),eo=(0,o.useMemo)(()=>{let e=ec(v,q.pageSize),t=en.length===q.pageSize&&v<=q.pageSize*(q.pageIndex+1),a=ei(q.pageIndex);return t?Math.max(a,e,q.pageIndex+2):Math.max(a,e)},[q.pageSize,q.pageIndex,en.length,v]),ed=(0,o.useMemo)(()=>en.map(e=>{let t=er[e.botAgentId];return t&&console.debug("Merging real-time status for",e.botAgentId,t.status),t?{...e,status:t.status}:e}),[en,er]),eu=(0,o.useMemo)(()=>!I&&en.length===q.pageSize,[I,en.length,q.pageSize]),em=(0,$.N4)({data:ed,columns:A(el),state:{sorting:U,columnVisibility:y,rowSelection:p,columnFilters:D,pagination:q},enableRowSelection:!0,onRowSelectionChange:f,onSortingChange:e=>{let t="function"==typeof e?e(U):e;W(t),t.length>0?c(a,{sort:t[0].id,order:t[0].desc?"desc":"asc",page:"1"}):c(a,{sort:null,order:null,page:"1"})},onColumnFiltersChange:O,onColumnVisibilityChange:j,onPaginationChange:e=>{let t="function"==typeof e?e(q):e;Y(t),c(a,{page:(t.pageIndex+1).toString(),size:t.pageSize.toString()})},getCoreRowModel:(0,L.HT)(),getFilteredRowModel:(0,L.hM)(),getPaginationRowModel:(0,L.kW)(),getSortedRowModel:(0,L.h5)(),getFacetedRowModel:(0,L.kQ)(),getFacetedUniqueValues:(0,L.oS)(),manualPagination:!0,pageCount:eo,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.botAgentId}),eg=(0,o.useCallback)(e=>{Q(e),C(!0),F.current&&clearTimeout(F.current),F.current=setTimeout(()=>{let t=em.getColumn("name");t&&(t.setFilterValue(e),c(a,{name:e||null,page:"1"})),C(!1)},500)},[em,c,a]),eh=(0,o.useCallback)(e=>{let t=em.getColumn("status");if(t){let s="all"===e?"":e;t.setFilterValue(s),c(a,{status:s||null,page:"1"})}},[em,c,a]);return(0,o.useEffect)(()=>()=>{F.current&&clearTimeout(F.current)},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Agents"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[v>0&&(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,s.jsxs)("span",{children:["Total: ",v," agent",1!==v?"s":""]})}),(0,s.jsxs)(r.$,{variant:"outline",onClick:()=>{window.location.href="https://openautomate-agent.s3.ap-southeast-1.amazonaws.com/OpenAutomate.BotAgent.Installer.msi"},className:"flex items-center justify-center",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Download Agent"]}),(0,s.jsxs)(r.$,{onClick:()=>{x("create"),m(!0)},className:"flex items-center justify-center",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Create"]})]})]}),et&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,s.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load agents. Please try again."}),(0,s.jsx)(r.$,{variant:"outline",className:"mt-2",onClick:()=>es(),children:"Retry"})]}),(0,s.jsx)(V,{table:em,statuses:[{value:"Available",label:"Available"},{value:"Busy",label:"Busy"},{value:"Disconnected",label:"Disconnected"}],onSearch:eg,onStatusChange:eh,searchValue:G,isFiltering:ea,isPending:w}),(0,s.jsx)(M.b,{data:ed,columns:A(el),table:em,onRowClick:e=>{let s=a.startsWith("/admin")?"/admin/agent/".concat(e.id):"/".concat(Z,"/agent/").concat(e.id);t.push(s)},isLoading:ea,totalCount:v}),(0,s.jsx)(_.d,{currentPage:q.pageIndex+1,pageSize:q.pageSize,totalCount:v,totalPages:eo,isLoading:ea,isChangingPageSize:k,isUnknownTotalCount:eu,onPageChange:e=>{Y({...q,pageIndex:e-1}),c(a,{page:e.toString()})},onPageSizeChange:e=>{z(!0);let t=Math.floor(q.pageIndex*q.pageSize/e);Y({pageSize:e,pageIndex:t}),c(a,{size:e.toString(),page:(t+1).toString()})}})]}),(0,s.jsx)(S,{isOpen:u,onClose:()=>m(!1),mode:g,onSuccess:()=>es()})]})}z.z.object({id:z.z.string(),botAgentId:z.z.string(),name:z.z.string(),machineName:z.z.string(),status:z.z.string(),lastConnected:z.z.string()})},89917:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,2178,8852,5699,5594,8523,9483,3085,3701,4727,5224,4046,8441,1684,7358],()=>t(28830)),_N_E=e.O()}]);