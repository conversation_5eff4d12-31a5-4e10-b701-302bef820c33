(()=>{var e={};e.id=1896,e.ids=[1896],e.modules={1303:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},23749:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(12907);(0,a.registerClientReference)(function(){throw Error("Attempted to call assetSchema() from the server but assetSchema is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\asset\\asset.tsx","assetSchema");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\asset\\\\asset.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\asset\\asset.tsx","default")},26075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>i});var a=r(65239),s=r(48088),o=r(31369),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let i={children:["",{children:["[tenant]",{children:["asset",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84756)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\asset\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\asset\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[tenant]/asset/page",pathname:"/[tenant]/asset",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},27964:(e,t,r)=>{Promise.resolve().then(r.bind(r,64706)),Promise.resolve().then(r.bind(r,81682))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31599:(e,t,r)=>{"use strict";r.d(t,{c:()=>i});var a=r(43210),s=r(39989),o=r(16189),n=r(31207),l=r(70891);function i(){let e=(0,o.useRouter)(),{data:t,error:r,isLoading:i,mutate:d}=(0,n.Ay)(l.DC.organizationUnits(),()=>s.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:i,error:r?"Failed to fetch organization units. Please try again later.":null,refresh:d,selectOrganizationUnit:(0,a.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},39989:(e,t,r)=>{"use strict";r.d(t,{K:()=>s});var a=r(51787);let s={getMyOrganizationUnits:async()=>await a.F.get("/api/ou/my-ous"),getBySlug:async e=>await a.F.get(`/api/ou/slug/${e}`),getById:async e=>await a.F.get(`/api/ou/${e}`),create:async e=>await a.F.post("/api/ou/create",e),update:async(e,t)=>await a.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await a.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await a.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await a.F.get(`/api/ou/${e}/deletion-status`)}},42300:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var a=r(16189),s=r(43210);function o(){let e=(0,a.useRouter)(),t=(0,a.useSearchParams)(),r=(0,s.useCallback)(e=>{let r=new URLSearchParams(t.toString());return Object.entries(e).forEach(([e,t])=>{null===t?r.delete(e):r.set(e,t)}),r.toString()},[t]),o=(0,s.useCallback)((t,a)=>{let s=r(a);e.push(`${t}?${s}`,{scroll:!1})},[r,e]);return{createQueryString:r,updateUrl:o}}},45996:(e,t,r)=>{"use strict";r.d(t,{PermissionRouteGuard:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call PermissionRouteGuard() from the server but PermissionRouteGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\permission-route-guard.tsx","PermissionRouteGuard")},53984:(e,t,r)=>{"use strict";r.d(t,{i:()=>i});var a=r(60687),s=r(4654),o=r(56476),n=r(29523),l=r(21342);function i({table:e}){return(0,a.jsxs)(l.rI,{children:[(0,a.jsx)(s.ty,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,a.jsx)(o.A,{}),"View"]})}),(0,a.jsxs)(l.SQ,{align:"end",className:"w-[150px]",children:[(0,a.jsx)(l.lp,{children:"Toggle columns"}),(0,a.jsx)(l.mB,{}),e.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,a.jsx)(l.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},57175:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},61018:(e,t,r)=>{"use strict";r.d(t,{TenantGuard:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},61170:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var a=r(43210);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var o=r(60687),n=Symbol("radix.slottable");function l(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===n}var i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...o}=e;if(a.isValidElement(r)){var n;let e,l;let i=(n=r,(l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(l=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),d=function(e,t){let r={...t};for(let a in t){let s=e[a],o=t[a];/^on[A-Z]/.test(a)?s&&o?r[a]=(...e)=>{o(...e),s(...e)}:s&&(r[a]=s):"style"===a?r[a]={...s,...o}:"className"===a&&(r[a]=[s,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==a.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}(t,i):i),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...n}=e,i=a.Children.toArray(s),d=i.find(l);if(d){let e=d.props.children,s=i.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...n,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,o.jsx)(t,{...n,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(s?r:t,{...n,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),d=a.forwardRef((e,t)=>(0,o.jsx)(i.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>p,Es:()=>f,HM:()=>u,L3:()=>h,c7:()=>g,lG:()=>i,rr:()=>x,zM:()=>d});var a=r(60687),s=r(43210),o=r(88562),n=r(11860),l=r(36966);let i=o.bL,d=o.l9,c=o.ZL,u=o.bm,m=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.hJ,{ref:r,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));m.displayName=o.hJ.displayName;let p=s.forwardRef(({className:e,children:t,...r},s)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(o.UC,{ref:s,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...r,children:[t,(0,a.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=o.UC.displayName;let g=({className:e,...t})=>(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});g.displayName="DialogHeader";let f=({className:e,...t})=>(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});f.displayName="DialogFooter";let h=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.hE,{ref:r,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=o.hE.displayName;let x=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.VY,{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=o.VY.displayName},64656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(37413),s=r(48974),o=r(31057),n=r(50417),l=r(92588),i=r(61018),d=r(2505);function c({children:e}){return(0,a.jsx)(i.TenantGuard,{children:(0,a.jsx)(d.ChatProvider,{children:(0,a.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,a.jsxs)(n.SidebarProvider,{className:"flex flex-col",children:[(0,a.jsx)(o.SiteHeader,{}),(0,a.jsxs)("div",{className:"flex flex-1",children:[(0,a.jsx)(s.AppSidebar,{}),(0,a.jsx)(n.SidebarInset,{children:(0,a.jsx)(l.SearchProvider,{children:(0,a.jsx)("main",{className:"",children:e})})})]})]})})})})}},64706:(e,t,r)=>{"use strict";r.d(t,{default:()=>W});var a=r(60687),s=r(43210),o=r(1303),n=r(29523),l=r(56896),i=r(34208),d=r(93661),c=r(57175),u=r(96362),m=r(11860),p=r(41862),g=r(21342),f=r(63503);function h({asset:e,onEdit:t,onDeleted:o}){let[l,i]=(0,s.useState)(!1),[h,x]=(0,s.useState)(!1),b=async()=>{x(!0);try{let{deleteAsset:t}=await Promise.resolve().then(r.bind(r,71769));await t(e.id),i(!1),o&&o()}catch{alert("Delete failed!")}finally{x(!1)}},y=e=>{e.stopPropagation()};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(g.rI,{children:[(0,a.jsx)(g.ty,{asChild:!0,onClick:y,children:(0,a.jsx)(n.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted","aria-label":"Open menu",onClick:y,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(g.SQ,{align:"start",className:"w-[160px]",onClick:y,onPointerDown:y,onMouseDown:y,children:[(0,a.jsxs)(g._2,{onClick:r=>{r.stopPropagation(),t&&t(e)},children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,a.jsx)("span",{children:"Edit"})]}),(0,a.jsxs)(g._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e.stopPropagation(),i(!0)},children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,a.jsx)("span",{children:"Delete"})]})]})]}),(0,a.jsx)(f.lG,{open:l,onOpenChange:i,children:(0,a.jsxs)(f.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,a.jsxs)(f.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,a.jsx)(f.c7,{children:(0,a.jsx)(f.L3,{children:"Confirm Delete"})}),(0,a.jsxs)("div",{children:["Are you sure you want to delete this asset ",(0,a.jsx)("b",{children:e.key}),"?"]}),(0,a.jsxs)(f.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,a.jsx)(n.$,{variant:"outline",onClick:e=>{e.stopPropagation(),i(!1)},disabled:h,children:"Cancel"}),(0,a.jsxs)(n.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:e=>{e.stopPropagation(),b()},disabled:h,children:[h&&(0,a.jsx)(p.A,{className:"animate-spin w-4 h-4 mr-2"}),h?"Deleting...":"Delete"]})]})]})})]})}let x=(e,t)=>[{id:"select",header:({table:e})=>(0,a.jsx)(l.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>{let t=e=>{e.stopPropagation()};return(0,a.jsx)("span",{onClick:t,onMouseDown:t,onPointerDown:t,children:(0,a.jsx)(l.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]",onClick:t})})},enableSorting:!1,enableHiding:!1},{id:"actions",header:({column:e})=>(0,a.jsx)(i.w,{column:e,title:"Actions"}),cell:({row:r})=>(0,a.jsx)(h,{asset:r.original,onEdit:e,onDeleted:t})},{accessorKey:"key",header:({column:e})=>(0,a.jsx)(i.w,{column:e,title:"Key",className:"font-bold text-base"}),cell:({row:e})=>(0,a.jsx)("span",{className:"font-medium truncate",children:e.getValue("key")}),filterFn:(e,t,r)=>{let a=e.getValue(t);return"string"==typeof a&&"string"==typeof r&&a.toLowerCase().includes(r.toLowerCase())}},{accessorKey:"type",header:({column:e})=>(0,a.jsx)(i.w,{column:e,title:"Type",className:"font-bold text-base"}),cell:({row:e})=>{let t=e.getValue("type");return(0,a.jsx)("span",{children:0===t||"0"===t?"String":"Secret"})},filterFn:(e,t,r)=>String(e.getValue(t))===String(r)},{accessorKey:"description",header:({column:e})=>(0,a.jsx)(i.w,{column:e,title:"Description",className:"font-bold text-base"}),cell:({row:e})=>{let t=e.getValue("description");return(0,a.jsx)("span",{className:"string"==typeof t&&t.trim()?"":"text-muted-foreground italic",children:"string"==typeof t&&t.trim()?t:"N/a"})}}];x();var b=r(50723),y=r(89667),v=r(80013),w=r(15079),k=r(16189),j=r(71769),N=r(35674),C=r(63143),S=r(19959),z=r(93613),A=r(10022),P=r(58869),$=r(96474),F=r(13964);function R({isOpen:e,onClose:t,mode:r,onCreated:l,existingKeys:i=[],asset:d}){let c;let[p,g]=(0,s.useState)(""),[h,x]=(0,s.useState)(""),[b,R]=(0,s.useState)(0),[M,I]=(0,s.useState)(""),[E,G]=(0,s.useState)(!1);(0,k.useParams)().tenant;let[O,D]=(0,s.useState)([]),[V,q]=(0,s.useState)(""),[T,_]=(0,s.useState)([]),[L,U]=(0,s.useState)(null),[K,W]=(0,s.useState)(null),[H,J]=(0,s.useState)(null),[B,Q]=(0,s.useState)(null),Y="edit"===r,Z=(0,s.useRef)(null),X=(0,s.useRef)(null),ee=(0,s.useRef)(null),et=e=>{setTimeout(()=>{e.target.selectionStart=e.target.selectionEnd=e.target.value.length},0)},er=()=>{let e=!0;return W(null),J(null),Q(null),U(null),h.trim()?/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/.test(h)?(W("Key must not contain Vietnamese characters or accents."),e=!1):!Y&&i.includes(h.trim())&&(W("Key already exists. Please choose a unique key."),e=!1):(W("Key is required."),e=!1),Y||b.toString().trim()||(J("Type is required."),e=!1),p.trim()||(Q("Value is required."),e=!1),e},ea=async()=>{if(er()){G(!0);try{let e=T.map(e=>e.id);Y&&d?.id?(await (0,j.gT)(d.id,{key:h,description:M,value:p},e),N.m.handleSuccess("updated",`Asset "${h}"`)):(await (0,j.$o)({key:h,description:M,value:p,type:Number(b),botAgentIds:e}),N.m.handleSuccess("created",`Asset "${h}"`)),es(),t(),l&&l()}catch(e){N.m.handleError(e,Y?"Updating asset":"Creating asset")}finally{G(!1)}}},es=()=>{g(""),x(""),R(0),I(""),q(""),_([]),W(null),J(null),Q(null),U(null)},eo=()=>{es(),t()},en=e=>{_(T.filter(t=>t.id!==e))},el="Add Item";return E?el="Submitting...":Y&&(el="Save Changes"),c=Y?d?.type===1?"password":"text":1===b?"password":"text",(0,a.jsx)(f.lG,{open:e,onOpenChange:eo,children:(0,a.jsxs)(f.Cf,{className:"sm:max-w-[800px] p-0 max-h-[85vh] flex flex-col",onInteractOutside:e=>e.preventDefault(),children:[(0,a.jsxs)(f.c7,{className:"flex items-center gap-2 p-6 pb-2 border-b",children:[Y?(0,a.jsx)(C.A,{className:"w-5 h-5 text-primary"}):(0,a.jsx)(o.A,{className:"w-5 h-5 text-primary"}),(0,a.jsx)(f.L3,{className:"text-xl font-bold",children:Y?"Edit Asset":"Create a new Asset"})]}),(0,a.jsxs)("div",{className:"px-6 py-4 flex-1 overflow-y-auto",children:[(0,a.jsxs)("form",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(v.J,{htmlFor:"key",className:"text-sm font-medium flex items-center gap-1",children:[(0,a.jsx)(S.A,{className:"w-4 h-4 text-muted-foreground"}),"Key",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(y.p,{id:"key",ref:Z,value:h,onChange:e=>{x(e.target.value),W(null)},className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20",autoComplete:"off",onFocus:et,spellCheck:"false"}),K&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mb-2",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),K]}),(0,a.jsxs)(v.J,{htmlFor:"description",className:"text-sm font-medium flex items-center gap-1",children:[(0,a.jsx)(A.A,{className:"w-4 h-4 text-muted-foreground"}),"Description"]}),(0,a.jsx)(y.p,{id:"description",ref:ee,value:M,onChange:e=>I(e.target.value),placeholder:"Enter description (optional)",className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20",autoComplete:"off",onFocus:et,spellCheck:"false"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(v.J,{htmlFor:"type",className:"text-sm font-medium flex items-center gap-1",children:[(0,a.jsx)(A.A,{className:"w-4 h-4 text-muted-foreground"}),"Type",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),Y?(0,a.jsxs)("div",{className:"flex items-center border rounded-xl px-3 py-2 text-sm bg-muted",children:[0===b?"String":"Secret"," ",(0,a.jsx)("span",{className:"text-muted-foreground ml-2 text-xs",children:"(Cannot be changed)"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(w.l6,{value:b.toString(),onValueChange:e=>{R(Number(e)),J(null)},children:[(0,a.jsx)(w.bq,{children:(0,a.jsx)(w.yv,{placeholder:"Select type"})}),(0,a.jsxs)(w.gC,{children:[(0,a.jsx)(w.eb,{value:"0",children:"String"}),(0,a.jsx)(w.eb,{value:"1",children:"Secret"})]})]}),H&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mb-2",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),H]})]}),(0,a.jsxs)(v.J,{htmlFor:"value",className:"text-sm font-medium flex items-center gap-1",children:[(0,a.jsx)(A.A,{className:"w-4 h-4 text-muted-foreground"}),"Value",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(y.p,{id:"value",ref:X,value:p,onChange:e=>{g(e.target.value),Q(null)},placeholder:"Type a string value",type:c,className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20",autoComplete:"off",onFocus:et,spellCheck:"false"}),B&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mb-2",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),B]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)(v.J,{htmlFor:"agent",className:"text-sm font-medium flex items-center gap-1",children:[(0,a.jsx)(P.A,{className:"w-4 h-4 text-muted-foreground"}),"Agent"]}),(0,a.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,a.jsxs)(w.l6,{value:V,onValueChange:e=>{q(e),U(null)},children:[(0,a.jsx)(w.bq,{children:(0,a.jsx)(w.yv,{placeholder:"Select Agent..."})}),(0,a.jsx)(w.gC,{children:O.map(e=>(0,a.jsx)(w.eb,{value:e.id,children:e.name},e.id))})]}),(0,a.jsxs)(n.$,{type:"button",onClick:()=>{if(!V)return;let e=O.find(e=>e.id===V);if(e){if(T.some(t=>t.id===e.id)){U("Agent already added.");return}_([...T,e]),q(""),U(null)}},disabled:!V,variant:V?"default":"secondary",className:"flex items-center gap-1 rounded-md px-4 py-2 transition-colors",children:[(0,a.jsx)($.A,{className:"w-4 h-4"})," Add"]})]}),L&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mt-1",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),L]}),T.length>0&&(0,a.jsx)("div",{className:"overflow-x-auto mt-4",children:(0,a.jsxs)("table",{className:"min-w-[300px] w-full text-sm rounded-xl overflow-hidden border bg-white dark:bg-neutral-900",children:[(0,a.jsx)("thead",{className:"bg-muted",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"border px-3 py-2 text-left font-semibold text-black dark:text-white",children:"#"}),(0,a.jsx)("th",{className:"border px-3 py-2 text-left font-semibold text-black dark:text-white",children:"Agent Name"}),(0,a.jsx)("th",{className:"border px-3 py-2 text-left font-semibold text-black dark:text-white",children:"Action"})]})}),(0,a.jsx)("tbody",{children:T.map((e,t)=>(0,a.jsxs)("tr",{className:"hover:bg-accent/30 transition",children:[(0,a.jsx)("td",{className:"border px-3 py-2 text-black dark:text-neutral-100",children:t+1}),(0,a.jsx)("td",{className:"border px-3 py-2 text-black dark:text-neutral-100",children:e.name}),(0,a.jsx)("td",{className:"border px-3 py-2",children:(0,a.jsx)(n.$,{size:"icon",variant:"ghost",onClick:()=>en(e.id),className:"text-red-400 hover:text-red-600",children:(0,a.jsx)(u.A,{className:"w-4 h-4"})})})]},e.id))})]})})]})]}),(0,a.jsxs)(f.Es,{className:"p-6 pt-4 border-t bg-background z-10 flex justify-end gap-2",children:[(0,a.jsxs)(n.$,{variant:"outline",onClick:eo,disabled:E,className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"})," Cancel"]}),(0,a.jsxs)(n.$,{onClick:ea,disabled:E,className:"flex items-center gap-1",children:[Y?(0,a.jsx)(F.A,{className:"w-4 h-4"}):(0,a.jsx)($.A,{className:"w-4 h-4"}),el]})]})]})})}var M=r(45880),I=r(99270),E=r(80462),G=r(53984),O=r(96834);function D({table:e,types:t,onSearch:r,onTypeChange:o,searchValue:l="",isFiltering:i=!1,isPending:d=!1}){let c=e.getState().columnFilters.length>0,u=e.getState().columnFilters.length,g=(0,s.useRef)(null),f=(0,s.useRef)(null),h=t=>{g.current&&(f.current=g.current.selectionStart),r?r(t):e.getColumn("key")?.setFilterValue(t)},x=(0,s.useMemo)(()=>{let t=e.getState().columnFilters.find(e=>"type"===e.id);return t?t.value:"all"},[e]);return(0,a.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,a.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,a.jsx)(I.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(y.p,{ref:g,placeholder:"Search by key...",value:l,onChange:e=>h(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:i,onFocus:()=>{g.current&&(f.current=g.current.selectionStart)}}),i&&(0,a.jsx)(p.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!i&&""!==l&&(0,a.jsx)(m.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>h("")})]}),(0,a.jsx)("div",{className:"flex items-center space-x-1",children:(0,a.jsxs)(w.l6,{onValueChange:t=>{o?o(t):"all"===t?e.getColumn("type")?.setFilterValue(void 0):e.getColumn("type")?.setFilterValue(t)},value:x,disabled:i||d,children:[(0,a.jsx)(w.bq,{className:"h-10 sm:w-[180px]",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(E.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)(w.yv,{placeholder:"Filter type"}),"all"!==x&&(0,a.jsx)(O.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,a.jsxs)(w.gC,{children:[(0,a.jsx)(w.eb,{value:"all",children:"All Types"}),t.map(e=>(0,a.jsx)(w.eb,{value:e.value,children:e.label},e.value))]})]})}),u>0&&(0,a.jsxs)(O.E,{variant:"secondary",className:"rounded-sm px-1",children:[u," active ",1===u?"filter":"filters"]}),c&&(0,a.jsxs)(n.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),r&&r("")},className:"h-8 px-2 lg:px-3",disabled:i,children:["Reset",(0,a.jsx)(m.A,{className:"ml-2 h-4 w-4"})]})]}),(0,a.jsx)(G.i,{table:e})]})}var V=r(56090),q=r(93772),T=r(42300),_=r(14583),L=r(31207),U=r(70891),K=r(20140);function W(){let e=(0,k.useRouter)(),t=(0,k.usePathname)(),r=(0,k.useSearchParams)(),{updateUrl:l}=(0,T.z)(),{toast:i}=(0,K.d)(),[d,c]=(0,s.useState)(!1),[u,m]=(0,s.useState)("create"),[p,g]=(0,s.useState)({}),[f,h]=(0,s.useState)({}),[y,v]=(0,s.useState)(0);(0,s.useRef)(0);let[w,N]=(0,s.useState)(!1),[C,S]=(0,s.useState)(!1),z=(0,s.useRef)(null),[A,P]=(0,s.useState)(!1),$=(e,t)=>Math.max(1,Math.ceil(e/t)),F=e=>e+1,[M,I]=(0,s.useState)(r.get("key")??""),[E,G]=(0,s.useState)(()=>{let e=[],t=r.get("key");t&&e.push({id:"key",value:t});let a=r.get("type");return a&&e.push({id:"type",value:a}),e}),[O,W]=(0,s.useState)(()=>{let e=r.get("sort"),t=r.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[H,J]=(0,s.useState)(()=>{let e=r.get("page"),t=r.get("size");return{pageIndex:e?Math.max(0,parseInt(e,10)-1):0,pageSize:t?parseInt(t,10):10}}),B=(0,s.useCallback)(()=>{let e={$count:!0,$top:H.pageSize,$skip:H.pageIndex*H.pageSize};O.length>0&&(e.$orderby=`${O[0].id} ${O[0].desc?"desc":"asc"}`);let t=[],r=E.find(e=>"key"===e.id);r?.value&&t.push(`contains(tolower(key), '${r.value.toLowerCase()}')`);let a=E.find(e=>"type"===e.id),s={0:"String",1:"Secret"},o=a?.value;return o&&s[o]&&t.push(`type eq '${s[o]}'`),t.length>0&&(e.$filter=t.join(" and ")),e},[H,O,E])(),{data:Q,error:Y,isLoading:Z,mutate:X}=(0,L.Ay)(U.DC.assetsWithOData(B),()=>(0,j.Lm)(B)),ee=(0,s.useMemo)(()=>Q?.value?Q.value.map(e=>({id:e.id,key:e.key,type:e.type,description:e.description,createdBy:e.createdBy})):[],[Q]),et=(0,s.useMemo)(()=>{let e=$(y,H.pageSize),t=ee.length===H.pageSize&&y<=H.pageSize*(H.pageIndex+1),r=F(H.pageIndex);return t?Math.max(r,e,H.pageIndex+2):Math.max(r,e)},[H.pageSize,H.pageIndex,ee.length,y]),er=(0,s.useMemo)(()=>!A&&ee.length===H.pageSize,[A,ee.length,H.pageSize]),[ea,es]=(0,s.useState)(null),eo=(0,s.useCallback)(async e=>{try{let t=await (0,j.qi)(e.id),r=(await (0,j.j0)(e.id)).map(e=>({id:e.id,name:e.name})),a={...e,type:"number"==typeof e.type?e.type:Number(e.type)||0,value:t.value??"",agents:r.length>0?r:[]};es(a),m("edit"),c(!0)}catch(e){console.error("Error preparing asset for edit:",e),i({title:"Error",description:"Failed to load asset details for editing.",variant:"destructive"})}},[i]),en=(0,s.useCallback)(async()=>{N(!1),S(!1),await X()},[X]),el=(0,s.useMemo)(()=>x(eo,en),[eo,en]),ei=(0,V.N4)({data:ee,columns:el,state:{sorting:O,columnVisibility:f,rowSelection:p,columnFilters:E,pagination:H},enableRowSelection:!0,onRowSelectionChange:g,onSortingChange:e=>{let r="function"==typeof e?e(O):e;W(r),r.length>0?l(t,{sort:r[0].id,order:r[0].desc?"desc":"asc",page:"1"}):l(t,{sort:null,order:null,page:"1"})},onColumnFiltersChange:G,onColumnVisibilityChange:h,onPaginationChange:e=>{let r="function"==typeof e?e(H):e;J(r),l(t,{page:(r.pageIndex+1).toString(),size:r.pageSize.toString()})},getCoreRowModel:(0,q.HT)(),getFilteredRowModel:(0,q.hM)(),getPaginationRowModel:(0,q.kW)(),getSortedRowModel:(0,q.h5)(),getFacetedRowModel:(0,q.kQ)(),getFacetedUniqueValues:(0,q.oS)(),manualPagination:!0,pageCount:et,manualSorting:!0,manualFiltering:!0}),ed=(0,s.useCallback)(e=>{I(e),N(!0),z.current&&clearTimeout(z.current),z.current=setTimeout(()=>{G(t=>(function(e,t){let r=e.filter(e=>"key"!==e.id);return t&&r.push({id:"key",value:t}),r})(t,e)),l(t,{key:e??null,page:"1"}),J(e=>({...e,pageIndex:0})),N(!1)},500)},[l,t]),ec=(0,s.useCallback)(e=>{"all"===e?G(e=>e.filter(e=>"type"!==e.id)):G(t=>{let r=t.filter(e=>"type"!==e.id);return r.push({id:"type",value:e}),r}),l(t,{type:"all"===e?null:e,page:"1"}),J(e=>({...e,pageIndex:0}))},[l,t]),eu=r=>{let a=t.startsWith("/admin"),s=t.split("/")[1],o=a?`/admin/asset/${r.id}`:`/${s}/asset/${r.id}`;e.push(o)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Assets"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[y>0&&(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,a.jsxs)("span",{children:["Total: ",y," asset",1!==y?"s":""]})}),(0,a.jsxs)(n.$,{onClick:()=>{m("create"),c(!0)},className:"flex items-center justify-center",children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Create"]})]})]}),Y&&(0,a.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,a.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load assets. Please try again."}),(0,a.jsx)(n.$,{variant:"outline",className:"mt-2",onClick:()=>X(),children:"Retry"})]}),(0,a.jsx)(D,{table:ei,types:[{value:"0",label:"String"},{value:"1",label:"Secret"}],onSearch:ed,onTypeChange:ec,searchValue:M,isFiltering:Z,isPending:w}),(0,a.jsx)(b.b,{data:ee??[],columns:el,onRowClick:e=>{d||eu(e)},table:ei,isLoading:Z,totalCount:y}),(0,a.jsx)(_.d,{currentPage:H.pageIndex+1,pageSize:H.pageSize,totalCount:y,totalPages:et,isLoading:Z,isChangingPageSize:C,isUnknownTotalCount:er,rowsLabel:"assets",onPageChange:e=>{J({...H,pageIndex:e-1}),l(t,{page:e.toString()})},onPageSizeChange:e=>{S(!0);let r=Math.floor(H.pageIndex*H.pageSize/e);J({pageSize:e,pageIndex:r}),l(t,{size:e.toString(),page:(r+1).toString()})}})]}),(0,a.jsx)(R,{isOpen:d,onClose:()=>{c(!1),es(null)},mode:u,onCreated:()=>X(),existingKeys:ee.map(e=>e.key),asset:ea})]})}M.z.object({id:M.z.string(),key:M.z.string(),type:M.z.number(),description:M.z.string(),createdBy:M.z.string()})},69231:(e,t,r)=>{"use strict";r.d(t,{TenantGuard:()=>i});var a=r(60687),s=r(43210),o=r(16189),n=r(31599),l=r(31568);function i({children:e}){let{tenant:t}=(0,o.useParams)();(0,o.useRouter)();let{isAuthenticated:r,isLoading:i}=(0,l.A)(),{organizationUnits:d,isLoading:c}=(0,n.c)(),[u,m]=(0,s.useState)(!0);return i||c||u?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,a.jsx)(a.Fragment,{children:e})}},69820:(e,t,r)=>{Promise.resolve().then(r.bind(r,23749)),Promise.resolve().then(r.bind(r,45996))},71769:(e,t,r)=>{"use strict";r.d(t,{$o:()=>o,Lm:()=>i,NH:()=>l,deleteAsset:()=>d,gT:()=>n,j0:()=>u,mK:()=>m,qi:()=>c});var a=r(51787);let s=()=>"default",o=async e=>{let t=s();return a.F.post(`${t}/api/assets`,e)},n=async(e,t,r)=>{let o=s(),n=await a.F.put(`${o}/api/assets/${e}`,t);return await a.F.put(`${o}/api/assets/${e}/bot-agents`,{botAgentIds:r}),n},l=async()=>{let e=s();return await a.F.get(`${e}/api/assets`)},i=async e=>{let t=s(),r=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,String(r))}),t.toString()}(e),o=`${t}/odata/Assets`;r&&(o+=`?${r}`),console.log("OData query endpoint:",o);try{let e=await a.F.get(o);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} items from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":e["@odata.count"]??e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let r=t[0];console.log(`Found array property "${r}" in response`);let a=e[r],s=e["@odata.count"];return{value:a,"@odata.count":("number"==typeof s?s:void 0)??a.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching assets with OData:",e),{value:[]}}},d=async e=>{let t=s();await a.F.delete(`${t}/api/assets/${e}`)},c=async e=>{let t=s();return a.F.get(`${t}/api/assets/${e}`)},u=async e=>{let t=s();return a.F.get(`${t}/api/assets/${e}/bot-agents`)},m=async()=>{let e=s();return a.F.get(`${e}/api/agents`)}},72826:(e,t,r)=>{Promise.resolve().then(r.bind(r,69231)),Promise.resolve().then(r.bind(r,83847)),Promise.resolve().then(r.bind(r,78526)),Promise.resolve().then(r.bind(r,97597)),Promise.resolve().then(r.bind(r,98641)),Promise.resolve().then(r.bind(r,80110))},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var a=r(60687);r(43210);var s=r(61170),o=r(36966);function n({className:e,...t}){return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81682:(e,t,r)=>{"use strict";r.d(t,{PermissionRouteGuard:()=>n});var a=r(60687),s=r(31568),o=r(16189);function n({children:e,resource:t,requiredPermission:r,redirectPath:n,loadingComponent:l=null}){let{userProfile:i,isLoading:d,isSystemAdmin:c,hasPermission:u}=(0,s.A)();(0,o.useRouter)();let m=(0,o.useParams)().tenant;return!d&&(c||i)?c||u(t,r,m)?(0,a.jsx)(a.Fragment,{children:e}):null:l}r(43210)},83442:(e,t,r)=>{Promise.resolve().then(r.bind(r,61018)),Promise.resolve().then(r.bind(r,2505)),Promise.resolve().then(r.bind(r,92588)),Promise.resolve().then(r.bind(r,48974)),Promise.resolve().then(r.bind(r,31057)),Promise.resolve().then(r.bind(r,50417))},84756:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eh});var a=r(37413),s=r(23749),o=r(45996);let n=e=>{let t=c(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),l(r,t)||d(e)},getConflictingClassGroupIds:(e,t)=>{let s=r[e]||[];return t&&a[e]?[...s,...a[e]]:s}}},l=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],a=t.nextPart.get(r),s=a?l(e.slice(1),a):void 0;if(s)return s;if(0===t.validators.length)return;let o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},i=/^\[(.+)\]$/,d=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},c=e=>{let{theme:t,classGroups:r}=e,a={nextPart:new Map,validators:[]};for(let e in r)u(r[e],a,e,t);return a},u=(e,t,r,a)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:m(t,e)).classGroupId=r;return}if("function"==typeof e){if(p(e)){u(e(a),t,r,a);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,s])=>{u(s,m(t,e),r,a)})})},m=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},p=e=>e.isThemeGetter,g=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,a=new Map,s=(s,o)=>{r.set(s,o),++t>e&&(t=0,a=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=a.get(e))?(s(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):s(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,a=e=>{let t;let r=[],a=0,s=0,o=0;for(let n=0;n<e.length;n++){let l=e[n];if(0===a&&0===s){if(":"===l){r.push(e.slice(o,n)),o=n+1;continue}if("/"===l){t=n;continue}}"["===l?a++:"]"===l?a--:"("===l?s++:")"===l&&s--}let n=0===r.length?e:e.substring(o),l=h(n);return{modifiers:r,hasImportantModifier:l!==n,baseClassName:l,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",r=a;a=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=a;a=t=>r({className:t,parseClassName:e})}return a},h=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,x=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],a=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...a.sort(),e),a=[]):a.push(e)}),r.push(...a.sort()),r}},b=e=>({cache:g(e.cacheSize),parseClassName:f(e),sortModifiers:x(e),...n(e)}),y=/\s+/,v=(e,t)=>{let{parseClassName:r,getClassGroupId:a,getConflictingClassGroupIds:s,sortModifiers:o}=t,n=[],l=e.trim().split(y),i="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:d,modifiers:c,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=r(t);if(d){i=t+(i.length>0?" "+i:i);continue}let g=!!p,f=a(g?m.substring(0,p):m);if(!f){if(!g||!(f=a(m))){i=t+(i.length>0?" "+i:i);continue}g=!1}let h=o(c).join(":"),x=u?h+"!":h,b=x+f;if(n.includes(b))continue;n.push(b);let y=s(f,g);for(let e=0;e<y.length;++e){let t=y[e];n.push(x+t)}i=t+(i.length>0?" "+i:i)}return i};function w(){let e,t,r=0,a="";for(;r<arguments.length;)(e=arguments[r++])&&(t=k(e))&&(a&&(a+=" "),a+=t);return a}let k=e=>{let t;if("string"==typeof e)return e;let r="";for(let a=0;a<e.length;a++)e[a]&&(t=k(e[a]))&&(r&&(r+=" "),r+=t);return r},j=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},N=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,C=/^\((?:(\w[\w-]*):)?(.+)\)$/i,S=/^\d+\/\d+$/,z=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,$=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,F=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,R=e=>S.test(e),M=e=>!!e&&!Number.isNaN(Number(e)),I=e=>!!e&&Number.isInteger(Number(e)),E=e=>e.endsWith("%")&&M(e.slice(0,-1)),G=e=>z.test(e),O=()=>!0,D=e=>A.test(e)&&!P.test(e),V=()=>!1,q=e=>$.test(e),T=e=>F.test(e),_=e=>!U(e)&&!Q(e),L=e=>ea(e,el,V),U=e=>N.test(e),K=e=>ea(e,ei,D),W=e=>ea(e,ed,M),H=e=>ea(e,eo,V),J=e=>ea(e,en,T),B=e=>ea(e,eu,q),Q=e=>C.test(e),Y=e=>es(e,ei),Z=e=>es(e,ec),X=e=>es(e,eo),ee=e=>es(e,el),et=e=>es(e,en),er=e=>es(e,eu,!0),ea=(e,t,r)=>{let a=N.exec(e);return!!a&&(a[1]?t(a[1]):r(a[2]))},es=(e,t,r=!1)=>{let a=C.exec(e);return!!a&&(a[1]?t(a[1]):r)},eo=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,el=e=>"length"===e||"size"===e||"bg-size"===e,ei=e=>"length"===e,ed=e=>"number"===e,ec=e=>"family-name"===e,eu=e=>"shadow"===e;Symbol.toStringTag;let em=function(e,...t){let r,a,s;let o=function(l){return a=(r=b(t.reduce((e,t)=>t(e),e()))).cache.get,s=r.cache.set,o=n,n(l)};function n(e){let t=a(e);if(t)return t;let o=v(e,r);return s(e,o),o}return function(){return o(w.apply(null,arguments))}}(()=>{let e=j("color"),t=j("font"),r=j("text"),a=j("font-weight"),s=j("tracking"),o=j("leading"),n=j("breakpoint"),l=j("container"),i=j("spacing"),d=j("radius"),c=j("shadow"),u=j("inset-shadow"),m=j("text-shadow"),p=j("drop-shadow"),g=j("blur"),f=j("perspective"),h=j("aspect"),x=j("ease"),b=j("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],v=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...v(),Q,U],k=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],C=()=>[Q,U,i],S=()=>[R,"full","auto",...C()],z=()=>[I,"none","subgrid",Q,U],A=()=>["auto",{span:["full",I,Q,U]},I,Q,U],P=()=>[I,"auto",Q,U],$=()=>["auto","min","max","fr",Q,U],F=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],D=()=>["start","end","center","stretch","center-safe","end-safe"],V=()=>["auto",...C()],q=()=>[R,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],T=()=>[e,Q,U],ea=()=>[...v(),X,H,{position:[Q,U]}],es=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",ee,L,{size:[Q,U]}],en=()=>[E,Y,K],el=()=>["","none","full",d,Q,U],ei=()=>["",M,Y,K],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[M,E,X,H],em=()=>["","none",g,Q,U],ep=()=>["none",M,Q,U],eg=()=>["none",M,Q,U],ef=()=>[M,Q,U],eh=()=>[R,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[G],breakpoint:[G],color:[O],container:[G],"drop-shadow":[G],ease:["in","out","in-out"],font:[_],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[G],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[G],shadow:[G],spacing:["px",M],text:[G],"text-shadow":[G],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",R,U,Q,h]}],container:["container"],columns:[{columns:[M,U,Q,l]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[I,"auto",Q,U]}],basis:[{basis:[R,"full","auto",l,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[M,R,"auto","initial","none",U]}],grow:[{grow:["",M,Q,U]}],shrink:[{shrink:["",M,Q,U]}],order:[{order:[I,"first","last","none",Q,U]}],"grid-cols":[{"grid-cols":z()}],"col-start-end":[{col:A()}],"col-start":[{"col-start":P()}],"col-end":[{"col-end":P()}],"grid-rows":[{"grid-rows":z()}],"row-start-end":[{row:A()}],"row-start":[{"row-start":P()}],"row-end":[{"row-end":P()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":$()}],"auto-rows":[{"auto-rows":$()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...F(),"normal"]}],"justify-items":[{"justify-items":[...D(),"normal"]}],"justify-self":[{"justify-self":["auto",...D()]}],"align-content":[{content:["normal",...F()]}],"align-items":[{items:[...D(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...D(),{baseline:["","last"]}]}],"place-content":[{"place-content":F()}],"place-items":[{"place-items":[...D(),"baseline"]}],"place-self":[{"place-self":["auto",...D()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:V()}],mx:[{mx:V()}],my:[{my:V()}],ms:[{ms:V()}],me:[{me:V()}],mt:[{mt:V()}],mr:[{mr:V()}],mb:[{mb:V()}],ml:[{ml:V()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:q()}],w:[{w:[l,"screen",...q()]}],"min-w":[{"min-w":[l,"screen","none",...q()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[n]},...q()]}],h:[{h:["screen",...q()]}],"min-h":[{"min-h":["screen","none",...q()]}],"max-h":[{"max-h":["screen",...q()]}],"font-size":[{text:["base",r,Y,K]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[a,Q,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",E,U]}],"font-family":[{font:[Z,U,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,Q,U]}],"line-clamp":[{"line-clamp":[M,"none",Q,W]}],leading:[{leading:[o,...C()]}],"list-image":[{"list-image":["none",Q,U]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Q,U]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:T()}],"text-color":[{text:T()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[M,"from-font","auto",Q,K]}],"text-decoration-color":[{decoration:T()}],"underline-offset":[{"underline-offset":[M,"auto",Q,U]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q,U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q,U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ea()}],"bg-repeat":[{bg:es()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},I,Q,U],radial:["",Q,U],conic:[I,Q,U]},et,J]}],"bg-color":[{bg:T()}],"gradient-from-pos":[{from:en()}],"gradient-via-pos":[{via:en()}],"gradient-to-pos":[{to:en()}],"gradient-from":[{from:T()}],"gradient-via":[{via:T()}],"gradient-to":[{to:T()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:T()}],"border-color-x":[{"border-x":T()}],"border-color-y":[{"border-y":T()}],"border-color-s":[{"border-s":T()}],"border-color-e":[{"border-e":T()}],"border-color-t":[{"border-t":T()}],"border-color-r":[{"border-r":T()}],"border-color-b":[{"border-b":T()}],"border-color-l":[{"border-l":T()}],"divide-color":[{divide:T()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[M,Q,U]}],"outline-w":[{outline:["",M,Y,K]}],"outline-color":[{outline:T()}],shadow:[{shadow:["","none",c,er,B]}],"shadow-color":[{shadow:T()}],"inset-shadow":[{"inset-shadow":["none",u,er,B]}],"inset-shadow-color":[{"inset-shadow":T()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:T()}],"ring-offset-w":[{"ring-offset":[M,K]}],"ring-offset-color":[{"ring-offset":T()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":T()}],"text-shadow":[{"text-shadow":["none",m,er,B]}],"text-shadow-color":[{"text-shadow":T()}],opacity:[{opacity:[M,Q,U]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[M]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":T()}],"mask-image-linear-to-color":[{"mask-linear-to":T()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":T()}],"mask-image-t-to-color":[{"mask-t-to":T()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":T()}],"mask-image-r-to-color":[{"mask-r-to":T()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":T()}],"mask-image-b-to-color":[{"mask-b-to":T()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":T()}],"mask-image-l-to-color":[{"mask-l-to":T()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":T()}],"mask-image-x-to-color":[{"mask-x-to":T()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":T()}],"mask-image-y-to-color":[{"mask-y-to":T()}],"mask-image-radial":[{"mask-radial":[Q,U]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":T()}],"mask-image-radial-to-color":[{"mask-radial-to":T()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":v()}],"mask-image-conic-pos":[{"mask-conic":[M]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":T()}],"mask-image-conic-to-color":[{"mask-conic-to":T()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ea()}],"mask-repeat":[{mask:es()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Q,U]}],filter:[{filter:["","none",Q,U]}],blur:[{blur:em()}],brightness:[{brightness:[M,Q,U]}],contrast:[{contrast:[M,Q,U]}],"drop-shadow":[{"drop-shadow":["","none",p,er,B]}],"drop-shadow-color":[{"drop-shadow":T()}],grayscale:[{grayscale:["",M,Q,U]}],"hue-rotate":[{"hue-rotate":[M,Q,U]}],invert:[{invert:["",M,Q,U]}],saturate:[{saturate:[M,Q,U]}],sepia:[{sepia:["",M,Q,U]}],"backdrop-filter":[{"backdrop-filter":["","none",Q,U]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[M,Q,U]}],"backdrop-contrast":[{"backdrop-contrast":[M,Q,U]}],"backdrop-grayscale":[{"backdrop-grayscale":["",M,Q,U]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[M,Q,U]}],"backdrop-invert":[{"backdrop-invert":["",M,Q,U]}],"backdrop-opacity":[{"backdrop-opacity":[M,Q,U]}],"backdrop-saturate":[{"backdrop-saturate":[M,Q,U]}],"backdrop-sepia":[{"backdrop-sepia":["",M,Q,U]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Q,U]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[M,"initial",Q,U]}],ease:[{ease:["linear","initial",x,Q,U]}],delay:[{delay:[M,Q,U]}],animate:[{animate:["none",b,Q,U]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,Q,U]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:eg()}],"scale-x":[{"scale-x":eg()}],"scale-y":[{"scale-y":eg()}],"scale-z":[{"scale-z":eg()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[Q,U,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:T()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:T()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q,U]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q,U]}],fill:[{fill:["none",...T()]}],"stroke-w":[{stroke:[M,Y,K,W]}],stroke:[{stroke:["none",...T()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ep({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"skeleton",className:function(...e){return em(function(){for(var e,t,r=0,a="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,a,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(a=e(t[r]))&&(s&&(s+=" "),s+=a)}else for(a in t)t[a]&&(s&&(s+=" "),s+=a)}return s}(e))&&(a&&(a+=" "),a+=t);return a}(e))}("bg-accent animate-pulse rounded-md",e),...t})}var eg=function(e){return e[e.View=1]="View",e[e.Create=2]="Create",e[e.Update=3]="Update",e[e.Delete=4]="Delete",e}({});let ef={ASSET:"Asset"};function eh(){return(0,a.jsx)(o.PermissionRouteGuard,{resource:ef.ASSET,requiredPermission:eg.View,loadingComponent:(0,a.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,a.jsx)(ep,{className:"h-8 w-[200px]"}),(0,a.jsx)(ep,{className:"h-[400px] w-full"})]}),children:(0,a.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,a.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,a.jsx)(s.default,{})]})})}},93661:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7966,5584,5156,4654,6467,5880,1694,6945,8759,6763,519,4881],()=>r(26075));module.exports=a})();