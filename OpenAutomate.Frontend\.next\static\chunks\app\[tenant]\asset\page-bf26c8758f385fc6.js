(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1896],{5623:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},11832:(e,t,a)=>{"use strict";a.d(t,{i:()=>o});var s=a(95155),l=a(18289),r=a(47330),n=a(30285),i=a(44838);function o(e){let{table:t}=e;return(0,s.jsxs)(i.rI,{children:[(0,s.jsx)(l.ty,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,s.jsx)(r.A,{}),"View"]})}),(0,s.jsxs)(i.SQ,{align:"end",className:"w-[150px]",children:[(0,s.jsx)(i.lp,{children:"Toggle columns"}),(0,s.jsx)(i.mB,{}),t.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,s.jsx)(i.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},21080:(e,t,a)=>{"use strict";a.d(t,{PermissionRouteGuard:()=>i});var s=a(95155),l=a(22100),r=a(35695),n=a(12115);function i(e){let{children:t,resource:a,requiredPermission:i,redirectPath:o,loadingComponent:c=null}=e,{userProfile:d,isLoading:u,isSystemAdmin:m,hasPermission:p}=(0,l.A)(),h=(0,r.useRouter)(),g=(0,r.useParams)().tenant;return((0,n.useEffect)(()=>{if(!u&&!m&&d&&!p(a,i,g)){let e=o||"/".concat(g,"/dashboard");h.replace(e);return}},[u,m,d,p,a,i,g,h,o]),!u&&(m||d))?m||p(a,i,g)?(0,s.jsx)(s.Fragment,{children:t}):null:c}},22100:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(12115),l=a(67057);function r(){let e=(0,s.useContext)(l.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},24265:(e,t,a)=>{"use strict";a.d(t,{b:()=>d});var s=a(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}a(47650);var r=a(95155),n=Symbol("radix.slottable");function i(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===n}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:a,...r}=e;if(s.isValidElement(a)){var n;let e,i;let o=(n=a,(i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(i=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),c=function(e,t){let a={...t};for(let s in t){let l=e[s],r=t[s];/^on[A-Z]/.test(s)?l&&r?a[s]=(...e)=>{r(...e),l(...e)}:l&&(a[s]=l):"style"===s?a[s]={...l,...r}:"className"===s&&(a[s]=[l,r].filter(Boolean).join(" "))}return{...e,...a}}(r,a.props);return a.type!==s.Fragment&&(c.ref=t?function(...e){return t=>{let a=!1,s=e.map(e=>{let s=l(e,t);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let t=0;t<s.length;t++){let a=s[t];"function"==typeof a?a():l(e[t],null)}}}}(t,o):o),s.cloneElement(a,c)}return s.Children.count(a)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=s.forwardRef((e,a)=>{let{children:l,...n}=e,o=s.Children.toArray(l),c=o.find(i);if(c){let e=c.props.children,l=o.map(t=>t!==c?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,r.jsx)(t,{...n,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,l):null})}return(0,r.jsx)(t,{...n,ref:a,children:l})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),n=s.forwardRef((e,s)=>{let{asChild:l,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,r.jsx)(l?a:t,{...n,ref:s})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),c=s.forwardRef((e,t)=>(0,r.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var d=c},47330:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},49103:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>p,Es:()=>g,HM:()=>u,L3:()=>x,c7:()=>h,lG:()=>o,rr:()=>f,zM:()=>c});var s=a(95155),l=a(12115),r=a(59096),n=a(54416),i=a(36928);let o=r.bL,c=r.l9,d=r.ZL,u=r.bm,m=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",a),...l})});m.displayName=r.hJ.displayName;let p=l.forwardRef((e,t)=>{let{className:a,children:l,...o}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(m,{}),(0,s.jsxs)(r.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",a),...o,children:[l,(0,s.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});p.displayName=r.UC.displayName;let h=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};h.displayName="DialogHeader";let g=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};g.displayName="DialogFooter";let x=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...l})});x.displayName=r.hE.displayName;let f=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...l})});f.displayName=r.VY.displayName},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},62668:(e,t,a)=>{"use strict";a.d(t,{z:()=>r});var s=a(35695),l=a(12115);function r(){let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),a=(0,l.useCallback)(e=>{let a=new URLSearchParams(t.toString());return Object.entries(e).forEach(e=>{let[t,s]=e;null===s?a.delete(t):a.set(t,s)}),a.toString()},[t]),r=(0,l.useCallback)((t,s)=>{let l=a(s);e.push("".concat(t,"?").concat(l),{scroll:!1})},[a,e]);return{createQueryString:a,updateUrl:r}}},63668:(e,t,a)=>{Promise.resolve().then(a.bind(a,84856)),Promise.resolve().then(a.bind(a,21080))},66932:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},69803:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},70449:(e,t,a)=>{"use strict";a.d(t,{DC:()=>i,EJ:()=>n,IS:()=>o,bb:()=>r});var s=a(7283),l=a(15874);function r(){return{fetcher:e=>(0,s.fetchApi)(e),onError:function(e){(0,l.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let n={fetcher:e=>(0,s.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},i={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},o=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74126:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},81053:(e,t,a)=>{"use strict";a.d(t,{$o:()=>r,Lm:()=>o,NH:()=>i,deleteAsset:()=>c,gT:()=>n,j0:()=>u,mK:()=>m,qi:()=>d});var s=a(7283);let l=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},r=async e=>{let t=l();return s.F.post("".concat(t,"/api/assets"),e)},n=async(e,t,a)=>{let r=l(),n=await s.F.put("".concat(r,"/api/assets/").concat(e),t);return await s.F.put("".concat(r,"/api/assets/").concat(e,"/bot-agents"),{botAgentIds:a}),n},i=async()=>{let e=l();return await s.F.get("".concat(e,"/api/assets"))},o=async e=>{let t=l(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[a,s]=e;null!=s&&t.append(a,String(s))}),t.toString()}(e),r="".concat(t,"/odata/Assets");a&&(r+="?".concat(a)),console.log("OData query endpoint:",r);try{let e=await s.F.get(r);return console.log("Raw OData response:",e),function(e){var t,a;if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," items from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":null!==(t=e["@odata.count"])&&void 0!==t?t:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let s=t[0];console.log('Found array property "'.concat(s,'" in response'));let l=e[s],r=e["@odata.count"];return{value:l,"@odata.count":null!==(a="number"==typeof r?r:void 0)&&void 0!==a?a:l.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching assets with OData:",e),{value:[]}}},c=async e=>{let t=l();await s.F.delete("".concat(t,"/api/assets/").concat(e))},d=async e=>{let t=l();return s.F.get("".concat(t,"/api/assets/").concat(e))},u=async e=>{let t=l();return s.F.get("".concat(t,"/api/assets/").concat(e,"/bot-agents"))},m=async()=>{let e=l();return s.F.get("".concat(e,"/api/agents"))}},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},84856:(e,t,a)=>{"use strict";a.d(t,{default:()=>_});var s=a(95155),l=a(12115),r=a(49103),n=a(30285),i=a(47262),o=a(87570),c=a(5623),d=a(89917),u=a(74126),m=a(54416),p=a(51154),h=a(44838),g=a(54165);function x(e){let{asset:t,onEdit:r,onDeleted:i}=e,[o,x]=(0,l.useState)(!1),[f,y]=(0,l.useState)(!1),v=async()=>{y(!0);try{let{deleteAsset:e}=await Promise.resolve().then(a.bind(a,81053));await e(t.id),x(!1),i&&i()}catch(e){alert("Delete failed!")}finally{y(!1)}},j=e=>{e.stopPropagation()};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(h.rI,{children:[(0,s.jsx)(h.ty,{asChild:!0,onClick:j,children:(0,s.jsx)(n.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted","aria-label":"Open menu",onClick:j,children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(h.SQ,{align:"start",className:"w-[160px]",onClick:j,onPointerDown:j,onMouseDown:j,children:[(0,s.jsxs)(h._2,{onClick:e=>{e.stopPropagation(),r&&r(t)},children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Edit"})]}),(0,s.jsxs)(h._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e.stopPropagation(),x(!0)},children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Delete"})]})]})]}),(0,s.jsx)(g.lG,{open:o,onOpenChange:x,children:(0,s.jsxs)(g.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsxs)(g.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,s.jsx)(g.c7,{children:(0,s.jsx)(g.L3,{children:"Confirm Delete"})}),(0,s.jsxs)("div",{children:["Are you sure you want to delete this asset ",(0,s.jsx)("b",{children:t.key}),"?"]}),(0,s.jsxs)(g.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:e=>{e.stopPropagation(),x(!1)},disabled:f,children:"Cancel"}),(0,s.jsxs)(n.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:e=>{e.stopPropagation(),v()},disabled:f,children:[f&&(0,s.jsx)(p.A,{className:"animate-spin w-4 h-4 mr-2"}),f?"Deleting...":"Delete"]})]})]})})]})}let f=(e,t)=>[{id:"select",header:e=>{let{table:t}=e;return(0,s.jsx)(i.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e,a=e=>{e.stopPropagation()};return(0,s.jsx)("span",{onClick:a,onMouseDown:a,onPointerDown:a,children:(0,s.jsx)(i.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]",onClick:a})})},enableSorting:!1,enableHiding:!1},{id:"actions",header:e=>{let{column:t}=e;return(0,s.jsx)(o.w,{column:t,title:"Actions"})},cell:a=>{let{row:l}=a;return(0,s.jsx)(x,{asset:l.original,onEdit:e,onDeleted:t})}},{accessorKey:"key",header:e=>{let{column:t}=e;return(0,s.jsx)(o.w,{column:t,title:"Key",className:"font-bold text-base"})},cell:e=>{let{row:t}=e;return(0,s.jsx)("span",{className:"font-medium truncate",children:t.getValue("key")})},filterFn:(e,t,a)=>{let s=e.getValue(t);return"string"==typeof s&&"string"==typeof a&&s.toLowerCase().includes(a.toLowerCase())}},{accessorKey:"type",header:e=>{let{column:t}=e;return(0,s.jsx)(o.w,{column:t,title:"Type",className:"font-bold text-base"})},cell:e=>{let{row:t}=e,a=t.getValue("type");return(0,s.jsx)("span",{children:0===a||"0"===a?"String":"Secret"})},filterFn:(e,t,a)=>String(e.getValue(t))===String(a)},{accessorKey:"description",header:e=>{let{column:t}=e;return(0,s.jsx)(o.w,{column:t,title:"Description",className:"font-bold text-base"})},cell:e=>{let{row:t}=e,a=t.getValue("description");return(0,s.jsx)("span",{className:"string"==typeof a&&a.trim()?"":"text-muted-foreground italic",children:"string"==typeof a&&a.trim()?a:"N/a"})}}];f();var y=a(54333),v=a(62523),j=a(85057),b=a(59409),w=a(35695),k=a(81053),N=a(43630),S=a(13717),C=a(69803),A=a(85339),z=a(57434),R=a(71007),I=a(84616),F=a(5196);function E(e){let t,{isOpen:a,onClose:i,mode:o,onCreated:c,existingKeys:d=[],asset:p}=e,[h,x]=(0,l.useState)(""),[f,y]=(0,l.useState)(""),[E,M]=(0,l.useState)(0),[P,D]=(0,l.useState)(""),[O,V]=(0,l.useState)(!1),T=(0,w.useParams)().tenant||"",[$,q]=(0,l.useState)([]),[L,H]=(0,l.useState)(""),[B,U]=(0,l.useState)([]),[W,_]=(0,l.useState)(null),[K,J]=(0,l.useState)(null),[G,Q]=(0,l.useState)(null),[Y,Z]=(0,l.useState)(null),X="edit"===o,ee=(0,l.useRef)(null),et=(0,l.useRef)(null),ea=(0,l.useRef)(null);(0,l.useEffect)(()=>{T&&a&&(async()=>{try{let e=await (0,k.mK)();q(e.map(e=>({id:e.id,name:e.name})))}catch(e){N.m.handleError(e,"Loading agents")}})()},[T,a]);let es=e=>{setTimeout(()=>{e.target.selectionStart=e.target.selectionEnd=e.target.value.length},0)};(0,l.useEffect)(()=>{if(X&&p&&a){var e,t,s;y(p.key||""),M("number"==typeof p.type?p.type:Number(p.type)||0),x(null!==(t=p.value)&&void 0!==t?t:""),D(p.description||""),U(null!==(s=null===(e=p.agents)||void 0===e?void 0:e.filter(e=>(null==e?void 0:e.id)&&(null==e?void 0:e.name)))&&void 0!==s?s:[]),setTimeout(()=>{[ee,et,ea].forEach(e=>{e.current&&(e.current.selectionStart=e.current.selectionEnd=e.current.value.length)})},100)}else a||en()},[X,p,a]);let el=()=>{let e=!0;return J(null),Q(null),Z(null),_(null),f.trim()?/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/.test(f)?(J("Key must not contain Vietnamese characters or accents."),e=!1):!X&&d.includes(f.trim())&&(J("Key already exists. Please choose a unique key."),e=!1):(J("Key is required."),e=!1),X||E.toString().trim()||(Q("Type is required."),e=!1),h.trim()||(Z("Value is required."),e=!1),e},er=async()=>{if(el()){V(!0);try{let e=B.map(e=>e.id);X&&(null==p?void 0:p.id)?(await (0,k.gT)(p.id,{key:f,description:P,value:h},e),N.m.handleSuccess("updated",'Asset "'.concat(f,'"'))):(await (0,k.$o)({key:f,description:P,value:h,type:Number(E),botAgentIds:e}),N.m.handleSuccess("created",'Asset "'.concat(f,'"'))),en(),i(),c&&c()}catch(e){N.m.handleError(e,X?"Updating asset":"Creating asset")}finally{V(!1)}}},en=()=>{x(""),y(""),M(0),D(""),H(""),U([]),J(null),Q(null),Z(null),_(null)},ei=()=>{en(),i()},eo=e=>{U(B.filter(t=>t.id!==e))},ec="Add Item";return O?ec="Submitting...":X&&(ec="Save Changes"),t=X?(null==p?void 0:p.type)===1?"password":"text":1===E?"password":"text",(0,s.jsx)(g.lG,{open:a,onOpenChange:ei,children:(0,s.jsxs)(g.Cf,{className:"sm:max-w-[800px] p-0 max-h-[85vh] flex flex-col",onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsxs)(g.c7,{className:"flex items-center gap-2 p-6 pb-2 border-b",children:[X?(0,s.jsx)(S.A,{className:"w-5 h-5 text-primary"}):(0,s.jsx)(r.A,{className:"w-5 h-5 text-primary"}),(0,s.jsx)(g.L3,{className:"text-xl font-bold",children:X?"Edit Asset":"Create a new Asset"})]}),(0,s.jsxs)("div",{className:"px-6 py-4 flex-1 overflow-y-auto",children:[(0,s.jsxs)("form",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(j.J,{htmlFor:"key",className:"text-sm font-medium flex items-center gap-1",children:[(0,s.jsx)(C.A,{className:"w-4 h-4 text-muted-foreground"}),"Key",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(v.p,{id:"key",ref:ee,value:f,onChange:e=>{y(e.target.value),J(null)},className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20",autoComplete:"off",onFocus:es,spellCheck:"false"}),K&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mb-2",children:[(0,s.jsx)(A.A,{className:"w-4 h-4"}),K]}),(0,s.jsxs)(j.J,{htmlFor:"description",className:"text-sm font-medium flex items-center gap-1",children:[(0,s.jsx)(z.A,{className:"w-4 h-4 text-muted-foreground"}),"Description"]}),(0,s.jsx)(v.p,{id:"description",ref:ea,value:P,onChange:e=>D(e.target.value),placeholder:"Enter description (optional)",className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20",autoComplete:"off",onFocus:es,spellCheck:"false"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(j.J,{htmlFor:"type",className:"text-sm font-medium flex items-center gap-1",children:[(0,s.jsx)(z.A,{className:"w-4 h-4 text-muted-foreground"}),"Type",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),X?(0,s.jsxs)("div",{className:"flex items-center border rounded-xl px-3 py-2 text-sm bg-muted",children:[0===E?"String":"Secret"," ",(0,s.jsx)("span",{className:"text-muted-foreground ml-2 text-xs",children:"(Cannot be changed)"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(b.l6,{value:E.toString(),onValueChange:e=>{M(Number(e)),Q(null)},children:[(0,s.jsx)(b.bq,{children:(0,s.jsx)(b.yv,{placeholder:"Select type"})}),(0,s.jsxs)(b.gC,{children:[(0,s.jsx)(b.eb,{value:"0",children:"String"}),(0,s.jsx)(b.eb,{value:"1",children:"Secret"})]})]}),G&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mb-2",children:[(0,s.jsx)(A.A,{className:"w-4 h-4"}),G]})]}),(0,s.jsxs)(j.J,{htmlFor:"value",className:"text-sm font-medium flex items-center gap-1",children:[(0,s.jsx)(z.A,{className:"w-4 h-4 text-muted-foreground"}),"Value",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(v.p,{id:"value",ref:et,value:h,onChange:e=>{x(e.target.value),Z(null)},placeholder:"Type a string value",type:t,className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20",autoComplete:"off",onFocus:es,spellCheck:"false"}),Y&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mb-2",children:[(0,s.jsx)(A.A,{className:"w-4 h-4"}),Y]})]})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)(j.J,{htmlFor:"agent",className:"text-sm font-medium flex items-center gap-1",children:[(0,s.jsx)(R.A,{className:"w-4 h-4 text-muted-foreground"}),"Agent"]}),(0,s.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,s.jsxs)(b.l6,{value:L,onValueChange:e=>{H(e),_(null)},children:[(0,s.jsx)(b.bq,{children:(0,s.jsx)(b.yv,{placeholder:"Select Agent..."})}),(0,s.jsx)(b.gC,{children:$.map(e=>(0,s.jsx)(b.eb,{value:e.id,children:e.name},e.id))})]}),(0,s.jsxs)(n.$,{type:"button",onClick:()=>{if(!L)return;let e=$.find(e=>e.id===L);if(e){if(B.some(t=>t.id===e.id)){_("Agent already added.");return}U([...B,e]),H(""),_(null)}},disabled:!L,variant:L?"default":"secondary",className:"flex items-center gap-1 rounded-md px-4 py-2 transition-colors",children:[(0,s.jsx)(I.A,{className:"w-4 h-4"})," Add"]})]}),W&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mt-1",children:[(0,s.jsx)(A.A,{className:"w-4 h-4"}),W]}),B.length>0&&(0,s.jsx)("div",{className:"overflow-x-auto mt-4",children:(0,s.jsxs)("table",{className:"min-w-[300px] w-full text-sm rounded-xl overflow-hidden border bg-white dark:bg-neutral-900",children:[(0,s.jsx)("thead",{className:"bg-muted",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"border px-3 py-2 text-left font-semibold text-black dark:text-white",children:"#"}),(0,s.jsx)("th",{className:"border px-3 py-2 text-left font-semibold text-black dark:text-white",children:"Agent Name"}),(0,s.jsx)("th",{className:"border px-3 py-2 text-left font-semibold text-black dark:text-white",children:"Action"})]})}),(0,s.jsx)("tbody",{children:B.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-accent/30 transition",children:[(0,s.jsx)("td",{className:"border px-3 py-2 text-black dark:text-neutral-100",children:t+1}),(0,s.jsx)("td",{className:"border px-3 py-2 text-black dark:text-neutral-100",children:e.name}),(0,s.jsx)("td",{className:"border px-3 py-2",children:(0,s.jsx)(n.$,{size:"icon",variant:"ghost",onClick:()=>eo(e.id),className:"text-red-400 hover:text-red-600",children:(0,s.jsx)(u.A,{className:"w-4 h-4"})})})]},e.id))})]})})]})]}),(0,s.jsxs)(g.Es,{className:"p-6 pt-4 border-t bg-background z-10 flex justify-end gap-2",children:[(0,s.jsxs)(n.$,{variant:"outline",onClick:ei,disabled:O,className:"flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"})," Cancel"]}),(0,s.jsxs)(n.$,{onClick:er,disabled:O,className:"flex items-center gap-1",children:[X?(0,s.jsx)(F.A,{className:"w-4 h-4"}):(0,s.jsx)(I.A,{className:"w-4 h-4"}),ec]})]})]})})}var M=a(55594),P=a(47924),D=a(66932),O=a(11832),V=a(26126);function T(e){let{table:t,types:a,onSearch:r,onTypeChange:i,searchValue:o="",isFiltering:c=!1,isPending:d=!1}=e,u=t.getState().columnFilters.length>0,h=t.getState().columnFilters.length,g=(0,l.useRef)(null),x=(0,l.useRef)(null);(0,l.useEffect)(()=>{document.activeElement!==g.current&&null!==x.current&&g.current&&(g.current.focus(),null!==x.current&&g.current.setSelectionRange(x.current,x.current))},[d,c]);let f=e=>{if(g.current&&(x.current=g.current.selectionStart),r)r(e);else{var a;null===(a=t.getColumn("key"))||void 0===a||a.setFilterValue(e)}},y=(0,l.useMemo)(()=>{let e=t.getState().columnFilters.find(e=>"type"===e.id);return e?e.value:"all"},[t]);return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,s.jsx)(P.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(v.p,{ref:g,placeholder:"Search by key...",value:o,onChange:e=>f(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:c,onFocus:()=>{g.current&&(x.current=g.current.selectionStart)}}),c&&(0,s.jsx)(p.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!c&&""!==o&&(0,s.jsx)(m.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>f("")})]}),(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(b.l6,{onValueChange:e=>{var a,s;i?i(e):"all"===e?null===(a=t.getColumn("type"))||void 0===a||a.setFilterValue(void 0):null===(s=t.getColumn("type"))||void 0===s||s.setFilterValue(e)},value:y,disabled:c||d,children:[(0,s.jsx)(b.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(D.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(b.yv,{placeholder:"Filter type"}),"all"!==y&&(0,s.jsx)(V.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(b.gC,{children:[(0,s.jsx)(b.eb,{value:"all",children:"All Types"}),a.map(e=>(0,s.jsx)(b.eb,{value:e.value,children:e.label},e.value))]})]})}),h>0&&(0,s.jsxs)(V.E,{variant:"secondary",className:"rounded-sm px-1",children:[h," active ",1===h?"filter":"filters"]}),u&&(0,s.jsxs)(n.$,{variant:"ghost",onClick:()=>{t.resetColumnFilters(),r&&r("")},className:"h-8 px-2 lg:px-3",disabled:c,children:["Reset",(0,s.jsx)(m.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(O.i,{table:t})]})}var $=a(36268),q=a(11032),L=a(62668),H=a(29797),B=a(34953),U=a(70449),W=a(88262);function _(){var e;let t=(0,w.useRouter)(),a=(0,w.usePathname)(),i=(0,w.useSearchParams)(),{updateUrl:o}=(0,L.z)(),{toast:c}=(0,W.d)(),[d,u]=(0,l.useState)(!1),[m,p]=(0,l.useState)("create"),[h,g]=(0,l.useState)({}),[x,v]=(0,l.useState)({}),[j,b]=(0,l.useState)(0),N=(0,l.useRef)(0),[S,C]=(0,l.useState)(!1),[A,z]=(0,l.useState)(!1),R=(0,l.useRef)(null),[I,F]=(0,l.useState)(!1),M=(e,t)=>Math.max(1,Math.ceil(e/t)),P=e=>e+1,[D,O]=(0,l.useState)(null!==(e=i.get("key"))&&void 0!==e?e:""),[V,_]=(0,l.useState)(()=>{let e=[],t=i.get("key");t&&e.push({id:"key",value:t});let a=i.get("type");return a&&e.push({id:"type",value:a}),e}),[K,J]=(0,l.useState)(()=>{let e=i.get("sort"),t=i.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[G,Q]=(0,l.useState)(()=>{let e=i.get("page"),t=i.get("size");return{pageIndex:e?Math.max(0,parseInt(e,10)-1):0,pageSize:t?parseInt(t,10):10}}),Y=(0,l.useCallback)(()=>{let e={$count:!0,$top:G.pageSize,$skip:G.pageIndex*G.pageSize};K.length>0&&(e.$orderby="".concat(K[0].id," ").concat(K[0].desc?"desc":"asc"));let t=[],a=V.find(e=>"key"===e.id);(null==a?void 0:a.value)&&t.push("contains(tolower(key), '".concat(a.value.toLowerCase(),"')"));let s=V.find(e=>"type"===e.id),l={0:"String",1:"Secret"},r=null==s?void 0:s.value;return r&&l[r]&&t.push("type eq '".concat(l[r],"'")),t.length>0&&(e.$filter=t.join(" and ")),e},[G,K,V])(),{data:Z,error:X,isLoading:ee,mutate:et}=(0,B.Ay)(U.DC.assetsWithOData(Y),()=>(0,k.Lm)(Y)),ea=(0,l.useMemo)(()=>(null==Z?void 0:Z.value)?Z.value.map(e=>({id:e.id,key:e.key,type:e.type,description:e.description,createdBy:e.createdBy})):[],[Z]),es=(0,l.useMemo)(()=>{let e=M(j,G.pageSize),t=ea.length===G.pageSize&&j<=G.pageSize*(G.pageIndex+1),a=P(G.pageIndex);return t?Math.max(a,e,G.pageIndex+2):Math.max(a,e)},[G.pageSize,G.pageIndex,ea.length,j]),el=(0,l.useMemo)(()=>!I&&ea.length===G.pageSize,[I,ea.length,G.pageSize]);(0,l.useEffect)(()=>{X&&(console.error("Failed to load assets:",X),c({title:"Error",description:"Failed to load assets. Please try again.",variant:"destructive"}))},[X,c]),(0,l.useEffect)(()=>{if(!Z)return;if("number"==typeof Z["@odata.count"]){b(Z["@odata.count"]),N.current=Z["@odata.count"],F(!0);return}if(!Array.isArray(Z.value))return;let e=G.pageIndex*G.pageSize+Z.value.length;e>N.current&&(b(e),N.current=e),Z.value.length===G.pageSize&&0===G.pageIndex&&(b(e+1),N.current=e+1),F(!1)},[Z,G.pageIndex,G.pageSize]);let[er,en]=(0,l.useState)(null),ei=(0,l.useCallback)(async e=>{try{var t;let a=await (0,k.qi)(e.id),s=(await (0,k.j0)(e.id)).map(e=>({id:e.id,name:e.name})),l={...e,type:"number"==typeof e.type?e.type:Number(e.type)||0,value:null!==(t=a.value)&&void 0!==t?t:"",agents:s.length>0?s:[]};en(l),p("edit"),u(!0)}catch(e){console.error("Error preparing asset for edit:",e),c({title:"Error",description:"Failed to load asset details for editing.",variant:"destructive"})}},[c]);(0,l.useEffect)(()=>{if((null==Z?void 0:Z.value)&&0===Z.value.length&&N.current>0&&G.pageIndex>0){let e=Math.max(1,Math.ceil(N.current/G.pageSize));G.pageIndex>=e&&(Q(e=>({...e,pageIndex:0})),o(a,{page:"1"}))}},[Z,G.pageIndex,G.pageSize,N,o,a]);let eo=(0,l.useCallback)(async()=>{C(!1),z(!1),await et()},[et]),ec=(0,l.useMemo)(()=>f(ei,eo),[ei,eo]),ed=(0,$.N4)({data:ea,columns:ec,state:{sorting:K,columnVisibility:x,rowSelection:h,columnFilters:V,pagination:G},enableRowSelection:!0,onRowSelectionChange:g,onSortingChange:e=>{let t="function"==typeof e?e(K):e;J(t),t.length>0?o(a,{sort:t[0].id,order:t[0].desc?"desc":"asc",page:"1"}):o(a,{sort:null,order:null,page:"1"})},onColumnFiltersChange:_,onColumnVisibilityChange:v,onPaginationChange:e=>{let t="function"==typeof e?e(G):e;Q(t),o(a,{page:(t.pageIndex+1).toString(),size:t.pageSize.toString()})},getCoreRowModel:(0,q.HT)(),getFilteredRowModel:(0,q.hM)(),getPaginationRowModel:(0,q.kW)(),getSortedRowModel:(0,q.h5)(),getFacetedRowModel:(0,q.kQ)(),getFacetedUniqueValues:(0,q.oS)(),manualPagination:!0,pageCount:es,manualSorting:!0,manualFiltering:!0}),eu=(0,l.useCallback)(e=>{O(e),C(!0),R.current&&clearTimeout(R.current),R.current=setTimeout(()=>{_(t=>(function(e,t){let a=e.filter(e=>"key"!==e.id);return t&&a.push({id:"key",value:t}),a})(t,e)),o(a,{key:null!=e?e:null,page:"1"}),Q(e=>({...e,pageIndex:0})),C(!1)},500)},[o,a]),em=(0,l.useCallback)(e=>{"all"===e?_(e=>e.filter(e=>"type"!==e.id)):_(t=>{let a=t.filter(e=>"type"!==e.id);return a.push({id:"type",value:e}),a}),o(a,{type:"all"===e?null:e,page:"1"}),Q(e=>({...e,pageIndex:0}))},[o,a]),ep=e=>{let s=a.startsWith("/admin"),l=a.split("/")[1],r=s?"/admin/asset/".concat(e.id):"/".concat(l,"/asset/").concat(e.id);t.push(r)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Assets"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[j>0&&(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,s.jsxs)("span",{children:["Total: ",j," asset",1!==j?"s":""]})}),(0,s.jsxs)(n.$,{onClick:()=>{p("create"),u(!0)},className:"flex items-center justify-center",children:[(0,s.jsx)(r.A,{className:"mr-2 h-4 w-4"}),"Create"]})]})]}),X&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,s.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load assets. Please try again."}),(0,s.jsx)(n.$,{variant:"outline",className:"mt-2",onClick:()=>et(),children:"Retry"})]}),(0,s.jsx)(T,{table:ed,types:[{value:"0",label:"String"},{value:"1",label:"Secret"}],onSearch:eu,onTypeChange:em,searchValue:D,isFiltering:ee,isPending:S}),(0,s.jsx)(y.b,{data:null!=ea?ea:[],columns:ec,onRowClick:e=>{d||ep(e)},table:ed,isLoading:ee,totalCount:j}),(0,s.jsx)(H.d,{currentPage:G.pageIndex+1,pageSize:G.pageSize,totalCount:j,totalPages:es,isLoading:ee,isChangingPageSize:A,isUnknownTotalCount:el,rowsLabel:"assets",onPageChange:e=>{Q({...G,pageIndex:e-1}),o(a,{page:e.toString()})},onPageSizeChange:e=>{z(!0);let t=Math.floor(G.pageIndex*G.pageSize/e);Q({pageSize:e,pageIndex:t}),o(a,{size:e.toString(),page:(t+1).toString()})}})]}),(0,s.jsx)(E,{isOpen:d,onClose:()=>{u(!1),en(null)},mode:m,onCreated:()=>et(),existingKeys:ea.map(e=>e.key),asset:er})]})}M.z.object({id:M.z.string(),key:M.z.string(),type:M.z.number(),description:M.z.string(),createdBy:M.z.string()})},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var s=a(95155);a(12115);var l=a(24265),r=a(36928);function n(e){let{className:t,...a}=e;return(0,s.jsx)(l.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},88262:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,d:()=>r});var s=a(12115);let l=(0,s.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function r(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}},89917:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,2178,8852,5699,5594,8523,9483,3085,4727,7057,5224,8441,1684,7358],()=>t(63668)),_N_E=e.O()}]);