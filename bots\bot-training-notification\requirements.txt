# OpenAutomate Bo<PERSON> Dependencies
# This bot supports Vietnamese text throughout the entire process

# Python version requirement
# Requires Python 3.7+ for typing support and modern features

# Optional: OpenAutomate Agent SDK (install manually if needed)
# openautomateagent

# Common automation libraries (uncomment as needed)
requests>=2.25.0          # For web requests and APIs
#beautifulsoup4>=4.9.0     # For web scraping  
#selenium>=4.0.0           # For browser automation

# Excel and data processing with Vietnamese text support
openpyxl>=3.0.9           # For Excel files with UTF-8 encoding support
pandas>=1.5.0             # For data processing with Unicode support

# Document processing with Vietnamese text support
docxtpl>=0.16.4           # For Word document template processing (supports UTF-8)
docx2pdf>=0.1.8           # For converting DOCX to PDF (preserves Vietnamese text)

# Email automation via Outlook (supports Unicode)
pywin32>=306              # For Outlook COM automation with Vietnamese text

# Additional libraries for text processing
#pillow>=8.0.0            # For image processing

# File processing (built-in modules with UTF-8 support)
# pathlib (built-in Python 3.4+)
# configparser (built-in Python 3.0+)
# csv (built-in)
# json (built-in)
# xml.etree.ElementTree (built-in)
# tempfile (built-in)
# os (built-in)
# re (built-in)
# logging (built-in)
# datetime (built-in)
# typing (built-in Python 3.5+)

# Note: All libraries used in this bot support UTF-8 encoding for Vietnamese text
# Ensure your Python environment is configured with UTF-8 locale 
# Minimum Python version: 3.7+ 