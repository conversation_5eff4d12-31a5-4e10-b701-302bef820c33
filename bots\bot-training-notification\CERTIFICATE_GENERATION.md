# Certificate Generation with docxtpl

This module provides functionality to generate certificates from Microsoft Word templates using the `docxtpl` library. It allows you to replace placeholders/bookmarks in Word templates with actual student data.

## Installation

First, install the required dependency:

```bash
pip install docxtpl
```

Or install all dependencies from requirements.txt:

```bash
pip install -r requirements.txt
```

## Quick Start

### 1. Create a Word Template

1. Open Microsoft Word and create a new document
2. Design your certificate layout (add borders, logos, formatting, etc.)
3. Insert placeholders using Jinja2 syntax: `{{ placeholder_name }}`
4. Save the document as a `.docx` file

Example template content:
```
CERTIFICATE OF COMPLETION

This is to certify that {{ student_name }} has successfully completed 
the course "{{ course_name }}" on {{ completion_date }} with a grade of {{ grade }}.

Instructor: {{ instructor_name }}
Certificate ID: {{ certificate_id }}
Date Issued: {{ current_date }}
```

### 2. Use the CertificateGenerator Class

```python
from tasks.create_cer import CertificateGenerator

# Sample student data
student_data = {
    "student_name": "<PERSON>",
    "course_name": "Python Programming Fundamentals",
    "completion_date": "January 15, 2024",
    "instructor_name": "Dr. <PERSON>",
    "grade": "A+",
    "certificate_id": "CERT-2024-001"
}

# Create generator and generate certificate
generator = CertificateGenerator("certificate_template.docx")
success = generator.generate_certificate(student_data, "john_doe_certificate.docx")

if success:
    print("Certificate generated successfully!")
```

## Available Placeholders

Common placeholders you can use in your Word template:

- `{{ student_name }}` - Student's full name
- `{{ course_name }}` - Name of the completed course
- `{{ completion_date }}` - Date of course completion
- `{{ instructor_name }}` - Name of the instructor
- `{{ grade }}` - Grade or score achieved
- `{{ certificate_id }}` - Unique certificate identifier
- `{{ current_date }}` - Current date (automatically added)
- `{{ institution_name }}` - Name of the institution

You can add any custom placeholders you need - just make sure they match the keys in your student data dictionary.

## Features

### Single Certificate Generation

```python
generator = CertificateGenerator("template.docx")
success = generator.generate_certificate(student_data, "output.docx")
```

### Multiple Certificates (Batch Processing)

```python
students_data = [
    {"student_name": "John Doe", "course_name": "Python", "grade": "A+"},
    {"student_name": "Jane Smith", "course_name": "Python", "grade": "A"},
    # ... more students
]

results = generator.generate_multiple_certificates(
    students_data, 
    "certificates/",
    filename_template="{student_name}_certificate.docx"
)
```

### Template Validation

```python
validation = generator.validate_template()
if validation["is_valid"]:
    print("Template is valid!")
else:
    print(f"Template error: {validation['error']}")
```

## Working with CSV Data

You can easily load student data from CSV files:

```python
import csv

# Load data from CSV
students_data = []
with open('students.csv', 'r') as f:
    reader = csv.DictReader(f)
    students_data = list(reader)

# Generate certificates for all students
generator = CertificateGenerator("template.docx")
results = generator.generate_multiple_certificates(students_data, "output/")
```

Sample CSV format:
```csv
student_name,course_name,completion_date,instructor_name,grade,certificate_id
John Doe,Python Programming,2024-01-15,Dr. Smith,A+,CERT-2024-001
Jane Smith,Python Programming,2024-01-15,Dr. Smith,A,CERT-2024-002
```

## Examples

Run the example script to see the certificate generator in action:

```bash
python examples/certificate_example.py
```

This will demonstrate:
- Single certificate generation
- Batch certificate generation
- Loading data from CSV
- Creating sample data

## File Structure

```
bots/training/
├── tasks/
│   └── create_cer.py          # Main certificate generation module
├── examples/
│   └── certificate_example.py # Usage examples
├── requirements.txt           # Dependencies (includes docxtpl)
└── CERTIFICATE_GENERATION.md # This documentation
```

## Error Handling

The module includes comprehensive error handling:

- **FileNotFoundError**: Template file doesn't exist
- **ValueError**: Invalid file format (not .docx)
- **ImportError**: docxtpl library not installed
- **Template errors**: Invalid template format or syntax

## Tips for Creating Templates

1. **Use clear placeholder names**: `{{ student_name }}` is better than `{{ name }}`
2. **Test with sample data**: Generate a test certificate to verify layout
3. **Include all necessary information**: Name, course, date, signatures, etc.
4. **Format properly**: Use Word's formatting tools for professional appearance
5. **Save as .docx**: Ensure compatibility with docxtpl

## Troubleshooting

### Common Issues

1. **"Template file not found"**
   - Check the file path is correct
   - Ensure the file exists and is accessible

2. **"docxtpl is required"**
   - Install docxtpl: `pip install docxtpl`

3. **"Template file must be a .docx file"**
   - Save your template as .docx format, not .doc or other formats

4. **Placeholders not being replaced**
   - Check placeholder syntax: use `{{ placeholder_name }}`
   - Ensure placeholder names match your data dictionary keys
   - Verify there are no extra spaces in placeholder names

5. **Generated certificate looks wrong**
   - Check your Word template formatting
   - Test with simple placeholders first
   - Ensure template is saved properly

## Advanced Usage

### Custom Filename Templates

```python
# Use multiple fields in filename
filename_template = "{course_name}_{student_name}_{completion_date}.docx"

# Clean special characters automatically
results = generator.generate_multiple_certificates(
    students_data, 
    "output/",
    filename_template
)
```

### Logging

The module uses Python's logging system. Enable logging to see detailed information:

```python
import logging
logging.basicConfig(level=logging.INFO)

# Now certificate generation will show detailed logs
generator = CertificateGenerator("template.docx")
```

## Integration with OpenAutomate

This certificate generation module is designed to work seamlessly with the OpenAutomate bot framework. You can integrate it into your automation workflows for:

- Automated course completion processing
- Bulk certificate generation from student databases
- Integration with learning management systems
- Scheduled certificate generation tasks

For more information about the OpenAutomate framework, see the main README.md file.
