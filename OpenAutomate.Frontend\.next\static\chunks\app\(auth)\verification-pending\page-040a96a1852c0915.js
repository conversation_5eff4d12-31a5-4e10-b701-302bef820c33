(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3446],{5196:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7283:(e,r,t)=>{"use strict";t.d(r,{F:()=>w,fetchApi:()=>y});var s=t(48133),a=t(67938);let n=a.$.api.defaultHeaders,o=!1,i=[],l=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;i.forEach(t=>{e?t.reject(e):t.resolve(r)}),i=[]},c=async e=>{let r={message:e.statusText,status:e.status};try{let t=await e.json();t.message?(r.message=t.message,r.details=t.details||t.message):t.error?(r.message=t.error,r.details=t.error):r.details=JSON.stringify(t)}catch(t){r.details=e.statusText}return r},d=()=>{window.dispatchEvent(new Event("auth:token-expired"))},u=e=>{if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw{message:"Network error. Please check your connection.",status:0,details:e.message};if(e instanceof Error)throw{message:e.message||"Network error occurred",status:0,details:e.stack||e.message};throw{message:"An unexpected error occurred",status:0,details:String(e)}},f=async e=>{if(204===e.status)return{};let r=e.headers.get("content-type"),t=e.headers.get("content-length");if(!r||-1===r.indexOf("application/json")||"0"===t)return{};let s=await e.text();return s?JSON.parse(s):{}},g=async()=>{if(o)return new Promise((e,r)=>{i.push({resolve:e,reject:r})});o=!0;try{let e=(await y("api/auth/refresh-token",{method:"POST",credentials:"include"})).token;return(0,s.O5)(e),l(null,e),e}catch(e){throw l(e),e}finally{o=!1}},h=async(e,r,t,s,a)=>{if(e.includes("refresh-token")||e.includes("login"))return d(),null;try{let e=await g();if(!e)return null;let s=v(t,a);s.Authorization="Bearer ".concat(e);let{body:n}=m(a),o=await fetch(r,{...t,body:n,headers:s,credentials:"include"});if(o.ok)return f(o);return null}catch(e){return d(),console.error("Token refresh failed:",e),null}},p=e=>{if(e.startsWith("http"))return e;let r=e.startsWith("/")?e.slice(1):e;return"".concat(a.$.api.baseUrl,"/").concat(r)},m=e=>e?e instanceof FormData?{body:e,headers:{}}:{body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}:{body:void 0,headers:{}},v=(e,r)=>{let t={...r instanceof FormData?{Accept:n.Accept}:{...n},...e.headers};if(!t.Authorization){let e=(0,s.c4)();e&&(t.Authorization="Bearer ".concat(e))}return t};async function y(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments.length>2?arguments[2]:void 0,s=p(e),{body:a,headers:n}=m(t),o={...v(r,t),...n};try{let n=await fetch(s,{...r,body:a,headers:o,credentials:"include"});if(n.ok)return f(n);if(401===n.status){let a=await h(e,s,r,o,t);if(a)return a}throw await c(n)}catch(e){if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;throw u(e)}}let w={get:(e,r)=>y(e,{...r,method:"GET"}),post:(e,r,t)=>{let{body:s,headers:a}=m(r);return y(e,{...t,method:"POST",body:s,headers:{...a,...null==t?void 0:t.headers}},r)},put:(e,r,t)=>{let{body:s,headers:a}=m(r);return y(e,{...t,method:"PUT",body:s,headers:{...a,...null==t?void 0:t.headers}},r)},patch:(e,r,t)=>{let{body:s,headers:a}=m(r);return y(e,{...t,method:"PATCH",body:s,headers:{...a,...null==t?void 0:t.headers}},r)},delete:(e,r)=>y(e,{...r,method:"DELETE"})}},12187:(e,r,t)=>{"use strict";function s(e){if(!e)return"An unexpected error occurred";if("string"==typeof e)return e;if("object"==typeof e&&null!==e&&"status"in e&&"message"in e&&"number"==typeof e.status&&"string"==typeof e.message)return 0===e.status?e.message||"Network error. Please check your connection.":function(e){var r;if(e.details){let r=function(e){try{var r,t;let s=JSON.parse(e);return null!==(t=null!==(r=s.error)&&void 0!==r?r:s.message)&&void 0!==t?t:null}catch(e){return null}}(e.details);return r||e.details}return null!==(r=e.message)&&void 0!==r?r:"An error occurred"}(e);if(e instanceof Error)return e.message.includes("Failed to fetch")||e.message.includes("NetworkError")?"Network error. Please check your connection.":e.message;let r=function(e){if(null!==e&&"object"==typeof e&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response){let r=e.response.data;if("object"==typeof r&&null!==r){if("message"in r&&"string"==typeof r.message)return r.message;if("error"in r&&"string"==typeof r.error)return r.error}if("string"==typeof r)return r}return null}(e);if(null!==r)return r;if("object"==typeof e&&null!==e){let r=void 0!==e.error&&null!==e.error&&"string"==typeof e.error?e.error:void 0!==e.message&&null!==e.message&&"string"==typeof e.message?e.message:null;if(null!==r)return r}return"An unexpected error occurred"}function a(e){return{title:"Error",description:s(e),variant:function(e){if("object"==typeof e&&null!==e&&"status"in e)return e.status<400?"default":"destructive";if("string"==typeof e){let r=e.toLowerCase();if(r.includes("warning")||r.includes("info"))return"default"}return"destructive"}(e)}}t.d(r,{PE:()=>s,m4:()=>a})},13052:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},22370:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(7283);let a={login:"api/auth/login",register:"api/auth/register",user:"api/auth/user",profile:"api/account/profile",refreshToken:"api/auth/refresh-token",revokeToken:"api/auth/revoke-token",forgotPassword:"api/auth/forgot-password",resetPassword:"api/auth/reset-password",changeUserName:"api/account/info",changePassword:"api/account/change-password",resendVerification:"api/email/resend",resendVerificationByEmail:"api/email/resend-public",verifyEmail:"api/email/verify"},n={login:async e=>await s.F.post(a.login,e,{credentials:"include"}),register:async e=>(await s.F.post(a.register,e,{credentials:"include"})).user,getCurrentUser:async()=>await s.F.get(a.user),getUserProfile:async()=>await s.F.get(a.profile),refreshToken:async()=>await s.F.post(a.refreshToken,{},{credentials:"include"}),logout:async()=>{await s.F.post(a.revokeToken,{},{credentials:"include"})},forgotPassword:async e=>{try{console.log("Sending forgot password request for email:",e.email),await s.F.post(a.forgotPassword,e),console.log("Forgot password request sent successfully")}catch(e){throw console.error("Forgot password request failed:",e),e}},resetPassword:async e=>{try{let r={email:e.email.trim(),token:function(e){let r=e.trim();r.includes(" ")&&(console.warn("Token contains spaces - cleaning up"),r=r.replace(/\s/g,""));try{if(r.includes("%"))return console.log("Token appears to be URL encoded - decoding"),decodeURIComponent(r)}catch(e){console.warn("Error trying to decode token:",e)}return r}(e.token.trim()),newPassword:e.newPassword,confirmPassword:e.confirmPassword};console.log("Sending reset password request with data:",{email:r.email,tokenLength:r.token.length,tokenPrefix:r.token.substring(0,10)+"...",newPassword:r.newPassword?"******":"MISSING",confirmPassword:r.confirmPassword?"******":"MISSING"}),console.log("Request payload structure:",JSON.stringify({...r,newPassword:"*****",confirmPassword:"*****"},null,2));let t=await s.F.post(a.resetPassword,r,{headers:{"Content-Type":"application/json"}});console.log("Password reset request successful",t)}catch(e){!function(e){if(console.error("Reset password request failed with error:",e),(null==e?void 0:e.status)&&console.error("Status code:",e.status),(null==e?void 0:e.message)&&console.error("Error message:",e.message),(null==e?void 0:e.details)&&console.error("Error details:",e.details),(null==e?void 0:e.errors)&&console.error("Validation errors:",e.errors),null==e?void 0:e.errors){let r=Object.entries(e.errors).map(e=>{let[r,t]=e;return"".concat(r,": ").concat(Array.isArray(t)?t.join(", "):t)}).join("; ");throw Error("Password reset failed with validation errors: ".concat(r))}if(null==e?void 0:e.message)throw e;throw Error("Password reset failed. Please try again.")}(e)}},changeUserName:async(e,r)=>{await s.F.put(a.changeUserName,r)},changePassword:async e=>{await s.F.post(a.changePassword,e)},resendVerificationEmail:async e=>{await s.F.post(a.resendVerification,{email:e})},resendVerificationEmailByEmail:async e=>{await s.F.post(a.resendVerificationByEmail,{email:e})},verifyEmail:async e=>{try{return await s.F.get("".concat(a.verifyEmail,"?token=").concat(e)),!0}catch(e){return console.error("Verification failed",e),!1}}}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>c,r:()=>l});var s=t(95155),a=t(12115),n=t(66634),o=t(74466),i=t(36928);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,r)=>{let{className:t,variant:a,size:o,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,s.jsx)(u,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:o,className:t})),ref:r,...d})});c.displayName="Button"},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},36928:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(52596),a=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},42355:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48133:(e,r,t)=>{"use strict";t.d(r,{O5:()=>c,c4:()=>l,gV:()=>u,m_:()=>f,wz:()=>d});var s=t(67938);let a=s.$.auth.tokenStorageKey,n=s.$.auth.userStorageKey,o=null,i=null,l=()=>{if(o)return o;try{let e=localStorage.getItem(a);if(e)return o=e,e}catch(e){console.error("Error accessing localStorage",e)}return null},c=e=>{o=e;try{e?localStorage.setItem(a,e):localStorage.removeItem(a)}catch(e){console.error("Error accessing localStorage",e)}},d=()=>{if(i)return i;try{let e=localStorage.getItem(n);if(e)try{let r=JSON.parse(e);return i=r,r}catch(e){console.error("Error parsing stored user data",e)}}catch(e){console.error("Error accessing localStorage",e)}return null},u=e=>{i=e;try{e?localStorage.setItem(n,JSON.stringify(e)):localStorage.removeItem(n)}catch(e){console.error("Error accessing localStorage",e)}},f=()=>{o=null,i=null;try{localStorage.removeItem(a),localStorage.removeItem(n)}catch(e){console.error("Error accessing localStorage",e)}}},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54416:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54629:(e,r,t)=>{"use strict";t.d(r,{F:()=>C});var s=t(54785),a=t(54416),n=t(51154),o=t(42355),i=t(13052),l=t(74126),c=t(381),d=t(81586),u=t(44020),f=t(84616),g=t(94788),h=t(71007),p=t(92138),m=t(62530),v=t(30130),y=t(93509),w=t(42148),x=t(59099),b=t(18175),k=t(5196),j=t(99890),A=t(57434),N=t(27213),P=t(85690),S=t(82178),E=t(57340),F=t(50741),T=t(17051),R=t(34835),O=t(53904),V=t(45347),_=t(52631),I=t(28883);let C={logo:s.A,close:a.A,Spinner:n.A,chevronLeft:o.A,chevronRight:i.A,trash:l.A,settings:c.A,billing:d.A,ellipsis:u.A,add:f.A,warning:g.A,user:h.A,arrowRight:p.A,help:g.A,pizza:m.A,sun:v.A,moon:y.A,laptop:w.A,gitHub:x.A,twitter:b.A,check:k.A,file:j.A,fileText:A.A,image:N.A,play:P.A,pause:S.A,home:E.A,chart:F.A,cog:T.A,logout:R.A,refresh:O.A,about:V.A,guide:_.A,contact:I.A}},55365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>l,TN:()=>d,XL:()=>c});var s=t(95155),a=t(12115),n=t(74466),o=t(36928);let i=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),l=a.forwardRef((e,r)=>{let{className:t,variant:a,...n}=e;return(0,s.jsx)("div",{ref:r,role:"alert",className:(0,o.cn)(i({variant:a}),t),...n})});l.displayName="Alert";let c=a.forwardRef((e,r)=>{let{className:t,children:a,...n}=e;return a||console.warn("AlertTitle must have content for accessibility"),(0,s.jsx)("h5",{ref:r,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",t),...n,children:a})});c.displayName="AlertTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",t),...a})});d.displayName="AlertDescription"},66634:(e,r,t)=>{"use strict";t.d(r,{DX:()=>o});var s=t(12115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var n=t(95155),o=function(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var o;let e,i;let l=(o=t,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),c=function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{let r=n(...e);return a(...e),r}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(c.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}(r,l):l),s.cloneElement(t,c)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:a,...o}=e,i=s.Children.toArray(a),c=i.find(l);if(c){let e=c.props.children,a=i.map(r=>r!==c?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...o,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(r,{...o,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}("Slot"),i=Symbol("radix.slottable");function l(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>d});var s=t(95155);t(12115);var a=t(36928);function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function l(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function c(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},67938:(e,r,t)=>{"use strict";t.d(r,{$:()=>s});let s={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>o});var s=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,o=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:i}=r,l=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],s=null==i?void 0:i[e];if(null===r)return null;let n=a(r)||a(s);return o[e][n]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return n(e,l,null==r?void 0:null===(s=r.compoundVariants)||void 0===s?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...c}[r]):({...i,...c})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},77153:(e,r,t)=>{Promise.resolve().then(t.bind(t,83256))},83256:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(95155),a=t(12115),n=t(35695),o=t(6874),i=t.n(o),l=t(30285),c=t(66695),d=t(55365),u=t(22370),f=t(54629),g=t(12187);function h(){return(0,s.jsx)("div",{className:"container flex-1 flex items-center justify-center py-12",children:(0,s.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(c.aR,{className:"space-y-1",children:[(0,s.jsx)(c.ZB,{className:"text-2xl font-bold text-center",children:"Loading..."}),(0,s.jsx)(c.BT,{className:"text-center",children:"Please wait while we load your verification status"})]}),(0,s.jsx)(c.Wu,{className:"space-y-6 flex justify-center",children:(0,s.jsx)(f.F.Spinner,{className:"h-8 w-8 animate-spin"})})]})})}function p(){let e=(0,n.useSearchParams)(),r=e.get("email"),t=e.get("returnUrl"),[o,h]=(0,a.useState)(!1),[p,m]=(0,a.useState)(null),v=async()=>{if(r){h(!0);try{await u.Z.resendVerificationEmailByEmail(r),m({type:"success",title:"Verification email sent",message:"Please check your inbox for the verification link."})}catch(e){console.error("Failed to resend verification email:",e),m({type:"error",title:"Failed to resend email",message:(0,g.PE)(e)})}finally{h(!1)}}},y=t?"/login?returnUrl=".concat(encodeURIComponent(t)):"/login";return(0,s.jsx)("div",{className:"container flex-1 flex items-center justify-center py-12",children:(0,s.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(c.aR,{className:"space-y-1",children:[(0,s.jsx)(c.ZB,{className:"text-2xl font-bold text-center",children:"Verify your email"}),(0,s.jsxs)(c.BT,{className:"text-center",children:["We've sent a verification email to"," ",(0,s.jsx)("span",{className:"font-medium",children:null!=r?r:"your email address"})]})]}),(0,s.jsxs)(c.Wu,{className:"space-y-6",children:[p&&(0,s.jsxs)(d.Fc,{variant:"error"===p.type?"destructive":"default",children:[(0,s.jsx)(d.XL,{children:p.title}),(0,s.jsx)(d.TN,{children:p.message})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[(0,s.jsx)("p",{children:"Please check your inbox and click the link in the email to verify your account."}),(0,s.jsx)("p",{className:"mt-2",children:"If you don't see the email, check your spam folder or request a new verification email."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(l.$,{onClick:v,variant:"outline",className:"w-full",disabled:o,children:[o&&(0,s.jsx)(f.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),o?"Sending...":"Resend verification email"]}),(0,s.jsx)(i(),{href:y,children:(0,s.jsx)(l.$,{variant:"ghost",className:"w-full",children:"Return to login"})})]})]})]})})}function m(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(h,{}),children:(0,s.jsx)(p,{})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,6874,8594,8441,1684,7358],()=>r(77153)),_N_E=e.O()}]);