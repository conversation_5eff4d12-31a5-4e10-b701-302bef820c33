"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7057],{12187:(e,r,t)=>{function s(e){if(!e)return"An unexpected error occurred";if("string"==typeof e)return e;if("object"==typeof e&&null!==e&&"status"in e&&"message"in e&&"number"==typeof e.status&&"string"==typeof e.message)return 0===e.status?e.message||"Network error. Please check your connection.":function(e){var r;if(e.details){let r=function(e){try{var r,t;let s=JSON.parse(e);return null!==(t=null!==(r=s.error)&&void 0!==r?r:s.message)&&void 0!==t?t:null}catch(e){return null}}(e.details);return r||e.details}return null!==(r=e.message)&&void 0!==r?r:"An error occurred"}(e);if(e instanceof Error)return e.message.includes("Failed to fetch")||e.message.includes("NetworkError")?"Network error. Please check your connection.":e.message;let r=function(e){if(null!==e&&"object"==typeof e&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response){let r=e.response.data;if("object"==typeof r&&null!==r){if("message"in r&&"string"==typeof r.message)return r.message;if("error"in r&&"string"==typeof r.error)return r.error}if("string"==typeof r)return r}return null}(e);if(null!==r)return r;if("object"==typeof e&&null!==e){let r=void 0!==e.error&&null!==e.error&&"string"==typeof e.error?e.error:void 0!==e.message&&null!==e.message&&"string"==typeof e.message?e.message:null;if(null!==r)return r}return"An unexpected error occurred"}function n(e){return{title:"Error",description:s(e),variant:function(e){if("object"==typeof e&&null!==e&&"status"in e)return e.status<400?"default":"destructive";if("string"==typeof e){let r=e.toLowerCase();if(r.includes("warning")||r.includes("info"))return"default"}return"destructive"}(e)}}t.d(r,{PE:()=>s,m4:()=>n})},22370:(e,r,t)=>{t.d(r,{Z:()=>a});var s=t(7283);let n={login:"api/auth/login",register:"api/auth/register",user:"api/auth/user",profile:"api/account/profile",refreshToken:"api/auth/refresh-token",revokeToken:"api/auth/revoke-token",forgotPassword:"api/auth/forgot-password",resetPassword:"api/auth/reset-password",changeUserName:"api/account/info",changePassword:"api/account/change-password",resendVerification:"api/email/resend",resendVerificationByEmail:"api/email/resend-public",verifyEmail:"api/email/verify"},a={login:async e=>await s.F.post(n.login,e,{credentials:"include"}),register:async e=>(await s.F.post(n.register,e,{credentials:"include"})).user,getCurrentUser:async()=>await s.F.get(n.user),getUserProfile:async()=>await s.F.get(n.profile),refreshToken:async()=>await s.F.post(n.refreshToken,{},{credentials:"include"}),logout:async()=>{await s.F.post(n.revokeToken,{},{credentials:"include"})},forgotPassword:async e=>{try{console.log("Sending forgot password request for email:",e.email),await s.F.post(n.forgotPassword,e),console.log("Forgot password request sent successfully")}catch(e){throw console.error("Forgot password request failed:",e),e}},resetPassword:async e=>{try{let r={email:e.email.trim(),token:function(e){let r=e.trim();r.includes(" ")&&(console.warn("Token contains spaces - cleaning up"),r=r.replace(/\s/g,""));try{if(r.includes("%"))return console.log("Token appears to be URL encoded - decoding"),decodeURIComponent(r)}catch(e){console.warn("Error trying to decode token:",e)}return r}(e.token.trim()),newPassword:e.newPassword,confirmPassword:e.confirmPassword};console.log("Sending reset password request with data:",{email:r.email,tokenLength:r.token.length,tokenPrefix:r.token.substring(0,10)+"...",newPassword:r.newPassword?"******":"MISSING",confirmPassword:r.confirmPassword?"******":"MISSING"}),console.log("Request payload structure:",JSON.stringify({...r,newPassword:"*****",confirmPassword:"*****"},null,2));let t=await s.F.post(n.resetPassword,r,{headers:{"Content-Type":"application/json"}});console.log("Password reset request successful",t)}catch(e){!function(e){if(console.error("Reset password request failed with error:",e),(null==e?void 0:e.status)&&console.error("Status code:",e.status),(null==e?void 0:e.message)&&console.error("Error message:",e.message),(null==e?void 0:e.details)&&console.error("Error details:",e.details),(null==e?void 0:e.errors)&&console.error("Validation errors:",e.errors),null==e?void 0:e.errors){let r=Object.entries(e.errors).map(e=>{let[r,t]=e;return"".concat(r,": ").concat(Array.isArray(t)?t.join(", "):t)}).join("; ");throw Error("Password reset failed with validation errors: ".concat(r))}if(null==e?void 0:e.message)throw e;throw Error("Password reset failed. Please try again.")}(e)}},changeUserName:async(e,r)=>{await s.F.put(n.changeUserName,r)},changePassword:async e=>{await s.F.post(n.changePassword,e)},resendVerificationEmail:async e=>{await s.F.post(n.resendVerification,{email:e})},resendVerificationEmailByEmail:async e=>{await s.F.post(n.resendVerificationByEmail,{email:e})},verifyEmail:async e=>{try{return await s.F.get("".concat(n.verifyEmail,"?token=").concat(e)),!0}catch(e){return console.error("Verification failed",e),!1}}}},59385:(e,r,t)=>{t.d(r,{i:()=>s});var s=function(e){return e[e.User=0]="User",e[e.Admin=1]="Admin",e}({})},67057:(e,r,t)=>{t.d(r,{c:()=>m,AuthProvider:()=>h,A:()=>p});var s=t(95155),n=t(12115),a=t(22370),o=t(35695),i=t(59385),l=t(48133);let u=function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];for(var r=arguments.length,t=Array(r>2?r-2:0),s=2;s<r;s++)t[s-2]=arguments[s]},c={log:u,success:function(e){for(var r=arguments.length,t=Array(r>1?r-1:0),s=1;s<r;s++)t[s-1]=arguments[s];u(e,"success",...t)},error:function(e){for(var r=arguments.length,t=Array(r>1?r-1:0),s=1;s<r;s++)t[s-1]=arguments[s];u(e,"error",...t)},warning:function(e){for(var r=arguments.length,t=Array(r>1?r-1:0),s=1;s<r;s++)t[s-1]=arguments[s];u(e,"warning",...t)},auth:(e,r)=>{}},d={logout:()=>{c.auth("User Logged Out",{timestamp:new Date().toLocaleString(),status:"success"})}};var f=t(67938),g=t(12187);let m=(0,n.createContext)(void 0),w=f.$.auth.tokenRefreshInterval;function h(e){let{children:r}=e,[t,u]=(0,n.useState)(null),[h,p]=(0,n.useState)(null),[y,v]=(0,n.useState)(!0),[k,P]=(0,n.useState)(!1),[E,b]=(0,n.useState)(null),N=(0,o.useRouter)(),A=(0,o.useParams)(),U=(0,n.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"profile fetch";try{let r=await a.Z.getUserProfile();p(r),c.success("User profile loaded successfully during ".concat(e))}catch(r){c.warning("Failed to load user profile during ".concat(e,":"),r)}},[]),F=(null==t?void 0:t.systemRole)===i.i.Admin||(null==t?void 0:t.systemRole)==="Admin",R=(0,n.useCallback)((e,r,t)=>{if(!h)return!1;if(F)return!0;let s=t||A.tenant;if(!s)return!1;let n=h.organizationUnits.find(e=>e.slug===s);if(!n)return!1;let a=n.permissions.find(r=>r.resourceName===e);return!!a&&a.permission>=r},[h,F,A.tenant]),S=(0,n.useCallback)(async()=>{try{let e=await a.Z.refreshToken();(0,l.O5)(e.token);let r=e.user||{id:e.id,email:e.email,firstName:e.firstName,lastName:e.lastName,systemRole:e.systemRole||i.i.User};return(0,l.gV)(r),u(r),await U("token refresh"),c.success("Token refreshed successfully"),!0}catch(e){return c.error("Token refresh failed:",e),(0,l.m_)(),u(null),p(null),!1}},[U]);(0,n.useEffect)(()=>{let e=null;if(t){e=setInterval(()=>{S()},w);let r=()=>{"visible"===document.visibilityState&&S()};return document.addEventListener("visibilitychange",r),()=>{e&&clearInterval(e),document.removeEventListener("visibilitychange",r)}}return()=>{e&&clearInterval(e)}},[t,S]),(0,n.useEffect)(()=>{let e=()=>{c.warning("Authentication token expired"),(0,l.m_)(),u(null),p(null),N.push("/login")};return window.addEventListener("auth:token-expired",e),()=>{window.removeEventListener("auth:token-expired",e)}},[N]),(0,n.useEffect)(()=>{(async()=>{v(!0);try{let e=(0,l.c4)(),r=(0,l.wz)();if(e&&r){u(r),c.log("User restored from storage","info",r);try{let e=await a.Z.getCurrentUser();u(e),c.success("User data refreshed from API"),await U("initialization")}catch(e){c.warning("Failed to get current user, attempting token refresh",e);try{let e=await a.Z.refreshToken();(0,l.O5)(e.token);let r=e.user||{id:e.id,email:e.email,firstName:e.firstName,lastName:e.lastName,systemRole:e.systemRole||i.i.User};console.log(r),(0,l.gV)(r),u(r),await U("initialization token refresh"),c.success("Token refreshed successfully during init")}catch(e){c.error("Token refresh failed during init:",e),(0,l.m_)(),u(null),p(null)}}}}catch(e){c.error("Authentication initialization failed:",e),(0,l.m_)(),u(null),p(null)}finally{v(!1)}})()},[]);let C=(0,n.useCallback)(async e=>{v(!0),b(null);try{let r=await a.Z.login(e);(0,l.O5)(r.token);let t=r.user||{id:r.id,email:r.email,firstName:r.firstName,lastName:r.lastName,systemRole:r.systemRole||i.i.User};return(0,l.gV)(t),u(t),await U("login"),c.success("User logged in: ".concat(t.email)),N.push(f.$.paths.defaultRedirect),t}catch(e){throw b((0,g.PE)(e)),c.error("Login failed:",e),e}finally{v(!1)}},[N,U]),T=(0,n.useCallback)(async e=>{v(!0),b(null);try{let r=await a.Z.register(e);return c.auth("Registration Successful",{email:e.email,message:"Verification email sent"}),r}catch(r){let e=(0,g.PE)(r);throw b(e),c.error("Registration failed:",e),r}finally{v(!1)}},[]),V=(0,n.useCallback)(async()=>{v(!0),P(!0);try{await a.Z.logout(),d.logout()}catch(e){c.error("Logout error:",e)}finally{(0,l.m_)(),u(null),p(null),P(!1),N.push("/login"),v(!1)}},[N]),j=(0,n.useCallback)(e=>{if(t){let r={...t,...e};u(r),(0,l.gV)(r),c.success("User data updated successfully")}},[t]);return(0,s.jsx)(m.Provider,{value:(0,n.useMemo)(()=>({user:t,userProfile:h,isLoading:y,isAuthenticated:!!t,isSystemAdmin:F,isLogout:k,login:C,register:T,logout:V,refreshToken:S,updateUser:j,hasPermission:R,error:E}),[t,h,y,F,k,C,T,V,S,j,R,E]),children:r})}let p=()=>{let e=(0,n.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}}]);