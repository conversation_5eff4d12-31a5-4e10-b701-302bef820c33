(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2316],{1243:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3294:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var n=t(95155),a=t(12115),s=t(35695),i=t(30285),l=t(55365),o=t(40646),c=t(54861),d=t(1243),u=t(67938);function f(){return(0,n.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"w-full max-w-md p-6 space-y-4 text-center",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold",children:"Verifying your email..."}),(0,n.jsx)("p",{className:"text-gray-500",children:"Please wait while we confirm your email verification."})]})})}function v(){let e=(0,s.useSearchParams)(),r=(0,s.useRouter)(),[t,v]=(0,a.useState)(!0),p="true"===e.get("success"),h=e.get("reason");(0,a.useEffect)(()=>{let e=setTimeout(()=>{v(!1)},500);return()=>clearTimeout(e)},[]);let m=()=>{r.push(u.$.paths.auth.login)};return t?(0,n.jsx)(f,{}):(0,n.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-4",children:(0,n.jsx)("div",{className:"w-full max-w-md p-6 space-y-6 text-center",children:p?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.A,{className:"mx-auto h-16 w-16 text-green-500"}),(0,n.jsx)("h1",{className:"text-2xl font-bold",children:"Email Verified!"}),(0,n.jsx)("p",{className:"text-gray-500",children:"Your email has been successfully verified. You can now login to your account."}),(0,n.jsx)(i.$,{className:"w-full",onClick:m,children:"Go to Login"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A,{className:"mx-auto h-16 w-16 text-red-500"}),(0,n.jsx)("h1",{className:"text-2xl font-bold",children:"Verification Failed"}),(0,n.jsxs)(l.Fc,{variant:"destructive",className:"text-left",children:[(0,n.jsx)(d.A,{className:"h-4 w-4"}),(0,n.jsx)(l.XL,{children:"Error"}),(0,n.jsxs)(l.TN,{children:["invalid-token"===h&&"The verification link is invalid or has expired.","verification-failed"===h&&"We couldn't verify your email. Please try again later.","missing-token"===h&&"No verification token was provided.","server-error"===h&&"A server error occurred. Please try again later.",!h&&"An unknown error occurred during verification."]})]}),(0,n.jsx)("p",{className:"text-gray-500",children:"If you continue to have problems, please contact support."}),(0,n.jsx)("div",{className:"space-y-3",children:(0,n.jsx)(i.$,{className:"w-full",onClick:m,children:"Go to Login"})})]})})})}function p(){return(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(f,{}),children:(0,n.jsx)(v,{})})}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>c,r:()=>o});var n=t(95155),a=t(12115),s=t(66634),i=t(74466),l=t(36928);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:c=!1,...d}=e,u=c?s.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,l.cn)(o({variant:a,size:i,className:t})),ref:r,...d})});c.displayName="Button"},35695:(e,r,t)=>{"use strict";var n=t(18999);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},36928:(e,r,t)=>{"use strict";t.d(r,{cn:()=>s});var n=t(52596),a=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,n.$)(r))}},40646:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54861:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d,XL:()=>c});var n=t(95155),a=t(12115),s=t(74466),i=t(36928);let l=(0,s.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,...s}=e;return(0,n.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(l({variant:a}),t),...s})});o.displayName="Alert";let c=a.forwardRef((e,r)=>{let{className:t,children:a,...s}=e;return a||console.warn("AlertTitle must have content for accessibility"),(0,n.jsx)("h5",{ref:r,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...s,children:a})});c.displayName="AlertTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...a})});d.displayName="AlertDescription"},66634:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i});var n=t(12115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var s=t(95155),i=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){var i;let e,l;let o=(i=t,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,r){let t={...r};for(let n in r){let a=e[n],s=r[n];/^on[A-Z]/.test(n)?a&&s?t[n]=(...e)=>{let r=s(...e);return a(...e),r}:a&&(t[n]=a):"style"===n?t[n]={...a,...s}:"className"===n&&(t[n]=[a,s].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(c.ref=r?function(...e){return r=>{let t=!1,n=e.map(e=>{let n=a(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():a(e[r],null)}}}}(r,o):o),n.cloneElement(t,c)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:a,...i}=e,l=n.Children.toArray(a),c=l.find(o);if(c){let e=c.props.children,a=l.map(r=>r!==c?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(r,{...i,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}("Slot"),l=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},67938:(e,r,t)=>{"use strict";t.d(r,{$:()=>n});let n={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var n=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,i=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return s(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:l}=r,o=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],n=null==l?void 0:l[e];if(null===r)return null;let s=a(r)||a(n);return i[e][s]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return s(e,o,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...c}[r]):({...l,...c})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},85556:(e,r,t)=>{Promise.resolve().then(t.bind(t,3294))}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,8441,1684,7358],()=>r(85556)),_N_E=e.O()}]);