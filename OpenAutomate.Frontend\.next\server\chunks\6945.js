"use strict";exports.id=6945,exports.ids=[6945],exports.modules={6945:(e,t,n)=>{n.d(t,{C1:()=>P,bL:()=>j});var r=n(43210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function l(...e){return r.useCallback(o(...e),e)}var u=n(60687);function a(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var s=n(67427),c=n(83721),d=n(88805),f=globalThis?.document?r.useLayoutEffect:()=>{},p=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[i,o]=r.useState(),l=r.useRef(null),u=r.useRef(e),a=r.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=m(l.current);a.current="mounted"===s?e:"none"},[s]),f(()=>{let t=l.current,n=u.current;if(n!==e){let r=a.current,i=m(t);e?c("MOUNT"):"none"===i||t?.display==="none"?c("UNMOUNT"):n&&r!==i?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),f(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,n=n=>{let r=m(l.current).includes(n.animationName);if(n.target===i&&r&&(c("ANIMATION_END"),!u.current)){let n=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=n)})}},r=e=>{e.target===i&&(a.current=m(l.current))};return i.addEventListener("animationstart",r),i.addEventListener("animationcancel",n),i.addEventListener("animationend",n),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",r),i.removeEventListener("animationcancel",n),i.removeEventListener("animationend",n)}}c("ANIMATION_END")},[i,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),u=l(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||i.isPresent?r.cloneElement(o,{ref:u}):null};function m(e){return e?.animationName||"none"}p.displayName="Presence",n(51215);var y=Symbol("radix.slottable");function v(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}var N=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var l;let e,u;let a=(l=n,(u=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(u=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{o(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(s.ref=t?o(t,a):a),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...o}=e,l=r.Children.toArray(i),a=l.find(v);if(a){let e=a.props.children,i=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,u.jsx)(t,{...o,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),h="Checkbox",[b,g]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),l=n.length;n=[...n,i];let a=t=>{let{scope:n,children:i,...a}=t,s=n?.[e]?.[l]||o,c=r.useMemo(()=>a,Object.values(a));return(0,u.jsx)(s.Provider,{value:c,children:i})};return a.displayName=t+"Provider",[a,function(n,u){let a=u?.[e]?.[l]||o,s=r.useContext(a);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(i,...t)]}(h),[E,w]=b(h),x=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:i,checked:o,defaultChecked:c,required:d,disabled:f,value:p="on",onCheckedChange:m,form:y,...v}=e,[b,g]=r.useState(null),w=l(t,e=>g(e)),x=r.useRef(!1),O=!b||y||!!b.closest("form"),[C,j]=(0,s.i)({prop:o,defaultProp:c??!1,onChange:m,caller:h}),P=r.useRef(C);return r.useEffect(()=>{let e=b?.form;if(e){let t=()=>j(P.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[b,j]),(0,u.jsxs)(E,{scope:n,state:C,disabled:f,children:[(0,u.jsx)(N.button,{type:"button",role:"checkbox","aria-checked":M(C)?"mixed":C,"aria-required":d,"data-state":k(C),"data-disabled":f?"":void 0,disabled:f,value:p,...v,ref:w,onKeyDown:a(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:a(e.onClick,e=>{j(e=>!!M(e)||!e),O&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),O&&(0,u.jsx)(R,{control:b,bubbles:!x.current,name:i,value:p,checked:C,required:d,disabled:f,form:y,style:{transform:"translateX(-100%)"},defaultChecked:!M(c)&&c})]})});x.displayName=h;var O="CheckboxIndicator",C=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...i}=e,o=w(O,n);return(0,u.jsx)(p,{present:r||M(o.state)||!0===o.state,children:(0,u.jsx)(N.span,{"data-state":k(o.state),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=O;var R=r.forwardRef(({__scopeCheckbox:e,control:t,checked:n,bubbles:i=!0,defaultChecked:o,...a},s)=>{let f=r.useRef(null),p=l(f,s),m=(0,c.Z)(n),y=(0,d.X)(t);r.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==n&&t){let r=new Event("click",{bubbles:i});e.indeterminate=M(n),t.call(e,!M(n)&&n),e.dispatchEvent(r)}},[m,n,i]);let v=r.useRef(!M(n)&&n);return(0,u.jsx)(N.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??v.current,...a,tabIndex:-1,ref:p,style:{...a.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function M(e){return"indeterminate"===e}function k(e){return M(e)?"indeterminate":e?"checked":"unchecked"}R.displayName="CheckboxBubbleInput";var j=x,P=C}};