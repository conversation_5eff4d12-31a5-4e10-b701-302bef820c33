(()=>{var e={};e.id=4170,e.ids=[4170],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16003:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>s.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var n=a(65239),r=a(48088),s=a(31369),i=a(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);a.d(t,o);let l={children:["",{children:["(systemAdmin)",{children:["org-unit-management",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,80338)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\org-unit-management\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,96707)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\org-unit-management\\[id]\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(systemAdmin)/org-unit-management/[id]/page",pathname:"/org-unit-management/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19080:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23904:(e,t,a)=>{Promise.resolve().then(a.bind(a,72128)),Promise.resolve().then(a.bind(a,2505)),Promise.resolve().then(a.bind(a,92588)),Promise.resolve().then(a.bind(a,48974)),Promise.resolve().then(a.bind(a,31057)),Promise.resolve().then(a.bind(a,50417))},27590:(e,t,a)=>{"use strict";function n(e,t={}){let{dateStyle:a="medium",timeStyle:r="short",fallback:s="N/A",customFormat:i,locale:o="en-US"}=t;if(!e)return s;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug(`Corrected UTC date format: ${e} -> ${a}`)),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn(`Invalid date provided to formatUtcToLocal: ${e}`),s;if(i)return function(e,t){let a=e.getFullYear(),n=e.getMonth()+1,r=e.getDate(),s=e.getHours(),i=e.getMinutes(),o={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][n-1],dd:r.toString().padStart(2,"0"),h:(s%12||12).toString(),mm:i.toString().padStart(2,"0"),a:s>=12?"PM":"AM"},l=t;return Object.entries(o).forEach(([e,t])=>{l=l.replace(RegExp(e,"g"),t)}),l}(t,i);return new Intl.DateTimeFormat(o,{dateStyle:a,timeStyle:r}).format(t)}catch(t){return console.error(`Error formatting date ${e}:`,t),s}}a.d(t,{Ej:()=>n})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37337:(e,t,a)=>{"use strict";a.d(t,{AW:()=>c,Cb:()=>u,QQ:()=>l,ae:()=>s,jm:()=>d,oy:()=>o,s9:()=>i});var n=a(51787);let r=()=>"default",s=async e=>{let t=r();return await n.F.get(`${t}/api/packages/${e}`)},i=async()=>{let e=r();return await n.F.get(`${e}/api/packages`)},o=async e=>{let t=r(),a=new FormData;return a.append("file",e.file),e.name&&a.append("name",e.name),e.description&&a.append("description",e.description),e.version&&a.append("version",e.version),await n.F.post(`${t}/api/packages/upload`,a)},l=async(e,t)=>{let a=r();return await n.F.get(`${a}/api/packages/${e}/versions/${t}/download`)},d=async e=>{let t=r();await n.F.delete(`${t}/api/packages/${e}`)},c=async(e,t)=>{let a=r();await n.F.delete(`${a}/api/packages/${e}/versions/${t}`)},u=async e=>{let t=r(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(e),s=`${t}/odata/AutomationPackages`;a&&(s+=`?${a}`),console.log("OData query endpoint:",s);try{let e=await n.F.get(s);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} packages from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log(`Found array property "${a}" in response`);let n=e[a],r=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof r?r:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},38475:(e,t,a)=>{Promise.resolve().then(a.bind(a,52162)),Promise.resolve().then(a.bind(a,83847)),Promise.resolve().then(a.bind(a,78526)),Promise.resolve().then(a.bind(a,97597)),Promise.resolve().then(a.bind(a,98641)),Promise.resolve().then(a.bind(a,80110))},39582:(e,t,a)=>{"use strict";a.d(t,{NA:()=>o,Qk:()=>c,Ri:()=>i,dT:()=>l,kz:()=>d,xR:()=>s});var n=a(51787);let r=()=>"default",s=async e=>{let t=r();return await n.F.post(`${t}/api/agents/create`,e)},i=async e=>{let t=r();return await n.F.get(`${t}/api/agents/${e}`)},o=async()=>{let e=r();return await n.F.get(`${e}/api/agents`)},l=async e=>{let t=r(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(e),s=`${t}/odata/BotAgents`;a&&(s+=`?${a}`),console.log("OData query endpoint:",s);try{let e=await n.F.get(s);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} items from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log(`Found array property "${a}" in response`);let n=e[a],r=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof r?r:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching agents with OData:",e),{value:[]}}},d=async e=>{let t=r();await n.F.delete(`${t}/api/agents/${e}`)},c=async(e,t)=>{let a=r();return await n.F.put(`${a}/api/agents/${e}`,t)}},39989:(e,t,a)=>{"use strict";a.d(t,{K:()=>r});var n=a(51787);let r={getMyOrganizationUnits:async()=>await n.F.get("/api/ou/my-ous"),getBySlug:async e=>await n.F.get(`/api/ou/slug/${e}`),getById:async e=>await n.F.get(`/api/ou/${e}`),create:async e=>await n.F.post("/api/ou/create",e),update:async(e,t)=>await n.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await n.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await n.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await n.F.get(`/api/ou/${e}/deletion-status`)}},41442:(e,t,a)=>{Promise.resolve().then(a.bind(a,80338))},47565:(e,t,a)=>{"use strict";a.d(t,{i:()=>r});var n=a(51787);let r={getAllUsers:async()=>(0,n.fetchApi)("api/admin/user/get-all",{method:"GET"}),getUserById:async e=>(0,n.fetchApi)(`api/admin/user/detail/${e}`,{method:"GET"}),updateUserInfo:async(e,t)=>(0,n.fetchApi)(`api/admin/user/update-detail/${e}`,{method:"PUT",body:JSON.stringify(t)}),changeUserPassword:async(e,t)=>(0,n.fetchApi)(`api/admin/user/change-password/${e}`,{method:"POST",body:JSON.stringify(t)}),getAllOrganizationUnits:async()=>await n.F.get("/api/admin/organization-unit/get-all"),deleteOrganizationUnit:async e=>{await n.F.delete(`/api/admin/organization-unit/${e}`)}}},52162:(e,t,a)=>{"use strict";a.d(t,{AdminRouteGuard:()=>o});var n=a(60687),r=a(86522),s=a(16189);a(43210);var i=a(85726);function o({children:e,redirectPath:t="/tenant-selector",loadingComponent:a}){let{isSystemAdmin:o,isLoading:l,isLogout:d}=(0,r.A)();return((0,s.useRouter)(),l)?a||(0,n.jsxs)("div",{className:"w-full p-8 space-y-4",children:[(0,n.jsx)(i.E,{className:"h-12 w-full rounded-lg"}),(0,n.jsx)(i.E,{className:"h-60 w-full rounded-lg"}),(0,n.jsx)(i.E,{className:"h-12 w-2/3 rounded-lg"})]}):o?(0,n.jsx)(n.Fragment,{children:e}):null}},59594:(e,t,a)=>{Promise.resolve().then(a.bind(a,63638))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>g,Es:()=>h,HM:()=>u,L3:()=>y,c7:()=>m,lG:()=>l,rr:()=>f,zM:()=>d});var n=a(60687),r=a(43210),s=a(88562),i=a(11860),o=a(36966);let l=s.bL,d=s.l9,c=s.ZL,u=s.bm,p=r.forwardRef(({className:e,...t},a)=>(0,n.jsx)(s.hJ,{ref:a,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));p.displayName=s.hJ.displayName;let g=r.forwardRef(({className:e,children:t,...a},r)=>(0,n.jsxs)(c,{children:[(0,n.jsx)(p,{}),(0,n.jsxs)(s.UC,{ref:r,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...a,children:[t,(0,n.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(i.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));g.displayName=s.UC.displayName;let m=({className:e,...t})=>(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let h=({className:e,...t})=>(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});h.displayName="DialogFooter";let y=r.forwardRef(({className:e,...t},a)=>(0,n.jsx)(s.hE,{ref:a,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));y.displayName=s.hE.displayName;let f=r.forwardRef(({className:e,...t},a)=>(0,n.jsx)(s.VY,{ref:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=s.VY.displayName},63600:(e,t,a)=>{"use strict";a.d(t,{Bx:()=>s,Gg:()=>o,H1:()=>i});var n=a(51787);function r(){return console.log("Using default tenant"),"default"}let s=async e=>{let t=r(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(([e,a])=>{null!=a&&t.append(e,String(a))}),t.toString()}(e),s=`${t}/odata/OrganizationUnitUsers`;return a&&(s+=`?${a}`),function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:{value:[]}}(await n.F.get(s))},i={getUsers:async e=>(await n.F.get(`${e}/api/ou/users`)).users,getRolesInOrganizationUnit:async e=>n.F.get(`${e}/api/ou/users/roles`),assignRolesBulk:async(e,t)=>{let a=r(),s=t.map(e=>e.trim()),i=`${a}/api/author/user/${e}/assign-multiple-roles`;try{return await n.F.post(i,{authorityIds:s})}catch(e){throw console.error("Error assigning roles:",e),e}}},o=async e=>{let t=r();await n.F.delete(`${t}/api/ou/users/${e}`)}},63638:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>R});var n=a(60687),r=a(16189),s=a(29523),i=a(44493),o=a(96834),l=a(63503),d=a(78122),c=a(28559),u=a(88233),p=a(83753),g=a(34410),m=a(41312),h=a(19080),y=a(99891),f=a(43210),x=a(39989),v=a(47565),j=a(39582),b=a(71769),A=a(37337),w=a(63600),$=a(31207),N=a(70891),k=a(20140),C=a(27590);function P(){return(0,n.jsx)("div",{className:"flex items-center justify-center h-full py-10",children:(0,n.jsx)("div",{className:"animate-spin text-primary",children:(0,n.jsx)(d.A,{className:"h-10 w-10"})})})}function F({onRetry:e}){return(0,n.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,n.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load organization unit details."}),(0,n.jsx)(s.$,{variant:"outline",className:"mt-2",onClick:e,children:"Retry"})]})}function O({countdown:e,deletionStatusData:t,onCancelClick:a,formatTimeRemaining:r}){return(0,n.jsxs)("div",{className:"flex items-center justify-between dark:bg-orange-950/50 bg-orange-50 border border-orange-300 dark:border-orange-800/50 rounded-lg px-4 py-3",children:[(0,n.jsx)("div",{className:"text-orange-700 dark:text-orange-400 font-semibold",children:"number"==typeof e&&e>0?`This organization unit will be deleted in ${r(e)}`:"Deleting organization unit..."}),t?.canCancel&&(0,n.jsx)(s.$,{variant:"outline",className:"ml-4 border-orange-600 text-orange-700 hover:bg-orange-100",onClick:a,children:"Cancel Deletion"})]})}function D({title:e,count:t,icon:a,isLoading:r}){return(0,n.jsx)(i.Zp,{children:(0,n.jsx)(i.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"p-2 bg-primary/10 rounded-lg",children:a}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:r?(0,n.jsx)("div",{className:"animate-pulse bg-muted h-8 w-12 rounded"}):t??0})]})]})})})}function S({id:e}){let t=(0,r.useRouter)(),a=(0,r.useParams)(),S=a?.tenant,{toast:R}=(0,k.d)(),{data:E,error:U,isLoading:G}=function(e){let t=(0,f.useCallback)(async()=>{if(!e)return Promise.reject(Error("No ID provided"));try{let t=(await v.i.getAllOrganizationUnits()).find(t=>t.id===e);if(!t)throw Error(`Organization unit with ID ${e} not found`);return t}catch(t){return console.error("Failed to fetch organization unit via admin API, trying regular API:",t),x.K.getById(e)}},[e]);return(0,$.Ay)(e?`organization-unit-${e}`:null,t)}(e),{agentsData:M,agentsLoading:L,assetsData:I,assetsLoading:T,packagesData:q,packagesLoading:_,usersData:H,usersLoading:B,rolesData:J,rolesLoading:V}=function(e){let t=(0,f.useCallback)(async()=>{let e=await (0,j.NA)();return{length:e.length,data:e}},[]),a=(0,f.useCallback)(async()=>{let e=await (0,b.NH)();return{length:e.length,data:e}},[]),n=(0,f.useCallback)(async()=>{let e=await (0,A.s9)();return{length:e.length,data:e}},[]),r=(0,f.useCallback)(async()=>{if(!e)return{length:0,data:[]};let t=await w.H1.getUsers(e);return{length:t.length,data:t}},[e]),s=(0,f.useCallback)(async()=>{if(!e)return{length:0,data:[]};let t=await w.H1.getRolesInOrganizationUnit(e);return{length:t.length,data:t}},[e]),{data:i,isLoading:o}=(0,$.Ay)(e?`agents-count-${e}`:null,t),{data:l,isLoading:d}=(0,$.Ay)(e?`assets-count-${e}`:null,a),{data:c,isLoading:u}=(0,$.Ay)(e?`packages-count-${e}`:null,n),{data:p,isLoading:g}=(0,$.Ay)(e?`users-count-${e}`:null,r),{data:m,isLoading:h}=(0,$.Ay)(e?`roles-count-${e}`:null,s);return{agentsData:i,agentsLoading:o,assetsData:l,assetsLoading:d,packagesData:c,packagesLoading:u,usersData:p,usersLoading:g,rolesData:m,rolesLoading:h}}(S),{deletionStatusData:K,mutateDeletionStatus:W,countdown:Z,showDeletionStatus:Q}=function(e){(0,f.useRef)(!1);let[t,a]=(0,f.useState)(null),n=async()=>{if(!e)throw Error("Missing ID");let t=await x.K.getDeletionStatus(e),a=t.isPendingDeletion??t.isDeletionPending??!1,n=null;return"number"==typeof t.remainingSeconds?n=t.remainingSeconds:"number"==typeof t.hoursUntilDeletion&&(n=3600*t.hoursUntilDeletion),{isPendingDeletion:a,remainingSeconds:n,scheduledDeletionAt:t.scheduledDeletionAt,canCancel:t.canCancel??!1}},{data:r,mutate:s}=(0,$.Ay)(e?N.DC.organizationUnitDeletionStatus(e):null,n,{refreshInterval:6e4,refreshWhenHidden:!0});return{deletionStatusData:r,mutateDeletionStatus:s,countdown:t,showDeletionStatus:!!r?.isPendingDeletion}}(e),[Y,X]=(0,f.useState)(!1),[ee,et]=(0,f.useState)(!1),[ea,en]=(0,f.useState)(!1),[er,es]=(0,f.useState)(!1),ei=async()=>{en(!0);try{await v.i.deleteOrganizationUnit(e),R({title:"Success",description:`Organization unit "${E?.name}" has been deleted successfully.`}),t.push("/system-admin/org-unit-management")}catch(e){console.error("Failed to delete organization unit:",e),R({title:"Error",description:"Failed to delete organization unit. Please try again.",variant:"destructive"})}finally{en(!1),X(!1)}},eo=async()=>{es(!0);try{await x.K.cancelDeletion(e),await W(),R({title:"Deletion Cancelled",description:"Organization unit deletion has been cancelled."})}catch{R({title:"Error",description:"Failed to cancel deletion.",variant:"destructive"})}finally{es(!1),et(!1)}},el=(0,f.useCallback)(e=>{if(e<=0)return"Deleting...";let t=Math.floor(e/86400),a=Math.floor(e%86400/3600),n=Math.floor(e%3600/60),r=e%60,s=[];return t>0&&s.push(`${t} day${t>1?"s":""}`),a>0&&s.push(`${a} hour${a>1?"s":""}`),n>0&&s.push(`${n} minute${n>1?"s":""}`),r>0&&0===s.length&&s.push(`${r} second${r>1?"s":""}`),s.join(", ")},[]);return G?(0,n.jsx)(P,{}):U?(0,n.jsx)(F,{onRetry:()=>window.location.reload()}):E?(0,n.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,n.jsxs)(i.Zp,{className:"border rounded-md shadow-sm",children:[(0,n.jsxs)(i.aR,{className:"flex items-center justify-between border-b p-4",children:[(0,n.jsxs)(s.$,{variant:"ghost",size:"sm",className:"gap-1",onClick:()=>{t.back()},children:[(0,n.jsx)(c.A,{className:"h-4 w-4"}),"Back"]}),!Q&&(0,n.jsxs)(s.$,{variant:"destructive",size:"sm",onClick:()=>{X(!0)},className:"gap-1",children:[(0,n.jsx)(u.A,{className:"h-4 w-4"}),"Delete"]})]}),(0,n.jsxs)(i.Wu,{className:"p-6 space-y-6",children:[Q&&(0,n.jsx)(O,{countdown:Z,deletionStatusData:K,onCancelClick:()=>et(!0),formatTimeRemaining:el}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(z,{label:"Name",children:E.name}),(0,n.jsx)(z,{label:"Description",children:E.description||"No description"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(z,{label:"Slug",children:E.slug}),(0,n.jsx)(z,{label:"Status",children:(0,n.jsx)(o.E,{variant:"outline",className:E.isActive?"bg-green-100 text-green-600 border-none":"bg-red-100 text-red-600 border-none",children:E.isActive?"Active":"Inactive"})})]})]}),(0,n.jsxs)("div",{className:"mt-8 pt-6 border-t",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Organization Statistics"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4",children:[(0,n.jsx)(D,{title:"Total Agents",count:M?.length,icon:(0,n.jsx)(p.A,{className:"h-5 w-5 text-primary"}),isLoading:L}),(0,n.jsx)(D,{title:"Total Assets",count:I?.length,icon:(0,n.jsx)(g.A,{className:"h-5 w-5 text-primary"}),isLoading:T}),(0,n.jsx)(D,{title:"Total Users",count:H?.length,icon:(0,n.jsx)(m.A,{className:"h-5 w-5 text-primary"}),isLoading:B}),(0,n.jsx)(D,{title:"Total Packages",count:q?.length,icon:(0,n.jsx)(h.A,{className:"h-5 w-5 text-primary"}),isLoading:_}),(0,n.jsx)(D,{title:"Total Roles",count:J?.length,icon:(0,n.jsx)(y.A,{className:"h-5 w-5 text-primary"}),isLoading:V})]})]}),(0,n.jsxs)("div",{className:"mt-8 pt-6 border-t",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Additional Information"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsx)(z,{label:"Created At",children:(0,C.Ej)(E.createdAt)}),E.updatedAt&&(0,n.jsx)(z,{label:"Last Updated",children:(0,C.Ej)(E.updatedAt)})]})]})]})]}),(0,n.jsx)(l.lG,{open:Y,onOpenChange:X,children:(0,n.jsxs)(l.Cf,{children:[(0,n.jsxs)(l.c7,{children:[(0,n.jsx)(l.L3,{children:"Delete Organization Unit"}),(0,n.jsxs)(l.rr,{children:['Are you sure you want to delete the organization unit "',E?.name,'"? It will be deleted in 7 days.']})]}),(0,n.jsxs)(l.Es,{children:[(0,n.jsx)(s.$,{variant:"outline",onClick:()=>X(!1),disabled:ea,children:"Cancel"}),(0,n.jsxs)(s.$,{variant:"destructive",onClick:ei,disabled:ea,className:"gap-1",children:[ea?(0,n.jsx)(d.A,{className:"h-4 w-4 animate-spin"}):(0,n.jsx)(u.A,{className:"h-4 w-4"}),ea?"Processing...":"Delete"]})]})]})}),(0,n.jsx)(l.lG,{open:ee,onOpenChange:et,children:(0,n.jsxs)(l.Cf,{children:[(0,n.jsx)(l.c7,{children:(0,n.jsx)(l.L3,{children:"Cancel Deletion"})}),(0,n.jsx)("div",{children:"Are you sure you want to cancel the deletion of this organization unit?"}),(0,n.jsxs)(l.Es,{children:[(0,n.jsx)(s.$,{variant:"outline",onClick:()=>et(!1),disabled:er,children:"No"}),(0,n.jsx)(s.$,{onClick:eo,className:"bg-[#FF6A1A] text-white hover:bg-orange-500",disabled:er,children:"Cancel Deletion"})]})]})})]}):(0,n.jsx)("div",{children:"Organization unit not found"})}function z({label:e,children:t}){return(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:e}),(0,n.jsx)("div",{className:"text-base font-medium pb-1 border-b",children:t})]})}function R(){let e=(0,r.useParams)().id;return(0,n.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,n.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,n.jsx)(S,{id:e})]})}},71769:(e,t,a)=>{"use strict";a.d(t,{$o:()=>s,Lm:()=>l,NH:()=>o,deleteAsset:()=>d,gT:()=>i,j0:()=>u,mK:()=>p,qi:()=>c});var n=a(51787);let r=()=>"default",s=async e=>{let t=r();return n.F.post(`${t}/api/assets`,e)},i=async(e,t,a)=>{let s=r(),i=await n.F.put(`${s}/api/assets/${e}`,t);return await n.F.put(`${s}/api/assets/${e}/bot-agents`,{botAgentIds:a}),i},o=async()=>{let e=r();return await n.F.get(`${e}/api/assets`)},l=async e=>{let t=r(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(([e,a])=>{null!=a&&t.append(e,String(a))}),t.toString()}(e),s=`${t}/odata/Assets`;a&&(s+=`?${a}`),console.log("OData query endpoint:",s);try{let e=await n.F.get(s);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} items from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":e["@odata.count"]??e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log(`Found array property "${a}" in response`);let n=e[a],r=e["@odata.count"];return{value:n,"@odata.count":("number"==typeof r?r:void 0)??n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching assets with OData:",e),{value:[]}}},d=async e=>{let t=r();await n.F.delete(`${t}/api/assets/${e}`)},c=async e=>{let t=r();return n.F.get(`${t}/api/assets/${e}`)},u=async e=>{let t=r();return n.F.get(`${t}/api/assets/${e}/bot-agents`)},p=async()=>{let e=r();return n.F.get(`${e}/api/agents`)}},72128:(e,t,a)=>{"use strict";a.d(t,{AdminRouteGuard:()=>n});let n=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call AdminRouteGuard() from the server but AdminRouteGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\admin-route-guard.tsx","AdminRouteGuard")},80338:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});let n=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\(systemAdmin)\\\\org-unit-management\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\org-unit-management\\[id]\\page.tsx","default")},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96707:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var n=a(37413),r=a(50417),s=a(48974),i=a(31057),o=a(92588),l=a(2505),d=a(72128);function c({children:e}){return(0,n.jsx)(d.AdminRouteGuard,{children:(0,n.jsx)(l.ChatProvider,{children:(0,n.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,n.jsxs)(r.SidebarProvider,{className:"flex flex-col",children:[(0,n.jsx)(i.SiteHeader,{}),(0,n.jsxs)("div",{className:"flex flex-1",children:[(0,n.jsx)(s.AppSidebar,{}),(0,n.jsx)(r.SidebarInset,{children:(0,n.jsx)(o.SearchProvider,{children:(0,n.jsx)("main",{className:"",children:e})})})]})]})})})})}},99891:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[4447,7966,5584,5156,4654,6467,6763,519],()=>a(16003));module.exports=n})();