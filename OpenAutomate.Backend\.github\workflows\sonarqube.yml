name: SonarQube Analysis

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop
  workflow_dispatch:  # Allow manual triggering

env:
  SONAR_PROJECT_KEY: openautomate-backend
  SONAR_PROJECT_NAME: openautomate-backend
  SONAR_HOST_URL: http://sonar.openautomate.me
  SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

jobs:
  sonar:
    name: SonarQube Analysis with Code Coverage
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository with full history for better SonarQube analysis
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Fetch all history for accurate blame information

      # Step 2: Set up .NET SDK
      - name: Set up .NET SDK
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'

      # Step 3: Setup Java (required for SonarScanner)
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      # Step 4: Install SonarQube Scanner
      - name: Install SonarScanner
        run: |
          dotnet tool install --global dotnet-sonarscanner
          dotnet tool install --global coverlet.console
      - name: Add dotnet tools to PATH
        run: echo "$HOME/.dotnet/tools" >> $GITHUB_PATH

      # Step 5: Cache SonarQube packages
      - name: Cache SonarQube packages
        uses: actions/cache@v3
        with:
          path: ~/*.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      # Step 6: Begin SonarQube analysis
      - name: Begin SonarQube analysis
        run: |
          dotnet sonarscanner begin \
            /k:"$SONAR_PROJECT_KEY" \
            /n:"$SONAR_PROJECT_NAME" \
            /d:sonar.host.url="$SONAR_HOST_URL" \
            /d:sonar.login="$SONAR_TOKEN" \
            /d:sonar.cs.opencover.reportsPaths="./TestResults/coverage.opencover.xml,./TestResults/**/*.opencover.xml,**/TestResults/**/*.opencover.xml" \
            /d:sonar.cs.vstest.reportsPaths="./TestResults/**/*.trx" \
            /d:sonar.coverage.exclusions="**/*Tests*.cs,**/obj/**/*,**/*.designer.cs,**/Migrations/**" \
            /d:sonar.exclusions="**/bin/**/*,**/obj/**/*" \
            /d:sonar.cpd.exclusions="**/Migrations/**/*" \
            /d:sonar.verbose=true

      # Step 7: Restore dependencies
      - name: Restore dependencies
        run: dotnet restore OpenAutomate.Backend.sln

      # Step 8: Build the solution
      - name: Build solution
        run: dotnet build OpenAutomate.Backend.sln --configuration Release --no-restore

      # Step 9: Run tests with coverage (proper project targeting)
      - name: Run tests with coverage
        run: |
          # Create a directory for the results
          mkdir -p ./TestResults
          
          # Run Core tests first (no merge needed)
          dotnet test OpenAutomate.Core.Tests/OpenAutomate.Core.Tests.csproj \
            --configuration Release \
            --no-build \
            --logger:trx \
            --results-directory:./TestResults \
            /p:CollectCoverage=true \
            /p:CoverletOutputFormat=opencover \
            /p:CoverletOutput=./TestResults/core_coverage.opencover.xml

          # Run API tests (merge with core coverage if it exists)
          if [ -f "./TestResults/core_coverage.opencover.xml" ]; then
            dotnet test OpenAutomate.API.Tests/OpenAutomate.API.Tests.csproj \
              --configuration Release \
              --no-build \
              --logger:trx \
              --results-directory:./TestResults \
              /p:CollectCoverage=true \
              /p:CoverletOutputFormat=opencover \
              /p:CoverletOutput=./TestResults/api_coverage.opencover.xml \
              /p:MergeWith="./TestResults/core_coverage.opencover.xml"
          else
            dotnet test OpenAutomate.API.Tests/OpenAutomate.API.Tests.csproj \
              --configuration Release \
              --no-build \
              --logger:trx \
              --results-directory:./TestResults \
              /p:CollectCoverage=true \
              /p:CoverletOutputFormat=opencover \
              /p:CoverletOutput=./TestResults/api_coverage.opencover.xml
          fi
          
          # Run Infrastructure tests (merge with existing coverage if it exists)
          MERGE_FILE=""
          if [ -f "./TestResults/api_coverage.opencover.xml" ]; then
            MERGE_FILE="./TestResults/api_coverage.opencover.xml"
          elif [ -f "./TestResults/core_coverage.opencover.xml" ]; then
            MERGE_FILE="./TestResults/core_coverage.opencover.xml"
          fi
          
          if [ -n "$MERGE_FILE" ]; then
            dotnet test OpenAutomate.Infrastructure.Tests/OpenAutomate.Infrastructure.Tests.csproj \
              --configuration Release \
              --no-build \
              --logger:trx \
              --results-directory:./TestResults \
              /p:CollectCoverage=true \
              /p:CoverletOutputFormat=opencover \
              /p:CoverletOutput=./TestResults/infrastructure_coverage.opencover.xml \
              /p:MergeWith="$MERGE_FILE"
          else
            dotnet test OpenAutomate.Infrastructure.Tests/OpenAutomate.Infrastructure.Tests.csproj \
              --configuration Release \
              --no-build \
              --logger:trx \
              --results-directory:./TestResults \
              /p:CollectCoverage=true \
              /p:CoverletOutputFormat=opencover \
              /p:CoverletOutput=./TestResults/infrastructure_coverage.opencover.xml
          fi
          
          # Check if coverage files were created
          echo "Listing coverage files in TestResults directory:"
          ls -la ./TestResults/*.opencover.xml || echo "No coverage files found in ./TestResults"
          
          # Install reportgenerator for merging reports
          dotnet tool install -g dotnet-reportgenerator-globaltool || true
          
          # Use the final merged coverage file or the most recent one
          FINAL_COVERAGE_FILE=""
          if [ -f "./TestResults/infrastructure_coverage.opencover.xml" ]; then
            FINAL_COVERAGE_FILE="./TestResults/infrastructure_coverage.opencover.xml"
          elif [ -f "./TestResults/api_coverage.opencover.xml" ]; then
            FINAL_COVERAGE_FILE="./TestResults/api_coverage.opencover.xml"
          elif [ -f "./TestResults/core_coverage.opencover.xml" ]; then
            FINAL_COVERAGE_FILE="./TestResults/core_coverage.opencover.xml"
          fi
          
          if [ -n "$FINAL_COVERAGE_FILE" ]; then
            echo "Using final coverage file: $FINAL_COVERAGE_FILE"
            # Copy to the expected location for SonarQube
            cp -f "$FINAL_COVERAGE_FILE" ./TestResults/coverage.opencover.xml
          else
            echo "No coverage files found!"
          fi

      # List coverage files for debugging
      - name: List coverage files
        run: |
          echo "=== Checking coverage files ==="
          find . -name "*.opencover.xml" -type f
          echo "=== End of coverage files ==="
          
      # Generate HTML coverage report (only if coverage file exists)
      - name: Generate coverage report
        run: |
          # Look for the main coverage file first
          if [ -f "./TestResults/coverage.opencover.xml" ]; then
            echo "Using main coverage file: ./TestResults/coverage.opencover.xml"
            reportgenerator -reports:./TestResults/coverage.opencover.xml -targetdir:./TestResults/CoverageReport/Html -reporttypes:Html
          # Then check for the merged coverage file
          elif [ -f "./TestResults/CoverageReport/Opencover.xml" ]; then
            echo "Using merged coverage file: ./TestResults/CoverageReport/Opencover.xml"
            reportgenerator -reports:./TestResults/CoverageReport/Opencover.xml -targetdir:./TestResults/CoverageReport/Html -reporttypes:Html
          # If neither exists, find any available coverage file
          else
            echo "No primary coverage files found, searching for any coverage files"
            COVERAGE_FILES=$(find . -name "*.opencover.xml" -type f | head -1)
            if [ -n "$COVERAGE_FILES" ]; then
              echo "Using found coverage file: $COVERAGE_FILES"
              # Copy this file to the expected location for SonarQube
              cp "$COVERAGE_FILES" ./TestResults/coverage.opencover.xml
              reportgenerator -reports:"$COVERAGE_FILES" -targetdir:./TestResults/CoverageReport/Html -reporttypes:Html
            else
              echo "No coverage files found anywhere, skipping report generation"
              # Create empty directory to avoid upload failure
              mkdir -p ./TestResults/CoverageReport/Html
            fi
          fi
          
          # Debug: Display coverage file contents to verify it's valid XML
          if [ -f "./TestResults/coverage.opencover.xml" ]; then
            echo "Validating coverage file format:"
            head -n 20 "./TestResults/coverage.opencover.xml"
          fi

      # Upload HTML report as artifact
      - name: Upload HTML coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report-html
          path: ./TestResults/CoverageReport/Html
          retention-days: 5

      # Step 10: End SonarQube analysis
      - name: End SonarQube analysis
        run: |
          # Make sure coverage file is where SonarQube expects it
          if [ -f "./TestResults/coverage.opencover.xml" ]; then
            echo "Coverage file found at expected location"
          else
            echo "Looking for coverage files to copy to the expected location"
            COVERAGE_FILE=$(find . -name "*.opencover.xml" -type f | head -1)
            if [ -n "$COVERAGE_FILE" ]; then
              echo "Copying $COVERAGE_FILE to ./TestResults/coverage.opencover.xml"
              mkdir -p ./TestResults
              cp "$COVERAGE_FILE" ./TestResults/coverage.opencover.xml
            else
              echo "WARNING: No coverage files found"
            fi
          fi
          
          # End SonarQube analysis
          dotnet sonarscanner end /d:sonar.login="$SONAR_TOKEN"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # Needed for PR decoration