"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8155],{6224:(t,e,r)=>{r.d(e,{E:()=>w});var n=r(92418),i=r(83394),o=r(96025),a=r(12115),c=r(52596),l=r(50091),u=r(45167),s=r(12814);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(p=function(){return!!t})()}function y(t){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function v(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}function b(){return(b=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var m=function(t){var e=t.yAxisId,r=(0,l.yi)(),n=(0,l.rY)(),i=(0,l.Nk)(e);return null==i?null:a.createElement(u.u,b({},i,{className:(0,c.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return(0,s.Rh)(t,!0)}}))},g=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=y(t),function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,p()?Reflect.construct(t,e||[],y(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h(t,e)}(r,t),e=[{key:"render",value:function(){return a.createElement(m,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a.Component);v(g,"displayName","YAxis"),v(g,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var O=r(83455),w=(0,n.gu)({chartName:"BarChart",GraphicalChild:i.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:o.W},{axisType:"yAxis",AxisComp:g}],formatAxisMap:O.pr})},36447:(t,e,r)=>{r.d(e,{f:()=>h});var n=r(40139),i=r.n(n),o=r(16377),a=r(46605),c=r(41643),l=r(83455);function u(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e){if(void 0!==r&&!0!==r(t[i]))return;n.push(t[i])}return n}function s(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){var n,f,p,h,v,d=t.tick,b=t.ticks,m=t.viewBox,g=t.minTickGap,O=t.orientation,w=t.interval,k=t.tickFormatter,x=t.unit,j=t.angle;if(!b||!b.length||!d)return[];if((0,o.Et)(w)||c.m.isSsr)return u(b,("number"==typeof w&&(0,o.Et)(w)?w:0)+1);var P=[],S="top"===O||"bottom"===O?"width":"height",E=x&&"width"===S?(0,a.Pu)(x,{fontSize:e,letterSpacing:r}):{width:0,height:0},C=function(t,n){var o,c,u=i()(k)?k(t.value,n):t.value;return"width"===S?(c={width:(o=(0,a.Pu)(u,{fontSize:e,letterSpacing:r})).width+E.width,height:o.height+E.height},(0,l.bx)(c,j)):(0,a.Pu)(u,{fontSize:e,letterSpacing:r})[S]},A=b.length>=2?(0,o.sA)(b[1].coordinate-b[0].coordinate):1,T=(n="width"===S,f=m.x,p=m.y,h=m.width,v=m.height,1===A?{start:n?f:p,end:n?f+h:p+v}:{start:n?f+h:p+v,end:n?f:p});return"equidistantPreserveStart"===w?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,l=e.end,f=0,p=1,y=c;p<=a.length;)if(o=function(){var e,o=null==n?void 0:n[f];if(void 0===o)return{v:u(n,p)};var a=f,h=function(){return void 0===e&&(e=r(o,a)),e},v=o.coordinate,d=0===f||s(t,v,h,y,l);d||(f=0,y=c,p+=1),d&&(y=v+t*(h()/2+i),f+=p)}())return o.v;return[]}(A,T,C,b,g):("preserveStart"===w||"preserveStartEnd"===w?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,l=e.start,u=e.end;if(o){var f=n[c-1],p=r(f,c-1),h=t*(f.coordinate+t*p/2-u);a[c-1]=f=y(y({},f),{},{tickCoord:h>0?f.coordinate-h*t:f.coordinate}),s(t,f.tickCoord,function(){return p},l,u)&&(u=f.tickCoord-t*(p/2+i),a[c-1]=y(y({},f),{},{isShow:!0}))}for(var v=o?c-1:c,d=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var f=t*(o.coordinate-t*c()/2-l);a[e]=o=y(y({},o),{},{tickCoord:f<0?o.coordinate-f*t:o.coordinate})}else a[e]=o=y(y({},o),{},{tickCoord:o.coordinate});s(t,o.tickCoord,c,l,u)&&(l=o.tickCoord+t*(c()/2+i),a[e]=y(y({},o),{},{isShow:!0}))},b=0;b<v;b++)d(b);return a}(A,T,C,b,g,"preserveStartEnd"===w):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,l=e.end,u=function(e){var n,u=o[e],f=function(){return void 0===n&&(n=r(u,e)),n};if(e===a-1){var p=t*(u.coordinate+t*f()/2-l);o[e]=u=y(y({},u),{},{tickCoord:p>0?u.coordinate-p*t:u.coordinate})}else o[e]=u=y(y({},u),{},{tickCoord:u.coordinate});s(t,u.tickCoord,f,c,l)&&(l=u.tickCoord-t*(f()/2+i),o[e]=y(y({},u),{},{isShow:!0}))},f=a-1;f>=0;f--)u(f);return o}(A,T,C,b,g)).filter(function(t){return t.isShow})}},45167:(t,e,r)=>{r.d(e,{u:()=>N});var n=r(12115),i=r(40139),o=r.n(i),a=r(48973),c=r.n(a),l=r(52596),u=r(15232),s=r(2348),f=r(79095),p=r(60379),y=r(16377),h=r(43597),v=r(70788),d=r(36447),b=["viewBox"],m=["viewBox"],g=["ticks"];function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function w(){return(w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){A(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function j(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,T(n.key),n)}}function S(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(S=function(){return!!t})()}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function C(t,e){return(C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function A(t,e,r){return(e=T(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}var N=function(t){var e,r;function i(t){var e,r,n;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),r=i,n=[t],r=E(r),(e=function(t,e){if(e&&("object"===O(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,S()?Reflect.construct(r,n||[],E(this).constructor):r.apply(this,n))).state={fontSize:"",letterSpacing:""},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&C(t,e)}(i,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=j(t,b),i=this.props,o=i.viewBox,a=j(i,m);return!(0,u.b)(r,o)||!(0,u.b)(n,a)||!(0,u.b)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,l=c.x,u=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,v=c.mirror,d=c.tickMargin,b=v?-1:1,m=t.tickSize||h,g=(0,y.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=u+ +!v*f)-b*m)-b*d,o=g;break;case"left":n=i=t.coordinate,o=(e=(r=l+ +!v*s)-b*m)-b*d,a=g;break;case"right":n=i=t.coordinate,o=(e=(r=l+ +v*s)+b*m)+b*d,a=g;break;default:e=r=t.coordinate,a=(n=(i=u+ +v*f)+b*m)+b*d,o=g}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.orientation,u=t.mirror,s=t.axisLine,f=x(x(x({},(0,v.J9)(this.props,!1)),(0,v.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!u||"bottom"===a&&u);f=x(x({},f),{},{x1:e,y1:r+p*o,x2:e+i,y2:r+p*o})}else{var y=+("left"===a&&!u||"right"===a&&u);f=x(x({},f),{},{x1:e+y*i,y1:r,x2:e+y*i,y2:r+o})}return n.createElement("line",w({},f,{className:(0,l.A)("recharts-cartesian-axis-line",c()(s,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var a=this,u=this.props,f=u.tickLine,p=u.stroke,y=u.tick,b=u.tickFormatter,m=u.unit,g=(0,d.f)(x(x({},this.props),{},{ticks:t}),e,r),O=this.getTickTextAnchor(),k=this.getTickVerticalAnchor(),j=(0,v.J9)(this.props,!1),P=(0,v.J9)(y,!1),S=x(x({},j),{},{fill:"none"},(0,v.J9)(f,!1)),E=g.map(function(t,e){var r=a.getTickLineCoord(t),u=r.line,v=r.tick,d=x(x(x(x({textAnchor:O,verticalAnchor:k},j),{},{stroke:"none",fill:p},P),v),{},{index:e,payload:t,visibleTicksCount:g.length,tickFormatter:b});return n.createElement(s.W,w({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,h.XC)(a.props,t,e)),f&&n.createElement("line",w({},S,u,{className:(0,l.A)("recharts-cartesian-axis-tick-line",c()(f,"className"))})),y&&i.renderTickItem(y,d,"".concat(o()(b)?b(t.value,e):t.value).concat(m||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,i=e.width,a=e.height,c=e.ticksGenerator,u=e.className;if(e.hide)return null;var f=this.props,y=f.ticks,h=j(f,g),v=y;return(o()(c)&&(v=c(y&&y.length>0?this.props:h)),i<=0||a<=0||!v||!v.length)?null:n.createElement(s.W,{className:(0,l.A)("recharts-cartesian-axis",u),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(v,this.state.fontSize,this.state.letterSpacing),p.J.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var i;return n.isValidElement(t)?n.cloneElement(t,e):o()(t)?t(e):n.createElement(f.E,w({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&P(i.prototype,e),r&&P(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.Component);A(N,"displayName","CartesianAxis"),A(N,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},94754:(t,e,r)=>{r.d(e,{d:()=>A});var n=r(12115),i=r(40139),o=r.n(i),a=r(675),c=r(16377),l=r(70788),u=r(12814),s=r(36447),f=r(45167),p=r(50091),y=["x1","y1","x2","y2","key"],h=["offset"];function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function g(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var O=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,i=t.x,o=t.y,a=t.width,c=t.height,l=t.ry;return n.createElement("rect",{x:i,y:o,ry:l,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function w(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(o()(t))r=t(e);else{var i=e.x1,a=e.y1,c=e.x2,u=e.y2,s=e.key,f=g(e,y),p=(0,l.J9)(f,!1),v=(p.offset,g(p,h));r=n.createElement("line",m({},v,{x1:i,y1:a,x2:c,y2:u,fill:"none",key:s}))}return r}function k(t){var e=t.x,r=t.width,i=t.horizontal,o=void 0===i||i,a=t.horizontalPoints;if(!o||!a||!a.length)return null;var c=a.map(function(n,i){return w(o,b(b({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function x(t){var e=t.y,r=t.height,i=t.vertical,o=void 0===i||i,a=t.verticalPoints;if(!o||!a||!a.length)return null;var c=a.map(function(n,i){return w(o,b(b({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function j(t){var e=t.horizontalFill,r=t.fillOpacity,i=t.x,o=t.y,a=t.width,c=t.height,l=t.horizontalPoints,u=t.horizontal;if(!(void 0===u||u)||!e||!e.length)return null;var s=l.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,l){var u=s[l+1]?s[l+1]-t:o+c-t;if(u<=0)return null;var f=l%e.length;return n.createElement("rect",{key:"react-".concat(l),y:t,x:i,height:u,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function P(t){var e=t.vertical,r=t.verticalFill,i=t.fillOpacity,o=t.x,a=t.y,c=t.width,l=t.height,u=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var u=s[e+1]?s[e+1]-t:o+c-t;if(u<=0)return null;var f=e%r.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:a,width:u,height:l,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var S=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return(0,u.PW)((0,s.f)(b(b(b({},f.u.defaultProps),r),{},{ticks:(0,u.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},E=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return(0,u.PW)((0,s.f)(b(b(b({},f.u.defaultProps),r),{},{ticks:(0,u.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},C={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function A(t){var e,r,i,l,u,s,f=(0,p.yi)(),y=(0,p.rY)(),h=(0,p.hj)(),d=b(b({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:C.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:C.fill,horizontal:null!==(i=t.horizontal)&&void 0!==i?i:C.horizontal,horizontalFill:null!==(l=t.horizontalFill)&&void 0!==l?l:C.horizontalFill,vertical:null!==(u=t.vertical)&&void 0!==u?u:C.vertical,verticalFill:null!==(s=t.verticalFill)&&void 0!==s?s:C.verticalFill,x:(0,c.Et)(t.x)?t.x:h.left,y:(0,c.Et)(t.y)?t.y:h.top,width:(0,c.Et)(t.width)?t.width:h.width,height:(0,c.Et)(t.height)?t.height:h.height}),g=d.x,w=d.y,A=d.width,T=d.height,N=d.syncWithTicks,z=d.horizontalValues,_=d.verticalValues,D=(0,p.pj)(),B=(0,p.$G)();if(!(0,c.Et)(A)||A<=0||!(0,c.Et)(T)||T<=0||!(0,c.Et)(g)||g!==+g||!(0,c.Et)(w)||w!==+w)return null;var R=d.verticalCoordinatesGenerator||S,F=d.horizontalCoordinatesGenerator||E,G=d.horizontalPoints,L=d.verticalPoints;if((!G||!G.length)&&o()(F)){var I=z&&z.length,J=F({yAxis:B?b(b({},B),{},{ticks:I?z:B.ticks}):void 0,width:f,height:y,offset:h},!!I||N);(0,a.R)(Array.isArray(J),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(v(J),"]")),Array.isArray(J)&&(G=J)}if((!L||!L.length)&&o()(R)){var W=_&&_.length,M=R({xAxis:D?b(b({},D),{},{ticks:W?_:D.ticks}):void 0,width:f,height:y,offset:h},!!W||N);(0,a.R)(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(v(M),"]")),Array.isArray(M)&&(L=M)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(O,{fill:d.fill,fillOpacity:d.fillOpacity,x:d.x,y:d.y,width:d.width,height:d.height,ry:d.ry}),n.createElement(k,m({},d,{offset:h,horizontalPoints:G,xAxis:D,yAxis:B})),n.createElement(x,m({},d,{offset:h,verticalPoints:L,xAxis:D,yAxis:B})),n.createElement(j,m({},d,{horizontalPoints:G})),n.createElement(P,m({},d,{verticalPoints:L})))}A.displayName="CartesianGrid"},96025:(t,e,r)=>{r.d(e,{W:()=>d});var n=r(12115),i=r(52596),o=r(50091),a=r(45167),c=r(12814);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(u=function(){return!!t})()}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function p(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function v(t){var e=t.xAxisId,r=(0,o.yi)(),l=(0,o.rY)(),u=(0,o.AF)(e);return null==u?null:n.createElement(a.u,h({},u,{className:(0,i.A)("recharts-".concat(u.axisType," ").concat(u.axisType),u.className),viewBox:{x:0,y:0,width:r,height:l},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))}var d=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=s(t),function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,u()?Reflect.construct(t,e||[],s(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&f(t,e)}(r,t),e=[{key:"render",value:function(){return n.createElement(v,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);p(d,"displayName","XAxis"),p(d,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})}}]);