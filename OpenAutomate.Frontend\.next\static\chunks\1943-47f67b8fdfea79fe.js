"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1943],{24265:(e,t,a)=>{a.d(t,{b:()=>c});var s=a(12115);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}a(47650);var n=a(95155),i=Symbol("radix.slottable");function l(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:a,...n}=e;if(s.isValidElement(a)){var i;let e,l;let o=(i=a,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let a={...t};for(let s in t){let r=e[s],n=t[s];/^on[A-Z]/.test(s)?r&&n?a[s]=(...e)=>{n(...e),r(...e)}:r&&(a[s]=r):"style"===s?a[s]={...r,...n}:"className"===s&&(a[s]=[r,n].filter(Boolean).join(" "))}return{...e,...a}}(n,a.props);return a.type!==s.Fragment&&(d.ref=t?function(...e){return t=>{let a=!1,s=e.map(e=>{let s=r(e,t);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let t=0;t<s.length;t++){let a=s[t];"function"==typeof a?a():r(e[t],null)}}}}(t,o):o),s.cloneElement(a,d)}return s.Children.count(a)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=s.forwardRef((e,a)=>{let{children:r,...i}=e,o=s.Children.toArray(r),d=o.find(l);if(d){let e=d.props.children,r=o.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,n.jsx)(t,{...i,ref:a,children:r})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(r?a:t,{...i,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),d=s.forwardRef((e,t)=>(0,n.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},37005:(e,t,a)=>{a.d(t,{HF:()=>i,S8:()=>o,eV:()=>n,gO:()=>l});var s=a(7283);let r=()=>{let e=window.location.pathname.split("/").filter(Boolean);return e.length>0?e[0]:""},n={getAvailableResources:async()=>{let e=r();return(0,s.fetchApi)("".concat(e,"/api/author/resources"))},getAllRoles:async()=>{let e=r();return(0,s.fetchApi)("".concat(e,"/api/author/authorities"))},getRoleById:async e=>{let t=r();return(0,s.fetchApi)("".concat(t,"/api/author/authority/").concat(e))},createRole:async e=>{let t=r();return(0,s.fetchApi)("".concat(t,"/api/author/authority"),{method:"POST"},e)},updateRole:async(e,t)=>{let a=r();return(0,s.fetchApi)("".concat(a,"/api/author/authority/").concat(e),{method:"PUT"},t)},deleteRole:async e=>{let t=r();return(0,s.fetchApi)("".concat(t,"/api/author/authority/").concat(e),{method:"DELETE"})},getUserAuthorities:async e=>{let t=r();return(0,s.fetchApi)("".concat(t,"/api/author/user/").concat(e))},assignRoleToUser:async(e,t)=>{let a=r();return(0,s.fetchApi)("".concat(a,"/api/author/user/").concat(e),{method:"POST"},{authorityId:t})},removeRoleFromUser:async(e,t)=>{let a=r();return(0,s.fetchApi)("".concat(a,"/api/author/user/").concat(e,"/authority/").concat(t),{method:"DELETE"})}},i={NO_ACCESS:0,VIEW:1,CREATE:2,UPDATE:3,DELETE:4},l=e=>{switch(e){case i.NO_ACCESS:return"No Access";case i.VIEW:return"View Only";case i.CREATE:return"View & Create";case i.UPDATE:return"View, Create & Update (includes Execute)";case i.DELETE:return"Full Administrative Access";default:return"Invalid Permission"}},o=e=>e>=i.NO_ACCESS&&e<=i.DELETE},54165:(e,t,a)=>{a.d(t,{Cf:()=>p,Es:()=>f,HM:()=>u,L3:()=>g,c7:()=>h,lG:()=>o,rr:()=>v,zM:()=>d});var s=a(95155),r=a(12115),n=a(59096),i=a(54416),l=a(36928);let o=n.bL,d=n.l9,c=n.ZL,u=n.bm,m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",a),...r})});m.displayName=n.hJ.displayName;let p=r.forwardRef((e,t)=>{let{className:a,children:r,...o}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(m,{}),(0,s.jsxs)(n.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",a),...o,children:[r,(0,s.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});p.displayName=n.UC.displayName;let h=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};h.displayName="DialogHeader";let f=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};f.displayName="DialogFooter";let g=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});g.displayName=n.hE.displayName;let v=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});v.displayName=n.VY.displayName},61943:(e,t,a)=>{a.d(t,{I:()=>x});var s=a(95155),r=a(12115),n=a(30285),i=a(54165),l=a(85057),o=a(62523),d=a(36928);let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("textarea",{className:(0,d.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...r})});c.displayName="Textarea";var u=a(59409),m=a(26126),p=a(62525),h=a(88262),f=a(37005),g=a(34953),v=a(70449);function x(e){var t,a,d;let{isOpen:x,onClose:y,editingRole:b}=e,{toast:N}=(0,h.d)(),[j,E]=(0,r.useState)(""),[w,C]=(0,r.useState)(""),[R,A]=(0,r.useState)(!1),{data:S,error:O,isLoading:k}=(0,g.Ay)(x?v.DC.availableResources():null,f.eV.getAvailableResources);(0,r.useEffect)(()=>{O&&(console.error("Failed to load resources:",O),N({title:"Error",description:"Failed to load available resources.",variant:"destructive"}))},[O,N]);let[D,T]=(0,r.useState)(null!==(t=null==b?void 0:b.name)&&void 0!==t?t:""),[I,P]=(0,r.useState)(null!==(a=null==b?void 0:b.description)&&void 0!==a?a:""),[F,V]=(0,r.useState)(()=>(null==b?void 0:b.permissions)&&S?b.permissions.map(e=>{var t;let a=S.find(t=>t.resourceName===e.resourceName);return{resourceName:e.resourceName,permission:e.permission,displayName:null!==(t=null==a?void 0:a.displayName)&&void 0!==t?t:e.resourceName}}):[]),U=e=>{V(t=>t.filter(t=>t.resourceName!==e))},L=()=>D.trim()?D.length<2||D.length>50?"Role name must be between 2 and 50 characters.":I.length>200?"Description cannot exceed 200 characters.":null:"Role name is required.",H=e=>e&&"object"==typeof e&&"message"in e?e.message.includes("already exists")?'A role with the name "'.concat(D,'" already exists. Please choose a different name.'):e.message.includes("validation")?"Please check your input and try again.":e.message.includes("permission")?"You do not have permission to perform this action.":e.message:"Failed to save role. Please try again.",W=async()=>{let e={name:D.trim(),description:I.trim(),resourcePermissions:F.map(e=>({resourceName:e.resourceName,permission:e.permission}))};b?(console.log("Updating role with data:",e),await f.eV.updateRole(b.id,e),N({title:"Success",description:"Role updated successfully."})):(console.log("Creating role with data:",e),await f.eV.createRole(e),N({title:"Success",description:"Role created successfully."}))},_=async e=>{e.preventDefault();let t=L();if(t){N({title:"Validation Error",description:t,variant:"destructive"});return}A(!0);try{await W(),y(!0)}catch(e){console.error("Failed to save role:",e),N({title:b?"Update Failed":"Creation Failed",description:H(e),variant:"destructive"})}finally{A(!1)}},z=null!==(d=null==S?void 0:S.filter(e=>!F.some(t=>t.resourceName===e.resourceName)))&&void 0!==d?d:[];return(0,s.jsx)(i.lG,{open:x,onOpenChange:()=>y(),children:(0,s.jsx)(i.Cf,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:(0,s.jsxs)("form",{onSubmit:_,children:[(0,s.jsx)(i.c7,{children:(0,s.jsx)(i.L3,{children:b?"Edit Role":"Create New Role"})}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(l.J,{htmlFor:"roleName",children:"Name *"}),(0,s.jsx)(o.p,{id:"roleName",value:D,onChange:e=>T(e.target.value),placeholder:"Enter role name",maxLength:50,required:!0}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:[D.length,"/50 characters"]})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(l.J,{htmlFor:"roleDescription",children:"Description"}),(0,s.jsx)(c,{id:"roleDescription",value:I,onChange:e=>P(e.target.value),placeholder:"Enter role description (optional)",maxLength:200,rows:3}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:[I.length,"/200 characters"]})]}),(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsx)(l.J,{children:"Resource Permissions"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,s.jsx)("div",{children:(0,s.jsxs)(u.l6,{value:j,onValueChange:E,children:[(0,s.jsx)(u.bq,{children:(0,s.jsx)(u.yv,{placeholder:"Select resource"})}),(0,s.jsx)(u.gC,{children:k?(0,s.jsx)(u.eb,{value:"loading",disabled:!0,children:"Loading..."}):z.map(e=>(0,s.jsx)(u.eb,{value:e.resourceName,children:e.displayName},e.resourceName))})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(u.l6,{value:w,onValueChange:C,children:[(0,s.jsx)(u.bq,{children:(0,s.jsx)(u.yv,{placeholder:"Permission level"})}),(0,s.jsxs)(u.gC,{children:[(0,s.jsx)(u.eb,{value:f.HF.NO_ACCESS.toString(),children:(0,f.gO)(f.HF.NO_ACCESS)}),(0,s.jsx)(u.eb,{value:f.HF.VIEW.toString(),children:(0,f.gO)(f.HF.VIEW)})," ",(0,s.jsx)(u.eb,{value:f.HF.CREATE.toString(),children:(0,f.gO)(f.HF.CREATE)}),(0,s.jsx)(u.eb,{value:f.HF.UPDATE.toString(),children:(0,f.gO)(f.HF.UPDATE)}),(0,s.jsx)(u.eb,{value:f.HF.DELETE.toString(),children:(0,f.gO)(f.HF.DELETE)})]})]})}),(0,s.jsx)(n.$,{type:"button",onClick:()=>{var e;if(!j||!w){N({title:"Validation Error",description:"Please select both a resource and permission level.",variant:"destructive"});return}let t=parseInt(w);if(!(0,f.S8)(t)){N({title:"Validation Error",description:"Invalid permission level selected.",variant:"destructive"});return}let a=F.findIndex(e=>e.resourceName===j),s=null==S?void 0:S.find(e=>e.resourceName===j),r={resourceName:j,permission:t,displayName:null!==(e=null==s?void 0:s.displayName)&&void 0!==e?e:j};if(a>=0){let e=[...F];e[a]=r,V(e)}else V([...F,r]);E(""),C("")},disabled:!j||!w,variant:"outline",children:"Add Permission"})]}),(0,s.jsx)("div",{className:"space-y-2",children:F.length>0?(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:"Current Permissions:"}),F.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("span",{className:"font-medium",children:e.displayName}),(0,s.jsx)(m.E,{variant:"outline",children:(0,f.gO)(e.permission)})]}),(0,s.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>U(e.resourceName),className:"text-destructive hover:text-destructive",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]},e.resourceName))]}):(0,s.jsx)("div",{className:"text-sm text-muted-foreground p-4 text-center border rounded-lg border-dashed",children:"No permissions assigned. Add permissions above to define what this role can access."})})]})]}),(0,s.jsxs)(i.Es,{children:[(0,s.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>y(),children:"Cancel"}),(0,s.jsx)(n.$,{type:"submit",disabled:R||k,children:R?"Saving...":b?"Update Role":"Create Role"})]})]})})})}},62525:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},70449:(e,t,a)=>{a.d(t,{DC:()=>l,EJ:()=>i,IS:()=>o,bb:()=>n});var s=a(7283),r=a(15874);function n(){return{fetcher:e=>(0,s.fetchApi)(e),onError:function(e){(0,r.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let i={fetcher:e=>(0,s.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},l={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},o=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},85057:(e,t,a)=>{a.d(t,{J:()=>i});var s=a(95155);a(12115);var r=a(24265),n=a(36928);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},88262:(e,t,a)=>{a.d(t,{$:()=>r,d:()=>n});var s=a(12115);let r=(0,s.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function n(){let e=(0,s.useContext)(r);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}}}]);