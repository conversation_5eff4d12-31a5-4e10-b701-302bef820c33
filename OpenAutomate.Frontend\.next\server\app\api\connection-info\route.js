(()=>{var e={};e.id=9735,e.ids=[9735],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17650:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var n={};t.r(n),t.d(n,{GET:()=>p});var o=t(96559),s=t(48088),i=t(37719),a=t(32190);async function p(){try{let e="http://localhost:5252";if(!e)return console.error("NEXT_PUBLIC_API_URL environment variable is not set"),a.NextResponse.json({error:"Backend API URL not configured"},{status:500});return a.NextResponse.json({apiUrl:e})}catch(e){return console.error("Error in connection-info endpoint:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/connection-info/route",pathname:"/api/connection-info",filename:"route",bundlePath:"app/api/connection-info/route"},resolvedPagePath:"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\api\\connection-info\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=u;function x(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,580],()=>t(17650));module.exports=n})();