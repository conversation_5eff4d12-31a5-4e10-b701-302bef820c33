(()=>{var e={};e.id=2509,e.ids=[2509],e.modules={1427:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(37413),i=r(33882);function o(){return(0,n.jsx)(i.default,{})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17687:(e,t,r)=>{"use strict";r.d(t,{II:()=>p,Nt:()=>h,at:()=>d});var n=r(60687),i=r(43210),o=r(48482),a=r(38246),s=r(57359),l=r(36966);let c={light:"",dark:".dark"},u=i.createContext(null);function d({id:e,className:t,children:r,config:a,...s}){let c=i.useId(),d=`chart-${e||c.replace(/:/g,"")}`;return(0,n.jsx)(u.Provider,{value:{config:a},children:(0,n.jsxs)("div",{"data-slot":"chart","data-chart":d,className:(0,l.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",t),...s,children:[(0,n.jsx)(f,{id:d,config:a}),(0,n.jsx)(o.u,{children:r})]})})}let f=({id:e,config:t})=>{let r=Object.entries(t).filter(([,e])=>e.theme||e.color);return r.length?(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(c).map(([t,n])=>`
${n} [data-chart=${e}] {
${r.map(([e,r])=>{let n=r.theme?.[t]||r.color;return n?`  --color-${e}: ${n};`:null}).join("\n")}
}
`).join("\n")}}):null},p=a.m;function h({active:e,payload:t,className:r,indicator:o="dot",hideLabel:a=!1,hideIndicator:s=!1,label:c,labelFormatter:d,labelClassName:f,formatter:p,color:h,nameKey:y,labelKey:b}){let{config:v}=function(){let e=i.useContext(u);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),x=i.useMemo(()=>{if(a||!t?.length)return null;let[e]=t,r=`${b||e?.dataKey||e?.name||"value"}`,i=m(v,e,r),o=b||"string"!=typeof c?i?.label:v[c]?.label||c;return d?(0,n.jsx)("div",{className:(0,l.cn)("font-medium",f),children:d(o,t)}):o?(0,n.jsx)("div",{className:(0,l.cn)("font-medium",f),children:o}):null},[c,d,t,a,f,v,b]);if(!e||!t?.length)return null;let g=1===t.length&&"dot"!==o;return(0,n.jsxs)("div",{className:(0,l.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",r),children:[g?null:x,(0,n.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${y||e.name||e.dataKey||"value"}`,i=m(v,e,r),a=h||e.payload.fill||e.color;return(0,n.jsx)("div",{className:(0,l.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===o&&"items-center"),children:p&&e?.value!==void 0&&e.name?p(e.value,e.name,e,t,e.payload):(0,n.jsxs)(n.Fragment,{children:[i?.icon?(0,n.jsx)(i.icon,{}):!s&&(0,n.jsx)("div",{className:(0,l.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===o,"w-1":"line"===o,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===o,"my-0.5":g&&"dashed"===o}),style:{"--color-bg":a,"--color-border":a}}),(0,n.jsxs)("div",{className:(0,l.cn)("flex flex-1 justify-between leading-none",g?"items-end":"items-center"),children:[(0,n.jsxs)("div",{className:"grid gap-1.5",children:[g?x:null,(0,n.jsx)("span",{className:"text-muted-foreground",children:i?.label||e.name})]}),e.value&&(0,n.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function m(e,t,r){if("object"!=typeof t||null===t)return;let n="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,i=r;return r in t&&"string"==typeof t[r]?i=t[r]:n&&r in n&&"string"==typeof n[r]&&(i=n[r]),i in e?e[i]:e[r]}s.s},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22766:(e,t,r)=>{Promise.resolve().then(r.bind(r,96287))},23904:(e,t,r)=>{Promise.resolve().then(r.bind(r,72128)),Promise.resolve().then(r.bind(r,2505)),Promise.resolve().then(r.bind(r,92588)),Promise.resolve().then(r.bind(r,48974)),Promise.resolve().then(r.bind(r,31057)),Promise.resolve().then(r.bind(r,50417))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33882:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\systemAdmin\\\\dashboard\\\\admin-dashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\systemAdmin\\dashboard\\admin-dashboard.tsx","default")},38475:(e,t,r)=>{Promise.resolve().then(r.bind(r,52162)),Promise.resolve().then(r.bind(r,83847)),Promise.resolve().then(r.bind(r,78526)),Promise.resolve().then(r.bind(r,97597)),Promise.resolve().then(r.bind(r,98641)),Promise.resolve().then(r.bind(r,80110))},47565:(e,t,r)=>{"use strict";r.d(t,{i:()=>i});var n=r(51787);let i={getAllUsers:async()=>(0,n.fetchApi)("api/admin/user/get-all",{method:"GET"}),getUserById:async e=>(0,n.fetchApi)(`api/admin/user/detail/${e}`,{method:"GET"}),updateUserInfo:async(e,t)=>(0,n.fetchApi)(`api/admin/user/update-detail/${e}`,{method:"PUT",body:JSON.stringify(t)}),changeUserPassword:async(e,t)=>(0,n.fetchApi)(`api/admin/user/change-password/${e}`,{method:"POST",body:JSON.stringify(t)}),getAllOrganizationUnits:async()=>await n.F.get("/api/admin/organization-unit/get-all"),deleteOrganizationUnit:async e=>{await n.F.delete(`/api/admin/organization-unit/${e}`)}}},52162:(e,t,r)=>{"use strict";r.d(t,{AdminRouteGuard:()=>s});var n=r(60687),i=r(86522),o=r(16189);r(43210);var a=r(85726);function s({children:e,redirectPath:t="/tenant-selector",loadingComponent:r}){let{isSystemAdmin:s,isLoading:l,isLogout:c}=(0,i.A)();return((0,o.useRouter)(),l)?r||(0,n.jsxs)("div",{className:"w-full p-8 space-y-4",children:[(0,n.jsx)(a.E,{className:"h-12 w-full rounded-lg"}),(0,n.jsx)(a.E,{className:"h-60 w-full rounded-lg"}),(0,n.jsx)(a.E,{className:"h-12 w-2/3 rounded-lg"})]}):s?(0,n.jsx)(n.Fragment,{children:e}):null}},53030:(e,t,r)=>{Promise.resolve().then(r.bind(r,33882))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.default,__next_app__:()=>u,pages:()=>c,routeModule:()=>d,tree:()=>l});var n=r(65239),i=r(48088),o=r(31369),a=r(30893),s={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);r.d(t,s);let l={children:["",{children:["(systemAdmin)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1427)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96707)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\dashboard\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(systemAdmin)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},72128:(e,t,r)=>{"use strict";r.d(t,{AdminRouteGuard:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AdminRouteGuard() from the server but AdminRouteGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\admin-route-guard.tsx","AdminRouteGuard")},96287:(e,t,r)=>{"use strict";r.d(t,{default:()=>eU});var n=r(60687),i=r(44493);function o({totalUsers:e=0,totalOrganizationUnits:t=0}){return(0,n.jsxs)("div",{className:"*:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-3 grid grid-cols-1 gap-4 *:data-[slot=card]:bg-white dark:*:data-[slot=card]:bg-neutral-900",children:[(0,n.jsxs)(i.Zp,{className:"@container/card",children:[(0,n.jsxs)(i.aR,{className:"relative",children:[(0,n.jsx)(i.ZB,{children:"Users"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(i.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:e.toLocaleString()})})]}),(0,n.jsx)(i.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Total organization users"})})]}),(0,n.jsxs)(i.Zp,{className:"@container/card",children:[(0,n.jsxs)(i.aR,{className:"relative",children:[(0,n.jsx)(i.ZB,{children:"Agents"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(i.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:t.toLocaleString()})})]}),(0,n.jsx)(i.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Total active agents"})})]}),(0,n.jsxs)(i.Zp,{className:"@container/card",children:[(0,n.jsxs)(i.aR,{className:"relative",children:[(0,n.jsx)(i.ZB,{children:"Total Billing"}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(i.BT,{className:"@[250px]/card:text-3xl text-orange-600 text-2xl font-semibold tabular-nums",children:"0"})})]}),(0,n.jsx)(i.wL,{className:"flex-col items-start gap-1 text-sm",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Resources under control"})})]})]})}var a=r(43210),s=r.n(a),l=r(92491),c=r(90812),u=r(49384),d=r(277),f=r(5231),p=r.n(f),h=r(40491),m=r.n(h),y=r(18842),b=r(98986),v=r(23561),x=r(97633),g=r(22989),j=r(4057),w=r(54186),O=r(96075),k=r(20237),P=r(84629);function S(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t){if(void 0!==r&&!0!==r(e[i]))return;n.push(e[i])}return n}function N(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var o=r();return e*(t-e*o/2-n)>=0&&e*(t+e*o/2-i)<=0}function A(e){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function C(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=A(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=A(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==A(t)?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function T(e,t,r){var n,i,o,a,s,l=e.tick,c=e.ticks,u=e.viewBox,d=e.minTickGap,f=e.orientation,h=e.interval,m=e.tickFormatter,y=e.unit,b=e.angle;if(!c||!c.length||!l)return[];if((0,g.Et)(h)||k.m.isSsr)return S(c,("number"==typeof h&&(0,g.Et)(h)?h:0)+1);var v=[],x="top"===f||"bottom"===f?"width":"height",j=y&&"width"===x?(0,O.Pu)(y,{fontSize:t,letterSpacing:r}):{width:0,height:0},w=function(e,n){var i,o,a=p()(m)?m(e.value,n):e.value;return"width"===x?(o={width:(i=(0,O.Pu)(a,{fontSize:t,letterSpacing:r})).width+j.width,height:i.height+j.height},(0,P.bx)(o,b)):(0,O.Pu)(a,{fontSize:t,letterSpacing:r})[x]},A=c.length>=2?(0,g.sA)(c[1].coordinate-c[0].coordinate):1,E=(n="width"===x,i=u.x,o=u.y,a=u.width,s=u.height,1===A?{start:n?i:o,end:n?i+a:o+s}:{start:n?i+a:o+s,end:n?i:o});return"equidistantPreserveStart"===h?function(e,t,r,n,i){for(var o,a=(n||[]).slice(),s=t.start,l=t.end,c=0,u=1,d=s;u<=a.length;)if(o=function(){var t,o=null==n?void 0:n[c];if(void 0===o)return{v:S(n,u)};var a=c,f=function(){return void 0===t&&(t=r(o,a)),t},p=o.coordinate,h=0===c||N(e,p,f,d,l);h||(c=0,d=s,u+=1),h&&(d=p+e*(f()/2+i),c+=u)}())return o.v;return[]}(A,E,w,c,d):("preserveStart"===h||"preserveStartEnd"===h?function(e,t,r,n,i,o){var a=(n||[]).slice(),s=a.length,l=t.start,c=t.end;if(o){var u=n[s-1],d=r(u,s-1),f=e*(u.coordinate+e*d/2-c);a[s-1]=u=C(C({},u),{},{tickCoord:f>0?u.coordinate-f*e:u.coordinate}),N(e,u.tickCoord,function(){return d},l,c)&&(c=u.tickCoord-e*(d/2+i),a[s-1]=C(C({},u),{},{isShow:!0}))}for(var p=o?s-1:s,h=function(t){var n,o=a[t],s=function(){return void 0===n&&(n=r(o,t)),n};if(0===t){var u=e*(o.coordinate-e*s()/2-l);a[t]=o=C(C({},o),{},{tickCoord:u<0?o.coordinate-u*e:o.coordinate})}else a[t]=o=C(C({},o),{},{tickCoord:o.coordinate});N(e,o.tickCoord,s,l,c)&&(l=o.tickCoord+e*(s()/2+i),a[t]=C(C({},o),{},{isShow:!0}))},m=0;m<p;m++)h(m);return a}(A,E,w,c,d,"preserveStartEnd"===h):function(e,t,r,n,i){for(var o=(n||[]).slice(),a=o.length,s=t.start,l=t.end,c=function(t){var n,c=o[t],u=function(){return void 0===n&&(n=r(c,t)),n};if(t===a-1){var d=e*(c.coordinate+e*u()/2-l);o[t]=c=C(C({},c),{},{tickCoord:d>0?c.coordinate-d*e:c.coordinate})}else o[t]=c=C(C({},c),{},{tickCoord:c.coordinate});N(e,c.tickCoord,u,s,l)&&(l=c.tickCoord-e*(u()/2+i),o[t]=C(C({},c),{},{isShow:!0}))},u=a-1;u>=0;u--)c(u);return o}(A,E,w,c,d)).filter(function(e){return e.isShow})}var _=["viewBox"],z=["viewBox"],D=["ticks"];function G(e){return(G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function R(){return(R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){J(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function I(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function L(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Z(n.key),n)}}function U(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(U=function(){return!!e})()}function M(e){return(M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function $(e,t){return($=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function J(e,t,r){return(t=Z(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Z(e){var t=function(e,t){if("object"!=G(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=G(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==G(t)?t:t+""}var K=function(e){var t,r;function n(e){var t,r,i;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,i=[e],r=M(r),(t=function(e,t){if(t&&("object"===G(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,U()?Reflect.construct(r,i||[],M(this).constructor):r.apply(this,i))).state={fontSize:"",letterSpacing:""},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$(e,t)}(n,e),t=[{key:"shouldComponentUpdate",value:function(e,t){var r=e.viewBox,n=I(e,_),i=this.props,o=i.viewBox,a=I(i,z);return!(0,y.b)(r,o)||!(0,y.b)(n,a)||!(0,y.b)(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,r,n,i,o,a,s=this.props,l=s.x,c=s.y,u=s.width,d=s.height,f=s.orientation,p=s.tickSize,h=s.mirror,m=s.tickMargin,y=h?-1:1,b=e.tickSize||p,v=(0,g.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,a=(n=(i=c+ +!h*d)-y*b)-y*m,o=v;break;case"left":n=i=e.coordinate,o=(t=(r=l+ +!h*u)-y*b)-y*m,a=v;break;case"right":n=i=e.coordinate,o=(t=(r=l+ +h*u)+y*b)+y*m,a=v;break;default:t=r=e.coordinate,a=(n=(i=c+ +h*d)+y*b)+y*m,o=v}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,r=t.orientation,n=t.mirror;switch(r){case"left":e=n?"start":"end";break;case"right":e=n?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,r=e.mirror,n="end";switch(t){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,i=e.height,o=e.orientation,a=e.mirror,l=e.axisLine,c=F(F(F({},(0,w.J9)(this.props,!1)),(0,w.J9)(l,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var d=+("top"===o&&!a||"bottom"===o&&a);c=F(F({},c),{},{x1:t,y1:r+d*i,x2:t+n,y2:r+d*i})}else{var f=+("left"===o&&!a||"right"===o&&a);c=F(F({},c),{},{x1:t+f*n,y1:r,x2:t+f*n,y2:r+i})}return s().createElement("line",R({},c,{className:(0,u.A)("recharts-cartesian-axis-line",m()(l,"className"))}))}},{key:"renderTicks",value:function(e,t,r){var i=this,o=this.props,a=o.tickLine,l=o.stroke,c=o.tick,d=o.tickFormatter,f=o.unit,h=T(F(F({},this.props),{},{ticks:e}),t,r),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),x=(0,w.J9)(this.props,!1),g=(0,w.J9)(c,!1),O=F(F({},x),{},{fill:"none"},(0,w.J9)(a,!1)),k=h.map(function(e,t){var r=i.getTickLineCoord(e),o=r.line,w=r.tick,k=F(F(F(F({textAnchor:y,verticalAnchor:v},x),{},{stroke:"none",fill:l},g),w),{},{index:t,payload:e,visibleTicksCount:h.length,tickFormatter:d});return s().createElement(b.W,R({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,j.XC)(i.props,e,t)),a&&s().createElement("line",R({},O,o,{className:(0,u.A)("recharts-cartesian-axis-tick-line",m()(a,"className"))})),c&&n.renderTickItem(c,k,"".concat(p()(d)?d(e.value,t):e.value).concat(f||"")))});return s().createElement("g",{className:"recharts-cartesian-axis-ticks"},k)}},{key:"render",value:function(){var e=this,t=this.props,r=t.axisLine,n=t.width,i=t.height,o=t.ticksGenerator,a=t.className;if(t.hide)return null;var l=this.props,c=l.ticks,d=I(l,D),f=c;return(p()(o)&&(f=o(c&&c.length>0?this.props:d)),n<=0||i<=0||!f||!f.length)?null:s().createElement(b.W,{className:(0,u.A)("recharts-cartesian-axis",a),ref:function(t){e.layerReference=t}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),x.J.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return s().isValidElement(e)?s().cloneElement(e,t):p()(e)?e(t):s().createElement(v.E,R({},t,{className:"recharts-cartesian-axis-tick-value"}),r)}}],t&&L(n.prototype,t),r&&L(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.Component);J(K,"displayName","CartesianAxis"),J(K,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var q=r(30087);function W(e){return(W="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function V(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(V=function(){return!!e})()}function Y(e){return(Y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function X(e,t){return(X=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function H(e,t,r){return(t=Q(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Q(e){var t=function(e,t){if("object"!=W(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=W(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==W(t)?t:t+""}function ee(){return(ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function et(e){var t=e.xAxisId,r=(0,d.yi)(),n=(0,d.rY)(),i=(0,d.AF)(t);return null==i?null:s().createElement(K,ee({},i,{className:(0,u.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return(0,q.Rh)(e,!0)}}))}var er=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=Y(e),function(e,t){if(t&&("object"===W(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,V()?Reflect.construct(e,t||[],Y(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&X(e,t)}(r,e),t=[{key:"render",value:function(){return s().createElement(et,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Q(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(s().Component);function en(e){return(en="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}H(er,"displayName","XAxis"),H(er,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function ei(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ei=function(){return!!e})()}function eo(e){return(eo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ea(e,t){return(ea=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function es(e,t,r){return(t=el(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function el(e){var t=function(e,t){if("object"!=en(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=en(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==en(t)?t:t+""}function ec(){return(ec=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var eu=function(e){var t=e.yAxisId,r=(0,d.yi)(),n=(0,d.rY)(),i=(0,d.Nk)(t);return null==i?null:s().createElement(K,ec({},i,{className:(0,u.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return(0,q.Rh)(e,!0)}}))},ed=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=eo(e),function(e,t){if(t&&("object"===en(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,ei()?Reflect.construct(e,t||[],eo(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ea(e,t)}(r,e),t=[{key:"render",value:function(){return s().createElement(eu,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,el(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(s().Component);es(ed,"displayName","YAxis"),es(ed,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var ef=(0,l.gu)({chartName:"BarChart",GraphicalChild:c.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:er},{axisType:"yAxis",AxisComp:ed}],formatAxisMap:P.pr}),ep=r(10521),eh=["x1","y1","x2","y2","key"],em=["offset"];function ey(e){return(ey="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ev(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eb(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=ey(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ey(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ey(t)?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ex(){return(ex=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function eg(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var ej=function(e){var t=e.fill;if(!t||"none"===t)return null;var r=e.fillOpacity,n=e.x,i=e.y,o=e.width,a=e.height,l=e.ry;return s().createElement("rect",{x:n,y:i,ry:l,width:o,height:a,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function ew(e,t){var r;if(s().isValidElement(e))r=s().cloneElement(e,t);else if(p()(e))r=e(t);else{var n=t.x1,i=t.y1,o=t.x2,a=t.y2,l=t.key,c=eg(t,eh),u=(0,w.J9)(c,!1),d=(u.offset,eg(u,em));r=s().createElement("line",ex({},d,{x1:n,y1:i,x2:o,y2:a,fill:"none",key:l}))}return r}function eO(e){var t=e.x,r=e.width,n=e.horizontal,i=void 0===n||n,o=e.horizontalPoints;if(!i||!o||!o.length)return null;var a=o.map(function(n,o){return ew(i,ev(ev({},e),{},{x1:t,y1:n,x2:t+r,y2:n,key:"line-".concat(o),index:o}))});return s().createElement("g",{className:"recharts-cartesian-grid-horizontal"},a)}function ek(e){var t=e.y,r=e.height,n=e.vertical,i=void 0===n||n,o=e.verticalPoints;if(!i||!o||!o.length)return null;var a=o.map(function(n,o){return ew(i,ev(ev({},e),{},{x1:n,y1:t,x2:n,y2:t+r,key:"line-".concat(o),index:o}))});return s().createElement("g",{className:"recharts-cartesian-grid-vertical"},a)}function eP(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,o=e.width,a=e.height,l=e.horizontalPoints,c=e.horizontal;if(!(void 0===c||c)||!t||!t.length)return null;var u=l.map(function(e){return Math.round(e+i-i)}).sort(function(e,t){return e-t});i!==u[0]&&u.unshift(0);var d=u.map(function(e,l){var c=u[l+1]?u[l+1]-e:i+a-e;if(c<=0)return null;var d=l%t.length;return s().createElement("rect",{key:"react-".concat(l),y:e,x:n,height:c,width:o,stroke:"none",fill:t[d],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return s().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},d)}function eS(e){var t=e.vertical,r=e.verticalFill,n=e.fillOpacity,i=e.x,o=e.y,a=e.width,l=e.height,c=e.verticalPoints;if(!(void 0===t||t)||!r||!r.length)return null;var u=c.map(function(e){return Math.round(e+i-i)}).sort(function(e,t){return e-t});i!==u[0]&&u.unshift(0);var d=u.map(function(e,t){var c=u[t+1]?u[t+1]-e:i+a-e;if(c<=0)return null;var d=t%r.length;return s().createElement("rect",{key:"react-".concat(t),x:e,y:o,width:c,height:l,stroke:"none",fill:r[d],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return s().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},d)}var eN=function(e,t){var r=e.xAxis,n=e.width,i=e.height,o=e.offset;return(0,q.PW)(T(ev(ev(ev({},K.defaultProps),r),{},{ticks:(0,q.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,t)},eA=function(e,t){var r=e.yAxis,n=e.width,i=e.height,o=e.offset;return(0,q.PW)(T(ev(ev(ev({},K.defaultProps),r),{},{ticks:(0,q.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,t)},eE={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function eC(e){var t,r,n,i,o,a,l=(0,d.yi)(),c=(0,d.rY)(),u=(0,d.hj)(),f=ev(ev({},e),{},{stroke:null!==(t=e.stroke)&&void 0!==t?t:eE.stroke,fill:null!==(r=e.fill)&&void 0!==r?r:eE.fill,horizontal:null!==(n=e.horizontal)&&void 0!==n?n:eE.horizontal,horizontalFill:null!==(i=e.horizontalFill)&&void 0!==i?i:eE.horizontalFill,vertical:null!==(o=e.vertical)&&void 0!==o?o:eE.vertical,verticalFill:null!==(a=e.verticalFill)&&void 0!==a?a:eE.verticalFill,x:(0,g.Et)(e.x)?e.x:u.left,y:(0,g.Et)(e.y)?e.y:u.top,width:(0,g.Et)(e.width)?e.width:u.width,height:(0,g.Et)(e.height)?e.height:u.height}),h=f.x,m=f.y,y=f.width,b=f.height,v=f.syncWithTicks,x=f.horizontalValues,j=f.verticalValues,w=(0,d.pj)(),O=(0,d.$G)();if(!(0,g.Et)(y)||y<=0||!(0,g.Et)(b)||b<=0||!(0,g.Et)(h)||h!==+h||!(0,g.Et)(m)||m!==+m)return null;var k=f.verticalCoordinatesGenerator||eN,P=f.horizontalCoordinatesGenerator||eA,S=f.horizontalPoints,N=f.verticalPoints;if((!S||!S.length)&&p()(P)){var A=x&&x.length,E=P({yAxis:O?ev(ev({},O),{},{ticks:A?x:O.ticks}):void 0,width:l,height:c,offset:u},!!A||v);(0,ep.R)(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(ey(E),"]")),Array.isArray(E)&&(S=E)}if((!N||!N.length)&&p()(k)){var C=j&&j.length,T=k({xAxis:w?ev(ev({},w),{},{ticks:C?j:w.ticks}):void 0,width:l,height:c,offset:u},!!C||v);(0,ep.R)(Array.isArray(T),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(ey(T),"]")),Array.isArray(T)&&(N=T)}return s().createElement("g",{className:"recharts-cartesian-grid"},s().createElement(ej,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),s().createElement(eO,ex({},f,{offset:u,horizontalPoints:S,xAxis:w,yAxis:O})),s().createElement(ek,ex({},f,{offset:u,verticalPoints:N,xAxis:w,yAxis:O})),s().createElement(eP,ex({},f,{horizontalPoints:S})),s().createElement(eS,ex({},f,{verticalPoints:N})))}eC.displayName="CartesianGrid";var eT=r(17687);let e_={views:{label:"System Statistics"},users:{label:"Users",color:"var(--chart-1)"},organizationUnits:{label:"Organization Units",color:"var(--chart-2)"}};function ez({users:e=[],organizationUnits:t=[]}){let[r,o]=a.useState("users"),s=a.useMemo(()=>{let r=new Date,n=Array.from({length:30},(e,t)=>{let n=new Date(r);return n.setDate(n.getDate()-(29-t)),n.toISOString().split("T")[0]}),i=e.reduce(e=>{let t=Math.floor(50*n.length),r=n[t];return e[r]=(e[r]||0)+1,e},{}),o=t.reduce((e,t)=>{let r;try{if(t.createdAt){let e=t.createdAt.includes(".")?t.createdAt.split(".")[0]+"Z":t.createdAt;r=new Date(e).toISOString().split("T")[0]}else r=new Date().toISOString().split("T")[0]}catch{r=new Date().toISOString().split("T")[0]}return n.includes(r)&&(e[r]=(e[r]||0)+1),e},{});return n.map(e=>({date:e,users:i[e]||0,organizationUnits:o[e]||0}))},[e,t]),l=a.useMemo(()=>({users:e.length,organizationUnits:t.length}),[e,t]);return(0,n.jsxs)(i.Zp,{className:"py-0 dark:*:data-[slot=card]:bg-neutral-900 ",children:[(0,n.jsxs)(i.aR,{className:"flex flex-col items-stretch border-b !p-0 sm:flex-row",children:[(0,n.jsxs)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 pt-4 pb-3 sm:!py-0",children:[(0,n.jsx)(i.ZB,{children:"System Statistics - Interactive"}),(0,n.jsx)(i.BT,{children:"Showing system data for the last 30 days"})]}),(0,n.jsx)("div",{className:"flex",children:["users","organizationUnits"].map(e=>(0,n.jsxs)("button",{"data-active":r===e,className:"data-[active=true]:bg-muted/50 relative z-30 flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6",onClick:()=>o(e),children:[(0,n.jsx)("span",{className:"text-muted-foreground text-xs",children:e_[e].label}),(0,n.jsx)("span",{className:"text-lg text-orange-600 leading-none font-bold sm:text-3xl",children:l[e].toLocaleString()})]},e))})]}),(0,n.jsx)(i.Wu,{className:"px-2 sm:p-6",children:(0,n.jsx)(eT.at,{config:e_,className:"aspect-auto h-[250px] w-full",children:(0,n.jsxs)(ef,{accessibilityLayer:!0,data:s,margin:{left:12,right:12},children:[(0,n.jsx)(eC,{vertical:!1}),(0,n.jsx)(er,{dataKey:"date",tickLine:!1,axisLine:!1,tickMargin:8,minTickGap:32,tickFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"})}),(0,n.jsx)(eT.II,{content:(0,n.jsx)(eT.Nt,{className:"w-[150px]",nameKey:"views",labelFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})})}),(0,n.jsx)(c.y,{dataKey:r,fill:`var(--color-${r})`})]})})})]})}var eD=r(25541);let eG=[{month:"January",desktop:186,mobile:80},{month:"February",desktop:305,mobile:200},{month:"March",desktop:237,mobile:120},{month:"April",desktop:73,mobile:190},{month:"May",desktop:209,mobile:130},{month:"June",desktop:214,mobile:140}],eR={desktop:{label:"Desktop",color:"var(--chart-1)"},mobile:{label:"Mobile",color:"var(--chart-2)"}};function eB(){return(0,n.jsxs)(i.Zp,{className:"dark:*:data-[slot=card]:bg-neutral-900 ",children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{children:"Bar Chart - Multiple"}),(0,n.jsx)(i.BT,{children:"January - June 2024"})]}),(0,n.jsx)(i.Wu,{children:(0,n.jsx)(eT.at,{config:eR,className:"aspect-auto h-[250px] w-full",children:(0,n.jsxs)(ef,{accessibilityLayer:!0,data:eG,children:[(0,n.jsx)(eC,{vertical:!1}),(0,n.jsx)(er,{dataKey:"month",tickLine:!1,tickMargin:10,axisLine:!1,tickFormatter:e=>e.slice(0,3)}),(0,n.jsx)(eT.II,{cursor:!1,content:(0,n.jsx)(eT.Nt,{indicator:"dashed"})}),(0,n.jsx)(c.y,{dataKey:"desktop",fill:"var(--color-desktop)",radius:4}),(0,n.jsx)(c.y,{dataKey:"mobile",fill:"var(--color-mobile)",radius:4})]})})}),(0,n.jsxs)(i.wL,{className:"flex-col items-start gap-2 text-sm",children:[(0,n.jsxs)("div",{className:"flex gap-2 leading-none font-medium",children:["Trending up by 5.2% this month ",(0,n.jsx)(eD.A,{className:"h-4 w-4"})]}),(0,n.jsx)("div",{className:"text-muted-foreground leading-none",children:"Showing total visitors for the last 6 months"})]})]})}var eF=r(47565),eI=r(70891),eL=r(31207);function eU(){let{data:e}=(0,eL.Ay)(eI.DC.adminUsers(),()=>eF.i.getAllUsers()),{data:t}=(0,eL.Ay)(eI.DC.adminAllOrganizationUnits(),()=>eF.i.getAllOrganizationUnits()),r=e?.length??0,i=t?.length??0;return(0,n.jsx)("div",{className:"flex flex-1 flex-col bg-muted/20 min-h-screen",children:(0,n.jsxs)("div",{className:"@container/main flex flex-1 flex-col gap-4 p-2 sm:p-4 lg:p-6 dark:bg-black/60",children:[(0,n.jsx)("div",{className:"rounded-xl",children:(0,n.jsx)(o,{totalUsers:r,totalOrganizationUnits:i})}),(0,n.jsx)("div",{className:"@container/main flex flex-1 flex-col gap-4 p-2 sm:p-4 lg:p-0 dark:bg-black/60",children:(0,n.jsx)(ez,{users:e,organizationUnits:t})}),(0,n.jsx)("div",{className:"@container/main flex flex-1 flex-col gap-4 p-2 sm:p-4 lg:p-0 dark:bg-black/60",children:(0,n.jsx)(eB,{})})]})})}},96707:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(37413),i=r(50417),o=r(48974),a=r(31057),s=r(92588),l=r(2505),c=r(72128);function u({children:e}){return(0,n.jsx)(c.AdminRouteGuard,{children:(0,n.jsx)(l.ChatProvider,{children:(0,n.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,n.jsxs)(i.SidebarProvider,{className:"flex flex-col",children:[(0,n.jsx)(a.SiteHeader,{}),(0,n.jsxs)("div",{className:"flex flex-1",children:[(0,n.jsx)(o.AppSidebar,{}),(0,n.jsx)(i.SidebarInset,{children:(0,n.jsx)(s.SearchProvider,{children:(0,n.jsx)("main",{className:"",children:e})})})]})]})})})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7966,5584,5156,4654,6467,2900,6763,519],()=>r(69249));module.exports=n})();