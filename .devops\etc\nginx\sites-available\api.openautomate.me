server {
    server_name api.openautomate.me;

    location / {
        proxy_pass         http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header   Upgrade $http_upgrade;
        proxy_set_header   Connection "upgrade"; # Changed from keep-alive to upgrade
        proxy_set_header   Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400; # Add longer timeout for persistent connections
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/api.openautomate.me/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/api.openautomate.me/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = api.openautomate.me) {
        return 301 https://$host$request_uri;
    } # managed by Certbot
    
    server_name api.openautomate.me;
    listen 80;
    return 404; # managed by Certbot
}