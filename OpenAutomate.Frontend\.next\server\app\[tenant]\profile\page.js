(()=>{var e={};e.id=4745,e.ids=[4745],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24520:(e,t,s)=>{"use strict";s.d(t,{default:()=>P});var r=s(60687),a=s(43210),n=s(29523),i=s(44493),l=s(89667),o=s(80013),d=s(58869),c=s(41862),u=s(62688);let m=(0,u.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var p=s(31568),h=s(96834),f=s(99891),x=s(20140);let v=(0,u.A)("lock-keyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]);var y=s(13861),g=s(12597),j=s(78706),w=s(7854);function b(){let[e,t]=(0,a.useState)(!1),[s,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),{toast:p}=(0,x.d)(),[h,f]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),b=(e,t)=>{f(s=>({...s,[e]:t}))},P=async e=>{if(e.preventDefault(),h.newPassword!==h.confirmPassword){p({title:"Error",description:"New passwords do not match",variant:"destructive"});return}if(h.newPassword.length<8){p({title:"Error",description:"Password must be at least 8 characters long",variant:"destructive"});return}t(!0);try{await j.Z.changePassword({currentPassword:h.currentPassword,newPassword:h.newPassword,confirmNewPassword:h.confirmPassword}),p({title:"Success",description:"Password changed successfully"}),f({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){(0,w.AG)(e)}finally{t(!1)}};return(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(v,{className:"h-5 w-5"}),"Change Password"]}),(0,r.jsx)(i.BT,{children:"Update your account password for enhanced security"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("form",{onSubmit:P,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.p,{id:"currentPassword",type:s?"text":"password",value:h.currentPassword,onChange:e=>b("currentPassword",e.target.value),required:!0,disabled:e}),(0,r.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>d(!s),disabled:e,children:s?(0,r.jsx)(y.A,{className:"h-4 w-4"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"newPassword",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.p,{id:"newPassword",type:u?"text":"password",value:h.newPassword,onChange:e=>b("newPassword",e.target.value),required:!0,disabled:e}),(0,r.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>m(!u),disabled:e,children:u?(0,r.jsx)(y.A,{className:"h-4 w-4"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(l.p,{id:"confirmPassword",type:"password",value:h.confirmPassword,onChange:e=>b("confirmPassword",e.target.value),required:!0,disabled:e})})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-2 pt-4",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>f({currentPassword:"",newPassword:"",confirmPassword:""}),disabled:e,children:"Cancel"}),(0,r.jsxs)(n.$,{type:"submit",disabled:e,children:[e&&(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Change Password"]})]})]})})]})}function P(){let[e,t]=(0,a.useState)(!1),[s,u]=(0,a.useState)(!1),{user:v,updateUser:y}=(0,p.A)(),{toast:g}=(0,x.d)(),[w,P]=(0,a.useState)({firstName:v?.firstName,lastName:v?.lastName,email:v?.email,systemRole:v?.systemRole}),N=(e,t)=>{P(s=>({...s,[e]:t}))},C=async()=>{if(!v?.id||!w.firstName||!w.lastName){g({title:"Error",description:"Please fill in all required fields",variant:"default"});return}u(!0);try{await j.Z.changeUserName(v.id,{firstName:w.firstName,lastName:w.lastName}),y({firstName:w.firstName,lastName:w.lastName}),g({title:"Success",description:"Profile updated successfully"}),t(!1)}catch(e){g({title:"Error",description:e instanceof Error?e.message:"Failed to update profile",variant:"default"})}finally{u(!1)}};return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(i.ZB,{children:"Personal Information"}),(0,r.jsx)(i.BT,{children:"Your basic account details"})]}),e?(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(n.$,{onClick:C,size:"sm",disabled:s,children:[s?(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(m,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,r.jsx)(n.$,{onClick:()=>{P({firstName:v?.firstName,lastName:v?.lastName,email:v?.email,systemRole:v?.systemRole}),t(!1)},variant:"outline",size:"sm",disabled:s,children:"Cancel"})]}):(0,r.jsxs)(n.$,{onClick:()=>t(!0),variant:"outline",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Edit Profile"]})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"firstName",children:"First Name"}),e?(0,r.jsx)(l.p,{id:"firstName",value:w.firstName,onChange:e=>N("firstName",e.target.value)}):(0,r.jsx)("div",{className:"p-2 bg-muted rounded-md",children:v?.firstName})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"lastName",children:"Last Name"}),e?(0,r.jsx)(l.p,{id:"lastName",value:w.lastName,onChange:e=>N("lastName",e.target.value)}):(0,r.jsx)("div",{className:"p-2 bg-muted rounded-md",children:v?.lastName})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"email",children:"Email Address"}),e?(0,r.jsx)(l.p,{id:"email",type:"email",value:w.email,onChange:e=>N("email",e.target.value)}):(0,r.jsx)("div",{className:"p-2 bg-muted rounded-md",children:v?.email})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{className:"text-sm font-medium text-muted-foreground",children:"System Role"}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:v?.systemRole===0?(0,r.jsx)(h.E,{variant:"secondary",className:"flex items-center gap-1.5 px-3 py-1",children:"User"}):(0,r.jsxs)(h.E,{variant:"default",className:"flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r from-orange-500 to-red-500",children:[(0,r.jsx)(f.A,{className:"h-3.5 w-3.5"}),"Admin"]})})]})]})]}),(0,r.jsx)(b,{})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29377:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=s(65239),a=s(48088),n=s(31369),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let o={children:["",{children:["[tenant]",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,77857)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\profile\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[tenant]/profile/page",pathname:"/[tenant]/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},31599:(e,t,s)=>{"use strict";s.d(t,{c:()=>o});var r=s(43210),a=s(39989),n=s(16189),i=s(31207),l=s(70891);function o(){let e=(0,n.useRouter)(),{data:t,error:s,isLoading:o,mutate:d}=(0,i.Ay)(l.DC.organizationUnits(),()=>a.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:o,error:s?"Failed to fetch organization units. Please try again later.":null,refresh:d,selectOrganizationUnit:(0,r.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},39989:(e,t,s)=>{"use strict";s.d(t,{K:()=>a});var r=s(51787);let a={getMyOrganizationUnits:async()=>await r.F.get("/api/ou/my-ous"),getBySlug:async e=>await r.F.get(`/api/ou/slug/${e}`),getById:async e=>await r.F.get(`/api/ou/${e}`),create:async e=>await r.F.post("/api/ou/create",e),update:async(e,t)=>await r.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await r.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await r.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await r.F.get(`/api/ou/${e}/deletion-status`)}},58486:(e,t,s)=>{Promise.resolve().then(s.bind(s,24520))},61018:(e,t,s)=>{"use strict";s.d(t,{TenantGuard:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},61170:(e,t,s)=>{"use strict";s.d(t,{b:()=>c});var r=s(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}s(51215);var n=s(60687),i=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...n}=e;if(r.isValidElement(s)){var i;let e,l;let o=(i=s,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let s={...t};for(let r in t){let a=e[r],n=t[r];/^on[A-Z]/.test(r)?a&&n?s[r]=(...e)=>{n(...e),a(...e)}:a&&(s[r]=a):"style"===r?s[r]={...a,...n}:"className"===r&&(s[r]=[a,n].filter(Boolean).join(" "))}return{...e,...s}}(n,s.props);return s.type!==r.Fragment&&(d.ref=t?function(...e){return t=>{let s=!1,r=e.map(e=>{let r=a(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():a(e[t],null)}}}}(t,o):o),r.cloneElement(s,d)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=r.forwardRef((e,s)=>{let{children:a,...i}=e,o=r.Children.toArray(a),d=o.find(l);if(d){let e=d.props.children,a=o.map(t=>t!==d?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...i,ref:s,children:a})});return s.displayName=`${e}.Slot`,s}(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?s:t,{...i,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),d=r.forwardRef((e,t)=>(0,n.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64656:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(37413),a=s(48974),n=s(31057),i=s(50417),l=s(92588),o=s(61018),d=s(2505);function c({children:e}){return(0,r.jsx)(o.TenantGuard,{children:(0,r.jsx)(d.ChatProvider,{children:(0,r.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,r.jsxs)(i.SidebarProvider,{className:"flex flex-col",children:[(0,r.jsx)(n.SiteHeader,{}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)(a.AppSidebar,{}),(0,r.jsx)(i.SidebarInset,{children:(0,r.jsx)(l.SearchProvider,{children:(0,r.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,s)=>{"use strict";s.d(t,{TenantGuard:()=>o});var r=s(60687),a=s(43210),n=s(16189),i=s(31599),l=s(31568);function o({children:e}){let{tenant:t}=(0,n.useParams)();(0,n.useRouter)();let{isAuthenticated:s,isLoading:o}=(0,l.A)(),{organizationUnits:d,isLoading:c}=(0,i.c)(),[u,m]=(0,a.useState)(!0);return o||c||u?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,r.jsx)(r.Fragment,{children:e})}},71541:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\profile\\\\profile.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\profile\\profile.tsx","default")},72826:(e,t,s)=>{Promise.resolve().then(s.bind(s,69231)),Promise.resolve().then(s.bind(s,83847)),Promise.resolve().then(s.bind(s,78526)),Promise.resolve().then(s.bind(s,97597)),Promise.resolve().then(s.bind(s,98641)),Promise.resolve().then(s.bind(s,80110))},77857:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>n});var r=s(37413),a=s(71541);let n={title:"Automation",description:"Agent management page"};function i(){return(0,r.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,r.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,r.jsx)(a.default,{})]})}},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var r=s(60687);s(43210);var a=s(61170),n=s(36966);function i({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},83442:(e,t,s)=>{Promise.resolve().then(s.bind(s,61018)),Promise.resolve().then(s.bind(s,2505)),Promise.resolve().then(s.bind(s,92588)),Promise.resolve().then(s.bind(s,48974)),Promise.resolve().then(s.bind(s,31057)),Promise.resolve().then(s.bind(s,50417))},95438:(e,t,s)=>{Promise.resolve().then(s.bind(s,71541))},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7966,5584,5156,4654,6467,6763,519],()=>s(29377));module.exports=r})();