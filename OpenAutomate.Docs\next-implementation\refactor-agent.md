# Refactoring Plan: Agent Re-Connection Logic

## 1. Executive Summary & Goals

This document outlines a refactoring plan to resolve a critical issue in the Bot Agent application where it fails to connect to a new tenant after the configuration is updated in the UI. The agent incorrectly uses a cached backend URL, requiring users to manually delete configuration files to establish a new connection.

The primary goals of this plan are:
*   **Goal 1: Automate Configuration Refresh:** Implement a mechanism to automatically detect changes in the Orchestrator URL and invalidate stale configuration, specifically the cached `BackendApiUrl`.
*   **Goal 2: Force Backend Discovery:** Ensure that after an Orchestrator URL change, the agent service re-runs its backend discovery process to fetch the correct API endpoint before attempting to connect.
*   **Goal 3: Improve User Experience:** Eliminate the need for manual file deletion, providing a seamless experience when switching tenants or agent connections directly from the UI.

## 2. Current Situation Analysis

Based on the user task and an analysis of the provided file structure, the current workflow has a significant flaw:

*   **Problematic Caching:** The `OpenAutomate.BotAgent.Service` reads its configuration from a `config.json` file located in `%programdata%/BotAgent`. This file, represented by the `BotAgentConfig.cs` model, stores both the user-provided `OrchestratorUrl` and a discovered `BackendApiUrl`.
*   **Lack of Invalidation:** When the user updates the `OrchestratorUrl` in the `OpenAutomate.BotAgent.UI`, the `ApiServer.cs` in the service updates the `config.json` file. However, it does not clear the now-invalid `BackendApiUrl`.
*   **Connection Failure:** The `BotAgentSignalRClient.cs`, which handles the connection, sees an existing `BackendApiUrl` in the configuration and attempts to use it, bypassing the discovery logic. This results in a connection failure, as the cached URL points to the old tenant's backend.
*   **Pain Point:** The only way for the user to force the backend discovery to run again is to manually delete the `config.json` file, which is poor design and not intuitive.

## 3. Proposed Solution / Refactoring Strategy

### 3.1. High-Level Design / Architectural Overview

The proposed solution is a targeted fix that introduces invalidation logic at the point of configuration change. We will modify the local API server within the Bot Agent Service to intelligently clear the cached `BackendApiUrl` whenever the `OrchestratorUrl` is modified. This change is minimal, precise, and maintains the existing separation of concerns.

The updated connection flow will be as follows:

```mermaid
sequenceDiagram
    participant UI as BotAgent.UI
    participant LocalApi as ApiServer.cs
    participant ConfigSvc as ConfigurationService.cs
    participant ServerComm as ServerCommunication.cs
    participant SignalRClient as BotAgentSignalRClient.cs
    participant Backend as Backend API

    UI->>LocalApi: POST /api/config (with new OrchestratorUrl)
    LocalApi->>ConfigSvc: GetConfiguration() (gets old config)
    LocalApi->>LocalApi: Compare old and new OrchestratorUrl
    alt OrchestratorUrl has changed
        LocalApi->>LocalApi: Set updatedConfig.BackendApiUrl = null
        LocalApi-->>UI: Log "OrchestratorUrl changed, clearing BackendApiUrl"
    end
    LocalApi->>ConfigSvc: SaveConfiguration(updated config)
    LocalApi->>ServerComm: ReconnectAsync()
    ServerComm->>SignalRClient: InitializeAsync()
    SignalRClient->>ConfigSvc: GetConfiguration()
    SignalRClient->>SignalRClient: if (BackendApiUrl is null)
    SignalRClient->>Backend: DiscoverApiUrlAsync()
    Backend-->>SignalRClient: Return new BackendApiUrl
    SignalRClient->>ConfigSvc: SaveConfiguration(with new BackendApiUrl)
    end
    SignalRClient->>Backend: Connect to SignalR Hub
```

### 3.2. Key Components / Modules

The changes will be confined to the `OpenAutomate.BotAgent.Service` project, primarily affecting one component:

*   **`Services/ApiServer.cs`**: This component's `HandleUpdateConfigAsync` method will be modified to contain the core invalidation logic. It will compare the incoming `OrchestratorUrl` with the existing one and clear the `BackendApiUrl` if they differ.

### 3.3. Detailed Action Plan / Phases

#### Phase 1: Implement Core Invalidation Logic

*   **Objective(s):** Modify the agent service to correctly handle Orchestrator URL changes by clearing the cached backend URL.
*   **Priority:** High

*   **Task 1.1:** Modify `ApiServer.cs` to Invalidate `BackendApiUrl`
    *   **Rationale/Goal:** This is the central part of the fix. By intercepting the configuration update, we can ensure stale data is purged before it's saved and used for a new connection attempt.
    *   **File to Modify:** `OpenAutomate.BotAgent.Service/Services/ApiServer.cs`
    *   **Action Details:**
        1.  Inside the `HandleUpdateConfigAsync` method, before saving the configuration, retrieve the current configuration using `_configService.GetConfiguration()`.
        2.  Compare `currentConfig.OrchestratorUrl` with the `finalOrchestratorUrl` that is about to be saved.
        3.  Add a condition: if the URLs are different, explicitly set the `BackendApiUrl` property on the `updatedConfig` object to `null`.
        4.  Add a log entry to confirm that the invalidation is happening (e.g., `_logger.LogInformation("OrchestratorUrl has changed. Clearing cached BackendApiUrl to force re-discovery.");`).
    *   **Estimated Effort (Optional):** S
    *   **Deliverable/Criteria for Completion:** When the UI saves a new `OrchestratorUrl`, the resulting `config.json` file on disk must have its `BackendApiUrl` field set to `null` or be absent.

*   **Task 1.2:** Verify Discovery Logic in `BotAgentSignalRClient.cs`
    *   **Rationale/Goal:** Confirm that the existing discovery logic correctly handles a null `BackendApiUrl` and initiates the discovery process. No changes are expected, but verification is crucial.
    *   **File to Review:** `OpenAutomate.BotAgent.Service/Services/BotAgentSignalRClient.cs`
    *   **Action Details:**
        1.  Review the `InitializeAsync` method.
        2.  Confirm that the code block `if (string.IsNullOrEmpty(apiUrl))` correctly triggers the call to `DiscoverApiUrlAsync`.
        3.  Confirm that the newly discovered URL is saved back to the configuration via `_configService.SaveConfiguration(config)`.
    *   **Estimated Effort (Optional):** S
    *   **Deliverable/Criteria for Completion:** A code review confirms the logic is sound and meets the requirements for re-discovery.

#### Phase 2: Improve UI Feedback and Robustness

*   **Objective(s):** Make the UI more responsive and informative during the connection process, especially when backend discovery adds a slight delay.
*   **Priority:** Medium

*   **Task 2.1:** Enhance Connection Logic in `MainViewModel.cs`
    *   **Rationale/Goal:** The connection process might now involve an extra HTTP request for discovery. The UI should reflect the "Connecting..." state accurately and provide clear feedback on success or failure.
    *   **File to Modify:** `OpenAutomate.BotAgent.UI/ViewModels/MainViewModel.cs`
    *   **Action Details:**
        1.  In the `ConnectAsync` method, after calling `_apiClient.UpdateConfigAsync(Config)`, immediately call `_apiClient.ResetConnectionStatusCache()` to ensure the next status check is fresh.
        2.  Introduce a brief `Task.Delay(2000)` to allow the service time to initiate the connection attempt.
        3.  Implement a `VerifyConnectionStatusAndUpdateUI` method that forces a fresh status check and updates the `IsConnected` property and `StatusMessage` based on the verified result from the service. This provides definitive feedback to the user.
    *   **Estimated Effort (Optional):** M
    *   **Deliverable/Criteria for Completion:** The UI correctly displays "Connecting...", followed by either "Connected" or a descriptive error message (e.g., "Failed to connect to server"). The connection status indicator is accurate.

## 4. Key Considerations & Risk Mitigation

### 4.1. Technical Risks & Challenges

*   **Risk:** The backend discovery endpoint (`/api/connection-info`) could be unavailable or fail.
    *   **Mitigation:** The `BotAgentSignalRClient` already has retry logic. The UI improvements in Phase 2 will ensure that this failure is communicated back to the user, who can then verify the Orchestrator URL.
*   **Risk:** Race conditions if multiple configuration changes happen in quick succession.
    *   **Mitigation:** The current architecture of a singleton service processing requests serially makes this unlikely. The logic change is simple and atomic.

### 4.2. Dependencies

*   The plan relies on the existing backend discovery endpoint functioning as expected.
*   The plan assumes the UI's "Connect" action correctly triggers the `POST /api/config` endpoint on the local service. Analysis confirms this is the case.

### 4.3. Non-Functional Requirements (NFRs) Addressed

*   **Usability:** Significantly improved. The primary user frustration is eliminated, making the process of switching tenants intuitive.
*   **Reliability:** Increased. The agent is now more resilient to configuration changes and can self-correct its connection parameters without manual intervention.
*   **Maintainability:** The change is localized and follows existing patterns, making the code easy to understand and maintain.

## 5. Success Metrics / Validation Criteria

The success of this refactoring will be measured by the following criteria:

1.  **Functional Test:** A user can successfully switch the agent's connection from Tenant A to Tenant B by only changing the `OrchestratorUrl` and `MachineKey` in the UI and clicking "Connect".
2.  **Negative Test:** Entering an invalid or unreachable `OrchestratorUrl` results in the UI showing a "Disconnected" status with an appropriate error message after a short timeout.
3.  **File Inspection:** After a successful connection to a new tenant, the `config.json` file in `%programdata%/BotAgent` is verified to contain the correct, newly discovered `BackendApiUrl`.
4.  **Regression Test:** Connecting with an existing, valid configuration without changing the URL does *not* trigger the discovery process unnecessarily.

## 6. Assumptions Made

*   The `OpenAutomate.BotAgent.Service` runs with sufficient permissions to read and write to its configuration file in `%programdata%/OpenAutomate/BotAgent`.
*   The user's description of the problem and the underlying cause (stale `BackendApiUrl`) is accurate. The code analysis strongly supports this assumption.

## 7. Open Questions / Areas for Further Investigation

*   There are no significant open questions at this time. The problem is well-defined, and the proposed solution is straightforward and low-risk.