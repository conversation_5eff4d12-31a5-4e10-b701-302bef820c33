(()=>{var e={};e.id=3180,e.ids=[3180],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10175:(e,s,r)=>{"use strict";r.d(s,{F:()=>I});var t=r(56363),o=r(62846),n=r(63420),a=r(81639),i=r(53702),l=r(12876),d=r(32769),c=r(5148),u=r(74286),m=r(61348),p=r(65866),h=r(88971),g=r(36440),x=r(30556),w=r(34318),f=r(13461),P=r(3492),j=r(92715),b=r(63353),v=r(56074),A=r(32250),y=r(88804),N=r(90883),R=r(59574),k=r(72832),S=r(18898),E=r(24263),F=r(62817),C=r(19321),_=r(21720),G=r(79173),z=r(44239),O=r(60343);let I={logo:t.A,close:o.A,Spinner:n.A,chevronLeft:a.A,chevronRight:i.A,trash:l.A,settings:d.A,billing:c.A,ellipsis:u.A,add:m.A,warning:p.A,user:h.A,arrowRight:g.A,help:p.A,pizza:x.A,sun:w.A,moon:f.A,laptop:P.A,gitHub:j.A,twitter:b.A,check:v.A,file:A.A,fileText:y.A,image:N.A,play:R.A,pause:k.A,home:S.A,chart:E.A,cog:F.A,logout:C.A,refresh:_.A,about:G.A,guide:z.A,contact:O.A}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23056:(e,s,r)=>{"use strict";r.d(s,{ResetPasswordForm:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ResetPasswordForm() from the server but ResetPasswordForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\forms\\reset-password-form.tsx","ResetPasswordForm")},24143:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var t=r(65239),o=r(48088),n=r(31369),a=r(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);r.d(s,i);let l={children:["",{children:["(auth)",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71426)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\reset-password\\page.tsx"]}]},{}]},{forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\reset-password\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(auth)/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34238:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,23056)),Promise.resolve().then(r.bind(r,10590))},36911:(e,s,r)=>{"use strict";r.d(s,{ResetPasswordForm:()=>j});var t=r(60687),o=r(43210),n=r(16189),a=r(27605),i=r(63442),l=r(45880),d=r(11365),c=r(29523),u=r(71669),m=r(89667),p=r(93613),h=r(14719),g=r(91821),x=r(78706);function w({password:e}){let s=(e=>{if(!e)return 0;let s=0;return e.length>=8&&(s+=1),e.length>=12&&(s+=1),/[A-Z]/.test(e)&&(s+=1),/[a-z]/.test(e)&&(s+=1),/[0-9]/.test(e)&&(s+=1),/[^A-Za-z0-9]/.test(e)&&(s+=1),Math.min(s,5)})(e),r=e=>{switch(e){case 0:default:return"bg-gray-200";case 1:return"bg-red-500";case 2:return"bg-orange-500";case 3:return"bg-yellow-500";case 4:return"bg-blue-500";case 5:return"bg-green-500"}};return(0,t.jsxs)("div",{className:"mt-1 w-full",children:[(()=>{let e=[];for(let o=0;o<5;o++)e.push((0,t.jsx)("div",{className:`h-1.5 w-full rounded-sm transition-colors ${o<s?r(s):"bg-gray-200"}`},o));return(0,t.jsx)("div",{className:"grid grid-cols-5 gap-1",children:e})})(),(0,t.jsx)("div",{className:"mt-1 text-xs text-gray-500 text-right",children:e&&(e=>{switch(e){case 0:return"Very weak";case 1:return"Weak";case 2:return"Medium";case 3:return"Good";case 4:return"Strong";case 5:return"Very strong";default:return""}})(s)})]})}let f=l.Yj().min(8,"Password must be at least 8 characters long").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),P=l.Ik({newPassword:f,confirmPassword:l.Yj()}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function j(){let e=(0,n.useRouter)(),s=(0,n.useSearchParams)(),[r,l]=o.useState(!1),[f,j]=o.useState(null),[b,v]=o.useState(!1),[A,y]=o.useState(""),N=s.get("token")||"",R=s.get("email")||"",[k,S]=o.useState("");o.useEffect(()=>{if(N){let e=N.trim();e.includes(" ")&&(e=e.replace(/\s/g,""));try{e.includes("%")&&(e=decodeURIComponent(e))}catch(e){console.warn("Error decoding token:",e)}S(e)}},[N]);let[E,F]=o.useState(!1);o.useEffect(()=>{F(!0)},[]);let C=(0,a.mN)({resolver:(0,i.u)(P),defaultValues:{newPassword:"",confirmPassword:""}});async function _(s){if(!k){j("Reset token is missing");return}l(!0),j(null),v(!1);try{if(console.log("Attempting to reset password with token:",k.substring(0,10)+"..."),console.log("Form data:",{newPassword:s.newPassword?"[PRESENT]":"[MISSING]",confirmPassword:s.confirmPassword?"[PRESENT]":"[MISSING]",passwordLength:s.newPassword?.length}),s.newPassword!==s.confirmPassword){j("Passwords do not match"),l(!1);return}let r={email:R,token:k,newPassword:s.newPassword,confirmPassword:s.confirmPassword};console.log("Sending reset password request with data:",{email:r.email,tokenLength:r.token.length,passwordLength:r.newPassword?.length||0,confirmPasswordLength:r.confirmPassword?.length||0,passwordsMatch:r.newPassword===r.confirmPassword}),await x.Z.resetPassword(r),console.log("Password reset successful"),v(!0),setTimeout(()=>{e.push("/login")},3e3)}catch(s){console.error("Password reset failed",s);let e="Password reset failed. The token may be invalid or expired.";if(s instanceof Error)e=s.message,console.error("Error instance message:",s.message);else if("object"==typeof s&&null!==s){if(console.error("Error object:",JSON.stringify(s,null,2)),s.message)e=s.message,console.error("Error message from object:",s.message);else if(s.details)e=s.details,console.error("Error details from object:",s.details);else if(s.errors){console.error("Validation errors object:",s.errors);let r=Object.entries(s.errors).map(([e,s])=>`${e}: ${Array.isArray(s)?s.join(", "):s}`).join("; ");e=`Validation errors: ${r}`}}console.error("Final error message:",e),j(e)}finally{l(!1)}}return(o.useEffect(()=>{k&&(console.log("Processed token length:",k.length),console.log("Token preview:",k.substring(0,10)+"...")),R&&console.log("Email:",R)},[k,R]),o.useEffect(()=>{k&&R?j(null):j("Invalid reset link. The token or email is missing.")},[k,R]),E)?(0,t.jsxs)("div",{className:"grid gap-6",children:[f&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:f})]}),b&&(0,t.jsxs)(g.Fc,{variant:"success",className:"border-green-500 bg-green-50",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)(g.TN,{className:"text-green-700",children:"Your password has been reset successfully! You will be redirected to the login page."})]}),(0,t.jsx)(u.lV,{...C,children:(0,t.jsxs)("form",{onSubmit:C.handleSubmit(_),className:"space-y-4",suppressHydrationWarning:!0,children:[(0,t.jsx)("div",{className:"p-3 bg-gray-50 rounded-md border w-full",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-0",children:["Resetting password for: ",(0,t.jsx)("span",{className:"font-medium",children:R})]})}),(0,t.jsx)(u.zB,{control:C.control,name:"newPassword",render:({field:e})=>(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"New Password"}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(m.p,{type:"password",placeholder:"••••••••",autoComplete:"new-password",...e,onChange:s=>{e.onChange(s),y(s.target.value)},disabled:r||b||!k})}),(0,t.jsx)(u.Rr,{className:"text-xs",children:"Use 8+ characters with a mix of uppercase, lowercase, numbers & symbols."}),(0,t.jsx)(w,{password:A}),(0,t.jsx)(u.C5,{})]})}),(0,t.jsx)(u.zB,{control:C.control,name:"confirmPassword",render:({field:e})=>(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"Confirm New Password"}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(m.p,{type:"password",placeholder:"••••••••",autoComplete:"new-password",...e,disabled:r||b||!k})}),(0,t.jsx)(u.C5,{})]})}),(0,t.jsxs)(c.$,{type:"submit",className:"w-full bg-orange-600 hover:bg-orange-700 transition-all duration-300 hover:translate-y-[-2px]",disabled:r||b||!k||!R,children:[r&&(0,t.jsx)(d.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),b?"Password Reset":"Reset Password"]})]})})]}):(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)(d.F.Spinner,{className:"h-8 w-8 animate-spin text-muted-foreground"})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71426:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m,metadata:()=>c});var t=r(37413),o=r(4536),n=r.n(o),a=r(61120),i=r(10590),l=r(10175),d=r(23056);let c={title:"Reset Password | OpenAutomate",description:"Reset your OpenAutomate account password"};function u(){return(0,t.jsx)("div",{className:"grid gap-6",children:(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)(l.F.Spinner,{className:"h-8 w-8 animate-spin text-muted-foreground"})})})}function m(e){let s=e.searchParams||{},r=s.token||"",o=s.email||"";return r&&console.log("Reset page received token length:",r.length),o&&console.log("Reset page received email:",o),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.Header,{}),(0,t.jsx)("div",{className:"container flex-1 flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold tracking-tight text-orange-600",children:"Reset Password"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enter your new password below"})]}),(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)(u,{}),children:(0,t.jsx)(d.ResetPasswordForm,{})}),(0,t.jsxs)("p",{className:"px-8 text-center text-sm text-muted-foreground",children:["Remember your password?"," ",(0,t.jsx)(n(),{href:"/login",className:"text-orange-600 underline underline-offset-4 hover:text-orange-700 font-medium transition-all duration-300 hover:underline-offset-8",children:"Sign in"})]})]})})]})}},74406:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,36911)),Promise.resolve().then(r.bind(r,64147))}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,7966,5584,5156,4654,5880,7943,5684,6053,6763,8826],()=>r(24143));module.exports=t})();