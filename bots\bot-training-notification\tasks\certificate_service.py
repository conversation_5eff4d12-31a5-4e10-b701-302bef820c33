"""
Certificate Service Module

Handles certificate generation functionality for the certificate notification bot.
Provides clean interfaces for generating certificates from Word templates with Vietnamese text support.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from .create_cer import CertificateGenerator


class CertificateService:
    """
    Service class for handling certificate generation operations.
    
    This service:
    - Manages certificate template and generation with Vietnamese text support
    - Creates PDF certificates from Word templates
    - Organizes certificate output and naming
    - Handles bulk certificate generation
    """
    
    def __init__(self, template_path: str = None, output_dir: str = None, 
                 logger: Optional[logging.Logger] = None):
        """
        Initialize the certificate service.
        
        Args:
            template_path: Path to Word template file (default: template/Template_1.docx)
            output_dir: Directory for certificate output (default: certificates)
            logger: Optional logger instance
        """
        self.logger = logger or logging.getLogger(__name__)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Set default paths
        base_path = Path(__file__).parent.parent
        self.template_path = template_path or str(base_path / "template" / "Template_1.docx")
        self.output_dir = output_dir or str(base_path / "certificates")
        
        # Certificate generator will be initialized when needed
        self.certificate_generator = None
        
        # Validate template and create output directory
        self._initialize_service()
        
        self.logger.info("Certificate service initialized with Vietnamese text support")
    
    def _initialize_service(self):
        """Initialize the certificate service and validate configuration."""
        try:
            # Check if template exists
            if not Path(self.template_path).exists():
                error_msg = f"Certificate template not found: {self.template_path}"
                self.logger.error(error_msg)
                raise FileNotFoundError(error_msg)
            
            # Create output directory
            output_path = Path(self.output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"Template: {self.template_path}")
            self.logger.info(f"Output directory: {self.output_dir}")
            
        except Exception as e:
            self.logger.error(f"Error initializing certificate service: {str(e)}")
            raise
    
    def _get_certificate_generator(self) -> CertificateGenerator:
        """
        Get or create certificate generator instance.
        
        Returns:
            CertificateGenerator instance
        """
        if self.certificate_generator is None:
            self.certificate_generator = CertificateGenerator(self.template_path)
            self.logger.info("Certificate generator initialized with Vietnamese text support")
        
        return self.certificate_generator
    
    def _ensure_utf8_encoding(self, text: str) -> str:
        """
        Ensure text is properly encoded for Vietnamese characters in certificates.
        
        Args:
            text: Input text string
            
        Returns:
            Properly encoded text string
        """
        if not text:
            return text
            
        try:
            # Ensure the text is properly decoded/encoded as UTF-8
            if isinstance(text, bytes):
                text = text.decode('utf-8')
            elif isinstance(text, str):
                # Ensure it can be encoded/decoded properly
                text = text.encode('utf-8').decode('utf-8')
            return text
        except (UnicodeDecodeError, UnicodeEncodeError) as e:
            self.logger.warning(f"Encoding issue with certificate text: {e}")
            # Fallback: remove problematic characters
            return text.encode('utf-8', errors='replace').decode('utf-8')
    
    def generate_certificate(self, student_data: Dict[str, Any], 
                           custom_filename: str = None) -> Optional[str]:
        """
        Generate a certificate for a student with proper Vietnamese text encoding.
        
        Args:
            student_data: Student data for certificate generation
            custom_filename: Custom filename (if None, generates automatically)
            
        Returns:
            Path to generated certificate file, or None if failed
        """
        try:
            # Validate student data
            if not self._validate_student_data(student_data):
                return None
            
            # Ensure all text fields are properly encoded for Vietnamese
            encoded_student_data = {}
            for key, value in student_data.items():
                if isinstance(value, str):
                    encoded_student_data[key] = self._ensure_utf8_encoding(value)
                else:
                    encoded_student_data[key] = value
            
            # Generate filename
            if custom_filename:
                filename = custom_filename
            else:
                filename = self._generate_filename(encoded_student_data)
            
            certificate_path = Path(self.output_dir) / filename
            
            # Generate certificate with properly encoded data
            generator = self._get_certificate_generator()
            success = generator.generate_certificate(
                encoded_student_data, 
                str(certificate_path), 
                "pdf"
            )
            
            if success and certificate_path.exists():
                self.logger.info(f"Certificate with Vietnamese text generated: {certificate_path.name}")
                return str(certificate_path)
            else:
                self.logger.error(f"Failed to generate certificate for {encoded_student_data.get('TEN_UNG_VIEN', 'Unknown')}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error generating certificate: {str(e)}")
            return None
    
    def _generate_filename(self, student_data: Dict[str, Any]) -> str:
        """
        Generate filename for certificate with proper Vietnamese text handling.
        
        Args:
            student_data: Student data dictionary
            
        Returns:
            Generated filename
        """
        employee_id = student_data.get('MA_NHAN_VIEN', 'UNKNOWN')
        student_name = student_data.get('TEN_UNG_VIEN', 'Unknown')
        
        # Clean name for filename - handle Vietnamese characters properly
        clean_name = self._sanitize_filename(student_name)
        
        return f"{employee_id}_{clean_name}_certificate_{self.timestamp}.pdf"
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename to be filesystem-safe while preserving Vietnamese characters where possible.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        # Replace problematic characters but keep Vietnamese characters where possible
        import re
        
        # Remove or replace characters that are problematic for filenames
        # Keep Vietnamese characters (Unicode ranges) but remove filesystem-unsafe chars
        clean_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        clean_name = re.sub(r'\s+', '_', clean_name)  # Replace spaces with underscores
        clean_name = clean_name.strip('._')  # Remove leading/trailing dots and underscores
        
        # Limit length
        if len(clean_name) > 50:
            clean_name = clean_name[:50]
        
        return clean_name if clean_name else 'Unknown'
    
    def _validate_student_data(self, student_data: Dict[str, Any]) -> bool:
        """
        Validate student data for certificate generation.
        
        Args:
            student_data: Student data dictionary
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ['TEN_UNG_VIEN', 'TEN_KHOA_HOC', 'MA_NHAN_VIEN']
        
        for field in required_fields:
            if not student_data.get(field) or str(student_data[field]).strip() == '':
                self.logger.error(f"Missing or empty required field for certificate: {field}")
                return False
        
        return True
    
    def generate_bulk_certificates(self, students_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate certificates for multiple students.
        
        Args:
            students_data: List of student data dictionaries
            
        Returns:
            Dictionary with generation results
        """
        results = {
            'total_attempted': len(students_data),
            'certificates_generated': 0,
            'certificates_failed': 0,
            'generated_files': [],
            'failed_students': []
        }
        
        self.logger.info(f"Starting bulk certificate generation for {len(students_data)} students")
        
        for student_data in students_data:
            try:
                certificate_path = self.generate_certificate(student_data)
                
                if certificate_path:
                    results['certificates_generated'] += 1
                    results['generated_files'].append({
                        'student_name': student_data.get('TEN_UNG_VIEN', 'Unknown'),
                        'employee_id': student_data.get('MA_NHAN_VIEN', 'Unknown'),
                        'certificate_path': certificate_path
                    })
                else:
                    results['certificates_failed'] += 1
                    results['failed_students'].append(student_data.get('TEN_UNG_VIEN', 'Unknown'))
                    
            except Exception as e:
                results['certificates_failed'] += 1
                student_name = student_data.get('TEN_UNG_VIEN', 'Unknown')
                results['failed_students'].append(student_name)
                self.logger.error(f"Error generating certificate for {student_name}: {str(e)}")
        
        self.logger.info(f"Bulk generation completed: {results['certificates_generated']} generated, "
                        f"{results['certificates_failed']} failed")
        
        return results
    
    def get_certificate_info(self, certificate_path: str) -> Dict[str, Any]:
        """
        Get information about a generated certificate.
        
        Args:
            certificate_path: Path to certificate file
            
        Returns:
            Dictionary with certificate information
        """
        try:
            cert_file = Path(certificate_path)
            
            if not cert_file.exists():
                return {'exists': False, 'error': 'File not found'}
            
            info = {
                'exists': True,
                'filename': cert_file.name,
                'size_bytes': cert_file.stat().st_size,
                'size_mb': round(cert_file.stat().st_size / (1024 * 1024), 2),
                'created': datetime.fromtimestamp(cert_file.stat().st_ctime),
                'modified': datetime.fromtimestamp(cert_file.stat().st_mtime)
            }
            
            return info
            
        except Exception as e:
            return {'exists': False, 'error': str(e)}
    
    def cleanup_old_certificates(self, days_old: int = 30) -> Dict[str, Any]:
        """
        Clean up old certificate files.
        
        Args:
            days_old: Age in days for files to be considered old
            
        Returns:
            Dictionary with cleanup results
        """
        try:
            from datetime import timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            output_path = Path(self.output_dir)
            
            if not output_path.exists():
                return {'files_removed': 0, 'space_freed_mb': 0}
            
            files_removed = 0
            space_freed = 0
            
            for cert_file in output_path.glob("*.pdf"):
                if datetime.fromtimestamp(cert_file.stat().st_mtime) < cutoff_date:
                    file_size = cert_file.stat().st_size
                    cert_file.unlink()
                    files_removed += 1
                    space_freed += file_size
                    self.logger.info(f"Removed old certificate: {cert_file.name}")
            
            space_freed_mb = round(space_freed / (1024 * 1024), 2)
            
            self.logger.info(f"Cleanup completed: {files_removed} files removed, "
                           f"{space_freed_mb} MB freed")
            
            return {
                'files_removed': files_removed,
                'space_freed_mb': space_freed_mb
            }
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
            return {'error': str(e)}
    
    def get_template_info(self) -> Dict[str, Any]:
        """
        Get information about the certificate template.
        
        Returns:
            Dictionary with template information
        """
        try:
            template_file = Path(self.template_path)
            
            if not template_file.exists():
                return {'exists': False, 'error': 'Template file not found'}
            
            # Use the existing template validation from CertificateGenerator
            generator = self._get_certificate_generator()
            validation_info = generator.validate_template()
            
            info = {
                'exists': True,
                'path': str(template_file),
                'filename': template_file.name,
                'size_bytes': template_file.stat().st_size,
                'size_mb': round(template_file.stat().st_size / (1024 * 1024), 2),
                'modified': datetime.fromtimestamp(template_file.stat().st_mtime),
                'validation': validation_info
            }
            
            return info
            
        except Exception as e:
            return {'exists': False, 'error': str(e)}


def test_certificate_service():
    """Test function for the certificate service."""
    try:
        # Test data
        student_data = {
            "TEN_UNG_VIEN": "Test Student",
            "TEN_KHOA_HOC": "Test Course",
            "THOI_GIAN_DAO_TAO": "01/01/2024 - 15/01/2024",
            "NGAY_CAP_CHUNG_CHI": "16/01/2024",
            "MA_NHAN_VIEN": "TEST001"
        }
        
        # Initialize certificate service
        cert_service = CertificateService()
        
        print("✅ Certificate service initialized successfully")
        print(f"📄 Template: {cert_service.template_path}")
        print(f"📁 Output directory: {cert_service.output_dir}")
        
        # Test template validation
        template_info = cert_service.get_template_info()
        if template_info.get('exists'):
            print("✅ Template file found and valid")
        else:
            print(f"❌ Template issue: {template_info.get('error', 'Unknown error')}")
        
        # Test data validation
        if cert_service._validate_student_data(student_data):
            print("✅ Student data validation works")
        else:
            print("❌ Student data validation failed")
            
        print("📜 Certificate service ready for generation")
        
    except Exception as e:
        print(f"❌ Error testing certificate service: {str(e)}")


if __name__ == "__main__":
    test_certificate_service() 