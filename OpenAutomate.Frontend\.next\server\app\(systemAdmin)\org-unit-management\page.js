(()=>{var e={};e.id=6464,e.ids=[6464],e.modules={2257:(e,t,r)=>{Promise.resolve().then(r.bind(r,90352))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7027:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\systemAdmin\\\\organizationUnit\\\\organization-unit.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\systemAdmin\\organizationUnit\\organization-unit.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11985:(e,t,r)=>{Promise.resolve().then(r.bind(r,7027))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23904:(e,t,r)=>{Promise.resolve().then(r.bind(r,72128)),Promise.resolve().then(r.bind(r,2505)),Promise.resolve().then(r.bind(r,92588)),Promise.resolve().then(r.bind(r,48974)),Promise.resolve().then(r.bind(r,31057)),Promise.resolve().then(r.bind(r,50417))},26918:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var s=r(37413),n=r(7027);let a={title:"Organization Units",description:"Organization unit management page"};function i(){return(0,s.jsx)(n.default,{})}},27590:(e,t,r)=>{"use strict";function s(e,t={}){let{dateStyle:r="medium",timeStyle:n="short",fallback:a="N/A",customFormat:i,locale:l="en-US"}=t;if(!e)return a;try{let t;if("string"==typeof e){let r=e;r.endsWith("Z")||r.includes("+")||r.includes("-",10)||(r=r.replace(/(\.\d+)?$/,"$1Z"),console.debug(`Corrected UTC date format: ${e} -> ${r}`)),t=new Date(r)}else t=e;if(isNaN(t.getTime()))return console.warn(`Invalid date provided to formatUtcToLocal: ${e}`),a;if(i)return function(e,t){let r=e.getFullYear(),s=e.getMonth()+1,n=e.getDate(),a=e.getHours(),i=e.getMinutes(),l={yyyy:r.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][s-1],dd:n.toString().padStart(2,"0"),h:(a%12||12).toString(),mm:i.toString().padStart(2,"0"),a:a>=12?"PM":"AM"},o=t;return Object.entries(l).forEach(([e,t])=>{o=o.replace(RegExp(e,"g"),t)}),o}(t,i);return new Intl.DateTimeFormat(l,{dateStyle:r,timeStyle:n}).format(t)}catch(t){return console.error(`Error formatting date ${e}:`,t),a}}r.d(t,{Ej:()=>s})},27695:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var s=r(65239),n=r(48088),a=r(31369),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let o={children:["",{children:["(systemAdmin)",{children:["org-unit-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26918)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\org-unit-management\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96707)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\org-unit-management\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(systemAdmin)/org-unit-management/page",pathname:"/org-unit-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38475:(e,t,r)=>{Promise.resolve().then(r.bind(r,52162)),Promise.resolve().then(r.bind(r,83847)),Promise.resolve().then(r.bind(r,78526)),Promise.resolve().then(r.bind(r,97597)),Promise.resolve().then(r.bind(r,98641)),Promise.resolve().then(r.bind(r,80110))},42300:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(16189),n=r(43210);function a(){let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),r=(0,n.useCallback)(e=>{let r=new URLSearchParams(t.toString());return Object.entries(e).forEach(([e,t])=>{null===t?r.delete(e):r.set(e,t)}),r.toString()},[t]),a=(0,n.useCallback)((t,s)=>{let n=r(s);e.push(`${t}?${n}`,{scroll:!1})},[r,e]);return{createQueryString:r,updateUrl:a}}},47565:(e,t,r)=>{"use strict";r.d(t,{i:()=>n});var s=r(51787);let n={getAllUsers:async()=>(0,s.fetchApi)("api/admin/user/get-all",{method:"GET"}),getUserById:async e=>(0,s.fetchApi)(`api/admin/user/detail/${e}`,{method:"GET"}),updateUserInfo:async(e,t)=>(0,s.fetchApi)(`api/admin/user/update-detail/${e}`,{method:"PUT",body:JSON.stringify(t)}),changeUserPassword:async(e,t)=>(0,s.fetchApi)(`api/admin/user/change-password/${e}`,{method:"POST",body:JSON.stringify(t)}),getAllOrganizationUnits:async()=>await s.F.get("/api/admin/organization-unit/get-all"),deleteOrganizationUnit:async e=>{await s.F.delete(`/api/admin/organization-unit/${e}`)}}},52162:(e,t,r)=>{"use strict";r.d(t,{AdminRouteGuard:()=>l});var s=r(60687),n=r(86522),a=r(16189);r(43210);var i=r(85726);function l({children:e,redirectPath:t="/tenant-selector",loadingComponent:r}){let{isSystemAdmin:l,isLoading:o,isLogout:d}=(0,n.A)();return((0,a.useRouter)(),o)?r||(0,s.jsxs)("div",{className:"w-full p-8 space-y-4",children:[(0,s.jsx)(i.E,{className:"h-12 w-full rounded-lg"}),(0,s.jsx)(i.E,{className:"h-60 w-full rounded-lg"}),(0,s.jsx)(i.E,{className:"h-12 w-2/3 rounded-lg"})]}):l?(0,s.jsx)(s.Fragment,{children:e}):null}},53984:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var s=r(60687),n=r(4654),a=r(56476),i=r(29523),l=r(21342);function o({table:e}){return(0,s.jsxs)(l.rI,{children:[(0,s.jsx)(n.ty,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,s.jsx)(a.A,{}),"View"]})}),(0,s.jsxs)(l.SQ,{align:"end",className:"w-[150px]",children:[(0,s.jsx)(l.lp,{children:"Toggle columns"}),(0,s.jsx)(l.mB,{}),e.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,s.jsx)(l.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72128:(e,t,r)=>{"use strict";r.d(t,{AdminRouteGuard:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AdminRouteGuard() from the server but AdminRouteGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\admin-route-guard.tsx","AdminRouteGuard")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},90352:(e,t,r)=>{"use strict";r.d(t,{default:()=>k});var s=r(60687),n=r(29523),a=r(44493),i=r(56896),l=r(96834),o=r(34208),d=r(27590);let c=[{id:"select",header:({table:e})=>(0,s.jsx)(i.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>(0,s.jsx)(i.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1},{accessorKey:"name",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Name"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:"font-medium",children:e.getValue("name")})})},{accessorKey:"slug",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Slug"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("code",{className:"text-sm bg-muted px-2 py-1 rounded",children:e.getValue("slug")})})},{accessorKey:"description",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Description"}),cell:({row:e})=>{let t=e.getValue("description");return(0,s.jsx)("div",{className:"max-w-[200px] truncate",children:t||(0,s.jsx)("span",{className:"text-muted-foreground",children:"No description"})})}},{accessorKey:"isActive",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Status"}),cell:({row:e})=>{let t=e.getValue("isActive");return(0,s.jsx)(l.E,{variant:t?"default":"secondary",children:t?"Active":"Inactive"})}},{accessorKey:"createdAt",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Created At"}),cell:({row:e})=>{let t=e.getValue("createdAt"),r=(0,d.Ej)(t,{fallback:"-"});return(0,s.jsx)("div",{className:"text-sm",children:r})}}];var u=r(50723),m=r(43210),g=r(16189),x=r(99270),h=r(41862),p=r(11860),f=r(80462),j=r(89667),v=r(53984),b=r(15079);function y({table:e,statuses:t,onSearch:r,onStatusChange:a,searchValue:i="",isFiltering:o=!1,isPending:d=!1}){let c=e.getState().columnFilters.length>0,u=e.getState().columnFilters.length,g=(0,m.useRef)(null),y=(0,m.useRef)(null),N=t=>{g.current&&(y.current=g.current.selectionStart),r?r(t):e.getColumn("name")?.setFilterValue(t)};return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,s.jsx)(x.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(j.p,{ref:g,placeholder:"Search by Name or Slug...",value:i,onChange:e=>N(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:o,onFocus:()=>{g.current&&(y.current=g.current.selectionStart)}}),o&&(0,s.jsx)(h.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!o&&""!==i&&(0,s.jsx)(p.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>N("")})]}),e.getColumn("isActive")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(b.l6,{onValueChange:t=>{a?a(t):e.getColumn("isActive")?.setFilterValue("all"===t?"":t)},value:e.getColumn("isActive")?.getFilterValue()||"all",disabled:o||d,children:[(0,s.jsx)(b.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(b.yv,{placeholder:"Filter status"}),e.getColumn("isActive")?.getFilterValue()&&(0,s.jsx)(l.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(b.gC,{children:[(0,s.jsx)(b.eb,{value:"all",children:"All Statuses"}),t.map(e=>(0,s.jsx)(b.eb,{value:e.value,children:e.label},e.value))]})]})}),u>0&&(0,s.jsxs)(l.E,{variant:"secondary",className:"rounded-sm px-1",children:[u," active ",1===u?"filter":"filters"]}),c&&(0,s.jsxs)(n.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),r&&r("")},className:"h-8 px-2 lg:px-3",disabled:o,children:["Reset",(0,s.jsx)(p.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(v.i,{table:e})]})}var N=r(56090),S=r(93772),A=r(47565),C=r(42300),w=r(14583);function P(){return(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-gray-100"})})}function z(){let e=(0,g.useRouter)(),t=(0,g.usePathname)(),r=(0,g.useSearchParams)(),{updateUrl:i}=(0,C.z)(),[l,o]=(0,m.useState)([]),[d,x]=(0,m.useState)(!0),[h,p]=(0,m.useState)(null),[f,j]=(0,m.useState)({}),[v,b]=(0,m.useState)({}),[P,z]=(0,m.useState)(!1),[k,R]=(0,m.useState)(!1),F=(0,m.useRef)(null);(0,m.useRef)(!0);let[O,I]=(0,m.useState)(()=>{let e=[],t=r.get("name");t&&e.push({id:"name",value:t});let s=r.get("status");return s&&e.push({id:"isActive",value:s}),e}),[M,E]=(0,m.useState)(()=>{let e=r.get("sort"),t=r.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[U,G]=(0,m.useState)(()=>{let e=r.get("page"),t=r.get("size");return{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:t?parseInt(t):10}}),[T,V]=(0,m.useState)(r.get("name")??""),$=(0,m.useCallback)(async()=>{try{x(!0),p(null);let e=await A.i.getAllOrganizationUnits();o(e)}catch(e){p("Failed to fetch organization units"),console.error("Error fetching organization units:",e)}finally{x(!1)}},[]),_=(e,t,r)=>{let s=e[r.id],n=t[r.id];if("string"==typeof s&&"string"==typeof n&&(s=s.toLowerCase(),n=n.toLowerCase()),null==s)return null==n?0:r.desc?1:-1;if(null==n)return r.desc?-1:1;let a=0;return s<n?a=-1:s>n&&(a=1),r.desc?-a:a},q=(0,m.useMemo)(()=>{let e=[...l];if(O.forEach(t=>{if("name"===t.id&&t.value){let r=t.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(r)||e.slug.toLowerCase().includes(r))}if("isActive"===t.id&&t.value){let r="true"===t.value;e=e.filter(e=>e.isActive===r)}}),M.length>0){let t=M[0];e.sort((e,r)=>_(e,r,t))}return e},[l,O,M]),L=q.length,Z=Math.max(1,Math.ceil(L/U.pageSize)),B=(0,m.useMemo)(()=>{let e=U.pageIndex*U.pageSize,t=e+U.pageSize;return q.slice(e,t)},[q,U]),H=(0,N.N4)({data:B,columns:c,state:{sorting:M,columnVisibility:v,rowSelection:f,columnFilters:O,pagination:U},enableRowSelection:!0,onRowSelectionChange:j,onSortingChange:e=>{let r="function"==typeof e?e(M):e;E(r),r.length>0?i(t,{sort:r[0].id,order:r[0].desc?"desc":"asc",page:"1"}):i(t,{sort:null,order:null,page:"1"}),G(e=>({...e,pageIndex:0}))},onColumnFiltersChange:I,onColumnVisibilityChange:b,onPaginationChange:e=>{let r="function"==typeof e?e(U):e;G(r),i(t,{page:(r.pageIndex+1).toString(),size:r.pageSize.toString()})},getCoreRowModel:(0,S.HT)(),getFilteredRowModel:(0,S.hM)(),getPaginationRowModel:(0,S.kW)(),getSortedRowModel:(0,S.h5)(),getFacetedRowModel:(0,S.kQ)(),getFacetedUniqueValues:(0,S.oS)(),manualPagination:!0,pageCount:Z,manualSorting:!0,manualFiltering:!0}),K=l.length,W=l.filter(e=>e.isActive).length,D=(0,m.useCallback)(()=>{$()},[$]),J=(0,m.useCallback)(e=>{V(e),z(!0),F.current&&clearTimeout(F.current),F.current=setTimeout(()=>{let r=H.getColumn("name");r&&(r.setFilterValue(e),i(t,{name:e||null,page:"1"})),G(e=>({...e,pageIndex:0})),z(!1)},500)},[H,i,t]),Q=(0,m.useCallback)(e=>{let r=H.getColumn("isActive");if(r){let s="all"===e?"":e;r.setFilterValue(s),i(t,{status:s||null,page:"1"})}G(e=>({...e,pageIndex:0}))},[H,i,t]);return(0,s.jsx)("div",{className:"h-full overflow-y-auto bg-background p-6",children:(0,s.jsxs)("div",{className:"mx-auto max-w-7xl space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h1",{className:"text-2xl md:text-3xl font-bold tracking-tight text-foreground",children:"Organization Units"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage organization units and their structure"})]}),(0,s.jsxs)("div",{className:"grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",children:[(0,s.jsxs)(a.Zp,{className:"bg-card border-border",children:[(0,s.jsx)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium text-card-foreground",children:"Total Organization Units"})}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:K}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Organization units managed"})]})]}),(0,s.jsxs)(a.Zp,{className:"bg-card border-border",children:[(0,s.jsx)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium text-card-foreground",children:"Active Organization Units"})}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:W}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently operational"})]})]}),(0,s.jsxs)(a.Zp,{className:"bg-card border-border",children:[(0,s.jsx)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium text-card-foreground",children:"Inactive Organization Units"})}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:K-W}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending deletion"})]})]})]}),h&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,s.jsx)("p",{className:"text-red-800 dark:text-red-300",children:h}),(0,s.jsx)(n.$,{variant:"outline",className:"mt-2",onClick:D,children:"Retry"})]}),(0,s.jsxs)(a.Zp,{className:"bg-card border-border",children:[(0,s.jsx)(a.aR,{children:(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(a.ZB,{className:"text-xl font-bold tracking-tight",children:"Organization Units"}),L>0&&(0,s.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:["Total: ",L," unit",1!==L?"s":""]})]})})}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsx)(y,{table:H,statuses:[{value:"true",label:"Active"},{value:"false",label:"Inactive"}],onSearch:J,onStatusChange:Q,searchValue:T,isFiltering:d,isPending:P}),(0,s.jsx)(u.b,{data:B,columns:c,onRowClick:t=>{let r=`/org-unit-management/${t.id}`;e.push(r)},table:H,isLoading:d,totalCount:L}),(0,s.jsx)(w.d,{currentPage:U.pageIndex+1,pageSize:U.pageSize,totalCount:L,totalPages:Z,isLoading:d,isChangingPageSize:k,isUnknownTotalCount:!1,onPageChange:e=>{G({...U,pageIndex:e-1}),i(t,{page:e.toString()})},onPageSizeChange:e=>{R(!0);let r=Math.floor(U.pageIndex*U.pageSize/e);G({pageSize:e,pageIndex:r}),i(t,{size:e.toString(),page:(r+1).toString()}),setTimeout(()=>R(!1),100)}}),!d&&0===B.length&&!h&&(0,s.jsx)("div",{className:"text-center py-10 text-muted-foreground",children:(0,s.jsxs)("p",{children:["No organization units found. ",T||O.some(e=>"isActive"===e.id&&e.value)?"Try adjusting your filters.":"No units available."]})})]})]})]})})}function k(){return(0,s.jsx)(m.Suspense,{fallback:(0,s.jsx)(P,{}),children:(0,s.jsx)(z,{})})}},96707:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(37413),n=r(50417),a=r(48974),i=r(31057),l=r(92588),o=r(2505),d=r(72128);function c({children:e}){return(0,s.jsx)(d.AdminRouteGuard,{children:(0,s.jsx)(o.ChatProvider,{children:(0,s.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,s.jsxs)(n.SidebarProvider,{className:"flex flex-col",children:[(0,s.jsx)(i.SiteHeader,{}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)(a.AppSidebar,{}),(0,s.jsx)(n.SidebarInset,{children:(0,s.jsx)(l.SearchProvider,{children:(0,s.jsx)("main",{className:"",children:e})})})]})]})})})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7966,5584,5156,4654,6467,1694,6945,8759,6763,519,4881],()=>r(27695));module.exports=s})();