"""
gui mail khoa hoc

Please make sure you install the bot dependencies:
pip install -r requirements.txt

For OpenAutomate platform integration, ensure the OpenAutomate Agent is running.
"""

import sys
import os
from pathlib import Path

# Add framework to path
framework_path = os.path.join(os.path.dirname(__file__), 'framework')
sys.path.insert(0, framework_path)

from base_bot import BaseBot
from transaction_folders import create_transaction_folders, ensure_folder

# Import certificate notification functionality
from tasks.send_certificate_notification import CertificateNotificationBot


class Bot(BaseBot):
    """
    training - Certificate Notification Bot
    
    A modern, organized certificate notification bot that processes Excel training data
    and sends certificate emails using Outlook with a clean service architecture.
    
    Features Vietnamese text support throughout the entire process:
    - Excel data reading with proper UTF-8 encoding
    - Certificate generation with Vietnamese characters
    - Email notifications with Vietnamese content
    """
    
    def execute(self):
        """
        Main automation logic - Certificate Notification Process

        This bot uses organized services:
        1. DataService - Reads Excel data and identifies eligible students
        2. CertificateService - Generates PDF certificates from Word templates  
        3. EmailService - Sends notifications via Outlook with attachments
        4. Preserves original Excel file (no modifications made)
        """
        self.logger.info("Starting certificate notification process with Vietnamese text support...")

        # Create working folders automatically
        create_transaction_folders(self.bot_name, self.logger)
        input_folder = ensure_folder(self.bot_name, "input")
        output_folder = ensure_folder(self.bot_name, "output")
        certificates_folder = ensure_folder(self.bot_name, "certificates")

        # Update status (visible in OpenAutomate platform)
        self.update_status("Initializing certificate notification services...")

        # Get configuration from platform assets
        excel_file_path = self.get_asset('excel_file_path')
        outlook_account = self.get_asset('Outlook Email Account')

        # Fallback to default file path if not configured in assets
        if not excel_file_path:
            excel_file_path = r"C:\Users\<USER>\OneDrive - VFC Corp\DuLieuDaoTao\DU_LIEU_DAO_TAO_HOC_VIEN.xlsx"
            self.logger.info(f"Using default Excel file path: {excel_file_path}")

        # Log configuration
        if outlook_account:
            self.logger.info(f"Outlook account configured: {outlook_account}")
        else:
            self.logger.info("Using default Outlook account for email sending")

        try:
            # Initialize certificate notification bot with organized services
            self.update_status("Initializing certificate services...")
            
            cert_bot = CertificateNotificationBot(
                outlook_account=outlook_account,
                template_path=None,  # Uses default template/Template_1.docx
                output_dir=str(certificates_folder),
                logger=self.logger
            )
            
            # Test all services before processing
            self.update_status("Testing service connections...")
            self.logger.info("Testing service connections...")
            
            test_results = cert_bot.test_services()
            
            if test_results['overall_status'] != 'success':
                # Report service test failures
                error_msg = "Service initialization failed:"
                for service, test in test_results['tests'].items():
                    if test['status'] == 'failed':
                        error_msg += f"\n  - {service}: {test['message']}"
                
                self.logger.error(error_msg)
                self.update_status(f"Failed: Service initialization issues")
                
                return {
                    'message': error_msg,
                    'success': False,
                    'data': {
                        'service_tests': test_results,
                        'error_type': 'service_initialization'
                    }
                }
            
            self.logger.info("✅ All services initialized and tested successfully")
            
            # Get service status for reporting
            service_status = cert_bot.get_service_status()
            
            # Process certificate notifications
            self.update_status("Processing Excel data and identifying eligible students...")
            
            results = cert_bot.process_certificate_notifications(excel_file_path)
            
            # Update status based on results
            if results['success']:
                self.update_status(f"Certificate process completed: {results['message']}")
                self.logger.info(f"SUCCESS: {results['message']}")
                
                # Log detailed results
                summary = results.get('data_summary', {})
                self.logger.info("📊 Data Summary:")
                self.logger.info(f"   - Total records: {summary.get('total_records', 0)}")
                self.logger.info(f"   - Students passed: {summary.get('students_passed', 0)}")
                self.logger.info(f"   - Certificates due today: {summary.get('certificates_due_today', 0)}")
                
                self.logger.info("📜 Processing Results:")
                self.logger.info(f"   - Eligible students: {results['total_eligible']}")
                self.logger.info(f"   - Certificates generated: {results['certificates_generated']}")
                self.logger.info(f"   - Emails sent: {results['emails_sent']}")
                
                if results['errors']:
                    self.logger.warning("⚠️ Some errors occurred:")
                    for error in results['errors']:
                        self.logger.warning(f"  - {error}")
                
            else:
                self.update_status(f"Certificate process failed: {results['message']}")
                self.logger.error(f"FAILED: {results['message']}")
                
                if results['errors']:
                    self.logger.error("Errors:")
                    for error in results['errors']:
                        self.logger.error(f"  - {error}")

            # Enhance return data with service information
            if 'data' not in results:
                results['data'] = {}
            
            results['data'].update({
                'service_status': service_status,
                'service_tests': test_results,
                'certificates_folder': str(certificates_folder),
                'outlook_account': outlook_account
            })

            return results

        except FileNotFoundError as e:
            error_msg = f"Excel file not found: {str(e)}"
            self.logger.error(error_msg)
            self.update_status(f"Failed: {error_msg}")
            return {
                'message': error_msg,
                'success': False,
                'data': {
                    'error_type': 'file_not_found',
                    'excel_file_path': excel_file_path
                }
            }
            
        except Exception as e:
            error_msg = f"Certificate notification process failed: {str(e)}"
            self.logger.error(error_msg)
            self.update_status(f"Failed: {error_msg}")
            return {
                'message': error_msg,
                'success': False,
                'data': {
                    'error_type': 'unexpected_error',
                    'error_details': str(e)
                }
            }


# Run the bot
if __name__ == "__main__":
    print("Starting Training Notification Bot...")

    # Create and run bot
    bot = Bot("training")
    results = bot.run()

    # Print results
    if results['success']:
        print(f"✅ SUCCESS: {results['message']}")
        print(f"⏱️ Completed in {results['execution_time']:.2f} seconds")
        
        # Print detailed results
        data = results.get('data', {})
        
        # Service information
        service_tests = data.get('service_tests', {})
        if service_tests.get('overall_status') == 'success':
            print("🔧 Services: All systems operational")
        
        # Processing results
        if 'total_eligible' in data:
            print(f"📊 Processing Summary:")
            print(f"   - Eligible students: {data['total_eligible']}")
            print(f"   - Certificates generated: {data['certificates_generated']}")
            print(f"   - Emails sent: {data['emails_sent']}")
            
            # Data summary
            summary = data.get('data_summary', {})
            if summary:
                print(f"📈 Data Summary:")
                print(f"   - Total records: {summary.get('total_records', 0)}")
                print(f"   - Students passed: {summary.get('students_passed', 0)}")
                print(f"   - Certificates due today: {summary.get('certificates_due_today', 0)}")
            
            # Outlook account info
            outlook_account = data.get('outlook_account')
            if outlook_account:
                print(f"📧 Email Account: {outlook_account}")
            else:
                print("📧 Email Account: Default Outlook account")
            
            if data.get('errors'):
                print("⚠️ Errors encountered:")
                for error in data['errors']:
                    print(f"   - {error}")
    else:
        print(f"❌ FAILED: {results['message']}")
        
        # Print error details
        data = results.get('data', {})
        error_type = data.get('error_type', 'unknown')
        
        if error_type == 'service_initialization':
            print("🔧 Service Issues:")
            service_tests = data.get('service_tests', {})
            for service, test in service_tests.get('tests', {}).items():
                if test.get('status') == 'failed':
                    print(f"   - {service}: {test.get('message', 'Unknown error')}")
        elif error_type == 'file_not_found':
            print(f"📁 File Path: {data.get('excel_file_path', 'Unknown')}")
        
        if data.get('error_details'):
            print(f"🔍 Details: {data['error_details']}")

    print("Bot finished!")