# Certificate Notification Bot

A bot that automatically processes training data from Excel files and sends certificate notifications to students who have completed their courses.

**🇻🇳 Features full Vietnamese text support** throughout the entire automation process.

## Overview

This bot reads an Excel file containing training data, identifies students eligible for certificate notifications, generates certificates using Word templates, and sends email notifications with the certificates attached.

## Features

- **Excel Data Processing**: Reads training data from Excel files with headers at row 3
- **Vietnamese Text Support**: Full UTF-8 encoding support for Vietnamese characters
- **Intelligent Filtering**: Identifies eligible students based on specific criteria
- **Certificate Generation**: Creates certificates using Word templates with Vietnamese placeholders
- **Email Automation**: Sends personalized Vietnamese emails with certificate attachments
- **Excel Updates**: Marks notifications as sent to prevent duplicates
- **Backup Creation**: Automatically creates backups before updating Excel files

## Eligibility Criteria

A student is eligible for certificate notification if:
1. **"Kết quả (ĐẠT/KHÔNG ĐẠT)"** = "Đạt" (Passed)
2. **"Thời gian gửi chứng chỉ"** is empty/null (Not yet notified)
3. **"<PERSON><PERSON><PERSON> cấp chứng chỉ"** = today's date (Certificate due today)

## Required Excel Columns

The Excel file must contain these columns (headers at row 3):
- `Họ và tên` - Student name
- `Tên khóa học` - Course name
- `Thời gian bắt đầu đào tạo` - Training start date
- `Thời gian kết thúc đào tạo` - Training end date
- `Ngày cấp chứng chỉ` - Certificate issue date
- `Mã nhân viên` - Employee ID
- `Kết quả (ĐẠT/KHÔNG ĐẠT)` - Result (PASS/FAIL)
- `Thời gian gửi chứng chỉ` - Notification sent time

## Certificate Template

The bot uses a Word template (`template/Template_1.docx`) with the following placeholders:
- `{{ TEN_UNG_VIEN }}` - Student name
- `{{ TEN_KHOA_HOC }}` - Course name
- `{{ THOI_GIAN_DAO_TAO }}` - Training period (start - end dates)
- `{{ NGAY_CAP_CHUNG_CHI }}` - Certificate issue date
- `{{ MA_NHAN_VIEN }}` - Employee ID

## Configuration

### OpenAutomate Assets

Configure these assets in the OpenAutomate platform:

**Required:**
- `excel_file_path` - Path to the Excel training data file

**Optional (for email notifications):**
- `email_username` - SMTP username
- `email_password` - SMTP password (use app password for Gmail)
- `email_server` - SMTP server (default: smtp.gmail.com)
- `email_port` - SMTP port (default: 587)
- `from_email` - Sender email address

### Default Configuration

If not configured in assets, the bot uses:
- **Excel File**: `C:\Users\<USER>\OneDrive - VFC Corp\DuLieuDaoTao\DU_LIEU_DAO_TAO_HOC_VIEN.xlsx`
- **Template**: `template/Template_1.docx`
- **Output Directory**: `certificates/`

## Installation

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Ensure the certificate template exists:
   - Place your Word template at `template/Template_1.docx`
   - Include the required placeholders (see Template section above)
   - **Ensure template supports Vietnamese fonts** (e.g., Times New Roman, Arial Unicode MS)

3. Configure the Excel file path and email settings in OpenAutomate assets

4. **Vietnamese Text Setup**:
   - Ensure your Excel file is saved with UTF-8 encoding
   - Verify Python environment supports UTF-8 locale
   - Test Vietnamese text handling with `python test_vietnamese_support.py`

## Usage

### Via OpenAutomate Platform

1. Deploy the bot to your OpenAutomate agent
2. Configure the required assets
3. Schedule or manually trigger the bot execution

### Standalone Execution

```bash
python bot.py
```

## Output

The bot creates:
- **Certificates**: PDF files in the certificates folder
- **Logs**: Detailed execution logs in the logs folder
- **Backup**: Excel file backup before updates
- **Updated Excel**: Original file with notification timestamps

## File Structure

```
training/
├── bot.py                              # Main bot script
├── tasks/
│   ├── send_certificate_notification.py  # Certificate notification logic
│   └── create_cer.py                   # Certificate generation logic
├── template/
│   └── Template_1.docx                 # Certificate template
├── certificates/                        # Generated certificates
├── logs/                               # Execution logs
├── requirements.txt                    # Dependencies
└── README.md                          # This file
```

## Email Template

The bot sends Vietnamese emails with the following structure:

```
Subject: Chứng chỉ hoàn thành khóa học - [Course Name]

Kính chào [Student Name],

Chúc mừng bạn đã hoàn thành khóa học "[Course Name]"!

Thông tin chứng chỉ:
- Họ và tên: [Student Name]
- Mã nhân viên: [Employee ID]
- Khóa học: [Course Name]
- Thời gian đào tạo: [Training Period]
- Ngày cấp chứng chỉ: [Certificate Date]

Chứng chỉ được đính kèm trong email này.

Trân trọng,
Phòng Đào tạo
```

## Error Handling

The bot includes comprehensive error handling:
- File not found errors
- Excel reading/writing errors
- Certificate generation failures
- Email sending failures
- Individual student processing errors

All errors are logged and reported in the execution results.

## Dependencies

- `pandas` - Excel data processing with Unicode support
- `openpyxl` - Excel file handling with UTF-8 encoding
- `docxtpl` - Word template processing (supports Vietnamese text)
- `docx2pdf` - PDF conversion (preserves Vietnamese characters)
- `pywin32` - Outlook COM automation (supports Unicode)

All dependencies support UTF-8 encoding for Vietnamese text processing.

## Troubleshooting

### Common Issues

1. **Excel file not found**
   - Check the file path in assets or update the default path in the code

2. **Template not found**
   - Ensure `template/Template_1.docx` exists with proper placeholders

3. **Email sending fails**
   - Verify email credentials and SMTP settings
   - For Gmail, use app passwords instead of regular passwords

4. **Certificate generation fails**
   - Check template placeholders match the expected format
   - Ensure sufficient disk space for PDF generation

5. **Permission errors**
   - Ensure the bot has read/write access to the Excel file and output directories

6. **Vietnamese text display issues**
   - Check Excel file encoding - save as UTF-8 if needed
   - Ensure Word template uses Vietnamese-compatible fonts
   - Verify Python locale supports UTF-8 (check with `python test_vietnamese_support.py`)
   - For email issues, check Outlook display settings

## Vietnamese Text Testing

Run the Vietnamese text support test:

```bash
python test_vietnamese_support.py
```

This will verify:
- ✅ Data service UTF-8 encoding
- ✅ Certificate service Vietnamese text handling  
- ✅ Email service Vietnamese content
- ✅ End-to-end integration

## License

This bot is part of the OpenAutomate platform and follows the project's licensing terms.