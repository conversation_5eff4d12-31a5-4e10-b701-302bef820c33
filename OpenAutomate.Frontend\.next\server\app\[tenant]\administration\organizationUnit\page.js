(()=>{var e={};e.id=6602,e.ids=[6602],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6602:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s,metadata:()=>i});var a=n(37413),r=n(54540);let i={title:"Automation",description:"Agent management page"};function s(){return(0,a.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,a.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,a.jsx)(r.default,{})]})}},7800:(e,t,n)=>{Promise.resolve().then(n.bind(n,54540))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17528:(e,t,n)=>{Promise.resolve().then(n.bind(n,44554))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31599:(e,t,n)=>{"use strict";n.d(t,{c:()=>l});var a=n(43210),r=n(39989),i=n(16189),s=n(31207),o=n(70891);function l(){let e=(0,i.useRouter)(),{data:t,error:n,isLoading:l,mutate:d}=(0,s.Ay)(o.DC.organizationUnits(),()=>r.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:l,error:n?"Failed to fetch organization units. Please try again later.":null,refresh:d,selectOrganizationUnit:(0,a.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},39989:(e,t,n)=>{"use strict";n.d(t,{K:()=>r});var a=n(51787);let r={getMyOrganizationUnits:async()=>await a.F.get("/api/ou/my-ous"),getBySlug:async e=>await a.F.get(`/api/ou/slug/${e}`),getById:async e=>await a.F.get(`/api/ou/${e}`),create:async e=>await a.F.post("/api/ou/create",e),update:async(e,t)=>await a.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await a.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await a.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await a.F.get(`/api/ou/${e}/deletion-status`)}},44554:(e,t,n)=>{"use strict";n.d(t,{default:()=>g});var a=n(60687),r=n(43210),i=n(89667),s=n(29523),o=n(16189),l=n(39989),d=n(20140),c=n(63503),u=n(57175),m=n(96362),p=n(31207),h=n(70891);function g(){(0,o.useParams)().tenant;let[e,t]=(0,r.useState)(null),[n,g]=(0,r.useState)(null),[x,f]=(0,r.useState)(!1),[b,v]=(0,r.useState)(!1),[j,y]=(0,r.useState)(""),[N,C]=(0,r.useState)(""),[w,P]=(0,r.useState)(!1),[A,F]=(0,r.useState)(!1),[z,k]=(0,r.useState)(!1),{toast:D}=(0,d.d)(),[S,$]=(0,r.useState)(null),[O,E]=(0,r.useState)(!1);(0,r.useRef)(!1);let G=async()=>{if(!j.trim()){D({title:"Error",description:"Organization unit name cannot be empty",variant:"destructive"});return}if(e){if(n&&j.trim()!==n.name){P(!0);return}v(!0);try{let t=await l.K.update(e,{name:j,description:N});g(t),f(!1),D({title:"Success",description:"Organization unit information updated successfully"})}catch{D({title:"Error",description:"Update failed",variant:"destructive"})}finally{v(!1)}}},U=async()=>{if(!e){D({title:"Error",description:"Organization unit ID is missing. Please try again.",variant:"destructive"});return}F(!0);try{let t=await l.K.update(e,{name:j,description:N});g(t),f(!1),P(!1),D({title:"Success",description:"Organization unit information updated successfully"}),window.location.href="/tenant-selector"}catch{D({title:"Error",description:"Update failed",variant:"destructive"})}finally{F(!1)}},_=async()=>{if(e){k(!1);try{await l.K.requestDeletion(e),I(),D({title:"Deletion Requested",description:"Organization unit deletion has been initiated."})}catch(t){let e="Failed to request deletion.";t instanceof Error&&(e=t.message),D({title:"Error",description:e,variant:"destructive"})}}},q=async()=>{if(e)try{await l.K.cancelDeletion(e),I(),D({title:"Deletion Cancelled",description:"Organization unit deletion has been cancelled."})}catch(t){let e="Failed to cancel deletion.";t instanceof Error&&(e=t.message),D({title:"Error",description:e,variant:"destructive"})}finally{E(!1)}},R=async()=>{if(!e)throw Error("Missing ID");let t=await l.K.getDeletionStatus(e),n=t.isPendingDeletion??t.isDeletionPending??!1,a=null;return"number"==typeof t.remainingSeconds?a=t.remainingSeconds:"number"==typeof t.hoursUntilDeletion&&(a=3600*t.hoursUntilDeletion),{isPendingDeletion:n,remainingSeconds:a,scheduledDeletionAt:t.scheduledDeletionAt,canCancel:t.canCancel??!1}},{data:M,mutate:I}=(0,p.Ay)(e?h.DC.organizationUnitDeletionStatus(e):null,R,{refreshInterval:6e4,refreshWhenHidden:!0}),L=!!M?.isPendingDeletion;return(0,a.jsx)("div",{className:"flex justify-center pt-8",children:(0,a.jsxs)("div",{className:"w-full max-w-2xl",children:[(0,a.jsxs)("div",{className:"bg-background rounded-2xl shadow border border-border px-8 py-7",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-bold",children:"Organization Unit Information"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Details of your organization unit"})]}),!x&&!L&&(0,a.jsxs)("div",{className:"flex gap-2 ml-auto",children:[(0,a.jsxs)(s.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 border-gray-300 hover:border-[#FF6A1A] hover:bg-[#FFF3EC] rounded-lg font-medium",onClick:()=>{n&&(y(n.name),C(n.description),f(!0))},children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Edit"]}),(0,a.jsxs)(s.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 border-red-300 hover:border-red-500 hover:bg-red-50 rounded-lg font-medium text-red-600 hover:text-red-600",onClick:()=>{k(!0)},children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-red-600"}),"Delete"]})]})]}),L&&(0,a.jsxs)("div",{className:"flex items-center justify-between dark:bg-orange-950/50 bg-orange-50 border border-orange-300 dark:border-orange-800/50 rounded-lg px-4 py-3 my-4",children:[(0,a.jsx)("div",{className:"text-orange-700 dark:text-orange-400 font-semibold",children:"number"==typeof S&&S>0?`This organization unit will be deleted in ${(e=>{if(e<=0)return"Deleting...";let t=Math.floor(e/86400),n=Math.floor(e%86400/3600),a=Math.floor(e%3600/60),r=e%60,i=[];return t>0&&i.push(`${t} day${t>1?"s":""}`),n>0&&i.push(`${n} hour${n>1?"s":""}`),a>0&&i.push(`${a} minute${a>1?"s":""}`),r>0&&0===i.length&&i.push(`${r} second${r>1?"s":""}`),i.join(", ")})(S)}`:"Deleting organization unit..."}),M?.canCancel&&(0,a.jsx)(s.$,{variant:"outline",className:"ml-4 border-orange-600 text-orange-700 hover:bg-orange-100",onClick:()=>E(!0),children:"Cancel Deletion"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5 mt-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"ou-name",className:"block text-xs font-semibold text-muted-foreground mb-1",children:"Name"}),x?(0,a.jsx)(i.p,{id:"ou-name",value:j,onChange:e=>y(e.target.value),className:"rounded-lg border-input bg-background focus:border-[#FF6A1A] focus:ring-[#FF6A1A]/30",placeholder:"Organization unit name"}):(0,a.jsx)("div",{className:"rounded-lg bg-muted px-3 py-2 text-base border border-border",children:n?.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"ou-description",className:"block text-xs font-semibold text-muted-foreground mb-1",children:"Description"}),x?(0,a.jsx)(i.p,{id:"ou-description",value:N,onChange:e=>C(e.target.value),className:"rounded-lg border-input bg-background focus:border-[#FF6A1A] focus:ring-[#FF6A1A]/30",placeholder:"Organization unit description"}):(0,a.jsx)("div",{className:"rounded-lg bg-muted px-3 py-2 text-base border border-border",children:n?.description||(0,a.jsx)("span",{className:"italic text-muted-foreground",children:"No description"})})]})]}),x&&(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-8",children:[(0,a.jsx)(s.$,{variant:"outline",onClick:()=>{f(!1)},disabled:b,className:"rounded-lg",children:"Cancel"}),(0,a.jsx)(s.$,{onClick:G,disabled:b,className:"rounded-lg bg-[#FF6A1A] text-white hover:bg-orange-500",children:b?"Saving...":"Save Changes"})]})]}),(0,a.jsx)(c.lG,{open:w,onOpenChange:P,children:(0,a.jsxs)(c.Cf,{children:[(0,a.jsx)(c.c7,{children:(0,a.jsx)(c.L3,{children:"Warning"})}),(0,a.jsx)("div",{children:"If you change the name, the tenant will also change, which will result in a changed URL and the Bot agent will be disconnected. Do you still want to proceed?"}),(0,a.jsxs)(c.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,a.jsx)(s.$,{variant:"outline",onClick:()=>P(!1),disabled:A,children:"Cancel"}),(0,a.jsx)(s.$,{onClick:U,disabled:A,className:"bg-[#FF6A1A] text-white hover:bg-orange-500",children:"Accept"})]})]})}),(0,a.jsx)(c.lG,{open:z,onOpenChange:k,children:(0,a.jsxs)(c.Cf,{children:[(0,a.jsx)(c.c7,{children:(0,a.jsx)(c.L3,{children:"Confirm Deletion"})}),(0,a.jsx)("div",{children:"Are you sure you want to delete this organization unit? It will be deleted in 7 days."}),(0,a.jsxs)(c.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,a.jsx)(s.$,{variant:"outline",onClick:()=>k(!1),children:"Cancel"}),(0,a.jsx)(s.$,{onClick:_,className:"bg-red-600 text-white hover:bg-red-700",children:"Delete"})]})]})}),(0,a.jsx)(c.lG,{open:O,onOpenChange:E,children:(0,a.jsxs)(c.Cf,{children:[(0,a.jsx)(c.c7,{children:(0,a.jsx)(c.L3,{children:"Cancel Deletion"})}),(0,a.jsx)("div",{children:"Are you sure you want to cancel the deletion of this organization unit?"}),(0,a.jsxs)(c.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,a.jsx)(s.$,{variant:"outline",onClick:()=>E(!1),children:"No"}),(0,a.jsx)(s.$,{onClick:q,className:"bg-[#FF6A1A] text-white hover:bg-orange-500",children:"Cancel Deletion"})]})]})})]})})}},54540:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});let a=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\administration\\\\organization-unit\\\\organization-unit.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\administration\\organization-unit\\organization-unit.tsx","default")},57175:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},61018:(e,t,n)=>{"use strict";n.d(t,{TenantGuard:()=>a});let a=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,n)=>{"use strict";n.d(t,{Cf:()=>p,Es:()=>g,HM:()=>u,L3:()=>x,c7:()=>h,lG:()=>l,rr:()=>f,zM:()=>d});var a=n(60687),r=n(43210),i=n(88562),s=n(11860),o=n(36966);let l=i.bL,d=i.l9,c=i.ZL,u=i.bm,m=r.forwardRef(({className:e,...t},n)=>(0,a.jsx)(i.hJ,{ref:n,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));m.displayName=i.hJ.displayName;let p=r.forwardRef(({className:e,children:t,...n},r)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(i.UC,{ref:r,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...n,children:[t,(0,a.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(s.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=i.UC.displayName;let h=({className:e,...t})=>(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});h.displayName="DialogHeader";let g=({className:e,...t})=>(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});g.displayName="DialogFooter";let x=r.forwardRef(({className:e,...t},n)=>(0,a.jsx)(i.hE,{ref:n,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=i.hE.displayName;let f=r.forwardRef(({className:e,...t},n)=>(0,a.jsx)(i.VY,{ref:n,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=i.VY.displayName},64656:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c});var a=n(37413),r=n(48974),i=n(31057),s=n(50417),o=n(92588),l=n(61018),d=n(2505);function c({children:e}){return(0,a.jsx)(l.TenantGuard,{children:(0,a.jsx)(d.ChatProvider,{children:(0,a.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,a.jsxs)(s.SidebarProvider,{className:"flex flex-col",children:[(0,a.jsx)(i.SiteHeader,{}),(0,a.jsxs)("div",{className:"flex flex-1",children:[(0,a.jsx)(r.AppSidebar,{}),(0,a.jsx)(s.SidebarInset,{children:(0,a.jsx)(o.SearchProvider,{children:(0,a.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,n)=>{"use strict";n.d(t,{TenantGuard:()=>l});var a=n(60687),r=n(43210),i=n(16189),s=n(31599),o=n(31568);function l({children:e}){let{tenant:t}=(0,i.useParams)();(0,i.useRouter)();let{isAuthenticated:n,isLoading:l}=(0,o.A)(),{organizationUnits:d,isLoading:c}=(0,s.c)(),[u,m]=(0,r.useState)(!0);return l||c||u?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,a.jsx)(a.Fragment,{children:e})}},72826:(e,t,n)=>{Promise.resolve().then(n.bind(n,69231)),Promise.resolve().then(n.bind(n,83847)),Promise.resolve().then(n.bind(n,78526)),Promise.resolve().then(n.bind(n,97597)),Promise.resolve().then(n.bind(n,98641)),Promise.resolve().then(n.bind(n,80110))},83442:(e,t,n)=>{Promise.resolve().then(n.bind(n,61018)),Promise.resolve().then(n.bind(n,2505)),Promise.resolve().then(n.bind(n,92588)),Promise.resolve().then(n.bind(n,48974)),Promise.resolve().then(n.bind(n,31057)),Promise.resolve().then(n.bind(n,50417))},88775:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var a=n(65239),r=n(48088),i=n(31369),s=n(30893),o={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>s[e]);n.d(t,o);let l={children:["",{children:["[tenant]",{children:["administration",{children:["organizationUnit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,6602)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\organizationUnit\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(n.bind(n,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(n.bind(n,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\organizationUnit\\page.tsx"],c={require:n,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[tenant]/administration/organizationUnit/page",pathname:"/[tenant]/administration/organizationUnit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[4447,7966,5584,5156,4654,6467,6763,519],()=>n(88775));module.exports=a})();