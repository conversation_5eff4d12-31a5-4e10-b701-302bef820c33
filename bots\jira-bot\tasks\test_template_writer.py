#!/usr/bin/env python3
"""
Test and validation script for ExcelTemplateWriter
"""

from write_to_template import ExcelTemplateWriter
import pandas as pd
from pathlib import Path

def create_sample_export_data():
    """
    Create sample JIRA export data for testing
    
    Returns:
        pandas.DataFrame: Sample export data
    """
    sample_data = {
        "Key": ["PROJ-001", "PROJ-002", "PROJ-003"],
        "Summary": ["Fix login bug", "Add new feature", "Update documentation"],
        "Issue Type": ["Bug", "Story", "Task"],
        "Assignee Email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "Priority": ["High", "Medium", "Low"],
        "Status": ["Done", "In Progress", "To Do"],
        "Resolution Date": ["2025-06-01", "2025-06-03", ""]
    }
    
    df = pd.DataFrame(sample_data)
    # Convert Resolution Date to datetime
    df["Resolution Date"] = pd.to_datetime(df["Resolution Date"], errors='coerce')
    
    return df

def test_column_mapping():
    """
    Test the column mapping functionality
    """
    print("Testing Column Mapping")
    print("="*30)
    
    template_path = "./template/report-template.xlsx"
    writer = ExcelTemplateWriter(template_path)
    
    print(f"Template path: {writer.template_path}")
    print(f"Output directory: {writer.output_dir}")
    print(f"Header row: {writer.header_row}")
    print(f"Data start row: {writer.data_start_row}")
    
    print("\nColumn Mapping:")
    for template_col, export_col in writer.column_mapping.items():
        print(f"  {template_col:<25} -> {export_col}")

def test_with_sample_data():
    """
    Test with sample data
    """
    print("\nTesting with Sample Data")
    print("="*30)
    
    # Create sample data
    sample_df = create_sample_export_data()
    print(f"Sample data shape: {sample_df.shape}")
    print("\nSample data columns:")
    for col in sample_df.columns:
        print(f"  - {col}")
    
    print("\nFirst few rows:")
    print(sample_df.head())
    
    # Save sample data to temporary file
    temp_export_path = Path("temp_sample_export.xlsx")
    sample_df.to_excel(temp_export_path, index=False)
    print(f"\nSample data saved to: {temp_export_path}")
    
    # Test processing
    template_path = "./template/report-template.xlsx"
    if Path(template_path).exists():        try:
            writer = ExcelTemplateWriter(template_path)
            output_file = writer.process_export(str(temp_export_path), "test_output.xlsx")
            print(f"[SUCCESS] Test successful! Output: {output_file}")
        except Exception as e:
            print(f"[ERROR] Test failed: {e}")
    else:
        print(f"[ERROR] Template file not found: {template_path}")
    
    # Cleanup
    if temp_export_path.exists():
        temp_export_path.unlink()
        print(f"Cleaned up temporary file: {temp_export_path}")

def validate_template_structure():
    """
    Validate the template structure
    """
    print("\nValidating Template Structure")
    print("="*35)
    
    template_path = "./template/report-template.xlsx"
    
    if not Path(template_path).exists():
        print(f"[ERROR] Template file not found: {template_path}")
        return
    
    try:
        from openpyxl import load_workbook
        
        workbook = load_workbook(template_path)
        worksheet = workbook.active
        
        print(f"[SUCCESS] Template loaded successfully")
        print(f"  Sheet name: {worksheet.title}")
        print(f"  Max row: {worksheet.max_row}")
        print(f"  Max column: {worksheet.max_column}")
        
        # Check headers at row 7
        print(f"\nHeaders at row 7:")
        headers = []
        for col in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=7, column=col).value
            if cell_value:
                headers.append(cell_value)
                print(f"  Column {col}: {cell_value}")
        
        # Check expected headers
        writer = ExcelTemplateWriter(template_path)
        expected_headers = list(writer.column_mapping.keys())
        
        print(f"\nExpected headers: {expected_headers}")
        print(f"Found headers: {headers}")
        
        missing_headers = [h for h in expected_headers if h not in headers]
        if missing_headers:            print(f"[ERROR] Missing headers: {missing_headers}")
        else:
            print("[SUCCESS] All expected headers found")
            
    except Exception as e:
        print(f"[ERROR] Error validating template: {e}")

if __name__ == "__main__":
    print("ExcelTemplateWriter Test & Validation")
    print("="*45)
    
    test_column_mapping()
    validate_template_structure() 
    test_with_sample_data()
