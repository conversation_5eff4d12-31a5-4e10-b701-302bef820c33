"use strict";exports.id=519,exports.ids=[519],exports.modules={2505:(e,t,n)=>{n.d(t,{ChatProvider:()=>a});var r=n(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call ChatWrapper() from the server but ChatWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\chat\\chat-wrapper.tsx","ChatWrapper");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ChatProvider() from the server but ChatProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\chat\\chat-wrapper.tsx","ChatProvider")},4781:(e,t,n)=>{n.d(t,{kV:()=>o});var r=n(51787);let a=()=>"",s=()=>{let e=a();if(!e)throw Error("No tenant context available");return{status:`${e}/api/subscription/status`,startTrial:`${e}/api/subscription/start-trial`}},o={getSubscriptionStatus:async()=>{let e=s();return r.F.get(e.status)},startTrial:async()=>{let e=s();return r.F.post(e.startTrial,{})}}},11365:(e,t,n)=>{n.d(t,{F:()=>z});var r=n(85397),a=n(11860),s=n(41862),o=n(47033),i=n(14952),l=n(96362),d=n(84027),c=n(85778),u=n(81904),p=n(96474),m=n(65668),h=n(58869),f=n(70334),b=n(47282),x=n(29104),g=n(363),v=n(34410),j=n(62157),C=n(72575),w=n(13964),A=n(78464),S=n(10022),N=n(9005),y=n(97840),k=n(36058),I=n(32192),E=n(24541),P=n(22915),R=n(40083),F=n(78122),O=n(45547),G=n(52069),T=n(41550);let z={logo:r.A,close:a.A,Spinner:s.A,chevronLeft:o.A,chevronRight:i.A,trash:l.A,settings:d.A,billing:c.A,ellipsis:u.A,add:p.A,warning:m.A,user:h.A,arrowRight:f.A,help:m.A,pizza:b.A,sun:x.A,moon:g.A,laptop:v.A,gitHub:j.A,twitter:C.A,check:w.A,file:A.A,fileText:S.A,image:N.A,play:y.A,pause:k.A,home:I.A,chart:E.A,cog:P.A,logout:R.A,refresh:F.A,about:O.A,guide:G.A,contact:T.A}},21342:(e,t,n)=>{n.d(t,{I:()=>c,SQ:()=>d,_2:()=>u,hO:()=>p,lp:()=>m,mB:()=>h,rI:()=>i,ty:()=>l});var r=n(60687);n(43210);var a=n(4654),s=n(13964),o=n(36966);function i({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dropdown-menu",...e})}function l({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...e})}function d({className:e,sideOffset:t=4,...n}){return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...n})})}function c({...e}){return(0,r.jsx)(a.YJ,{"data-slot":"dropdown-menu-group",...e})}function u({className:e,inset:t,variant:n="default",...s}){return(0,r.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function p({className:e,children:t,checked:n,...i}){return(0,r.jsxs)(a.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:n,...i,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(s.A,{className:"size-4"})})}),t]})}function m({className:e,inset:t,...n}){return(0,r.jsx)(a.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function h({className:e,...t}){return(0,r.jsx)(a.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},31057:(e,t,n)=>{n.d(t,{SiteHeader:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call SiteHeader() from the server but SiteHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\layout\\sidebar\\site-header.tsx","SiteHeader")},31568:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(43210),a=n(86522);function s(){let e=(0,r.useContext)(a.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},35950:(e,t,n)=>{n.d(t,{w:()=>o});var r=n(60687);n(43210);var a=n(70709),s=n(36966);function o({className:e,orientation:t="horizontal",decorative:n=!0,...o}){return(0,r.jsx)(a.b,{"data-slot":"separator-root",decorative:n,orientation:t,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...o})}},48974:(e,t,n)=>{n.d(t,{AppSidebar:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\layout\\sidebar\\app-sidebar.tsx","AppSidebar")},50417:(e,t,n)=>{n.d(t,{SidebarInset:()=>a,SidebarProvider:()=>s});var r=n(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","Sidebar"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarContent"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarFooter"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarGroup"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarGroupAction"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarGroupContent"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarGroupLabel"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarHeader"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarInput");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarInset");(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenu"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuAction"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuBadge"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuButton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuItem"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuSub"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuSubButton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuSubItem");let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarRail"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarSeparator"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","SidebarTrigger"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\sidebar.tsx","useSidebar")},51551:(e,t,n)=>{n.d(t,{R:()=>o});var r=n(31207),a=n(4781),s=n(70891);function o(){let{data:e,error:t,isLoading:n,mutate:o}=(0,r.Ay)(s.DC.subscription(),()=>a.kV.getSubscriptionStatus(),{refreshInterval:3e4,revalidateOnFocus:!0});return{subscription:e||null,isLoading:n,error:t?(0,s.IS)(t):null,mutate:o}}},67146:(e,t,n)=>{n.d(t,{CG:()=>l,Fm:()=>p,Qs:()=>h,cj:()=>i,h:()=>u,qp:()=>m});var r=n(60687);n(43210);var a=n(88562),s=n(11860),o=n(36966);function i({...e}){return(0,r.jsx)(a.bL,{"data-slot":"sheet",...e})}function l({...e}){return(0,r.jsx)(a.l9,{"data-slot":"sheet-trigger",...e})}function d({...e}){return(0,r.jsx)(a.ZL,{"data-slot":"sheet-portal",...e})}function c({className:e,...t}){return(0,r.jsx)(a.hJ,{"data-slot":"sheet-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,side:n="right",...i}){return(0,r.jsxs)(d,{children:[(0,r.jsx)(c,{}),(0,r.jsxs)(a.UC,{"data-slot":"sheet-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...i,children:[t,(0,r.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(s.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,o.cn)("flex flex-col gap-1.5 p-4",e),...t})}function m({className:e,...t}){return(0,r.jsx)(a.hE,{"data-slot":"sheet-title",className:(0,o.cn)("text-foreground font-semibold",e),...t})}function h({className:e,...t}){return(0,r.jsx)(a.VY,{"data-slot":"sheet-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}},75139:(e,t,n)=>{n.d(t,{U:()=>l});var r=n(60687);n(43210);var a=n(10218),s=n(29523),o=n(11365),i=n(21342);function l(){let{setTheme:e}=(0,a.D)();return(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(i.ty,{asChild:!0,children:(0,r.jsxs)(s.$,{variant:"ghost",size:"sm",className:"h-8 w-8 px-0",children:[(0,r.jsx)(o.F.sun,{className:"rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,r.jsx)(o.F.moon,{className:"absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,r.jsxs)(i.SQ,{align:"end",children:[(0,r.jsxs)(i._2,{onClick:()=>e("light"),children:[(0,r.jsx)(o.F.sun,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Light"})]}),(0,r.jsxs)(i._2,{onClick:()=>e("dark"),children:[(0,r.jsx)(o.F.moon,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Dark"})]}),(0,r.jsxs)(i._2,{onClick:()=>e("system"),children:[(0,r.jsx)(o.F.laptop,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"System"})]})]})]})}},76242:(e,t,n)=>{n.d(t,{Bc:()=>o,ZI:()=>d,k$:()=>l,m_:()=>i});var r=n(60687);n(43210);var a=n(77071),s=n(36966);function o({delayDuration:e=0,...t}){return(0,r.jsx)(a.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function i({...e}){return(0,r.jsx)(o,{children:(0,r.jsx)(a.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,r.jsx)(a.l9,{"data-slot":"tooltip-trigger",...e})}function d({className:e,sideOffset:t=0,children:n,...o}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,s.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...o,children:[n,(0,r.jsx)(a.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},78526:(e,t,n)=>{n.d(t,{SearchProvider:()=>o});var r=n(60687),a=n(43210);let s=(0,a.createContext)(void 0);function o({children:e}){let[t,n]=(0,a.useState)(""),o={searchTerm:t,setSearchTerm:n,isSearching:t.length>0};return(0,r.jsx)(s.Provider,{value:o,children:e})}},78570:(e,t,n)=>{n.d(t,{R:()=>r});var r=function(e){return e.Eligible="Eligible",e.Active="Active",e.Used="Used",e.NotEligible="NotEligible",e}({})},80110:(e,t,n)=>{n.d(t,{Bx:()=>m,Yv:()=>x,CG:()=>b,Cn:()=>g,rQ:()=>j,jj:()=>v,Gh:()=>f,SidebarInset:()=>h,wZ:()=>C,Uj:()=>S,FX:()=>w,q9:()=>N,Cp:()=>k,Fg:()=>y,SidebarProvider:()=>p,cL:()=>u});var r=n(60687),a=n(43210),s=n(11329),o=n(24224),i=n(36966);n(29523),n(89667),n(35950);var l=n(67146);n(85726);var d=n(76242);let c=a.createContext(null);function u(){let e=a.useContext(c);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function p({defaultOpen:e=!0,open:t,onOpenChange:n,className:s,style:o,children:l,...u}){let p=function(){let[e,t]=a.useState(void 0);return a.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),n=()=>{t(window.innerWidth<768)};return e.addEventListener("change",n),t(window.innerWidth<768),()=>e.removeEventListener("change",n)},[]),!!e}(),[m,h]=a.useState(!1),[f,b]=a.useState(e),x=t??f,g=a.useCallback(e=>{let t="function"==typeof e?e(x):e;n?n(t):b(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[n,x]),v=a.useCallback(()=>p?h(e=>!e):g(e=>!e),[p,g,h]);a.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),v())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[v]);let j=x?"expanded":"collapsed",C=a.useMemo(()=>({state:j,open:x,setOpen:g,isMobile:p,openMobile:m,setOpenMobile:h,toggleSidebar:v}),[j,x,g,p,m,h,v]);return(0,r.jsx)(c.Provider,{value:C,children:(0,r.jsx)(d.Bc,{delayDuration:0,children:(0,r.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...o},className:(0,i.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...u,children:l})})})}function m({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:a,children:s,...o}){let{isMobile:d,state:c,openMobile:p,setOpenMobile:m}=u();return"none"===n?(0,r.jsx)("div",{"data-slot":"sidebar",className:(0,i.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",a),...o,children:s}):d?(0,r.jsx)(l.cj,{open:p,onOpenChange:m,...o,children:(0,r.jsxs)(l.h,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,r.jsxs)(l.Fm,{className:"sr-only",children:[(0,r.jsx)(l.qp,{children:"Sidebar"}),(0,r.jsx)(l.Qs,{children:"Displays the mobile sidebar."})]}),(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:s})]})}):(0,r.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":"collapsed"===c?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[(0,r.jsx)("div",{"data-slot":"sidebar-gap",className:(0,i.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,r.jsx)("div",{"data-slot":"sidebar-container",className:(0,i.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",a),...o,children:(0,r.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function h({className:e,...t}){return(0,r.jsx)("main",{"data-slot":"sidebar-inset",className:(0,i.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function f({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,i.cn)("flex flex-col gap-2 p-2",e),...t})}function b({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,i.cn)("flex flex-col gap-2 p-2",e),...t})}function x({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,i.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function g({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,i.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t})}function v({className:e,asChild:t=!1,...n}){let a=t?s.DX:"div";return(0,r.jsx)(a,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,i.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})}function j({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,i.cn)("w-full text-sm",e),...t})}function C({className:e,...t}){return(0,r.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,i.cn)("flex w-full min-w-0 flex-col gap-1",e),...t})}function w({className:e,...t}){return(0,r.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,i.cn)("group/menu-item relative",e),...t})}let A=(0,o.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function S({asChild:e=!1,isActive:t=!1,variant:n="default",size:a="default",tooltip:o,className:l,...c}){let p=e?s.DX:"button",{isMobile:m,state:h}=u(),f=(0,r.jsx)(p,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":a,"data-active":t,className:(0,i.cn)(A({variant:n,size:a}),l),...c});return o?("string"==typeof o&&(o={children:o}),(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{asChild:!0,children:f}),(0,r.jsx)(d.ZI,{side:"right",align:"center",hidden:"collapsed"!==h||m,...o})]})):f}function N({className:e,...t}){return(0,r.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,i.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})}function y({className:e,...t}){return(0,r.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,i.cn)("group/menu-sub-item relative",e),...t})}function k({asChild:e=!1,size:t="md",isActive:n=!1,className:a,...o}){let l=e?s.DX:"a";return(0,r.jsx)(l,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:(0,i.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===t&&"text-xs","md"===t&&"text-sm","group-data-[collapsible=icon]:hidden",a),...o})}},83847:(e,t,n)=>{n.d(t,{ChatProvider:()=>u});var r=n(60687),a=n(43210),s=n(10218);function o({webhookUrl:e="https://mingonguyen.app.n8n.cloud/webhook/a889d2ae-2159-402f-b326-5f61e90f602e/chat",enabled:t=!0,position:n="bottom-right",chatConfig:r={},tenantSlug:o="",jwtToken:i=""}){let{theme:l}=(0,s.D)(),[d,c]=(0,a.useState)(!1),[u,p]=(0,a.useState)(null),[m,h]=(0,a.useState)(!1);return(0,a.useRef)(null),t,null}n(17575);var i=n(16189),l=n(31568),d=n(38605);function c(){let{config:e,tenantSlug:t,jwtToken:n}=function(){let e=(0,i.useParams)(),{user:t,isAuthenticated:n}=(0,l.A)(),[r,s]=(0,a.useState)(!0),o=e.tenant,c=(0,a.useCallback)(()=>{let e="https://mingonguyen.app.n8n.cloud/webhook/a889d2ae-2159-402f-b326-5f61e90f602e/chat",a=(0,d.c4)(),s=r&&!!e&&n&&!!o,i=t?.firstName??"there",l=t?.firstName&&t?.lastName?`${t.firstName} ${t.lastName}`:t?.firstName??t?.email??"User";return{webhookUrl:e,enabled:s,position:"bottom-right",chatConfig:{width:"420px",height:"600px",mode:"window",chatInputKey:"chatInput",chatSessionKey:"sessionId",loadPreviousSession:!0,showWelcomeScreen:!1,defaultLanguage:"en",metadata:{tenant:o,userId:t?.id,userEmail:t?.email,userName:l,userRole:t?.systemRole??"user",timestamp:new Date().toISOString(),platform:"openAutomate",source:"frontend-chat",hasToken:!!a},initialMessages:[`Hi ${i}! 👋`,"Welcome to OpenAutomate. I'm your AI assistant ready to help you with automation and general questions.","What can I help you with today?"],i18n:{en:{title:"Assistant",subtitle:"",footer:"Powered by OpenAutomate",getStarted:"Start New Conversation",inputPlaceholder:"Ask automation, or anything else...",closeButtonTooltip:"Close chat"}},webhookConfig:a?{method:"POST",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json","X-Tenant":o??""}}:void 0}}},[r,n,t,o]),u=(0,a.useCallback)(()=>{s(!0)},[]),p=(0,a.useCallback)(()=>{s(!1)},[]),m=(0,a.useCallback)(()=>{s(e=>!e)},[]),h=(0,a.useMemo)(()=>({currentTenant:o,isInTenant:!!o,tenantDisplayName:o?o.charAt(0).toUpperCase()+o.slice(1).replace(/-/g," "):null}),[o]);return{config:c(),tenantInfo:h,isEnabled:r,enableChat:u,disableChat:p,toggleChat:m,tenantSlug:o,jwtToken:(0,d.c4)()}}();return(0,r.jsx)(o,{...e,tenantSlug:t||"",jwtToken:n||""})}function u({children:e}){return(0,r.jsxs)(r.Fragment,{children:[e,(0,r.jsx)(c,{})]})}},85726:(e,t,n)=>{n.d(t,{E:()=>s});var r=n(60687),a=n(36966);function s({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",e),...t})}},89667:(e,t,n)=>{n.d(t,{p:()=>s});var r=n(60687);n(43210);var a=n(36966);function s({className:e,type:t,...n}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...n})}},92588:(e,t,n)=>{n.d(t,{SearchProvider:()=>a});var r=n(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call SearchProvider() from the server but SearchProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\layout\\search\\search-context.tsx","SearchProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useSearchContext() from the server but useSearchContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\layout\\search\\search-context.tsx","useSearchContext")},96834:(e,t,n)=>{n.d(t,{E:()=>l});var r=n(60687);n(43210);var a=n(11329),s=n(24224),o=n(36966);let i=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:n=!1,...s}){let l=n?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...s})}},97597:(e,t,n)=>{n.d(t,{AppSidebar:()=>er});var r=n(60687),a=n(43210),s=n(16189),o=n(80110),i=n(14952),l=n(36966),d=n(6094);function c({...e}){return(0,r.jsx)(d.bL,{"data-slot":"collapsible",...e})}function u({...e}){return(0,r.jsx)(d.R6,{"data-slot":"collapsible-trigger",...e})}function p({...e}){return(0,r.jsx)(d.Ke,{"data-slot":"collapsible-content",...e})}var m=n(85814),h=n.n(m);function f({items:e}){let t=(0,s.usePathname)();return(0,r.jsxs)(o.Cn,{children:[(0,r.jsx)(o.jj,{children:"Platform"}),(0,r.jsx)(o.wZ,{children:e?.map(e=>e.url?r.jsx(o.FX,{children:r.jsx(o.Uj,{asChild:!0,className:"py-7 hover:bg-orange-600/10 hover:text-orange-600 hover:outline  hover:outline-orange-600 transition-all duration-200",tooltip:e.title,children:r.jsxs(h(),{href:e.url,children:[e.icon&&r.jsx(e.icon,{}),r.jsx("span",{children:e.title})]})})},e.title):r.jsx(c,{asChild:!0,defaultOpen:e.isActive,className:"group/collapsible",children:r.jsxs(o.FX,{children:[r.jsx(u,{asChild:!0,children:r.jsxs(o.Uj,{className:"py-7 hover:bg-orange-600/10 hover:text-orange-600 hover:outline hover:outline-orange-600 transition-all duration-200",tooltip:e.title,children:[e.icon&&r.jsx(e.icon,{}),r.jsx("span",{children:e.title}),e.items&&e.items.length>0&&r.jsx(i.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),r.jsx(p,{children:r.jsx(o.q9,{children:e.items?.map(e=>{let n=t===e.url;return r.jsx(o.Fg,{className:l.cn("transition-all duration-200",n&&"hover:outline hover:outline-orange-600 text-orange-600 bg-orange-600/10"),children:r.jsx(o.Cp,{asChild:!0,className:l.cn("py-5 transition-all duration-200 hover:bg-orange-600/10 hover:text-orange-600",n&&"outline  outline-orange-600 text-orange-600 bg-orange-600/10"),children:r.jsx(h(),{href:e.url,children:r.jsx("span",{children:e.title})})})},e.title)})})})]})},e.title))})]})}var b=n(67141),x=n(29523);function g({organizations:e}){let t=(0,s.useRouter)(),n=a.useMemo(()=>e.find(e=>e.isActive)||e[0],[e]);return(0,r.jsx)(o.Cn,{children:(0,r.jsx)(o.rQ,{children:(0,r.jsx)(o.wZ,{children:(0,r.jsx)(o.FX,{children:(0,r.jsxs)(o.Uj,{size:"lg",className:"",children:[(0,r.jsx)(n.icon,{className:"size-4"}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:n.name}),n.plan&&(0,r.jsx)("span",{className:"truncate text-xs",children:n.plan})]}),(0,r.jsx)(x.$,{variant:"ghost",className:"ml-auto text-xs font-semibold hover:text-orange-600 text-orange-600 transition-all duration-200",onClick:()=>t.push("/tenant-selector"),children:(0,r.jsx)(b.A,{})})]})})})})})}function v({items:e,...t}){return(0,r.jsx)(o.Cn,{...t,children:(0,r.jsx)(o.rQ,{children:(0,r.jsx)(o.wZ,{children:e.map(e=>(0,r.jsx)(o.FX,{children:(0,r.jsx)(o.Uj,{asChild:!0,size:"sm",className:"hover:bg-orange-600/10 hover:text-orange-600 hover:outline  hover:outline-orange-600 transition-all duration-200",children:(0,r.jsxs)(h(),{href:e.url,children:[(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})})}var j=n(17971),C=n(21342),w=n(86522);function A({user:e,navUser:t}){let{isMobile:n}=(0,o.cL)(),{logout:a}=(0,w.A)();return(0,r.jsx)(o.wZ,{children:(0,r.jsx)(o.FX,{children:(0,r.jsxs)(C.rI,{children:[(0,r.jsx)(C.ty,{asChild:!0,children:(0,r.jsxs)(o.Uj,{size:"lg",className:"data-[state=open]:-accent data-[state=open]:text-sidebar-accent-foreground hover:bg-orange-600/10 hover:text-orange-600 hover:outline  hover:outline-orange-600 transition-all duration-200",children:[(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:e.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:e.email})]}),(0,r.jsx)(j.A,{className:"ml-auto size-4"})]})}),(0,r.jsxs)(C.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:n?"bottom":"right",align:"end",sideOffset:4,children:[(0,r.jsx)(C.lp,{className:"p-0 font-normal",children:(0,r.jsx)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:e.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:e.email})]})})}),(0,r.jsx)(C.mB,{}),(0,r.jsx)(C.I,{children:t.management.map(e=>(0,r.jsx)(C._2,{children:(0,r.jsx)(h(),{href:e.url,children:(0,r.jsxs)("div",{className:"flex align-center items-center gap-2",children:[e.icon&&(0,r.jsx)(e.icon,{}),e.title]})},e.title)},e.title))}),(0,r.jsx)(C.mB,{}),(0,r.jsx)(h(),{onClick:()=>a(),href:"",children:(0,r.jsx)(C.I,{children:(0,r.jsx)(C._2,{children:(0,r.jsxs)("div",{className:"flex align-center items-center gap-2",children:[t.logout.icon&&(0,r.jsx)(t.logout.icon,{}),(0,r.jsx)("span",{children:t.logout.title})]})})})})]})]})})})}var S=n(53881);function N({adminContent:e,userContent:t,fallback:n=null}){let{user:r,isLoading:a}=(0,w.A)();return a||!r?n:r.systemRole===S.i.Admin||"Admin"===r.systemRole?e:t}var y=n(51551),k=n(4781),I=n(78570),E=n(44493),P=n(96834),R=n(41862),F=n(93613),O=n(48730),G=n(5336),T=n(20140),z=n(29113);function U(){let{subscription:e,isLoading:t,mutate:n}=(0,y.R)(),[s,o]=(0,a.useState)(!1),{toast:i}=(0,T.d)(),l=async()=>{if(!s){o(!0),i({title:"Starting Trial...",description:"Please wait while we activate your free trial."});try{let e=await k.kV.startTrial();e.success?(i({title:"Trial Started Successfully!",description:"Your free trial has been activated. Refreshing subscription status..."}),await n()):i({title:"Failed to Start Trial",description:e.message||"Unable to start trial. Please try again.",variant:"destructive"})}catch(e){i({title:"Error Starting Trial",description:e instanceof Error?e.message:"An error occurred while starting your trial.",variant:"destructive"})}finally{o(!1)}}};if(t)return(0,r.jsx)(E.Zp,{className:"w-full",children:(0,r.jsx)(E.Wu,{className:"flex items-center justify-center p-6",children:(0,r.jsx)(R.A,{className:"h-6 w-6 animate-spin"})})});if(!e?.userTrialStatus)return(0,r.jsx)(E.Zp,{className:"w-full",children:(0,r.jsxs)(E.aR,{children:[(0,r.jsxs)(E.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(F.A,{className:"h-5 w-5 text-gray-500"}),"Loading..."]}),(0,r.jsx)(E.BT,{children:"Checking subscription status..."})]})});switch(e.userTrialStatus){case I.R.Eligible:return(0,r.jsxs)(E.Zp,{className:"w-full",children:[(0,r.jsxs)(E.aR,{children:[(0,r.jsxs)(E.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(O.A,{className:"h-5 w-5"}),"Start Your Free Trial"]}),(0,r.jsx)(E.BT,{children:"Get full access to all features with a free trial period."})]}),(0,r.jsx)(E.Wu,{children:(0,r.jsx)(x.$,{onClick:l,disabled:s,className:"w-full transition-all duration-200",children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(R.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Activating Trial..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(O.A,{className:"mr-2 h-4 w-4"}),"Start Free Trial"]})})})]});case I.R.Active:return(0,r.jsx)(E.Zp,{className:"w-full",children:(0,r.jsxs)(E.aR,{children:[(0,r.jsxs)(E.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.isActive&&e.isInTrial?(0,r.jsx)(O.A,{className:"h-5 w-5 text-blue-500"}):e.isActive?(0,r.jsx)(G.A,{className:"h-5 w-5 text-green-500"}):(0,r.jsx)(F.A,{className:"h-5 w-5 text-red-500"}),e.planName," Plan"]}),e.isInTrial?(0,r.jsx)(P.E,{variant:"secondary",children:"Trial"}):e.isActive?(0,r.jsx)(P.E,{variant:"default",children:"Active"}):(0,r.jsx)(P.E,{variant:"destructive",children:"Expired"})]}),(0,r.jsx)(E.BT,{children:(()=>{if(e.isInTrial&&e.trialEndsAt){let t=new Date(e.trialEndsAt+"Z");if(t>new Date){let e=(0,z.m)(t,{addSuffix:!0});return`Trial expires ${e}`}{let e=(0,z.m)(t,{addSuffix:!0});return`Trial expired ${e}`}}if(e.isActive&&e.renewsAt){let t=new Date(e.renewsAt+"Z"),n=(0,z.m)(t,{addSuffix:!0});return`Renews ${n}`}if(e.endsAt){let t=new Date(e.endsAt+"Z"),n=(0,z.m)(t,{addSuffix:!0});return`Ended ${n}`}return""})()})]})});case I.R.Used:return(0,r.jsxs)(E.Zp,{className:"w-full",children:[(0,r.jsxs)(E.aR,{children:[(0,r.jsxs)(E.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(F.A,{className:"h-5 w-5 text-orange-500"}),"Trial Already Used"]}),(0,r.jsx)(E.BT,{children:"You have already used your free trial. Upgrade to continue using all features."})]}),(0,r.jsx)(E.Wu,{children:(0,r.jsx)(x.$,{className:"w-full",children:"Upgrade to Premium"})})]});case I.R.NotEligible:return(0,r.jsxs)(E.Zp,{className:"w-full",children:[(0,r.jsxs)(E.aR,{children:[(0,r.jsxs)(E.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(F.A,{className:"h-5 w-5 text-orange-500"}),"Trial Not Available"]}),(0,r.jsx)(E.BT,{children:"Free trial is only available on your first organization unit. Upgrade to access premium features."})]}),(0,r.jsx)(E.Wu,{children:(0,r.jsx)(x.$,{className:"w-full",children:"Upgrade to Premium"})})]});default:return(0,r.jsx)(E.Zp,{className:"w-full",children:(0,r.jsxs)(E.aR,{children:[(0,r.jsxs)(E.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(F.A,{className:"h-5 w-5 text-gray-500"}),"Unknown Status"]}),(0,r.jsx)(E.BT,{children:"Unable to determine subscription status. Please refresh the page."})]})})}}var M=n(31568),_=n(32192),B=n(22915),Z=n(83753),L=n(86885),$=n(56476),D=n(49625),W=n(41312),V=n(17313),H=n(32940),X=n(56085);let Q={AGENT:"BotAgent",ASSET:"Asset",PACKAGE:"AutomationPackage",EXECUTION:"Execution",SCHEDULE:"Schedule",USER:"User",ORGANIZATION_UNIT:"OrganizationUnit"},Y={VIEW:1,UPDATE:3},K=e=>[{title:"Home",url:e("/dashboard"),icon:_.A,isActive:!0},{title:"Automation",icon:B.A,items:[{title:"Executions",url:e("/automation/executions"),permission:{resource:Q.EXECUTION,level:Y.VIEW}},{title:"Schedule",url:e("/automation/schedule"),permission:{resource:Q.SCHEDULE,level:Y.VIEW}},{title:"Package",url:e("/automation/package"),permission:{resource:Q.PACKAGE,level:Y.VIEW}}]},{title:"Agent",url:e("/agent"),icon:Z.A,permission:{resource:Q.AGENT,level:Y.VIEW},items:[{title:"Agent",url:e("/agent"),permission:{resource:Q.AGENT,level:Y.VIEW}}]},{title:"Asset",url:e("/asset"),icon:L.A,permission:{resource:Q.ASSET,level:Y.VIEW}},{title:"Administration",icon:$.A,permission:{resource:Q.ORGANIZATION_UNIT,level:Y.VIEW},items:[{title:"Users",url:e("/administration/users"),permission:{resource:Q.USER,level:Y.VIEW}},{title:"Roles",url:e("/administration/roles"),permission:{resource:Q.ORGANIZATION_UNIT,level:Y.VIEW}},{title:"Organization Unit",url:e("/administration/organizationUnit"),permission:{resource:Q.ORGANIZATION_UNIT,level:Y.UPDATE}},{title:"Subscription",url:e("/administration/subscription"),permission:{resource:Q.ORGANIZATION_UNIT,level:Y.VIEW}}]}],q=[{title:"Dashboard",url:"/dashboard",icon:D.A},{title:"User Management",url:"/user-management",icon:W.A},{title:"Organization Units",url:"/org-unit-management",icon:V.A},{title:"Agent Management",url:"/agent-management",icon:Z.A}],J=[{title:"Support",url:"#",icon:H.A}],ee=e=>({management:[{title:"Profile",url:e("/profile"),icon:X.A},{title:"Notifications",url:"",icon:X.A}],logout:{title:"Log out",url:""}}),et=e=>[{name:e||"OpenAutomate",plan:"Enterprise",url:"/",icon:V.A,isActive:!0}],en=(e,t)=>e.reduce((e,n)=>{if(n.permission&&!t(n.permission.resource,n.permission.level))return e;let r={...n};if(n.items){let a=n.items.filter(e=>!e.permission||t(e.permission.resource,e.permission.level));if(!(a.length>0)&&!n.url)return e;r.items=a}return e.push(r),e},[]);function er({...e}){let{user:t,hasPermission:n,isSystemAdmin:i,userProfile:l}=(0,M.A)(),d=(0,s.useParams)().tenant,c=a.useCallback(e=>d?`/${d}${e}`:e,[d]),u=et("OpenAutomate"),p=(0,a.useMemo)(()=>l||i?i?{admin:q}:{user:en(K(c),n),admin:[]}:{user:[],admin:[]},[c,n,l,i]),m=t?{name:`${t.firstName} ${t.lastName}`.trim()||t.email,email:t.email}:{name:"User",email:"Loading..."},h=ee(c);return l||i?(0,r.jsxs)(o.Bx,{className:" top-(--header-height) h-[calc(100svh-var(--header-height))]! dark:bg-black/60",...e,children:[!i&&(0,r.jsx)(o.Gh,{className:"px-0 py-2",children:(0,r.jsx)(g,{organizations:u})}),(0,r.jsxs)(o.Yv,{children:[(0,r.jsx)(N,{adminContent:(0,r.jsx)(f,{items:p.admin}),userContent:(0,r.jsx)(f,{items:p.user}),fallback:(0,r.jsx)(f,{items:p.user})}),!i&&d&&(0,r.jsx)("div",{className:"px-3 py-2",children:(0,r.jsx)(U,{})}),(0,r.jsx)(v,{items:J,className:"mt-auto"})]}),(0,r.jsx)(o.CG,{children:(0,r.jsx)(A,{user:m,navUser:h})})]}):(0,r.jsxs)(o.Bx,{className:" top-(--header-height) h-[calc(100svh-var(--header-height))]! dark:bg-black/60",...e,children:[!i&&(0,r.jsx)(o.Gh,{children:(0,r.jsx)(g,{organizations:u})}),(0,r.jsx)(o.Yv,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading navigation..."})})}),(0,r.jsx)(o.CG,{children:(0,r.jsx)(A,{user:m,navUser:h})})]})}},98641:(e,t,n)=>{n.d(t,{SiteHeader:()=>b});var r=n(60687),a=n(51214),s=n(85814),o=n.n(s),i=n(29523),l=n(35950),d=n(75139),c=n(21342),u=n(11437),p=n(56992);let m=[{code:"en",name:"English"},{code:"vi",name:"Tiếng Việt"}];function h(){let{locale:e,setLocale:t}=(0,p.Y)();return(0,r.jsxs)(c.rI,{children:[(0,r.jsx)(c.ty,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"outline",size:"sm",className:"hover:border-orange-600 gap-2 px-2 sm:px-4 py-1 sm:py-2 min-w-0",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"hidden xs:inline-block sm:inline-block text-xs sm:text-base",children:[m.find(t=>t.code===e)?.name||"Language"," "]})]})}),(0,r.jsx)(c.SQ,{align:"end",className:"min-w-[120px] w-32 sm:w-40",sideOffset:4,children:m.map(n=>(0,r.jsx)(c._2,{onClick:()=>t(n.code),className:(n.code===e?"font-bold ":"")+"text-xs sm:text-base",children:n.name},n.code))})]})}var f=n(80110);function b(){let{toggleSidebar:e}=(0,f.cL)();return(0,r.jsx)("header",{className:"bg-background sticky top-0 z-50 flex w-full items-center border-b ",children:(0,r.jsxs)("div",{className:"flex h-(--header-height) w-full items-center gap-2 px-4",children:[(0,r.jsx)(i.$,{className:"h-8 w-8",variant:"ghost",size:"icon",onClick:e,children:(0,r.jsx)(a.A,{})}),(0,r.jsx)(l.w,{orientation:"vertical",className:"mr-2 h-4"}),(0,r.jsx)(o(),{className:"font-bold text-xl text-orange-600",href:"",children:"OpenAutomate"}),(0,r.jsxs)("div",{className:"w-full sm:ml-auto sm:w-auto",children:[(0,r.jsx)(h,{}),(0,r.jsx)(d.U,{})]})]})})}}};