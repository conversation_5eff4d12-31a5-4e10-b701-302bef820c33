"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5033],{6470:(e,t,a)=>{a.d(t,{SiteHeader:()=>x});var n=a(95155),s=a(22432),r=a(6874),i=a.n(r),l=a(30285),o=a(22346),d=a(71185),c=a(44838),u=a(34869),h=a(54844);let m=[{code:"en",name:"English"},{code:"vi",name:"Tiếng Việt"}];function p(){var e;let{locale:t,setLocale:a}=(0,h.Y)();return(0,n.jsxs)(c.rI,{children:[(0,n.jsx)(c.ty,{asChild:!0,children:(0,n.jsxs)(l.$,{variant:"outline",size:"sm",className:"hover:border-orange-600 gap-2 px-2 sm:px-4 py-1 sm:py-2 min-w-0",children:[(0,n.jsx)(u.A,{className:"h-4 w-4"}),(0,n.jsxs)("span",{className:"hidden xs:inline-block sm:inline-block text-xs sm:text-base",children:[(null===(e=m.find(e=>e.code===t))||void 0===e?void 0:e.name)||"Language"," "]})]})}),(0,n.jsx)(c.SQ,{align:"end",className:"min-w-[120px] w-32 sm:w-40",sideOffset:4,children:m.map(e=>(0,n.jsx)(c._2,{onClick:()=>a(e.code),className:(e.code===t?"font-bold ":"")+"text-xs sm:text-base",children:e.name},e.code))})]})}var g=a(7838);function x(){let{toggleSidebar:e}=(0,g.cL)();return(0,n.jsx)("header",{className:"bg-background sticky top-0 z-50 flex w-full items-center border-b ",children:(0,n.jsxs)("div",{className:"flex h-(--header-height) w-full items-center gap-2 px-4",children:[(0,n.jsx)(l.$,{className:"h-8 w-8",variant:"ghost",size:"icon",onClick:e,children:(0,n.jsx)(s.A,{})}),(0,n.jsx)(o.w,{orientation:"vertical",className:"mr-2 h-4"}),(0,n.jsx)(i(),{className:"font-bold text-xl text-orange-600",href:"",children:"OpenAutomate"}),(0,n.jsxs)("div",{className:"w-full sm:ml-auto sm:w-auto",children:[(0,n.jsx)(p,{}),(0,n.jsx)(d.U,{})]})]})})}},7838:(e,t,a)=>{a.d(t,{Bx:()=>m,Yv:()=>f,CG:()=>x,Cn:()=>v,rQ:()=>j,jj:()=>b,Gh:()=>g,SidebarInset:()=>p,wZ:()=>y,Uj:()=>A,FX:()=>w,q9:()=>k,Cp:()=>T,Fg:()=>S,SidebarProvider:()=>h,cL:()=>u});var n=a(95155),s=a(12115),r=a(66634),i=a(74466),l=a(36928);a(30285),a(62523),a(22346);var o=a(38382);a(68856);var d=a(46102);let c=s.createContext(null);function u(){let e=s.useContext(c);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function h(e){let{defaultOpen:t=!0,open:a,onOpenChange:r,className:i,style:o,children:u,...h}=e,m=function(){let[e,t]=s.useState(void 0);return s.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[p,g]=s.useState(!1),[x,f]=s.useState(t),v=null!=a?a:x,b=s.useCallback(e=>{let t="function"==typeof e?e(v):e;r?r(t):f(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[r,v]),j=s.useCallback(()=>m?g(e=>!e):b(e=>!e),[m,b,g]);s.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),j())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[j]);let y=v?"expanded":"collapsed",w=s.useMemo(()=>({state:y,open:v,setOpen:b,isMobile:m,openMobile:p,setOpenMobile:g,toggleSidebar:j}),[y,v,b,m,p,g,j]);return(0,n.jsx)(c.Provider,{value:w,children:(0,n.jsx)(d.Bc,{delayDuration:0,children:(0,n.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...o},className:(0,l.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",i),...h,children:u})})})}function m(e){let{side:t="left",variant:a="sidebar",collapsible:s="offcanvas",className:r,children:i,...d}=e,{isMobile:c,state:h,openMobile:m,setOpenMobile:p}=u();return"none"===s?(0,n.jsx)("div",{"data-slot":"sidebar",className:(0,l.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...d,children:i}):c?(0,n.jsx)(o.cj,{open:m,onOpenChange:p,...d,children:(0,n.jsxs)(o.h,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,n.jsxs)(o.Fm,{className:"sr-only",children:[(0,n.jsx)(o.qp,{children:"Sidebar"}),(0,n.jsx)(o.Qs,{children:"Displays the mobile sidebar."})]}),(0,n.jsx)("div",{className:"flex h-full w-full flex-col",children:i})]})}):(0,n.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":h,"data-collapsible":"collapsed"===h?s:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[(0,n.jsx)("div",{"data-slot":"sidebar-gap",className:(0,l.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,n.jsx)("div",{"data-slot":"sidebar-container",className:(0,l.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...d,children:(0,n.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:i})})]})}function p(e){let{className:t,...a}=e;return(0,n.jsx)("main",{"data-slot":"sidebar-inset",className:(0,l.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...a})}function g(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,l.cn)("flex flex-col gap-2 p-2",t),...a})}function x(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,l.cn)("flex flex-col gap-2 p-2",t),...a})}function f(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,l.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function v(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,l.cn)("relative flex w-full min-w-0 flex-col p-2",t),...a})}function b(e){let{className:t,asChild:a=!1,...s}=e,i=a?r.DX:"div";return(0,n.jsx)(i,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,l.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...s})}function j(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,l.cn)("w-full text-sm",t),...a})}function y(e){let{className:t,...a}=e;return(0,n.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,l.cn)("flex w-full min-w-0 flex-col gap-1",t),...a})}function w(e){let{className:t,...a}=e;return(0,n.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,l.cn)("group/menu-item relative",t),...a})}let N=(0,i.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function A(e){let{asChild:t=!1,isActive:a=!1,variant:s="default",size:i="default",tooltip:o,className:c,...h}=e,m=t?r.DX:"button",{isMobile:p,state:g}=u(),x=(0,n.jsx)(m,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":i,"data-active":a,className:(0,l.cn)(N({variant:s,size:i}),c),...h});return o?("string"==typeof o&&(o={children:o}),(0,n.jsxs)(d.m_,{children:[(0,n.jsx)(d.k$,{asChild:!0,children:x}),(0,n.jsx)(d.ZI,{side:"right",align:"center",hidden:"collapsed"!==g||p,...o})]})):x}function k(e){let{className:t,...a}=e;return(0,n.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,l.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...a})}function S(e){let{className:t,...a}=e;return(0,n.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,l.cn)("group/menu-sub-item relative",t),...a})}function T(e){let{asChild:t=!1,size:a="md",isActive:s=!1,className:i,...o}=e,d=t?r.DX:"a";return(0,n.jsx)(d,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":a,"data-active":s,className:(0,l.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===a&&"text-xs","md"===a&&"text-sm","group-data-[collapsible=icon]:hidden",i),...o})}},19034:(e,t,a)=>{a.d(t,{SearchProvider:()=>i});var n=a(95155),s=a(12115);let r=(0,s.createContext)(void 0);function i(e){let{children:t}=e,[a,i]=(0,s.useState)(""),l={searchTerm:a,setSearchTerm:i,isSearching:a.length>0};return(0,n.jsx)(r.Provider,{value:l,children:t})}},19597:(e,t,a)=>{a.d(t,{kV:()=>i});var n=a(7283);let s=()=>{let e=window.location.pathname.split("/").filter(Boolean);return e.length>0?e[0]:""},r=()=>{let e=s();if(!e)throw Error("No tenant context available");return{status:"".concat(e,"/api/subscription/status"),startTrial:"".concat(e,"/api/subscription/start-trial")}},i={getSubscriptionStatus:async()=>{let e=r();return n.F.get(e.status)},startTrial:async()=>{let e=r();return n.F.post(e.startTrial,{})}}},22100:(e,t,a)=>{a.d(t,{A:()=>r});var n=a(12115),s=a(67057);function r(){let e=(0,n.useContext)(s.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},22346:(e,t,a)=>{a.d(t,{w:()=>i});var n=a(95155);a(12115);var s=a(86986),r=a(36928);function i(e){let{className:t,orientation:a="horizontal",decorative:i=!0,...l}=e;return(0,n.jsx)(s.b,{"data-slot":"separator-root",decorative:i,orientation:a,className:(0,r.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...l})}},26126:(e,t,a)=>{a.d(t,{E:()=>o});var n=a(95155);a(12115);var s=a(66634),r=a(74466),i=a(36928);let l=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:r=!1,...o}=e,d=r?s.DX:"span";return(0,n.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(l({variant:a}),t),...o})}},29090:(e,t,a)=>{a.d(t,{R:()=>n});var n=function(e){return e.Eligible="Eligible",e.Active="Active",e.Used="Used",e.NotEligible="NotEligible",e}({})},34811:(e,t,a)=>{a.d(t,{R:()=>i});var n=a(34953),s=a(19597),r=a(70449);function i(){let{data:e,error:t,isLoading:a,mutate:i}=(0,n.Ay)(r.DC.subscription(),()=>s.kV.getSubscriptionStatus(),{refreshInterval:3e4,revalidateOnFocus:!0});return{subscription:e||null,isLoading:a,error:t?(0,r.IS)(t):null,mutate:i}}},38891:(e,t,a)=>{a.d(t,{AppSidebar:()=>en});var n=a(95155),s=a(12115),r=a(35695),i=a(7838),l=a(13052),o=a(36928),d=a(28890);function c(e){let{...t}=e;return(0,n.jsx)(d.bL,{"data-slot":"collapsible",...t})}function u(e){let{...t}=e;return(0,n.jsx)(d.R6,{"data-slot":"collapsible-trigger",...t})}function h(e){let{...t}=e;return(0,n.jsx)(d.Ke,{"data-slot":"collapsible-content",...t})}var m=a(6874),p=a.n(m);function g(e){let{items:t}=e,a=(0,r.usePathname)();return(0,n.jsxs)(i.Cn,{children:[(0,n.jsx)(i.jj,{children:"Platform"}),(0,n.jsx)(i.wZ,{children:null==t?void 0:t.map(e=>{var t;return e.url?(0,n.jsx)(i.FX,{children:(0,n.jsx)(i.Uj,{asChild:!0,className:"py-7 hover:bg-orange-600/10 hover:text-orange-600 hover:outline  hover:outline-orange-600 transition-all duration-200",tooltip:e.title,children:(0,n.jsxs)(p(),{href:e.url,children:[e.icon&&(0,n.jsx)(e.icon,{}),(0,n.jsx)("span",{children:e.title})]})})},e.title):(0,n.jsx)(c,{asChild:!0,defaultOpen:e.isActive,className:"group/collapsible",children:(0,n.jsxs)(i.FX,{children:[(0,n.jsx)(u,{asChild:!0,children:(0,n.jsxs)(i.Uj,{className:"py-7 hover:bg-orange-600/10 hover:text-orange-600 hover:outline hover:outline-orange-600 transition-all duration-200",tooltip:e.title,children:[e.icon&&(0,n.jsx)(e.icon,{}),(0,n.jsx)("span",{children:e.title}),e.items&&e.items.length>0&&(0,n.jsx)(l.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,n.jsx)(h,{children:(0,n.jsx)(i.q9,{children:null===(t=e.items)||void 0===t?void 0:t.map(e=>{let t=a===e.url;return(0,n.jsx)(i.Fg,{className:(0,o.cn)("transition-all duration-200",t&&"hover:outline hover:outline-orange-600 text-orange-600 bg-orange-600/10"),children:(0,n.jsx)(i.Cp,{asChild:!0,className:(0,o.cn)("py-5 transition-all duration-200 hover:bg-orange-600/10 hover:text-orange-600",t&&"outline  outline-orange-600 text-orange-600 bg-orange-600/10"),children:(0,n.jsx)(p(),{href:e.url,children:(0,n.jsx)("span",{children:e.title})})})},e.title)})})})]})},e.title)})})]})}var x=a(84109),f=a(30285);function v(e){let{organizations:t}=e,a=(0,r.useRouter)(),l=s.useMemo(()=>t.find(e=>e.isActive)||t[0],[t]);return(0,n.jsx)(i.Cn,{children:(0,n.jsx)(i.rQ,{children:(0,n.jsx)(i.wZ,{children:(0,n.jsx)(i.FX,{children:(0,n.jsxs)(i.Uj,{size:"lg",className:"",children:[(0,n.jsx)(l.icon,{className:"size-4"}),(0,n.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,n.jsx)("span",{className:"truncate font-medium",children:l.name}),l.plan&&(0,n.jsx)("span",{className:"truncate text-xs",children:l.plan})]}),(0,n.jsx)(f.$,{variant:"ghost",className:"ml-auto text-xs font-semibold hover:text-orange-600 text-orange-600 transition-all duration-200",onClick:()=>a.push("/tenant-selector"),children:(0,n.jsx)(x.A,{})})]})})})})})}function b(e){let{items:t,...a}=e;return(0,n.jsx)(i.Cn,{...a,children:(0,n.jsx)(i.rQ,{children:(0,n.jsx)(i.wZ,{children:t.map(e=>(0,n.jsx)(i.FX,{children:(0,n.jsx)(i.Uj,{asChild:!0,size:"sm",className:"hover:bg-orange-600/10 hover:text-orange-600 hover:outline  hover:outline-orange-600 transition-all duration-200",children:(0,n.jsxs)(p(),{href:e.url,children:[(0,n.jsx)(e.icon,{}),(0,n.jsx)("span",{children:e.title})]})})},e.title))})})})}var j=a(10081),y=a(44838),w=a(67057);function N(e){let{user:t,navUser:a}=e,{isMobile:s}=(0,i.cL)(),{logout:r}=(0,w.A)();return(0,n.jsx)(i.wZ,{children:(0,n.jsx)(i.FX,{children:(0,n.jsxs)(y.rI,{children:[(0,n.jsx)(y.ty,{asChild:!0,children:(0,n.jsxs)(i.Uj,{size:"lg",className:"data-[state=open]:-accent data-[state=open]:text-sidebar-accent-foreground hover:bg-orange-600/10 hover:text-orange-600 hover:outline  hover:outline-orange-600 transition-all duration-200",children:[(0,n.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,n.jsx)("span",{className:"truncate font-medium",children:t.name}),(0,n.jsx)("span",{className:"truncate text-xs",children:t.email})]}),(0,n.jsx)(j.A,{className:"ml-auto size-4"})]})}),(0,n.jsxs)(y.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:s?"bottom":"right",align:"end",sideOffset:4,children:[(0,n.jsx)(y.lp,{className:"p-0 font-normal",children:(0,n.jsx)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:(0,n.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,n.jsx)("span",{className:"truncate font-medium",children:t.name}),(0,n.jsx)("span",{className:"truncate text-xs",children:t.email})]})})}),(0,n.jsx)(y.mB,{}),(0,n.jsx)(y.I,{children:a.management.map(e=>(0,n.jsx)(y._2,{children:(0,n.jsx)(p(),{href:e.url,children:(0,n.jsxs)("div",{className:"flex align-center items-center gap-2",children:[e.icon&&(0,n.jsx)(e.icon,{}),e.title]})},e.title)},e.title))}),(0,n.jsx)(y.mB,{}),(0,n.jsx)(p(),{onClick:()=>r(),href:"",children:(0,n.jsx)(y.I,{children:(0,n.jsx)(y._2,{children:(0,n.jsxs)("div",{className:"flex align-center items-center gap-2",children:[a.logout.icon&&(0,n.jsx)(a.logout.icon,{}),(0,n.jsx)("span",{children:a.logout.title})]})})})})]})]})})})}var A=a(59385);function k(e){let{adminContent:t,userContent:a,fallback:n=null}=e,{user:s,isLoading:r}=(0,w.A)();return r||!s?n:s.systemRole===A.i.Admin||"Admin"===s.systemRole?t:a}var S=a(34811),T=a(19597),E=a(29090),I=a(66695),C=a(26126),P=a(51154),U=a(85339),O=a(14186),z=a(40646),R=a(88262),D=a(90498);function B(){let{subscription:e,isLoading:t,mutate:a}=(0,S.R)(),[r,i]=(0,s.useState)(!1),{toast:l}=(0,R.d)(),o=async()=>{if(!r){i(!0),l({title:"Starting Trial...",description:"Please wait while we activate your free trial."});try{let e=await T.kV.startTrial();e.success?(l({title:"Trial Started Successfully!",description:"Your free trial has been activated. Refreshing subscription status..."}),await a()):l({title:"Failed to Start Trial",description:e.message||"Unable to start trial. Please try again.",variant:"destructive"})}catch(e){l({title:"Error Starting Trial",description:e instanceof Error?e.message:"An error occurred while starting your trial.",variant:"destructive"})}finally{i(!1)}}};if(t)return(0,n.jsx)(I.Zp,{className:"w-full",children:(0,n.jsx)(I.Wu,{className:"flex items-center justify-center p-6",children:(0,n.jsx)(P.A,{className:"h-6 w-6 animate-spin"})})});if(!(null==e?void 0:e.userTrialStatus))return(0,n.jsx)(I.Zp,{className:"w-full",children:(0,n.jsxs)(I.aR,{children:[(0,n.jsxs)(I.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(U.A,{className:"h-5 w-5 text-gray-500"}),"Loading..."]}),(0,n.jsx)(I.BT,{children:"Checking subscription status..."})]})});switch(e.userTrialStatus){case E.R.Eligible:return(0,n.jsxs)(I.Zp,{className:"w-full",children:[(0,n.jsxs)(I.aR,{children:[(0,n.jsxs)(I.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(O.A,{className:"h-5 w-5"}),"Start Your Free Trial"]}),(0,n.jsx)(I.BT,{children:"Get full access to all features with a free trial period."})]}),(0,n.jsx)(I.Wu,{children:(0,n.jsx)(f.$,{onClick:o,disabled:r,className:"w-full transition-all duration-200",children:r?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(P.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Activating Trial..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(O.A,{className:"mr-2 h-4 w-4"}),"Start Free Trial"]})})})]});case E.R.Active:return(0,n.jsx)(I.Zp,{className:"w-full",children:(0,n.jsxs)(I.aR,{children:[(0,n.jsxs)(I.ZB,{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[e.isActive&&e.isInTrial?(0,n.jsx)(O.A,{className:"h-5 w-5 text-blue-500"}):e.isActive?(0,n.jsx)(z.A,{className:"h-5 w-5 text-green-500"}):(0,n.jsx)(U.A,{className:"h-5 w-5 text-red-500"}),e.planName," Plan"]}),e.isInTrial?(0,n.jsx)(C.E,{variant:"secondary",children:"Trial"}):e.isActive?(0,n.jsx)(C.E,{variant:"default",children:"Active"}):(0,n.jsx)(C.E,{variant:"destructive",children:"Expired"})]}),(0,n.jsx)(I.BT,{children:(()=>{if(e.isInTrial&&e.trialEndsAt){let t=new Date(e.trialEndsAt+"Z");if(t>new Date){let e=(0,D.m)(t,{addSuffix:!0});return"Trial expires ".concat(e)}{let e=(0,D.m)(t,{addSuffix:!0});return"Trial expired ".concat(e)}}if(e.isActive&&e.renewsAt){let t=new Date(e.renewsAt+"Z"),a=(0,D.m)(t,{addSuffix:!0});return"Renews ".concat(a)}if(e.endsAt){let t=new Date(e.endsAt+"Z"),a=(0,D.m)(t,{addSuffix:!0});return"Ended ".concat(a)}return""})()})]})});case E.R.Used:return(0,n.jsxs)(I.Zp,{className:"w-full",children:[(0,n.jsxs)(I.aR,{children:[(0,n.jsxs)(I.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(U.A,{className:"h-5 w-5 text-orange-500"}),"Trial Already Used"]}),(0,n.jsx)(I.BT,{children:"You have already used your free trial. Upgrade to continue using all features."})]}),(0,n.jsx)(I.Wu,{children:(0,n.jsx)(f.$,{className:"w-full",children:"Upgrade to Premium"})})]});case E.R.NotEligible:return(0,n.jsxs)(I.Zp,{className:"w-full",children:[(0,n.jsxs)(I.aR,{children:[(0,n.jsxs)(I.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(U.A,{className:"h-5 w-5 text-orange-500"}),"Trial Not Available"]}),(0,n.jsx)(I.BT,{children:"Free trial is only available on your first organization unit. Upgrade to access premium features."})]}),(0,n.jsx)(I.Wu,{children:(0,n.jsx)(f.$,{className:"w-full",children:"Upgrade to Premium"})})]});default:return(0,n.jsx)(I.Zp,{className:"w-full",children:(0,n.jsxs)(I.aR,{children:[(0,n.jsxs)(I.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(U.A,{className:"h-5 w-5 text-gray-500"}),"Unknown Status"]}),(0,n.jsx)(I.BT,{children:"Unable to determine subscription status. Please refresh the page."})]})})}}var Z=a(22100),W=a(57340),_=a(17051),F=a(25657),L=a(22503),G=a(47330),V=a(73783),X=a(17580),K=a(23227),M=a(5560),q=a(53311);let Y={AGENT:"BotAgent",ASSET:"Asset",PACKAGE:"AutomationPackage",EXECUTION:"Execution",SCHEDULE:"Schedule",USER:"User",ORGANIZATION_UNIT:"OrganizationUnit"},$={VIEW:1,UPDATE:3},H=e=>[{title:"Home",url:e("/dashboard"),icon:W.A,isActive:!0},{title:"Automation",icon:_.A,items:[{title:"Executions",url:e("/automation/executions"),permission:{resource:Y.EXECUTION,level:$.VIEW}},{title:"Schedule",url:e("/automation/schedule"),permission:{resource:Y.SCHEDULE,level:$.VIEW}},{title:"Package",url:e("/automation/package"),permission:{resource:Y.PACKAGE,level:$.VIEW}}]},{title:"Agent",url:e("/agent"),icon:F.A,permission:{resource:Y.AGENT,level:$.VIEW},items:[{title:"Agent",url:e("/agent"),permission:{resource:Y.AGENT,level:$.VIEW}}]},{title:"Asset",url:e("/asset"),icon:L.A,permission:{resource:Y.ASSET,level:$.VIEW}},{title:"Administration",icon:G.A,permission:{resource:Y.ORGANIZATION_UNIT,level:$.VIEW},items:[{title:"Users",url:e("/administration/users"),permission:{resource:Y.USER,level:$.VIEW}},{title:"Roles",url:e("/administration/roles"),permission:{resource:Y.ORGANIZATION_UNIT,level:$.VIEW}},{title:"Organization Unit",url:e("/administration/organizationUnit"),permission:{resource:Y.ORGANIZATION_UNIT,level:$.UPDATE}},{title:"Subscription",url:e("/administration/subscription"),permission:{resource:Y.ORGANIZATION_UNIT,level:$.VIEW}}]}],Q=[{title:"Dashboard",url:"/dashboard",icon:V.A},{title:"User Management",url:"/user-management",icon:X.A},{title:"Organization Units",url:"/org-unit-management",icon:K.A},{title:"Agent Management",url:"/agent-management",icon:F.A}],J=[{title:"Support",url:"#",icon:M.A}],ee=e=>({management:[{title:"Profile",url:e("/profile"),icon:q.A},{title:"Notifications",url:"",icon:q.A}],logout:{title:"Log out",url:""}}),et=e=>[{name:e||"OpenAutomate",plan:"Enterprise",url:"/",icon:K.A,isActive:!0}],ea=(e,t)=>e.reduce((e,a)=>{if(a.permission&&!t(a.permission.resource,a.permission.level))return e;let n={...a};if(a.items){let s=a.items.filter(e=>!e.permission||t(e.permission.resource,e.permission.level));if(!(s.length>0)&&!a.url)return e;n.items=s}return e.push(n),e},[]);function en(e){let{...t}=e,{user:a,hasPermission:l,isSystemAdmin:o,userProfile:d}=(0,Z.A)(),c=(0,r.useParams)().tenant,u=s.useCallback(e=>c?"/".concat(c).concat(e):e,[c]),h=et("OpenAutomate"),m=(0,s.useMemo)(()=>d||o?o?{admin:Q}:{user:ea(H(u),l),admin:[]}:{user:[],admin:[]},[u,l,d,o]),p=a?{name:"".concat(a.firstName," ").concat(a.lastName).trim()||a.email,email:a.email}:{name:"User",email:"Loading..."},x=ee(u);return d||o?(0,n.jsxs)(i.Bx,{className:" top-(--header-height) h-[calc(100svh-var(--header-height))]! dark:bg-black/60",...t,children:[!o&&(0,n.jsx)(i.Gh,{className:"px-0 py-2",children:(0,n.jsx)(v,{organizations:h})}),(0,n.jsxs)(i.Yv,{children:[(0,n.jsx)(k,{adminContent:(0,n.jsx)(g,{items:m.admin}),userContent:(0,n.jsx)(g,{items:m.user}),fallback:(0,n.jsx)(g,{items:m.user})}),!o&&c&&(0,n.jsx)("div",{className:"px-3 py-2",children:(0,n.jsx)(B,{})}),(0,n.jsx)(b,{items:J,className:"mt-auto"})]}),(0,n.jsx)(i.CG,{children:(0,n.jsx)(N,{user:p,navUser:x})})]}):(0,n.jsxs)(i.Bx,{className:" top-(--header-height) h-[calc(100svh-var(--header-height))]! dark:bg-black/60",...t,children:[!o&&(0,n.jsx)(i.Gh,{children:(0,n.jsx)(v,{organizations:h})}),(0,n.jsx)(i.Yv,{children:(0,n.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading navigation..."})})}),(0,n.jsx)(i.CG,{children:(0,n.jsx)(N,{user:p,navUser:x})})]})}},46102:(e,t,a)=>{a.d(t,{Bc:()=>i,ZI:()=>d,k$:()=>o,m_:()=>l});var n=a(95155);a(12115);var s=a(9635),r=a(36928);function i(e){let{delayDuration:t=0,...a}=e;return(0,n.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function l(e){let{...t}=e;return(0,n.jsx)(i,{children:(0,n.jsx)(s.bL,{"data-slot":"tooltip",...t})})}function o(e){let{...t}=e;return(0,n.jsx)(s.l9,{"data-slot":"tooltip-trigger",...t})}function d(e){let{className:t,sideOffset:a=0,children:i,...l}=e;return(0,n.jsx)(s.ZL,{children:(0,n.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,r.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...l,children:[i,(0,n.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},54844:(e,t,a)=>{a.d(t,{LocaleProvider:()=>l,Y:()=>o});var n=a(95155),s=a(12115);let r=(0,s.createContext)(void 0),i={en:()=>a.e(8083).then(a.t.bind(a,48083,19)).then(e=>e.default),vi:()=>a.e(7613).then(a.t.bind(a,97613,19)).then(e=>e.default)};function l(e){let{children:t}=e,[a,l]=(0,s.useState)("en"),[o,d]=(0,s.useState)({});(0,s.useEffect)(()=>{let e=localStorage.getItem("locale");e&&l(e)},[]),(0,s.useEffect)(()=>{localStorage.setItem("locale",a),i[a]().then(d)},[a]);let c=(0,s.useCallback)(e=>{var t;return null!==(t=e.split(".").reduce((e,t)=>"object"==typeof e&&e?e[t]:void 0,o))&&void 0!==t?t:e},[o]);return(0,n.jsx)(r.Provider,{value:{locale:a,setLocale:l,t:c},children:t})}function o(){let e=(0,s.useContext)(r);if(!e)throw Error("useLocale must be used within LocaleProvider");return e}},66695:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>r,aR:()=>i,wL:()=>c});var n=a(95155);a(12115);var s=a(36928);function r(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},68755:(e,t,a)=>{a.d(t,{ChatProvider:()=>g});var n=a(95155),s=a(12115),r=a(51362);a(75819);let i=e=>"string"==typeof e?e:e instanceof URL?e.toString():e.url||"",l=(e,t,a)=>{var n,s,r;let i=null!==(s=null!==(n=e.chatInput)&&void 0!==n?n:e.message)&&void 0!==s?s:"",l=[];t&&l.push("[tenant: ".concat(t,"]")),a&&l.push("[token: ".concat(a,"]"));let o=l.length>0?"".concat(i," ").concat(l.join(" ")):i;return{sessionId:e.sessionId,action:null!==(r=e.action)&&void 0!==r?r:"sendMessage",chatInput:o}},o=async(e,t,a,n,s)=>{var r,i;let o=null!==(i=null!==(r=n.chatInput)&&void 0!==r?r:n.message)&&void 0!==i?i:"";s.onTypingStart&&o.trim()&&s.onTypingStart();let d=l(n,s.tenantSlug,s.authToken),c={...a,body:JSON.stringify(d)};try{let a=await e(t,c);return s.onTypingEnd&&s.onTypingEnd(),a}catch(e){throw s.onTypingEnd&&s.onTypingEnd(),e}},d=async(e,t,a,n,s)=>{let r=window.fetch,l=async(l,d)=>{if(i(l)===e&&(null==d?void 0:d.method)==="POST")try{let e=JSON.parse(d.body);return await o(r,l,d,e,{tenantSlug:t,authToken:a,onTypingStart:n,onTypingEnd:s})}catch(e){}return r(l,d)};return window.fetch=l,()=>{window.fetch=r}};function c(e){let{webhookUrl:t="https://mingonguyen.app.n8n.cloud/webhook/a889d2ae-2159-402f-b326-5f61e90f602e/chat",enabled:n=!0,position:i="bottom-right",chatConfig:l={},tenantSlug:o="",jwtToken:c=""}=e,{theme:u}=(0,r.D)(),[h,m]=(0,s.useState)(!1),[p,g]=(0,s.useState)(null),[x,f]=(0,s.useState)(!1),v=(0,s.useRef)(null),b=()=>{f(!0),v.current&&clearTimeout(v.current),v.current=setTimeout(()=>{f(!1)},3e4)},j=()=>{f(!1),v.current&&(clearTimeout(v.current),v.current=null)};return(0,s.useEffect)(()=>{let e;if(!n||!t)return;let s=null;return(async()=>{try{var n,r,i,u,h,p;let{createChat:x}=await Promise.all([a.e(8834),a.e(5014),a.e(718)]).then(a.bind(a,45224)),f=await d(t,null!=o?o:"",null!=c?c:"",b,j);f&&(s=f),x({webhookUrl:t,mode:null!==(n=l.mode)&&void 0!==n?n:"window",chatInputKey:null!==(r=l.chatInputKey)&&void 0!==r?r:"chatInput",chatSessionKey:null!==(i=l.chatSessionKey)&&void 0!==i?i:"sessionId",loadPreviousSession:!1!==l.loadPreviousSession,metadata:null!==(u=l.metadata)&&void 0!==u?u:{},showWelcomeScreen:!1!==l.showWelcomeScreen,defaultLanguage:"en",initialMessages:null!==(h=l.initialMessages)&&void 0!==h?h:[],i18n:null!==(p=l.i18n)&&void 0!==p?p:{}}),m(!0),g(null),e=()=>{console.log("Chat component cleanup"),s&&s()}}catch(e){console.error("Failed to initialize n8n chat:",e),g("Failed to load chat widget"),m(!1)}})(),()=>{e&&e(),s&&s(),v.current&&(clearTimeout(v.current),v.current=null)}},[t,n,l,o,c]),(0,s.useEffect)(()=>{if(!h||!x)return;let e=()=>{let e=document.querySelector('.n8n-chat .chat-messages, .n8n-chat [class*="messages"], .n8n-chat [class*="conversation"]');if(!e||document.querySelector(".typing-indicator"))return;let t=document.createElement("div");t.className="typing-indicator",t.innerHTML='\n        <div class="typing-indicator-content">\n          <div class="typing-indicator-avatar">\uD83E\uDD16</div>\n          <div class="typing-indicator-message">\n            <div class="typing-indicator-text">Assistant is typing</div>\n            <div class="typing-indicator-dots">\n              <span></span>\n              <span></span>\n              <span></span>\n            </div>\n          </div>\n        </div>\n      ';let a=document.createElement("style");a.textContent="\n        .typing-indicator {\n          padding: 8px 16px;\n          margin: 4px 0;\n          display: flex;\n          align-items: flex-start;\n          animation: fadeIn 0.3s ease-in;\n        }\n\n        .typing-indicator-content {\n          display: flex;\n          align-items: flex-start;\n          gap: 8px;\n          max-width: 80%;\n        }\n\n        .typing-indicator-avatar {\n          font-size: 24px;\n          line-height: 1;\n          flex-shrink: 0;\n        }\n\n        .typing-indicator-message {\n          background: var(--chat--message--bot--background, #f1f1f1);\n          color: var(--chat--message--bot--color, #333);\n          padding: 12px 16px;\n          border-radius: 18px;\n          border-bottom-left-radius: 4px;\n          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n        }\n\n        .typing-indicator-text {\n          font-size: 14px;\n          margin-bottom: 4px;\n          opacity: 0.7;\n        }\n\n        .typing-indicator-dots {\n          display: flex;\n          gap: 4px;\n        }\n\n        .typing-indicator-dots span {\n          width: 6px;\n          height: 6px;\n          background: currentColor;\n          border-radius: 50%;\n          opacity: 0.4;\n          animation: typingDot 1.4s infinite ease-in-out;\n        }\n\n        .typing-indicator-dots span:nth-child(1) { animation-delay: 0s; }\n        .typing-indicator-dots span:nth-child(2) { animation-delay: 0.2s; }\n        .typing-indicator-dots span:nth-child(3) { animation-delay: 0.4s; }\n\n        @keyframes typingDot {\n          0%, 60%, 100% { opacity: 0.4; transform: scale(1); }\n          30% { opacity: 1; transform: scale(1.2); }\n        }\n\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n      ",document.querySelector("#typing-indicator-styles")||(a.id="typing-indicator-styles",document.head.appendChild(a)),e.appendChild(t),e.scrollTop=e.scrollHeight};e();let t=setTimeout(e,100),a=setTimeout(e,500);return()=>{clearTimeout(t),clearTimeout(a)}},[h,x]),(0,s.useEffect)(()=>{if(!x){let e=document.querySelector(".typing-indicator");e&&e.remove()}},[x]),(0,s.useEffect)(()=>{if(!h)return;let e=document.documentElement;"dark"===u?(e.style.setProperty("--chat--color-primary","oklch(0.6641 0.2155 46.96)"),e.style.setProperty("--chat--color-secondary","oklch(0.6 0.118 184.704)"),e.style.setProperty("--chat--color-white","oklch(0.208 0.042 265.755)"),e.style.setProperty("--chat--color-light","oklch(0.279 0.041 260.031)"),e.style.setProperty("--chat--color-light-shade-50","oklch(0.208 0.042 265.755)"),e.style.setProperty("--chat--color-dark","oklch(0.984 0.003 247.858)"),e.style.setProperty("--chat--header--background","oklch(0.6241 0.2155 46.96)"),e.style.setProperty("--chat--header--color","oklch(1 0 0)"),e.style.setProperty("--chat--message--bot--background","oklch(0.279 0.041 260.031)"),e.style.setProperty("--chat--message--bot--color","oklch(0.984 0.003 247.858)")):(e.style.setProperty("--chat--color-primary","oklch(0.6641 0.2155 46.96)"),e.style.setProperty("--chat--color-secondary","oklch(0.6 0.118 184.704)"),e.style.setProperty("--chat--color-white","oklch(1 0 0)"),e.style.setProperty("--chat--color-light","oklch(0.984 0.003 247.858)"),e.style.setProperty("--chat--color-light-shade-50","oklch(0.968 0.007 247.896)"),e.style.setProperty("--chat--color-dark","oklch(0.129 0.042 264.695)"),e.style.setProperty("--chat--header--background","oklch(0.6641 0.2155 46.96)"),e.style.setProperty("--chat--header--color","oklch(1 0 0)"),e.style.setProperty("--chat--message--bot--background","oklch(1 0 0)"),e.style.setProperty("--chat--message--bot--color","oklch(0.129 0.042 264.695)")),Object.entries({"bottom-right":{bottom:"20px",right:"20px"},"bottom-left":{bottom:"20px",left:"20px"},"top-right":{top:"20px",right:"20px"},"top-left":{top:"20px",left:"20px"}}[i]).forEach(t=>{let[a,n]=t;e.style.setProperty("--chat--toggle--".concat(a),n)}),l.width&&e.style.setProperty("--chat--window--width",l.width),l.height&&e.style.setProperty("--chat--window--height",l.height)},[u,h,i,l]),n,null}var u=a(35695),h=a(22100),m=a(48133);function p(){let{config:e,tenantSlug:t,jwtToken:a}=function(){let e=(0,u.useParams)(),{user:t,isAuthenticated:a}=(0,h.A)(),[n,r]=(0,s.useState)(!0),i=e.tenant,l=(0,s.useCallback)(()=>{var e,s,r,l;let o="https://mingonguyen.app.n8n.cloud/webhook/a889d2ae-2159-402f-b326-5f61e90f602e/chat",d=(0,m.c4)(),c=n&&!!o&&a&&!!i,u=null!==(e=null==t?void 0:t.firstName)&&void 0!==e?e:"there",h=(null==t?void 0:t.firstName)&&(null==t?void 0:t.lastName)?"".concat(t.firstName," ").concat(t.lastName):null!==(r=null!==(s=null==t?void 0:t.firstName)&&void 0!==s?s:null==t?void 0:t.email)&&void 0!==r?r:"User";return{webhookUrl:o,enabled:c,position:"bottom-right",chatConfig:{width:"420px",height:"600px",mode:"window",chatInputKey:"chatInput",chatSessionKey:"sessionId",loadPreviousSession:!0,showWelcomeScreen:!1,defaultLanguage:"en",metadata:{tenant:i,userId:null==t?void 0:t.id,userEmail:null==t?void 0:t.email,userName:h,userRole:null!==(l=null==t?void 0:t.systemRole)&&void 0!==l?l:"user",timestamp:new Date().toISOString(),platform:"openAutomate",source:"frontend-chat",hasToken:!!d},initialMessages:["Hi ".concat(u,"! \uD83D\uDC4B"),"Welcome to OpenAutomate. I'm your AI assistant ready to help you with automation and general questions.","What can I help you with today?"],i18n:{en:{title:"Assistant",subtitle:"",footer:"Powered by OpenAutomate",getStarted:"Start New Conversation",inputPlaceholder:"Ask automation, or anything else...",closeButtonTooltip:"Close chat"}},webhookConfig:d?{method:"POST",headers:{Authorization:"Bearer ".concat(d),"Content-Type":"application/json","X-Tenant":null!=i?i:""}}:void 0}}},[n,a,t,i]),o=(0,s.useCallback)(()=>{r(!0)},[]),d=(0,s.useCallback)(()=>{r(!1)},[]),c=(0,s.useCallback)(()=>{r(e=>!e)},[]),p=(0,s.useMemo)(()=>({currentTenant:i,isInTenant:!!i,tenantDisplayName:i?i.charAt(0).toUpperCase()+i.slice(1).replace(/-/g," "):null}),[i]);return{config:l(),tenantInfo:p,isEnabled:n,enableChat:o,disableChat:d,toggleChat:c,tenantSlug:i,jwtToken:(0,m.c4)()}}();return(0,n.jsx)(c,{...e,tenantSlug:t||"",jwtToken:a||""})}function g(e){let{children:t}=e;return(0,n.jsxs)(n.Fragment,{children:[t,(0,n.jsx)(p,{})]})}},68856:(e,t,a)=>{a.d(t,{E:()=>r});var n=a(95155),s=a(36928);function r(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",t),...a})}},70449:(e,t,a)=>{a.d(t,{DC:()=>l,EJ:()=>i,IS:()=>o,bb:()=>r});var n=a(7283),s=a(15874);function r(){return{fetcher:e=>(0,n.fetchApi)(e),onError:function(e){(0,s.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let i={fetcher:e=>(0,n.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},l={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},o=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},88262:(e,t,a)=>{a.d(t,{$:()=>s,d:()=>r});var n=a(12115);let s=(0,n.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function r(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}}}]);