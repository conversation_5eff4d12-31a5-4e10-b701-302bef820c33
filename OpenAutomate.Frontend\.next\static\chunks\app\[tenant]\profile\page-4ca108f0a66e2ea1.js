(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4745],{13209:(e,s,a)=>{"use strict";a.d(s,{default:()=>j});var r=a(95155),t=a(12115),i=a(30285),n=a(66695),d=a(62523),o=a(85057),l=a(71007),c=a(51154),u=a(4229),m=a(22100),v=a(26126),x=a(75525),f=a(88262),p=a(25115),h=a(92657),g=a(78749),b=a(22370),w=a(15874);function N(){let[e,s]=(0,t.useState)(!1),[a,l]=(0,t.useState)(!1),[u,m]=(0,t.useState)(!1),{toast:v}=(0,f.d)(),[x,N]=(0,t.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),j=(e,s)=>{N(a=>({...a,[e]:s}))},y=async e=>{if(e.preventDefault(),x.newPassword!==x.confirmPassword){v({title:"Error",description:"New passwords do not match",variant:"destructive"});return}if(x.newPassword.length<8){v({title:"Error",description:"Password must be at least 8 characters long",variant:"destructive"});return}s(!0);try{await b.Z.changePassword({currentPassword:x.currentPassword,newPassword:x.newPassword,confirmNewPassword:x.confirmPassword}),v({title:"Success",description:"Password changed successfully"}),N({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){(0,w.AG)(e)}finally{s(!1)}};return(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),"Change Password"]}),(0,r.jsx)(n.BT,{children:"Update your account password for enhanced security"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.p,{id:"currentPassword",type:a?"text":"password",value:x.currentPassword,onChange:e=>j("currentPassword",e.target.value),required:!0,disabled:e}),(0,r.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>l(!a),disabled:e,children:a?(0,r.jsx)(h.A,{className:"h-4 w-4"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"newPassword",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.p,{id:"newPassword",type:u?"text":"password",value:x.newPassword,onChange:e=>j("newPassword",e.target.value),required:!0,disabled:e}),(0,r.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>m(!u),disabled:e,children:u?(0,r.jsx)(h.A,{className:"h-4 w-4"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(d.p,{id:"confirmPassword",type:"password",value:x.confirmPassword,onChange:e=>j("confirmPassword",e.target.value),required:!0,disabled:e})})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-2 pt-4",children:[(0,r.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>N({currentPassword:"",newPassword:"",confirmPassword:""}),disabled:e,children:"Cancel"}),(0,r.jsxs)(i.$,{type:"submit",disabled:e,children:[e&&(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Change Password"]})]})]})})]})}function j(){let[e,s]=(0,t.useState)(!1),[a,p]=(0,t.useState)(!1),{user:h,updateUser:g}=(0,m.A)(),{toast:w}=(0,f.d)(),[j,y]=(0,t.useState)({firstName:null==h?void 0:h.firstName,lastName:null==h?void 0:h.lastName,email:null==h?void 0:h.email,systemRole:null==h?void 0:h.systemRole}),P=(e,s)=>{y(a=>({...a,[e]:s}))},k=async()=>{if(!(null==h?void 0:h.id)||!j.firstName||!j.lastName){w({title:"Error",description:"Please fill in all required fields",variant:"default"});return}p(!0);try{await b.Z.changeUserName(h.id,{firstName:j.firstName,lastName:j.lastName}),g({firstName:j.firstName,lastName:j.lastName}),w({title:"Success",description:"Profile updated successfully"}),s(!1)}catch(e){w({title:"Error",description:e instanceof Error?e.message:"Failed to update profile",variant:"default"})}finally{p(!1)}};return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{children:"Personal Information"}),(0,r.jsx)(n.BT,{children:"Your basic account details"})]}),e?(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(i.$,{onClick:k,size:"sm",disabled:a,children:[a?(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,r.jsx)(i.$,{onClick:()=>{y({firstName:null==h?void 0:h.firstName,lastName:null==h?void 0:h.lastName,email:null==h?void 0:h.email,systemRole:null==h?void 0:h.systemRole}),s(!1)},variant:"outline",size:"sm",disabled:a,children:"Cancel"})]}):(0,r.jsxs)(i.$,{onClick:()=>s(!0),variant:"outline",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Edit Profile"]})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"firstName",children:"First Name"}),e?(0,r.jsx)(d.p,{id:"firstName",value:j.firstName,onChange:e=>P("firstName",e.target.value)}):(0,r.jsx)("div",{className:"p-2 bg-muted rounded-md",children:null==h?void 0:h.firstName})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"lastName",children:"Last Name"}),e?(0,r.jsx)(d.p,{id:"lastName",value:j.lastName,onChange:e=>P("lastName",e.target.value)}):(0,r.jsx)("div",{className:"p-2 bg-muted rounded-md",children:null==h?void 0:h.lastName})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"email",children:"Email Address"}),e?(0,r.jsx)(d.p,{id:"email",type:"email",value:j.email,onChange:e=>P("email",e.target.value)}):(0,r.jsx)("div",{className:"p-2 bg-muted rounded-md",children:null==h?void 0:h.email})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{className:"text-sm font-medium text-muted-foreground",children:"System Role"}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(null==h?void 0:h.systemRole)===0?(0,r.jsx)(v.E,{variant:"secondary",className:"flex items-center gap-1.5 px-3 py-1",children:"User"}):(0,r.jsxs)(v.E,{variant:"default",className:"flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r from-orange-500 to-red-500",children:[(0,r.jsx)(x.A,{className:"h-3.5 w-3.5"}),"Admin"]})})]})]})]}),(0,r.jsx)(N,{})]})})}},22100:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});var r=a(12115),t=a(67057);function i(){let e=(0,r.useContext)(t.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},26126:(e,s,a)=>{"use strict";a.d(s,{E:()=>o});var r=a(95155);a(12115);var t=a(66634),i=a(74466),n=a(36928);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:s,variant:a,asChild:i=!1,...o}=e,l=i?t.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:a}),s),...o})}},30285:(e,s,a)=>{"use strict";a.d(s,{$:()=>l,r:()=>o});var r=a(95155),t=a(12115),i=a(66634),n=a(74466),d=a(36928);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),l=t.forwardRef((e,s)=>{let{className:a,variant:t,size:n,asChild:l=!1,...c}=e,u=l?i.DX:"button";return(0,r.jsx)(u,{"data-slot":"button",className:(0,d.cn)(o({variant:t,size:n,className:a})),ref:s,...c})});l.displayName="Button"},62523:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var r=a(95155);a(12115);var t=a(36928);function i(e){let{className:s,type:a,...i}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},66695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>c});var r=a(95155);a(12115);var t=a(36928);function i(e){let{className:s,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function n(e){let{className:s,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function d(e){let{className:s,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",s),...a})}function o(e){let{className:s,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",s),...a})}function l(e){let{className:s,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",s),...a})}function c(e){let{className:s,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,t.cn)("flex items-center px-6 [.border-t]:pt-6",s),...a})}},80514:(e,s,a)=>{Promise.resolve().then(a.bind(a,13209))},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>n});var r=a(95155);a(12115);var t=a(24265),i=a(36928);function n(e){let{className:s,...a}=e;return(0,r.jsx)(t.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},88262:(e,s,a)=>{"use strict";a.d(s,{$:()=>t,d:()=>i});var r=a(12115);let t=(0,r.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function i(){let e=(0,r.useContext)(t);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:s=>e.addToast(s),dismiss:s=>e.dismissToast(s),toasts:e.toasts}}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,7742,4727,7057,8441,1684,7358],()=>s(80514)),_N_E=e.O()}]);