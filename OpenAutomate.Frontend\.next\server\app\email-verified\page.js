(()=>{var e={};e.id=2316,e.ids=[2316],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27996:(e,r,t)=>{Promise.resolve().then(t.bind(t,34772))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30859:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),a=t(48088),n=t(31369),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let l={children:["",{children:["email-verified",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34772)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\email-verified\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\email-verified\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/email-verified/page",pathname:"/email-verified",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33873:e=>{"use strict";e.exports=require("path")},34772:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\email-verified\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\email-verified\\page.tsx","default")},35071:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37724:(e,r,t)=>{Promise.resolve().then(t.bind(t,55402))},55402:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(60687),a=t(43210),n=t(16189),i=t(29523),o=t(91821),l=t(5336),d=t(35071),c=t(43649),u=t(4344);function p(){return(0,s.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-6 space-y-4 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Verifying your email..."}),(0,s.jsx)("p",{className:"text-gray-500",children:"Please wait while we confirm your email verification."})]})})}function x(){let e=(0,n.useSearchParams)(),r=(0,n.useRouter)(),[t,x]=(0,a.useState)(!0),m="true"===e.get("success"),f=e.get("reason"),v=()=>{r.push(u.$.paths.auth.login)};return t?(0,s.jsx)(p,{}):(0,s.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-4",children:(0,s.jsx)("div",{className:"w-full max-w-md p-6 space-y-6 text-center",children:m?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.A,{className:"mx-auto h-16 w-16 text-green-500"}),(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Email Verified!"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Your email has been successfully verified. You can now login to your account."}),(0,s.jsx)(i.$,{className:"w-full",onClick:v,children:"Go to Login"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mx-auto h-16 w-16 text-red-500"}),(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Verification Failed"}),(0,s.jsxs)(o.Fc,{variant:"destructive",className:"text-left",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)(o.XL,{children:"Error"}),(0,s.jsxs)(o.TN,{children:["invalid-token"===f&&"The verification link is invalid or has expired.","verification-failed"===f&&"We couldn't verify your email. Please try again later.","missing-token"===f&&"No verification token was provided.","server-error"===f&&"A server error occurred. Please try again later.",!f&&"An unknown error occurred during verification."]})]}),(0,s.jsx)("p",{className:"text-gray-500",children:"If you continue to have problems, please contact support."}),(0,s.jsx)("div",{className:"space-y-3",children:(0,s.jsx)(i.$,{className:"w-full",onClick:v,children:"Go to Login"})})]})})})}function m(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(p,{}),children:(0,s.jsx)(x,{})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>l,TN:()=>c,XL:()=>d});var s=t(60687),a=t(43210),n=t(24224),i=t(36966);let o=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),l=a.forwardRef(({className:e,variant:r,...t},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,i.cn)(o({variant:r}),e),...t}));l.displayName="Alert";let d=a.forwardRef(({className:e,children:r,...t},a)=>(r||console.warn("AlertTitle must have content for accessibility"),(0,s.jsx)("h5",{ref:a,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...t,children:r})));d.displayName="AlertTitle";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...r}));c.displayName="AlertDescription"}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7966,6763],()=>t(30859));module.exports=s})();