#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vietnamese Text Support Test

This script tests Vietnamese text handling across all services in the certificate notification bot.
"""

import sys
import os
from pathlib import Path

# Add framework to path
framework_path = os.path.join(os.path.dirname(__file__), 'framework')
sys.path.insert(0, framework_path)

# Add tasks to path
tasks_path = os.path.join(os.path.dirname(__file__), 'tasks')
sys.path.insert(0, tasks_path)

from data_service import DataService
from certificate_service import CertificateService
from email_service import EmailService
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_vietnamese_text_support():
    """Test Vietnamese text support across all services."""
    print("🇻🇳 Testing Vietnamese Text Support for Certificate Notification Bot")
    print("=" * 70)
    
    # Test data with Vietnamese characters
    vietnamese_test_data = {
        "TEN_UNG_VIEN": "Nguyễn Văn <PERSON>",
        "TEN_KHOA_HOC": "Khóa học Lập trình Python cơ bản",
        "THOI_GIAN_DAO_TAO": "01/01/2024 - 15/01/2024",
        "NGAY_CAP_CHUNG_CHI": "16/01/2024",
        "MA_NHAN_VIEN": "VN001",
        "EMAIL": "<EMAIL>"
    }
    
    print(f"📋 Test Data:")
    for key, value in vietnamese_test_data.items():
        print(f"   {key}: {value}")
    print()
    
    # Test 1: Data Service
    print("1️⃣ Testing Data Service Vietnamese Text Handling...")
    try:
        data_service = DataService(logger=logger)
        
        # Test UTF-8 encoding function
        test_text = "Nguyễn Văn Anh - Khóa học Tiếng Việt"
        encoded_text = data_service._ensure_utf8_text(test_text)
        
        if encoded_text == test_text:
            print("   ✅ Data service UTF-8 encoding: PASSED")
        else:
            print("   ❌ Data service UTF-8 encoding: FAILED")
            
        # Test student data validation
        if data_service.validate_student_data(vietnamese_test_data):
            print("   ✅ Vietnamese student data validation: PASSED")
        else:
            print("   ❌ Vietnamese student data validation: FAILED")
            
    except Exception as e:
        print(f"   ❌ Data service test failed: {e}")
    
    print()
    
    # Test 2: Certificate Service
    print("2️⃣ Testing Certificate Service Vietnamese Text Handling...")
    try:
        cert_service = CertificateService(logger=logger)
        
        # Test UTF-8 encoding function
        test_text = "Chứng chỉ hoàn thành khóa học"
        encoded_text = cert_service._ensure_utf8_encoding(test_text)
        
        if encoded_text == test_text:
            print("   ✅ Certificate service UTF-8 encoding: PASSED")
        else:
            print("   ❌ Certificate service UTF-8 encoding: FAILED")
            
        # Test filename sanitization with Vietnamese characters
        test_name = "Nguyễn Văn Anh - Khóa học Python"
        sanitized = cert_service._sanitize_filename(test_name)
        
        if sanitized and len(sanitized) > 0:
            print(f"   ✅ Vietnamese filename sanitization: PASSED ({sanitized})")
        else:
            print("   ❌ Vietnamese filename sanitization: FAILED")
            
        # Test student data validation
        if cert_service._validate_student_data(vietnamese_test_data):
            print("   ✅ Vietnamese certificate data validation: PASSED")
        else:
            print("   ❌ Vietnamese certificate data validation: FAILED")
            
    except Exception as e:
        print(f"   ❌ Certificate service test failed: {e}")
    
    print()
    
    # Test 3: Email Service  
    print("3️⃣ Testing Email Service Vietnamese Text Handling...")
    try:
        email_service = EmailService(logger=logger)
        
        # Test UTF-8 encoding function
        test_text = "Chúc mừng bạn đã hoàn thành khóa học"
        encoded_text = email_service._ensure_utf8_encoding(test_text)
        
        if encoded_text == test_text:
            print("   ✅ Email service UTF-8 encoding: PASSED")
        else:
            print("   ❌ Email service UTF-8 encoding: FAILED")
            
        # Test email subject creation
        subject = email_service._create_email_subject(vietnamese_test_data)
        expected_course = vietnamese_test_data['TEN_KHOA_HOC']
        
        if expected_course in subject:
            print(f"   ✅ Vietnamese email subject: PASSED")
            print(f"      Subject: {subject}")
        else:
            print("   ❌ Vietnamese email subject: FAILED")
            
        # Test email body creation
        body = email_service._create_email_body(vietnamese_test_data)
        expected_name = vietnamese_test_data['TEN_UNG_VIEN']
        
        if expected_name in body and "Kính chào" in body:
            print("   ✅ Vietnamese email body: PASSED")
        else:
            print("   ❌ Vietnamese email body: FAILED")
            
    except Exception as e:
        print(f"   ❌ Email service test failed: {e}")
    
    print()
    
    # Test 4: Overall Integration
    print("4️⃣ Testing Overall Vietnamese Text Integration...")
    
    # Test encoding consistency
    all_services_support_utf8 = True
    test_phrases = [
        "Nguyễn Văn Anh",
        "Khóa học lập trình",
        "Chứng chỉ hoàn thành",
        "Trân trọng cảm ơn",
        "Đào tạo nhân viên"
    ]
    
    for phrase in test_phrases:
        try:
            # Test if phrase survives encoding/decoding
            encoded = phrase.encode('utf-8').decode('utf-8')
            if encoded != phrase:
                all_services_support_utf8 = False
                break
        except Exception:
            all_services_support_utf8 = False
            break
    
    if all_services_support_utf8:
        print("   ✅ UTF-8 encoding consistency: PASSED")
    else:
        print("   ❌ UTF-8 encoding consistency: FAILED")
    
    print()
    print("🎯 Test Summary:")
    print("   - Data Service: Vietnamese text reading from Excel")
    print("   - Certificate Service: Vietnamese text in PDF certificates")  
    print("   - Email Service: Vietnamese text in email notifications")
    print("   - Integration: End-to-end Vietnamese text support")
    print()
    print("📝 Notes:")
    print("   - Ensure your Excel file is saved with UTF-8 encoding")
    print("   - Word template should support Vietnamese fonts")
    print("   - Outlook will display Vietnamese text correctly")
    print("   - Generated PDFs will preserve Vietnamese characters")
    print()
    print("✅ Vietnamese text support testing completed!")

if __name__ == "__main__":
    test_vietnamese_text_support() 