"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4046],{70449:(e,t,n)=>{n.d(t,{DC:()=>i,EJ:()=>r,IS:()=>c,bb:()=>s});var a=n(7283),o=n(15874);function s(){return{fetcher:e=>(0,a.fetchApi)(e),onError:function(e){(0,o.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let r={fetcher:e=>(0,a.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},i={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},c=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},75420:(e,t,n)=>{n.d(t,{d:()=>y});var a=n(12115),o=n(35984),s=n(67136),r=n(99667),i=n(48133);let c=e=>["Server timeout elapsed","The connection was stopped during negotiation","Failed to start the connection","Error: Error: The connection was stopped","connection disconnected","Unable to connect","disconnected with error","Stopping the connection","WebSocket closed","network error","transport timed out","Transport disconnected","Response status code does not indicate success: 401","Unauthorized","Cannot start a HubConnection"].some(t=>e.includes(t)),l=e=>{console.debug("[SignalR] Attempting to reconnect after delay..."),e.start().catch(e=>{console.debug("[SignalR] Reconnection attempt also failed:",e instanceof Error?e.message:"Unknown error")})},u=async e=>{try{await e.start(),console.debug("[SignalR] Connected successfully")}catch(t){t instanceof Error&&t.message&&c(t.message)?(console.debug("[SignalR] Expected connection issue (suppressed):",t.message),setTimeout(()=>l(e),3e3)):console.error("[SignalR] Connection Error:",t)}},d=async()=>{try{let e=await fetch("/api/connection-info");if(!e.ok)throw Error("Discovery failed: ".concat(e.status));return(await e.json()).apiUrl}catch(e){return console.error("[SignalR] Failed to discover API URL:",e),null}},g=async e=>{let t=await d();if(!t)return console.error("[SignalR] Cannot create connection: Failed to discover backend API URL"),null;let n="".concat(t.replace(/\/$/,""),"/").concat(e,"/hubs/botagent"),a=(0,i.c4)();if(!a)return console.warn("[SignalR] No auth token available for SignalR connection"),null;console.debug("[SignalR] Creating direct connection to backend hub:",n);let c={accessTokenFactory:()=>a,transport:o.w.WebSockets|o.w.LongPolling,headers:{Authorization:"Bearer ".concat(a)}};return new s.$().withUrl(n,c).configureLogging(r.$.Warning).withAutomaticReconnect({nextRetryDelayInMilliseconds:e=>Math.min(1e3*Math.pow(2,e.previousRetryCount),6e4)}).withServerTimeout(12e4).withKeepAliveInterval(3e4).build()},p=e=>{var t,n,a,o,s,r,i,c,l,u;return{botAgentId:null!==(n=null!==(t=e.botAgentId)&&void 0!==t?t:e.BotAgentId)&&void 0!==n?n:"",botAgentName:null!==(o=null!==(a=e.botAgentName)&&void 0!==a?a:e.BotAgentName)&&void 0!==o?o:"",status:null!==(r=null!==(s=e.status)&&void 0!==s?s:e.Status)&&void 0!==r?r:"",executionId:null!==(i=e.executionId)&&void 0!==i?i:e.ExecutionId,timestamp:null!==(l=null!==(c=e.timestamp)&&void 0!==c?c:e.Timestamp)&&void 0!==l?l:new Date().toISOString(),lastHeartbeat:null!==(u=e.lastHeartbeat)&&void 0!==u?u:e.LastHeartbeat}},h=e=>{e&&(e.message&&c(e.message)?console.debug("[SignalR] Connection issue - will automatically reconnect if possible"):console.error("[SignalR] Connection closed with error:",e))},v=(e,t,n)=>{e.onclose(h),e.on("BotStatusUpdate",e=>{let a=p(e);console.debug("[SignalR] BotStatusUpdate received:",a),t(e=>({...e,[a.botAgentId]:a})),n&&n(a)}),e.onreconnecting(e=>{console.debug("[SignalR] Attempting to reconnect...",null==e?void 0:e.message)}),e.onreconnected(e=>{console.debug("[SignalR] Reconnected successfully with ID:",e)})};function y(e,t){let[n,o]=(0,a.useState)({}),s=(0,a.useRef)(null);return(0,a.useEffect)(()=>{if(e)return(async()=>{let n=await g(e);if(!n){console.error("[SignalR] Failed to create connection");return}v(n,o,t),await u(n),s.current=n})(),()=>{s.current&&s.current.stop().catch(e=>{console.debug("[SignalR] Error during connection stop:",null==e?void 0:e.message)})}},[e,t]),n}},86490:(e,t,n)=>{n.d(t,{NA:()=>i,Qk:()=>u,Ri:()=>r,dT:()=>c,kz:()=>l,xR:()=>s});var a=n(7283);let o=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},s=async e=>{let t=o();return await a.F.post("".concat(t,"/api/agents/create"),e)},r=async e=>{let t=o();return await a.F.get("".concat(t,"/api/agents/").concat(e))},i=async()=>{let e=o();return await a.F.get("".concat(e,"/api/agents"))},c=async e=>{let t=o(),n=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[n,a]=e;null!=a&&"$count"!==n&&t.append(n,String(a))}),t.toString()}(e),s="".concat(t,"/odata/BotAgents");n&&(s+="?".concat(n)),console.log("OData query endpoint:",s);try{let e=await a.F.get(s);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," items from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let n=t[0];console.log('Found array property "'.concat(n,'" in response'));let a=e[n],o=e["@odata.count"];return{value:a,"@odata.count":"number"==typeof o?o:a.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching agents with OData:",e),{value:[]}}},l=async e=>{let t=o();await a.F.delete("".concat(t,"/api/agents/").concat(e))},u=async(e,t)=>{let n=o();return await a.F.put("".concat(n,"/api/agents/").concat(e),t)}},88262:(e,t,n)=>{n.d(t,{$:()=>o,d:()=>s});var a=n(12115);let o=(0,a.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function s(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}}}]);