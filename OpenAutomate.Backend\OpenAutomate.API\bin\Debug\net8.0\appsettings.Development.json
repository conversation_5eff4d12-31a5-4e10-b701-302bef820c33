{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "OpenAutomate.API.Middleware.RequestLoggingMiddleware": "Debug", "Microsoft.AspNetCore.Mvc": "Information", "Microsoft.AspNetCore.Hosting": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "OpenAutomate.API.Middleware.RequestLoggingMiddleware": "Debug", "Microsoft.AspNetCore.Mvc": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}", "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"}}], "Enrich": ["FromLogContext", "WithThreadId"], "Properties": {"Application": "OpenAutomate.API"}}, "FrontendUrl": "http://localhost:3001", "AllowedHosts": "*", "AppSettings": {"Database": {"DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=OpenAutomateDb;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "Redis": {"ConnectionString": "redis-16150.c292.ap-southeast-1-1.ec2.redns.redis-cloud.com:16150", "InstanceName": "OpenAutomate_Dev", "Database": 0, "AbortOnConnectFail": false, "Username": "default", "Password": "JpDlZHjmQyW8bTNvqLmeKuDOtzsOFM0e"}, "RedisCache": {"BatchSize": 100, "ScanCount": 1000, "BatchDelayMs": 0, "MaxKeysPerPattern": 10000, "PermissionCacheTtlMinutes": 15, "TenantCacheTtlMinutes": 30, "JwtBlocklistTtlHours": 24, "ApiResponseCacheTtlMinutes": 1, "HealthCheckCacheTtlMinutes": 5}, "Jwt": {"Secret": "YourSecretKeyHere_ThisShouldBeAtLeast32CharsLong", "Issuer": "OpenAutomate", "Audience": "OpenAutomateClients", "AccessTokenExpirationMinutes": 600000, "RefreshTokenExpirationDays": 7}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:5252", "https://localhost:5252"]}, "EmailSettings": {"SmtpServer": "email-smtp.ap-southeast-1.amazonaws.com", "Port": 2587, "EnableSsl": true, "SenderEmail": "<EMAIL>", "SenderName": "OpenAutomate", "Username": "********************", "Password": "BALCDiJjnUrvnZbqOFXusV1nLn1Q8Xt3rWL2CaLDN19t"}, "LemonSqueezy": {"ApiKey": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************.ZiDo7BfyC7jvIOuDE1FvyqiVcI8B_IxpmTNGwpkFv62GYBN4-F-aZmpXvTXHn4HX1G1db_q9B1L3H-1cSgwQ3zjxxM6ODsU15Y--d9O6tK3thLk3anGOEznkiUJax04_UopLg4-bSix9fAYVdRXvgQ9jRc5VayPHQ-smBszI_vfPkf70pp_lRF7INge3K_M45A1Gn8Bqjt136COkziu08TJvApJb6o_wGyp1HMJhMpX2eY4QozFoYmlPmQJvGxQLIkUoDXInSRdA4vhjQWy12juJcEmlgXRmfIKXVcMy5sukTPCy6E1E5fcjFcn2mzQSwQ1LA3LDJP3CXFxP9UEHgP_YQXKCHkrjQng2ZD62nV9QdDuMpmcNYmCiwaK8OJqugSf0_yimMnxdm59n8QwR8Bh6sfkVVAGo07n3kdSipP2aY1zqCqb7bgisVSVnX8msBb2bmrMh86cS5ShQFvRw-pFiWWLEsAjkn6rBgw_UWvEfYsmCt6f2Y91l7htdmBQ-", "StoreId": "198989", "WebhookSecret": "mingoNew@12345", "ProductId": "586150", "ProVariantId": "914845", "ApiBaseUrl": "https://api.lemonsqueezy.com/v1", "UseSandbox": true, "TrialDurationMinutes": 200, "EnableAutoTrialCreation": true}, "Subscription": {"EnforceSubscriptions": true, "DefaultPlanName": "Pro"}}, "AWS": {"Region": "ap-southeast-1", "BucketName": "openautomate-packages", "AccessKey": "********************", "SecretKey": "Aj1Pq7l0S/u5BDgbe6mnONT4MQIe04T1etJzQHgo", "PresignedUrlExpirationMinutes": 15}}