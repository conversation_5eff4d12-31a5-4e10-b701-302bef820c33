(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2678],{22100:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var a=n(12115),r=n(67057);function s(){let e=(0,a.useContext)(r.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},25237:(e,t,n)=>{Promise.resolve().then(n.bind(n,69517)),Promise.resolve().then(n.bind(n,75074))},69517:(e,t,n)=>{"use strict";n.d(t,{RegisterClient:()=>y});var a=n(95155),r=n(12115),s=n(6874),i=n.n(s),o=n(35695),l=n(62177),c=n(90221),d=n(55594),u=n(54629),m=n(30285),h=n(17759),p=n(62523),x=n(22100),f=n(55365),v=n(79285),j=n(12187);let w=d.Ik({firstName:d.Yj().min(2,"First name must be at least 2 characters"),lastName:d.Yj().min(2,"Last name must be at least 2 characters"),email:d.Yj().email("Please enter a valid email"),password:d.Yj().min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),confirmPassword:d.Yj().min(8,"Password must be at least 8 characters")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function g(){let e=(0,o.useRouter)(),{register:t,error:n}=(0,x.A)(),[s,i]=r.useState(!1),[d,g]=r.useState(null),y=(0,o.useSearchParams)().get("returnUrl"),N=null==y?void 0:y.includes("/invitation/accept"),b=()=>{if(!y)return{token:null,tenant:null};try{let e=new URL(y,window.location.origin),t=e.searchParams.get("token"),n=e.pathname.split("/")[1];return{token:t,tenant:n}}catch(e){return console.error("Error parsing return URL:",e),{token:null,tenant:null}}},P=async()=>{try{let{token:t,tenant:n}=b();if(t&&n)return await v.g.acceptInvitation(n,t),e.push("/".concat(n,"/tenant-selector")),!0}catch(e){console.error("Error accepting invitation:",e)}return!1},k=(0,l.mN)({resolver:(0,c.u)(w),defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:""}});async function E(n){i(!0),g(null);try{if(await t({email:n.email,password:n.password,confirmPassword:n.confirmPassword,firstName:n.firstName,lastName:n.lastName}),N&&await P())return;let a=new URLSearchParams({email:n.email});y&&a.append("returnUrl",y);let r="/verification-pending?".concat(a.toString());e.push(r)}catch(e){g((0,j.PE)(e))}finally{i(!1)}}return(0,a.jsx)("div",{className:"grid gap-6",children:(0,a.jsx)(h.lV,{...k,children:(0,a.jsxs)("form",{onSubmit:k.handleSubmit(E),className:"space-y-4",children:[(d||n)&&(0,a.jsx)(f.Fc,{variant:"destructive",className:"mb-4",children:(0,a.jsx)(f.TN,{children:null!=d?d:n})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(h.zB,{control:k.control,name:"firstName",render:e=>{let{field:t}=e;return(0,a.jsxs)(h.eI,{children:[(0,a.jsx)(h.lR,{children:"First Name"}),(0,a.jsx)(h.MJ,{children:(0,a.jsx)(p.p,{placeholder:"John",...t,disabled:s})}),(0,a.jsx)(h.C5,{})]})}}),(0,a.jsx)(h.zB,{control:k.control,name:"lastName",render:e=>{let{field:t}=e;return(0,a.jsxs)(h.eI,{children:[(0,a.jsx)(h.lR,{children:"Last Name"}),(0,a.jsx)(h.MJ,{children:(0,a.jsx)(p.p,{placeholder:"Doe",...t,disabled:s})}),(0,a.jsx)(h.C5,{})]})}})]}),(0,a.jsx)(h.zB,{control:k.control,name:"email",render:e=>{let{field:t}=e;return(0,a.jsxs)(h.eI,{children:[(0,a.jsx)(h.lR,{children:"Email"}),(0,a.jsx)(h.MJ,{children:(0,a.jsx)(p.p,{type:"email",placeholder:"<EMAIL>",...t,disabled:s})}),(0,a.jsx)(h.C5,{})]})}}),(0,a.jsx)(h.zB,{control:k.control,name:"password",render:e=>{let{field:t}=e;return(0,a.jsxs)(h.eI,{children:[(0,a.jsx)(h.lR,{children:"Password"}),(0,a.jsx)(h.MJ,{children:(0,a.jsx)(p.p,{type:"password",placeholder:"••••••••",...t,disabled:s})}),(0,a.jsx)(h.Rr,{className:"text-xs",children:"Use 8+ characters with a mix of uppercase, lowercase, numbers & symbols."}),(0,a.jsx)(h.C5,{})]})}}),(0,a.jsx)(h.zB,{control:k.control,name:"confirmPassword",render:e=>{let{field:t}=e;return(0,a.jsxs)(h.eI,{children:[(0,a.jsx)(h.lR,{children:"Confirm Password"}),(0,a.jsx)(h.MJ,{children:(0,a.jsx)(p.p,{type:"password",placeholder:"••••••••",...t,disabled:s})}),(0,a.jsx)(h.C5,{})]})}}),(0,a.jsxs)(m.$,{type:"submit",className:"w-full transition-all duration-300 hover:translate-y-[-2px]",disabled:s,children:[s&&(0,a.jsx)(u.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Account"]})]})})})}function y(){let[e,t]=r.useState("");r.useEffect(()=>{var e;t(null!==(e=new URLSearchParams(window.location.search).get("returnUrl"))&&void 0!==e?e:"")},[]);let n=e?"/login?returnUrl=".concat(encodeURIComponent(e)):"/login";return(0,a.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:"Create an account"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enter your details to create your account"})]}),(0,a.jsx)(g,{}),(0,a.jsxs)("p",{className:"px-8 text-center text-sm text-muted-foreground",children:["Already have an account?"," ",(0,a.jsx)(i(),{href:n,className:"underline underline-offset-4 hover:text-primary font-medium transition-all duration-300 hover:underline-offset-8",children:"Sign in"})]})]})}},79285:(e,t,n)=>{"use strict";n.d(t,{g:()=>r});var a=n(7283);let r={inviteUser:async(e,t)=>a.F.post("".concat(e,"/api/organization-unit-invitation"),{email:t}),acceptInvitation:async(e,t)=>{try{let n=await a.F.post("".concat(e,"/api/organization-unit-invitation/accept"),{token:t});if(!n)throw Error("Empty response received");if(void 0===n.success)return{success:!0};return n}catch(e){return function(e){var t,n;if((null==e?void 0:e.status)===409)return{success:!0};if(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(t=n.data)||void 0===t?void 0:t.message)throw Error(e.response.data.message);if(null==e?void 0:e.message)throw Error(e.message);throw Error("Failed to accept invitation.")}(e)}},checkInvitation:async(e,t)=>a.F.get("".concat(e,"/api/organization-unit-invitation/check?email=").concat(encodeURIComponent(t))),checkInvitationToken:async(e,t)=>{try{return await a.F.get("".concat(e,"/api/organization-unit-invitation/check-token?token=").concat(encodeURIComponent(t)))}catch(e){if(console.error("Error checking invitation token:",e),(null==e?void 0:e.status)===404)return{status:"Invalid",recipientEmail:"",expiresAt:"",organizationUnitId:""};throw e}},listInvitations:async(e,t)=>{let n="/".concat(e,"/odata/OrganizationUnitInvitations");if(t){let e=new URLSearchParams;Object.entries(t).forEach(t=>{let[n,a]=t;void 0!==a&&e.append(n,a)}),n+="?".concat(e.toString())}return function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:"object"==typeof e&&null!==e&&"invitations"in e?{value:e.invitations,"@odata.count":e.count}:{value:[]}}(await a.F.get(n))}}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6341,2178,8852,5699,5594,6874,8594,1941,3112,7057,8916,5642,8441,1684,7358],()=>t(25237)),_N_E=e.O()}]);