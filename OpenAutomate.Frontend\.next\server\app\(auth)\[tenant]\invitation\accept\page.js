(()=>{var e={};e.id=6164,e.ids=[6164],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15499:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.default,__next_app__:()=>d,pages:()=>l,routeModule:()=>u,tree:()=>c});var r=n(65239),i=n(48088),a=n(31369),o=n(30893),s={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>o[e]);n.d(t,s);let c={children:["",{children:["(auth)",{children:["[tenant]",{children:["invitation",{children:["accept",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,44765)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\[tenant]\\invitation\\accept\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,66549)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\[tenant]\\invitation\\accept\\layout.tsx"]}]},{}]},{}]},{forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(n.bind(n,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(n.bind(n,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\[tenant]\\invitation\\accept\\page.tsx"],d={require:n,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(auth)/[tenant]/invitation/accept/page",pathname:"/[tenant]/invitation/accept",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19043:(e,t,n)=>{Promise.resolve().then(n.bind(n,87760))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19723:(e,t,n)=>{Promise.resolve().then(n.bind(n,44765))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31567:(e,t,n)=>{"use strict";n.d(t,{g:()=>i});var r=n(51787);let i={inviteUser:async(e,t)=>r.F.post(`${e}/api/organization-unit-invitation`,{email:t}),acceptInvitation:async(e,t)=>{try{let n=await r.F.post(`${e}/api/organization-unit-invitation/accept`,{token:t});if(!n)throw Error("Empty response received");if(void 0===n.success)return{success:!0};return n}catch(e){return function(e){if(e?.status===409)return{success:!0};if(e?.response?.data?.message)throw Error(e.response.data.message);if(e?.message)throw Error(e.message);throw Error("Failed to accept invitation.")}(e)}},checkInvitation:async(e,t)=>r.F.get(`${e}/api/organization-unit-invitation/check?email=${encodeURIComponent(t)}`),checkInvitationToken:async(e,t)=>{try{return await r.F.get(`${e}/api/organization-unit-invitation/check-token?token=${encodeURIComponent(t)}`)}catch(e){if(console.error("Error checking invitation token:",e),e?.status===404)return{status:"Invalid",recipientEmail:"",expiresAt:"",organizationUnitId:""};throw e}},listInvitations:async(e,t)=>{let n=`/${e}/odata/OrganizationUnitInvitations`;if(t){let e=new URLSearchParams;Object.entries(t).forEach(([t,n])=>{void 0!==n&&e.append(t,n)}),n+=`?${e.toString()}`}return function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:"object"==typeof e&&null!==e&&"invitations"in e?{value:e.invitations,"@odata.count":e.count}:{value:[]}}(await r.F.get(n))}}},31568:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(43210),i=n(86522);function a(){let e=(0,r.useContext)(i.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},33873:e=>{"use strict";e.exports=require("path")},44765:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\(auth)\\\\[tenant]\\\\invitation\\\\accept\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\[tenant]\\invitation\\accept\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66549:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var r=n(37413);function i({children:e}){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:e})}},78335:()=>{},87760:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d});var r=n(60687),i=n(85814),a=n.n(i),o=n(16189),s=n(43210),c=n(31567),l=n(31568);function d(){let e=(0,o.useRouter)(),t=(0,o.useSearchParams)(),n=(0,o.useParams)(),i=n?.tenant||"",d=t.get("token"),{isAuthenticated:u,user:p,logout:g}=(0,l.A)(),[x,h]=(0,s.useState)("loading"),[m,v]=(0,s.useState)(""),[f,b]=(0,s.useState)(0),[j,k]=(0,s.useState)(null),w=async()=>{if(function(e){let{token:t,tenant:n,isAuthenticated:r,invitedEmail:i,user:a,setStatus:o,setErrorMsg:s,router:c}=e;return t&&n?r?!i||a?.email?.toLowerCase()===i.toLowerCase()||(o("wrong_email"),s(`You are currently logged in as ${a?.email}, but this invitation was sent to ${i}.`),!1):(c.replace(`/login?returnUrl=/${n}/invitation/accept?token=${t}`),!1):(o("error"),s("Invalid invitation link."),!1)}({token:d,tenant:i,isAuthenticated:u,invitedEmail:j,user:p,setStatus:h,setErrorMsg:v,router:e})){h("accepting");try{var t;if(t=await c.g.acceptInvitation(i,d),t?.success===!0&&(t.invitedEmail&&p?.email?.toLowerCase()!==t.invitedEmail.toLowerCase()?(h("wrong_email"),k(t.invitedEmail),v(`You are currently logged in as ${p?.email}, but this invitation was sent to ${t.invitedEmail}.`)):h("success"),1))return;b(e=>e+1),h("error"),v("Failed to accept invitation. Please try again.")}catch(e){!function(e,t,n,r){if(e instanceof Response){e.json().then(e=>{e?.message&&n(e.message)}).catch(()=>{}),r(e=>e+1),t("error");return}let i=function(e){if("object"==typeof e&&null!==e){if(e.message?.includes("not invited")||e.message?.includes("You are not invited to this OU"))return{status:"wrong_email",msg:"You are not invited to this organization with your current email address."};if(e.response?.data?.message)return{status:"error",msg:e.response.data.message};if(e.message||e.status&&e.message)return{status:"error",msg:e.message}}return null}(e);if(i){if(t(i.status),n(i.msg),"wrong_email"===i.status)return}else t("error"),n("Failed to accept invitation.");r(e=>e+1)}(e,h,v,b)}}},y=async()=>{await g(),e.replace(`/login?returnUrl=/${i}/invitation/accept?token=${d}`)},z=()=>{e.push("/tenant-selector")};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"w-full max-w-md bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 shadow-xl rounded-2xl p-8 flex flex-col items-center space-y-6 transition-colors",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-orange-600 text-center",children:"Organization Invitation"}),"loading"===x&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-2 text-zinc-500 dark:text-zinc-300",children:[(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5 text-orange-600 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8z"})]}),"Verifying invitation..."]}),"idle"===x&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("p",{className:"text-sm text-zinc-600 dark:text-zinc-300 text-center",children:["You have been invited to join this Organization Unit (OU).",!u&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("br",{}),"Please sign in or register with the invited email address first."]}),u&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("br",{}),"Click ",(0,r.jsx)("b",{children:"Accept"})," to join."]})]}),u&&(0,r.jsx)("button",{className:"w-full px-8 py-2 bg-orange-600 text-white rounded font-semibold hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:w,children:"Accept"})]}),"accepting"===x&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-2 text-zinc-500 dark:text-zinc-300",children:[(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5 text-orange-600 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8z"})]}),"Accepting invitation..."]}),"success"===x&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-green-600 dark:text-green-400 text-center font-medium",children:"Successfully joined the organization!"}),(0,r.jsx)("button",{className:"w-full px-8 py-2 bg-orange-600 text-white rounded font-semibold hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:z,children:"Go to My Organizations"})]}),"already_accepted"===x&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-green-600 dark:text-green-400 text-center font-medium",children:"You have already joined this organization."}),(0,r.jsx)("button",{className:"w-full px-8 py-2 bg-orange-600 text-white rounded font-semibold hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:z,children:"Go to My Organizations"})]}),"wrong_email"===x&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"text-red-600 dark:text-red-400 text-center",children:m}),(0,r.jsx)("div",{className:"text-sm text-zinc-600 dark:text-zinc-300 text-center",children:"Please sign in with the email address that was invited or ask for a new invitation to your current email."}),(0,r.jsxs)("div",{className:"flex flex-col w-full space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-8 py-2 bg-orange-600 text-white rounded font-semibold hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:y,children:"Sign in with different account"}),(0,r.jsx)("div",{className:"flex justify-center items-center space-x-2",children:(0,r.jsx)("span",{className:"text-xs text-zinc-500",children:"or"})}),(0,r.jsx)(a(),{href:`/register?returnUrl=/${i}/invitation/accept?token=${d}`,className:"w-full px-8 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-800 dark:text-zinc-200 rounded font-semibold hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors text-center",children:"Register new account"})]})]}),"error"===x&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"text-red-600 dark:text-red-400 text-center",children:m}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{className:"px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-800 dark:text-zinc-200 rounded font-medium hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors",onClick:()=>{h("loading"),b(e=>e+1)},children:"Retry"}),(0,r.jsx)("button",{className:"px-4 py-2 bg-orange-600 text-white rounded font-medium hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:z,children:"Go to Organizations"})]})]}),!u&&"loading"!==x&&(0,r.jsxs)("p",{className:"text-center text-xs text-zinc-400 dark:text-zinc-500",children:["Already have an account?"," ",(0,r.jsx)(a(),{href:`/login?returnUrl=/${i}/invitation/accept?token=${d}`,className:"text-orange-600 underline underline-offset-4 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-all duration-300 hover:underline-offset-8",children:"Sign in"})," ","or"," ",(0,r.jsx)(a(),{href:`/register?returnUrl=/${i}/invitation/accept?token=${d}`,className:"text-orange-600 underline underline-offset-4 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-all duration-300 hover:underline-offset-8",children:"Register"})]})]})})}},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[4447,7966,6763],()=>n(15499));module.exports=r})();