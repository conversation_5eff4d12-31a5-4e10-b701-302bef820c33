(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4930],{22346:(e,s,t)=>{"use strict";t.d(s,{w:()=>n});var a=t(95155);t(12115);var r=t(86986),i=t(36928);function n(e){let{className:s,orientation:t="horizontal",decorative:n=!0,...c}=e;return(0,a.jsx)(r.b,{"data-slot":"separator-root",decorative:n,orientation:t,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",s),...c})}},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(95155);t(12115);var r=t(66634),i=t(74466),n=t(36928);let c=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,asChild:i=!1,...l}=e,d=i?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(c({variant:t}),s),...l})}},30285:(e,s,t)=>{"use strict";t.d(s,{$:()=>d,r:()=>l});var a=t(95155),r=t(12115),i=t(66634),n=t(74466),c=t(36928);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:d=!1,...o}=e,u=d?i.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,c.cn)(l({variant:r,size:n,className:t})),ref:s,...o})});d.displayName="Button"},40565:(e,s,t)=>{Promise.resolve().then(t.bind(t,68973))},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>l,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>n,wL:()=>o});var a=t(95155);t(12115);var r=t(36928);function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...t})}},68973:(e,s,t)=>{"use strict";t.d(s,{default:()=>A});var a=t(95155),r=t(26126),i=t(30285),n=t(66695),c=t(22346),l=t(55365),d=t(88262),o=t(12115),u=t(34953),m=t(70449),x=t(35695),v=t(51154),h=t(35169),f=t(62525),g=t(37108),p=t(69074),j=t(57434),b=t(29869),N=t(95880),w=t(91788),y=t(32771),k=t(12187);function A(){let e=(0,x.useParams)(),s=(0,x.useRouter)(),{toast:t}=(0,d.d)(),A=e.id,{data:z,error:E,isLoading:B,mutate:D}=(0,u.Ay)(A?m.DC.packageById(A):null,()=>(0,y.ae)(A)),[_,C]=(0,o.useState)(null);(0,o.useEffect)(()=>{E&&(console.error("Failed to load package details:",E),t({title:"Error",description:(0,m.IS)(E),variant:"destructive"}))},[E,t]);let P=async e=>{try{C(e.versionNumber);let s=await (0,y.QQ)(A,e.versionNumber);window.open(s.downloadUrl,"_blank"),t({title:"Download Started",description:"Downloading ".concat(e.fileName),variant:"default"})}catch(e){console.error("Error downloading package:",e),t((0,k.m4)(e))}finally{C(null)}},$=async e=>{if(confirm("Are you sure you want to delete version ".concat(e.versionNumber,"?")))try{await (0,y.AW)(A,e.versionNumber),D(),t({title:"Version Deleted",description:"Version ".concat(e.versionNumber," has been deleted successfully"),variant:"default"})}catch(e){console.error("Error deleting version:",e),t((0,k.m4)(e))}},F=async()=>{if(confirm("Are you sure you want to delete this package and all its versions?"))try{await (0,y.jm)(A),t({title:"Package Deleted",description:'Package "'.concat(null==z?void 0:z.name,'" has been deleted successfully'),variant:"default"}),s.back()}catch(e){console.error("Error deleting package:",e),t((0,k.m4)(e))}},R=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},S=e=>new Date(e).toLocaleString();if(B)return(0,a.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.A,{className:"h-6 w-6 animate-spin"}),(0,a.jsx)("span",{children:"Loading package details..."})]})});if(E&&!z){var V;return(0,a.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,a.jsx)(l.Fc,{className:"max-w-md",children:(0,a.jsxs)(l.TN,{children:[null!==(V=(0,m.IS)(E))&&void 0!==V?V:"Package not found",(0,a.jsx)(i.$,{variant:"outline",size:"sm",className:"ml-2",onClick:()=>D(),children:"Retry"})]})})})}if(!z)return(0,a.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,a.jsx)(l.Fc,{className:"max-w-md",children:(0,a.jsxs)(l.TN,{children:["Package not found",(0,a.jsx)(i.$,{variant:"outline",size:"sm",className:"ml-2",onClick:()=>D(),children:"Retry"})]})})});let I=[...z.versions].sort((e,s)=>new Date(s.uploadedAt).getTime()-new Date(e.uploadedAt).getTime());return(0,a.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>s.back(),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:z.name}),(0,a.jsx)("p",{className:"text-muted-foreground",children:z.description})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(r.E,{variant:z.isActive?"default":"secondary",children:z.isActive?"Active":"Inactive"}),(0,a.jsxs)(i.$,{variant:"destructive",onClick:F,children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Delete Package"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Package Information"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Name"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:z.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Description"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:z.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Created"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),S(z.createdAt)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Total Versions"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:z.versions.length})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Status"}),(0,a.jsx)(r.E,{variant:z.isActive?"default":"secondary",children:z.isActive?"Active":"Inactive"})]})]})]})}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 mr-2"}),"Package Versions"]}),(0,a.jsxs)(i.$,{size:"sm",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Upload New Version"]})]})}),(0,a.jsx)(n.Wu,{children:0===I.length?(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No versions uploaded yet"}):(0,a.jsx)("div",{className:"space-y-4",children:I.map((e,s)=>(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(r.E,{variant:0===s?"default":"secondary",children:["v",e.versionNumber]}),0===s&&(0,a.jsx)(r.E,{variant:"outline",children:"Latest"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.fileName})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center text-muted-foreground",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-1"}),R(e.fileSize)]}),(0,a.jsxs)("div",{className:"flex items-center text-muted-foreground",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),S(e.uploadedAt)]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>P(e),disabled:_===e.versionNumber,children:_===e.versionNumber?(0,a.jsx)(v.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(w.A,{className:"h-4 w-4"})}),(0,a.jsx)(i.$,{size:"sm",variant:"destructive",onClick:()=>$(e),children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]})]})]}),s<I.length-1&&(0,a.jsx)(c.w,{className:"my-2"})]},e.id))})})]})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,4953,3920,4727,2338,8441,1684,7358],()=>s(40565)),_N_E=e.O()}]);