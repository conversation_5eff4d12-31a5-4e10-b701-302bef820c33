"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2178],{35695:(e,t,n)=>{var r=n(18999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},59096:(e,t,n)=>{n.d(t,{bm:()=>ec,UC:()=>ea,VY:()=>es,hJ:()=>el,ZL:()=>ei,bL:()=>er,hE:()=>eu,l9:()=>eo});var r=n(12115);function o(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function a(...e){return r.useCallback(l(...e),e)}var u=n(95155),s=n(52496),c=n(12640),d=n(7166),f=n(12307),p=n(962),m=globalThis?.document?r.useLayoutEffect:()=>{},v=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef({}),a=r.useRef(e),u=r.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=g(l.current);u.current="mounted"===s?e:"none"},[s]),m(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=g(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),m(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=g(l.current).includes(e.animationName);if(e.target===o&&r&&(c("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(u.current=g(l.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),l=a(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||o.isPresent?r.cloneElement(i,{ref:l}):null};function g(e){return(null==e?void 0:e.animationName)||"none"}function y(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,a;let u=(i=n,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?l(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),a=l.find(N);if(a){let e=a.props.children,o=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,u.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}v.displayName="Presence",n(47650);var h=Symbol("radix.slottable");function N(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===h}var w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=y(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),R=n(92293),b=n(93795),x=n(38168),O="Dialog",[D,C]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;n=[...n,o];let a=t=>{let{scope:n,children:o,...a}=t,s=n?.[e]?.[l]||i,c=r.useMemo(()=>a,Object.values(a));return(0,u.jsx)(s.Provider,{value:c,children:o})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=r.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(O),[j,E]=D(O),P=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:l,modal:a=!0}=e,d=r.useRef(null),f=r.useRef(null),[p,m]=(0,c.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:O});return(0,u.jsx)(j,{scope:t,triggerRef:d,contentRef:f,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:m,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),modal:a,children:n})};P.displayName=O;var _="DialogTrigger",I=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(_,n),l=a(t,i.triggerRef);return(0,u.jsx)(w.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":G(i.open),...r,ref:l,onClick:o(e.onClick,i.onOpenToggle)})});I.displayName=_;var M="DialogPortal",[T,A]=D(M,{forceMount:void 0}),F=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,l=E(M,t);return(0,u.jsx)(T,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,u.jsx)(v,{present:n||l.open,children:(0,u.jsx)(p.Z,{asChild:!0,container:i,children:e})}))})};F.displayName=M;var S="DialogOverlay",U=r.forwardRef((e,t)=>{let n=A(S,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=E(S,e.__scopeDialog);return i.modal?(0,u.jsx)(v,{present:r||i.open,children:(0,u.jsx)(k,{...o,ref:t})}):null});U.displayName=S;var W=y("DialogOverlay.RemoveScroll"),k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(S,n);return(0,u.jsx)(b.A,{as:W,allowPinchZoom:!0,shards:[o.contentRef],children:(0,u.jsx)(w.div,{"data-state":G(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),$="DialogContent",L=r.forwardRef((e,t)=>{let n=A($,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=E($,e.__scopeDialog);return(0,u.jsx)(v,{present:r||i.open,children:i.modal?(0,u.jsx)(V,{...o,ref:t}):(0,u.jsx)(B,{...o,ref:t})})});L.displayName=$;var V=r.forwardRef((e,t)=>{let n=E($,e.__scopeDialog),i=r.useRef(null),l=a(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,x.Eq)(e)},[]),(0,u.jsx)(Z,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:o(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:o(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:o(e.onFocusOutside,e=>e.preventDefault())})}),B=r.forwardRef((e,t)=>{let n=E($,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,u.jsx)(Z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,l;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,c=E($,n),p=r.useRef(null),m=a(t,p);return(0,R.Oh)(),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(f.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,u.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":G(c.open),...s,ref:m,onDismiss:()=>c.onOpenChange(!1)})}),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(et,{titleId:c.titleId}),(0,u.jsx)(en,{contentRef:p,descriptionId:c.descriptionId})]})]})}),q="DialogTitle",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(q,n);return(0,u.jsx)(w.h2,{id:o.titleId,...r,ref:t})});H.displayName=q;var J="DialogDescription",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(J,n);return(0,u.jsx)(w.p,{id:o.descriptionId,...r,ref:t})});K.displayName=J;var Y="DialogClose",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(Y,n);return(0,u.jsx)(w.button,{type:"button",...r,ref:t,onClick:o(e.onClick,()=>i.onOpenChange(!1))})});function G(e){return e?"open":"closed"}z.displayName=Y;var Q="DialogTitleWarning",[X,ee]=function(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,i=r.useMemo(()=>o,Object.values(o));return(0,u.jsx)(n.Provider,{value:i,children:t})};return o.displayName=e+"Provider",[o,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}(Q,{contentName:$,titleName:q,docsSlug:"dialog"}),et=e=>{let{titleId:t}=e,n=ee(Q),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},en=e=>{let{contentRef:t,descriptionId:n}=e,o=ee("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},er=P,eo=I,ei=F,el=U,ea=L,eu=H,es=K,ec=z}}]);