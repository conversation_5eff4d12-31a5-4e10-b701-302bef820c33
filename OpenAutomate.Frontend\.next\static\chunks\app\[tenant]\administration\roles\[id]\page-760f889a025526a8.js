(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7830],{12187:(e,t,s)=>{"use strict";function r(e){if(!e)return"An unexpected error occurred";if("string"==typeof e)return e;if("object"==typeof e&&null!==e&&"status"in e&&"message"in e&&"number"==typeof e.status&&"string"==typeof e.message)return 0===e.status?e.message||"Network error. Please check your connection.":function(e){var t;if(e.details){let t=function(e){try{var t,s;let r=JSON.parse(e);return null!==(s=null!==(t=r.error)&&void 0!==t?t:r.message)&&void 0!==s?s:null}catch(e){return null}}(e.details);return t||e.details}return null!==(t=e.message)&&void 0!==t?t:"An error occurred"}(e);if(e instanceof Error)return e.message.includes("Failed to fetch")||e.message.includes("NetworkError")?"Network error. Please check your connection.":e.message;let t=function(e){if(null!==e&&"object"==typeof e&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response){let t=e.response.data;if("object"==typeof t&&null!==t){if("message"in t&&"string"==typeof t.message)return t.message;if("error"in t&&"string"==typeof t.error)return t.error}if("string"==typeof t)return t}return null}(e);if(null!==t)return t;if("object"==typeof e&&null!==e){let t=void 0!==e.error&&null!==e.error&&"string"==typeof e.error?e.error:void 0!==e.message&&null!==e.message&&"string"==typeof e.message?e.message:null;if(null!==t)return t}return"An unexpected error occurred"}function a(e){return{title:"Error",description:r(e),variant:function(e){if("object"==typeof e&&null!==e&&"status"in e)return e.status<400?"default":"destructive";if("string"==typeof e){let t=e.toLowerCase();if(t.includes("warning")||t.includes("info"))return"default"}return"destructive"}(e)}}s.d(t,{PE:()=>r,m4:()=>a})},13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var r=s(95155);s(12115);var a=s(66634),i=s(74466),n=s(36928);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:s,asChild:i=!1,...d}=e,l=i?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:s}),t),...d})}},30005:(e,t,s)=>{Promise.resolve().then(s.bind(s,55736))},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,r:()=>d});var r=s(95155),a=s(12115),i=s(66634),n=s(74466),o=s(36928);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:l=!1,...c}=e,u=l?i.DX:"button";return(0,r.jsx)(u,{"data-slot":"button",className:(0,o.cn)(d({variant:a,size:n,className:s})),ref:t,...c})});l.displayName="Button"},35169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},55736:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(95155),a=s(35695),i=s(30285),n=s(66695),o=s(26126),d=s(35169),l=s(13717),c=s(62525),u=s(12115),m=s(34953),p=s(70449),x=s(37005),f=s(88262),v=s(12187),g=s(67426),h=s(61943);function b(e){let{id:t}=e,s=(0,a.useRouter)(),{toast:b}=(0,f.d)(),{data:j,error:N,isLoading:w,mutate:k}=(0,m.Ay)(p.DC.roleById(t),()=>x.eV.getRoleById(t)),[A,z]=(0,u.useState)(!1);(0,u.useEffect)(()=>{N&&(console.error("Failed to load role:",N),b((0,v.m4)(N)))},[N,b]);let _=()=>{s.back()},P=()=>{z(!0)},C=async()=>{if(j)try{await x.eV.deleteRole(j.id),b({title:"Success",description:"Role deleted successfully."}),s.back()}catch(e){console.error("Failed to delete role:",e),b((0,v.m4)(e))}},R=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];z(!1),e&&await k()};return w?(0,r.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"})})}):j?(0,r.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,r.jsxs)(n.Zp,{className:"border rounded-md shadow-sm",children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between border-b p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",className:"gap-1",onClick:_,children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"Back"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.ZB,{className:"text-xl",children:j.name}),j.isSystemAuthority&&(0,r.jsx)(o.E,{variant:"secondary",className:"text-xs",children:"System Role"})]})]}),!j.isSystemAuthority&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:P,children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:C,className:"text-destructive hover:text-destructive",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]}),(0,r.jsxs)(n.Wu,{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(y,{label:"Role Name",children:j.name}),(0,r.jsx)(y,{label:"Description",children:j.description||"No description provided"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(y,{label:"Type",children:(0,r.jsx)(o.E,{variant:j.isSystemAuthority?"secondary":"outline",children:j.isSystemAuthority?"System Role":"Custom Role"})}),(0,r.jsx)(y,{label:"Created",children:(0,g.GP)(new Date(j.createdAt),"PPP")}),j.updatedAt&&(0,r.jsx)(y,{label:"Last Updated",children:(0,g.GP)(new Date(j.updatedAt),"PPP")})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Permissions"}),j.permissions&&j.permissions.length>0?(0,r.jsx)("div",{className:"grid gap-3",children:j.permissions.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-muted/30",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"font-medium",children:e.resourceName}),(0,r.jsx)(o.E,{variant:"outline",children:(0,x.gO)(e.permission)})]}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Level ",e.permission]})]},e.resourceName))}):(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground border rounded-lg border-dashed",children:[(0,r.jsx)("p",{children:"No permissions assigned to this role."}),!j.isSystemAuthority&&(0,r.jsx)(i.$,{variant:"outline",onClick:P,className:"mt-2",children:"Add Permissions"})]})]})})]})]}),(0,r.jsx)(h.I,{isOpen:A,onClose:R,editingRole:{id:j.id,name:j.name,description:j.description,isSystemAuthority:j.isSystemAuthority,createdAt:j.createdAt,updatedAt:j.updatedAt,permissions:j.permissions}},j.id)]}):(0,r.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,r.jsx)(n.Zp,{className:"border rounded-md shadow-sm",children:(0,r.jsxs)(n.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Role not found."}),(0,r.jsxs)(i.$,{variant:"outline",onClick:_,className:"mt-4",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Back to Roles"]})]})})})}function y(e){let{label:t,children:s}=e;return(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:t}),(0,r.jsx)("div",{className:"text-base font-medium pb-1 border-b",children:s})]})}function j(){let e=(0,a.useParams)().id;return(0,r.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,r.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,r.jsx)(b,{id:e})]})}},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>l,yv:()=>c});var r=s(95155);s(12115);var a=s(46002),i=s(66474),n=s(5196),o=s(47863),d=s(36928);function l(e){let{...t}=e;return(0,r.jsx)(a.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(a.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:n,...o}=e;return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":s,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[n,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:s,position:i="popper",...n}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,r.jsx)(x,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,r.jsx)(f,{})]})})}function p(e){let{className:t,children:s,...i}=e;return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:s})]})}function x(e){let{className:t,...s}=e;return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(o.A,{className:"size-4"})})}function f(e){let{className:t,...s}=e;return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(i.A,{className:"size-4"})})}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(95155);s(12115);var a=s(36928);function i(e){let{className:t,type:s,...i}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>c});var r=s(95155);s(12115);var a=s(36928);function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,2178,8852,8523,106,7426,4727,1943,8441,1684,7358],()=>t(30005)),_N_E=e.O()}]);