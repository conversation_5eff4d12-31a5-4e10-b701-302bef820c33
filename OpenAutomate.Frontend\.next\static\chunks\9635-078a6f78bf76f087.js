"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9635],{9635:(e,t,n)=>{n.d(t,{i3:()=>Q,UC:()=>J,ZL:()=>G,Kq:()=>Y,bL:()=>z,l9:()=>K});var r=n(12115);function o(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function u(...e){return r.useCallback(l(...e),e)}var a=n(95155),s=n(7166),c=n(52496),d=n(13227),p=n(962),f=globalThis?.document?r.useLayoutEffect:()=>{},m=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef({}),u=r.useRef(e),a=r.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=v(l.current);a.current="mounted"===s?e:"none"},[s]),f(()=>{let t=l.current,n=u.current;if(n!==e){let r=a.current,o=v(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),f(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=v(l.current).includes(e.animationName);if(e.target===o&&r&&(c("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(a.current=v(l.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),l=u(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||o.isPresent?r.cloneElement(i,{ref:l}):null};function v(e){return(null==e?void 0:e.animationName)||"none"}m.displayName="Presence",n(47650);var y=Symbol("radix.slottable");function g(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}var h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,u;let a=(i=n,(u=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(u=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?l(t,a):a),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),u=l.find(g);if(u){let e=u.props.children,o=l.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),x=n(12640),b=n(99853),[w,C]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;n=[...n,o];let u=t=>{let{scope:n,children:o,...u}=t,s=n?.[e]?.[l]||i,c=r.useMemo(()=>u,Object.values(u));return(0,a.jsx)(s.Provider,{value:c,children:o})};return u.displayName=t+"Provider",[u,function(n,u){let a=u?.[e]?.[l]||i,s=r.useContext(a);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}("Tooltip",[d.Bk]),T=(0,d.Bk)(),E="TooltipProvider",N="tooltip.open",[R,_]=w(E),O=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,u=r.useRef(!0),s=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,a.jsx)(R,{scope:t,isOpenDelayedRef:u,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),u.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};O.displayName=E;var j="Tooltip",[M,L]=w(j),k=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:u,delayDuration:s}=e,p=_(j,e.__scopeTooltip),f=T(t),[m,v]=r.useState(null),y=(0,c.B)(),g=r.useRef(0),h=null!=u?u:p.disableHoverableContent,b=null!=s?s:p.delayDuration,w=r.useRef(!1),[C,E]=(0,x.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(p.onOpen(),document.dispatchEvent(new CustomEvent(N))):p.onClose(),null==l||l(e)},caller:j}),R=r.useMemo(()=>C?w.current?"delayed-open":"instant-open":"closed",[C]),O=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,w.current=!1,E(!0)},[E]),L=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,E(!1)},[E]),k=r.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{w.current=!0,E(!0),g.current=0},b)},[b,E]);return r.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,a.jsx)(d.bL,{...f,children:(0,a.jsx)(M,{scope:t,contentId:y,open:C,stateAttribute:R,trigger:m,onTriggerChange:v,onTriggerEnter:r.useCallback(()=>{p.isOpenDelayedRef.current?k():O()},[p.isOpenDelayedRef,k,O]),onTriggerLeave:r.useCallback(()=>{h?L():(window.clearTimeout(g.current),g.current=0)},[L,h]),onOpen:O,onClose:L,disableHoverableContent:h,children:n})})};k.displayName=j;var P="TooltipTrigger",I=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...i}=e,l=L(P,n),s=_(P,n),c=T(n),p=u(t,r.useRef(null),l.onTriggerChange),f=r.useRef(!1),m=r.useRef(!1),v=r.useCallback(()=>f.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,a.jsx)(d.Mz,{asChild:!0,...c,children:(0,a.jsx)(h.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...i,ref:p,onPointerMove:o(e.onPointerMove,e=>{"touch"===e.pointerType||m.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),m.current=!0)}),onPointerLeave:o(e.onPointerLeave,()=>{l.onTriggerLeave(),m.current=!1}),onPointerDown:o(e.onPointerDown,()=>{l.open&&l.onClose(),f.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:o(e.onFocus,()=>{f.current||l.onOpen()}),onBlur:o(e.onBlur,l.onClose),onClick:o(e.onClick,l.onClose)})})});I.displayName=P;var D="TooltipPortal",[A,S]=w(D,{forceMount:void 0}),U=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=L(D,t);return(0,a.jsx)(A,{scope:t,forceMount:n,children:(0,a.jsx)(m,{present:n||i.open,children:(0,a.jsx)(p.Z,{asChild:!0,container:o,children:r})})})};U.displayName=D;var $="TooltipContent",F=r.forwardRef((e,t)=>{let n=S($,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=L($,e.__scopeTooltip);return(0,a.jsx)(m,{present:r||l.open,children:l.disableHoverableContent?(0,a.jsx)(Z,{side:o,...i,ref:t}):(0,a.jsx)(W,{side:o,...i,ref:t})})}),W=r.forwardRef((e,t)=>{let n=L($,e.__scopeTooltip),o=_($,e.__scopeTooltip),i=r.useRef(null),l=u(t,i),[s,c]=r.useState(null),{trigger:d,onClose:p}=n,f=i.current,{onPointerInTransitChange:m}=o,v=r.useCallback(()=>{c(null),m(!1)},[m]),y=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),m(!0)},[m]);return r.useEffect(()=>()=>v(),[v]),r.useEffect(()=>{if(d&&f){let e=e=>y(e,f),t=e=>y(e,d);return d.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[d,f,y,v]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==d?void 0:d.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],u=t[i],a=l.x,s=l.y,c=u.x,d=u.y;s>r!=d>r&&n<(c-a)*(r-s)/(d-s)+a&&(o=!o)}return o}(n,s);r?v():o&&(v(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[d,f,s,p,v]),(0,a.jsx)(Z,{...e,ref:l})}),[B,V]=w(j,{isInside:!1}),H=function(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=y,t}("TooltipContent"),Z=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:u,...c}=e,p=L($,n),f=T(n),{onClose:m}=p;return r.useEffect(()=>(document.addEventListener(N,m),()=>document.removeEventListener(N,m)),[m]),r.useEffect(()=>{if(p.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(p.trigger))&&m()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[p.trigger,m]),(0,a.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:m,children:(0,a.jsxs)(d.UC,{"data-state":p.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,a.jsx)(H,{children:o}),(0,a.jsx)(B,{scope:n,isInside:!0,children:(0,a.jsx)(b.bL,{id:p.contentId,role:"tooltip",children:i||o})})]})})});F.displayName=$;var q="TooltipArrow",X=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=T(n);return V(q,n).isInside?null:(0,a.jsx)(d.i3,{...o,...r,ref:t})});X.displayName=q;var Y=O,z=k,K=I,G=U,J=F,Q=X}}]);