#!/usr/bin/env python3
"""
Simple script to convert jira_tickets_filtered.json into a simpler format
containing only Summary, Issue Type, Description, and Status fields.
"""

import json
from pathlib import Path


def convert_jira_tickets():
    """
    Convert the filtered Jira tickets JSON to a simpler format with only
    the required fields: Summary, Issue Type, Description, and Status.
    """
    # Get the directory where this script is located
    script_dir = Path(__file__).parent

    # Define input and output file paths
    input_file = script_dir.parent / "jira_tickets_filtered.json"
    output_file = script_dir.parent / "jira_tickets_simple.json"

    try:
        # Read the input JSON file
        print(f"Reading from: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            tickets = json.load(f)

        # Extract only the required fields
        simplified_tickets = []
        for ticket in tickets:
            simplified_ticket = {
                "Key": ticket.get("Key", ""),
                "Summary": ticket.get("Summary", ""),
                "Issue Type": ticket.get("Issue Type", ""),
                "Description": ticket.get("Description", ""),
                "Status": ticket.get("Status", "")
            }
            simplified_tickets.append(simplified_ticket)

        # Write the simplified JSON to output file
        print(f"Writing to: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(simplified_tickets, f, indent=2, ensure_ascii=False)

        print(f"Successfully converted {len(simplified_tickets)} tickets")
        print(f"Output saved to: {output_file}")

        # Print a sample of the first few tickets for verification
        print("\nSample of converted data:")
        for i, ticket in enumerate(simplified_tickets[:3]):
            print(f"\nTicket {i+1}:")
            for key, value in ticket.items():
                # Truncate long descriptions for display
                display_value = value[:100] + "..." if len(str(value)) > 100 else value
                print(f"  {key}: {display_value}")

        return True

    except FileNotFoundError:
        print(f"Error: Input file not found: {input_file}")
        return False
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in input file: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False


if __name__ == "__main__":
    success = convert_jira_tickets()
    if success:
        print("\nConversion completed successfully!")
    else:
        print("\nConversion failed!")
        exit(1)