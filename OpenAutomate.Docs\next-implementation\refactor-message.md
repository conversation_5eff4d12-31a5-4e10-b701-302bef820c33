# Refactoring Plan: Comprehensive Frontend API Error Handling

## 1. Executive Summary & Goals
This document outlines a strategic plan to implement a comprehensive, robust, and user-friendly error handling system for all API calls within the `OpenAutomate.Frontend` application. The current system often fails to report API errors to the user, leading to a confusing experience where issues are only visible in the developer console.

-   **Primary Objective:** To eliminate silent API errors and provide clear, consistent, and meaningful feedback to the user for any failed backend communication.
-   **Key Goals:**
    1.  **Centralize Error Handling:** Implement a single, reliable mechanism to catch and process all API errors globally.
    2.  **Improve User Experience (UX):** Display user-friendly toast notifications for all critical API errors, using the message provided by the backend.
    3.  **Enhance Developer Experience (DX):** Simplify error handling across the application by leveraging the existing SWR library, reducing boilerplate `try/catch` blocks.

## 2. Current Situation Analysis
The frontend application, built with Next.js and SWR, currently lacks a standardized approach to API error management.

-   **Silent Failures:** Most API requests that result in an error (e.g., HTTP 4xx or 5xx status codes) do not trigger any user-facing notification. The application state remains unchanged, making it seem like nothing happened.
-   **Inconsistent Handling:** In the few places where errors are caught, the implementation is inconsistent. This leads to duplicated logic and varying quality of error feedback.
-   **Incorrect Messages:** When an error message is shown, it is often a generic, hard-coded string rather than the specific, contextual message returned by the `OpenAutomate.API`.
-   **Over-reliance on DevTools:** Developers and support personnel must rely on the browser's developer console to diagnose what should be user-visible errors.

## 3. Proposed Solution / Refactoring Strategy
The proposed strategy is to create a layered, centralized error handling mechanism by enhancing the existing API client and integrating it with the global SWR configuration. This will make robust error handling an automatic feature for the majority of data-fetching operations.

### 3.1. High-Level Design / Architectural Overview
The flow of an API error will be managed as follows:

1.  An API call is made via `fetchApi` in `src/lib/api/client.ts`.
2.  If the HTTP response is not `ok` (i.e., status is not 2xx), `fetchApi` will parse the JSON error body from the API.
3.  `fetchApi` will then throw a custom, structured `ApiError` object containing the status, message, and details.
4.  For calls wrapped in `useSWR`, SWR's global `onError` handler (which we will configure) will catch this `ApiError`.
5.  The global `onError` handler will use a new utility function to extract a user-friendly message from the error.
6.  This message will be displayed to the user via the existing toast notification system.

```mermaid
sequenceDiagram
    participant Component as React Component
    participant SWR as useSWR Hook
    participant Client as fetchApi (`client.ts`)
    participant API as Backend API
    participant Toast as Toast Notification

    Component->>SWR: Trigger data fetch
    SWR->>Client: fetchApi('/resource')
    Client->>API: GET /resource
    API-->>Client: 500 Internal Server Error (JSON with "message")
    Client-->>SWR: Throws structured ApiError
    SWR-->>Component: Enters `error` state
    Note over SWR: Global `onError` handler is triggered
    SWR->>Toast: Display error toast with API message
```

### 3.2. Key Components / Modules
-   **`src/lib/api/client.ts`:** The core `fetchApi` function will be modified to be the central point of error interception. It will be responsible for identifying failed HTTP requests, parsing their JSON body, and throwing a standardized error object.
-   **`src/lib/utils/error-utils.ts`:** This existing file will be enhanced with a robust `extractErrorMessage` function. This function will be responsible for intelligently parsing various error types (our custom `ApiError`, network errors, standard `Error` objects) and returning a clean, user-friendly string.
-   **`src/lib/config/swr-config.ts`:** We will define a global `onError` handler in the SWR configuration. This handler will be the primary mechanism for automatically displaying toasts for any error caught by any `useSWR` hook across the application.
-   **`src/providers/swr-provider.tsx`:** This provider will be updated to pass the new global configuration to the `<SWRConfig>` component.

### 3.3. Detailed Action Plan / Phases

---

#### **Phase 1: Foundational Error Handling Logic**
-   **Objective(s):** Create the core, reusable logic for intercepting, parsing, and formatting API errors.
-   **Priority:** High

-   **Task 1.1:** Enhance `fetchApi` in `src/lib/api/client.ts`
    -   **Rationale/Goal:** To make the API client responsible for distinguishing successful and failed API calls and for standardizing the error structure.
    -   **Estimated Effort:** Medium
    -   **Deliverable/Criteria for Completion:** The `fetchApi` function correctly processes successful (2xx) responses. For non-2xx responses, it awaits the JSON body, creates a custom `ApiError` object containing `status`, `message`, and `details`, and throws it. Network errors (e.g., `Failed to fetch`) are also caught and re-thrown as a structured error.

-   **Task 1.2:** Enhance `extractErrorMessage` in `src/lib/utils/error-utils.ts`
    -   **Rationale/Goal:** To create a single, reliable function that can convert any thrown error into a displayable string, preventing technical jargon from reaching the user.
    -   **Estimated Effort:** Small
    -   **Deliverable/Criteria for Completion:** The function `extractErrorMessage(error: unknown): string` is created. It correctly handles custom `ApiError` objects, standard `Error` objects, and other potential error types, returning a safe, user-friendly message in all cases.

-   **Task 1.3:** Create a `createErrorToast` helper function
    -   **Rationale/Goal:** To provide a simple, one-line method for triggering a standardized error toast, promoting UI consistency.
    -   **Estimated Effort:** Small
    -   **Deliverable/Criteria for Completion:** A function is created in `src/lib/utils/error-utils.ts` that accepts an `unknown` error, passes it to `extractErrorMessage`, and calls the `toast()` function with a "destructive" variant and the resulting message.

---

#### **Phase 2: Global Integration with SWR**
-   **Objective(s):** Automate error handling for all data fetching done via SWR hooks.
-   **Priority:** High

-   **Task 2.1:** Configure Global `onError` in `src/lib/config/swr-config.ts`
    -   **Rationale/Goal:** To leverage SWR's global error handling capabilities, making error notifications automatic and removing the need for repetitive `try/catch` blocks in components.
    -   **Estimated Effort:** Small
    -   **Deliverable/Criteria for Completion:** The `swrConfig` object is updated with an `onError` property. This handler calls the `createErrorToast` function created in Phase 1.

-   **Task 2.2:** Update `SWRProvider` in `src/providers/swr-provider.tsx`
    -   **Rationale/Goal:** To ensure the new global SWR configuration is applied to the entire application.
    -   **Estimated Effort:** X-Small
    -   **Deliverable/Criteria for Completion:** The `SWRConfig` component within the provider is correctly passed the updated `swrConfig` object.

---

#### **Phase 3: Refactoring and Verification**
-   **Objective(s):** Apply the new global system to existing code, remove redundant local error handling, and confirm the solution works as expected.
-   **Priority:** Medium

-   **Task 3.1:** Refactor Representative Components
    -   **Rationale/Goal:** To clean up the codebase by removing now-unnecessary local error handling and to validate that the global system works for various use cases (e.g., data tables, forms).
    -   **Estimated Effort:** Medium
    -   **Deliverable/Criteria for Completion:** At least two components (e.g., `src/components/agent/agent.tsx` and `src/components/forms/login-form.tsx`) are refactored. Redundant `try/catch` blocks around `useSWR` calls or direct API calls are removed, relying on the global handler instead.

-   **Task 3.2:** End-to-End Testing
    -   **Rationale/Goal:** To ensure the system is robust and correctly handles all expected error scenarios.
    -   **Estimated Effort:** Small
    -   **Deliverable/Criteria for Completion:** Manually test and confirm that the frontend correctly displays toast notifications for various backend errors, including:
        -   `401 Unauthorized` (e.g., expired token)
        -   `403 Forbidden` (e.g., insufficient permissions)
        -   `404 Not Found` (e.g., invalid ID)
        -   `400 Bad Request` (e.g., validation error)
        -   `500 Internal Server Error`
        -   Network failure (e.g., API server is down)

## 4. Key Considerations & Risk Mitigation
### 4.1. Technical Risks & Challenges
-   **Risk:** Modifying the central `fetchApi` client could introduce a single point of failure affecting all API calls.
    -   **Mitigation:** The new error handling logic will be contained within a `try/catch` block inside `fetchApi` itself. The thrown error object will be carefully structured to be backward compatible and include the original response, allowing for specific, local overrides if a component needs to handle a particular error differently.
-   **Risk:** Some errors (like form validation `400 Bad Request`) might be better handled inline within the form rather than with a global toast.
    -   **Mitigation:** The global SWR `onError` handler is the default for *unhandled* errors. Individual `useSWR` hooks can still provide their own local `onError` callback, which would override the global one and prevent the toast from appearing. This allows for component-specific error handling when needed.

### 4.2. Dependencies
-   The phases are sequential. Phase 1 (foundational logic) must be completed before Phase 2 (global integration). Phase 3 (refactoring) depends on the successful completion of the first two phases.

### 4.3. Non-Functional Requirements (NFRs) Addressed
-   **Usability:** Significantly improved. Users will receive immediate and clear feedback when an operation fails, reducing confusion and frustration.
-   **Maintainability:** Greatly enhanced. Error handling logic is centralized in `client.ts` and `swr-config.ts`, removing duplicated code from dozens of components. Future changes to error handling can be made in one place.
-   **Reliability:** The system becomes more reliable from a user's perspective, as they are now aware of backend issues instead of experiencing a silent failure.

## 5. Success Metrics / Validation Criteria
The implementation of this plan will be considered successful when:
1.  Any API call that returns a non-2xx status code consistently and automatically triggers a user-facing toast notification.
2.  The message displayed in the toast notification accurately reflects the `message` field from the API's JSON error response.
3.  The amount of boilerplate `try/catch` code for error handling in individual components is significantly reduced.
4.  Network-level failures (when the API is unreachable) result in a user-friendly "Network error" toast.

## 6. Assumptions Made
-   The backend API will consistently return JSON-formatted error responses containing a `message` property for HTTP statuses in the 4xx and 5xx range.
-   The existing UI toast component (`use-toast`) is suitable for displaying these error notifications.
-   SWR will remain the primary data-fetching library for the foreseeable future.

## 7. Open Questions / Areas for Further Investigation
-   **Form Validation Errors:** Should all `400 Bad Request` errors trigger a global toast? The team should discuss if these are better handled inline within forms. The proposed solution allows for this flexibility, but a convention should be established.
-   **Error Logging:** While out of scope for this plan, the centralized error handler is the perfect place to integrate a third-party error logging service (like Sentry or LogRocket) in the future. This should be considered for a follow-up task.