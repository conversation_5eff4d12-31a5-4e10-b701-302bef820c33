(()=>{var e={};e.id=9770,e.ids=[9770],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17539:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=t(65239),r=t(48088),n=t(31369),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(s,l);let c={children:["",{children:["[tenant]",{children:["administration",{children:["subscription",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47770)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\subscription\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\subscription\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[tenant]/administration/subscription/page",pathname:"/[tenant]/administration/subscription",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28750:(e,s,t)=>{Promise.resolve().then(t.bind(t,77032))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31599:(e,s,t)=>{"use strict";t.d(s,{c:()=>c});var a=t(43210),r=t(39989),n=t(16189),i=t(31207),l=t(70891);function c(){let e=(0,n.useRouter)(),{data:s,error:t,isLoading:c,mutate:o}=(0,i.Ay)(l.DC.organizationUnits(),()=>r.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:s??[],isLoading:c,error:t?"Failed to fetch organization units. Please try again later.":null,refresh:o,selectOrganizationUnit:(0,a.useCallback)(s=>{e.push(`/${s}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},39989:(e,s,t)=>{"use strict";t.d(s,{K:()=>r});var a=t(51787);let r={getMyOrganizationUnits:async()=>await a.F.get("/api/ou/my-ous"),getBySlug:async e=>await a.F.get(`/api/ou/slug/${e}`),getById:async e=>await a.F.get(`/api/ou/${e}`),create:async e=>await a.F.post("/api/ou/create",e),update:async(e,s)=>await a.F.put(`/api/ou/${e}`,s),requestDeletion:async e=>await a.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await a.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await a.F.get(`/api/ou/${e}/deletion-status`)}},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},47770:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>n});var a=t(37413),r=t(77202);let n={title:"Subscription Management",description:"Manage your organization subscription and billing"};function i(){return(0,a.jsx)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:(0,a.jsx)(r.default,{})})}},59014:(e,s,t)=>{Promise.resolve().then(t.bind(t,77202))},61018:(e,s,t)=>{"use strict";t.d(s,{TenantGuard:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64656:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var a=t(37413),r=t(48974),n=t(31057),i=t(50417),l=t(92588),c=t(61018),o=t(2505);function d({children:e}){return(0,a.jsx)(c.TenantGuard,{children:(0,a.jsx)(o.ChatProvider,{children:(0,a.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,a.jsxs)(i.SidebarProvider,{className:"flex flex-col",children:[(0,a.jsx)(n.SiteHeader,{}),(0,a.jsxs)("div",{className:"flex flex-1",children:[(0,a.jsx)(r.AppSidebar,{}),(0,a.jsx)(i.SidebarInset,{children:(0,a.jsx)(l.SearchProvider,{children:(0,a.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,s,t)=>{"use strict";t.d(s,{TenantGuard:()=>c});var a=t(60687),r=t(43210),n=t(16189),i=t(31599),l=t(31568);function c({children:e}){let{tenant:s}=(0,n.useParams)();(0,n.useRouter)();let{isAuthenticated:t,isLoading:c}=(0,l.A)(),{organizationUnits:o,isLoading:d}=(0,i.c)(),[m,u]=(0,r.useState)(!0);return c||d||m?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,a.jsx)(a.Fragment,{children:e})}},72826:(e,s,t)=>{Promise.resolve().then(t.bind(t,69231)),Promise.resolve().then(t.bind(t,83847)),Promise.resolve().then(t.bind(t,78526)),Promise.resolve().then(t.bind(t,97597)),Promise.resolve().then(t.bind(t,98641)),Promise.resolve().then(t.bind(t,80110))},77032:(e,s,t)=>{"use strict";t.d(s,{default:()=>A});var a=t(60687),r=t(43210),n=t(31568),i=t(51551),l=t(4781),c=t(78570),o=t(29523),d=t(44493),m=t(96834),u=t(41862),x=t(56085),p=t(5336),h=t(93613),j=t(48730),f=t(85778),g=t(40228),v=t(17313),b=t(41312),N=t(20140),y=t(73437);let w=(e,s,t)=>{if(!e?.userTrialStatus)return null;switch(e.userTrialStatus){case c.R.Eligible:return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You can start a free trial to explore all premium features."}),(0,a.jsx)(o.$,{onClick:t,disabled:s,className:"w-full transition-all duration-200",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Activating Trial..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Start Free Trial"]})})]});case c.R.Active:return(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 text-green-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You have an active trial subscription"})]});case c.R.Used:return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You have already used your free trial. Upgrade to continue using premium features."}),(0,a.jsx)(o.$,{className:"w-full",children:"Upgrade to Premium"})]});case c.R.NotEligible:return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Free trial is only available on your first organization unit. Upgrade to access premium features."}),(0,a.jsx)(o.$,{className:"w-full",children:"Upgrade to Premium"})]});default:return(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 text-gray-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Unable to determine trial status"})]})}};function A(){let{userProfile:e}=(0,n.A)(),{subscription:s,isLoading:t,mutate:A}=(0,i.R)(),[P,S]=(0,r.useState)(!1),{toast:T}=(0,N.d)(),F=async()=>{if(!P){S(!0),T({title:"Starting Trial...",description:"Please wait while we activate your free trial."});try{let e=await l.kV.startTrial();e.success?(T({title:"Trial Started Successfully!",description:"Your free trial has been activated. Refreshing subscription status..."}),await A()):T({title:"Failed to Start Trial",description:e.message||"Unable to start trial. Please try again.",variant:"destructive"})}catch(e){T({title:"Error Starting Trial",description:e instanceof Error?e.message:"An error occurred while starting your trial.",variant:"destructive"})}finally{S(!1)}}},C=()=>s?.isInTrial?(0,a.jsx)(m.E,{variant:"secondary",className:"ml-2",children:"Trial"}):s?.isActive?(0,a.jsx)(m.E,{variant:"default",className:"ml-2",children:"Active"}):(0,a.jsx)(m.E,{variant:"destructive",className:"ml-2",children:"Expired"});return t?(0,a.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Subscription Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your organization's subscription and billing"})]})}),(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(u.A,{className:"h-8 w-8 animate-spin"})})]}):(0,a.jsxs)("div",{className:"flex-1 space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Subscription Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your organization's subscription and billing"})]})}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(d.ZB,{className:"text-sm font-medium",children:"Status"}),s?.isActive&&s?.isInTrial?(0,a.jsx)(j.A,{className:"h-6 w-6 text-blue-500"}):s?.isActive?(0,a.jsx)(p.A,{className:"h-6 w-6 text-green-500"}):(0,a.jsx)(h.A,{className:"h-6 w-6 text-red-500"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"text-2xl font-bold flex items-center",children:[s?.status||"No Subscription",s?.hasSubscription&&C()]})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(d.ZB,{className:"text-sm font-medium",children:"Plan"}),(0,a.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:s?.planName||"No Plan"})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(d.ZB,{className:"text-sm font-medium",children:"Days Remaining"}),(0,a.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:s?.daysRemaining??"N/A"})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(d.ZB,{className:"text-sm font-medium",children:"Trial Status"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:s?.userTrialStatus===c.R.Eligible?"Available":s?.userTrialStatus===c.R.Used?"Used":s?.userTrialStatus===c.R.Active?"Active":"Not Available"})})]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),"Current Subscription"]}),(0,a.jsx)(d.BT,{children:"Details about your current subscription status"})]}),(0,a.jsx)(d.Wu,{className:"space-y-4",children:s?.hasSubscription?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Status:"}),(0,a.jsxs)("div",{className:"flex items-center",children:[s.status,C()]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Plan:"}),(0,a.jsx)("span",{children:s.planName})]}),s.isInTrial&&s.trialEndsAt&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Trial Ends:"}),(0,a.jsx)("span",{children:(0,y.GP)(new Date(s.trialEndsAt+"Z"),"PPp")})]}),s.renewsAt&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Next Billing:"}),(0,a.jsx)("span",{children:(0,y.GP)(new Date(s.renewsAt+"Z"),"PP")})]}),s.isActive&&(0,a.jsx)("div",{className:"pt-2",children:(0,a.jsx)(o.$,{className:"w-full",variant:"outline",children:"Manage Billing"})})]}):(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No active subscription"})]})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),"Free Trial"]}),(0,a.jsx)(d.BT,{children:"Start your free trial to access all features"})]}),(0,a.jsx)(d.Wu,{className:"space-y-4",children:w(s,P,F)})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-5 w-5"}),"Account Information"]}),(0,a.jsx)(d.BT,{children:"Information about your account and trial usage"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Account Email:"}),(0,a.jsx)("span",{className:"text-sm",children:e?.email})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Full Name:"}),(0,a.jsx)("span",{className:"text-sm",children:e?`${e.firstName} ${e.lastName}`:"N/A"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Trial Used:"}),(0,a.jsx)("span",{className:"text-sm",children:e?.hasUsedTrial?"Yes":"No"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Organization Units:"}),(0,a.jsx)("span",{className:"text-sm",children:e?.organizationUnits.length||0})]})]})]})})]})]})}},77202:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\administration\\\\subscription\\\\subscription-management.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\administration\\subscription\\subscription-management.tsx","default")},83442:(e,s,t)=>{Promise.resolve().then(t.bind(t,61018)),Promise.resolve().then(t.bind(t,2505)),Promise.resolve().then(t.bind(t,92588)),Promise.resolve().then(t.bind(t,48974)),Promise.resolve().then(t.bind(t,31057)),Promise.resolve().then(t.bind(t,50417))}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,7966,5584,5156,4654,6467,3437,6763,519],()=>t(17539));module.exports=a})();