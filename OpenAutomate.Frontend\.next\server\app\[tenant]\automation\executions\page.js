(()=>{var e={};e.id=6415,e.ids=[6415],e.modules={617:(e,t,a)=>{Promise.resolve().then(a.bind(a,48587))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42473:(e,t,a)=>{Promise.resolve().then(a.bind(a,72150))},48587:(e,t,a)=>{"use strict";a.d(t,{default:()=>l});var s=a(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call executionsSchema() from the server but executionsSchema is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\automation\\executions\\executions.tsx","executionsSchema");let l=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\automation\\\\executions\\\\executions.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\automation\\executions\\executions.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64993:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=a(65239),l=a(48088),n=a(31369),r=a(30893),i={};for(let e in r)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>r[e]);a.d(t,i);let o={children:["",{children:["[tenant]",{children:["automation",{children:["executions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,83111)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\automation\\executions\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\automation\\executions\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/[tenant]/automation/executions/page",pathname:"/[tenant]/automation/executions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},71669:(e,t,a)=>{"use strict";a.d(t,{C5:()=>v,MJ:()=>p,Rr:()=>j,eI:()=>x,lR:()=>h,lV:()=>c,zB:()=>u});var s=a(60687),l=a(43210),n=a(11329),r=a(27605),i=a(36966),o=a(80013);let c=r.Op,d=l.createContext({}),u=({...e})=>(0,s.jsx)(d.Provider,{value:{name:e.name},children:(0,s.jsx)(r.xI,{...e})}),m=()=>{let e=l.useContext(d),t=l.useContext(g),{getFieldState:a}=(0,r.xW)(),s=(0,r.lN)({name:e.name}),n=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}},g=l.createContext({});function x({className:e,...t}){let a=l.useId();return(0,s.jsx)(g.Provider,{value:{id:a},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",e),...t})})}function h({className:e,...t}){let{error:a,formItemId:l}=m();return(0,s.jsx)(o.J,{"data-slot":"form-label","data-error":!!a,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:l,...t})}function p({...e}){let{error:t,formItemId:a,formDescriptionId:l,formMessageId:r}=m();return(0,s.jsx)(n.DX,{"data-slot":"form-control",id:a,"aria-describedby":t?`${l} ${r}`:`${l}`,"aria-invalid":!!t,...e})}function j({className:e,...t}){let{formDescriptionId:a}=m();return(0,s.jsx)("p",{"data-slot":"form-description",id:a,className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function v({className:e,...t}){let{error:a,formMessageId:l}=m(),n=a?String(a?.message??""):t.children;return n?(0,s.jsx)("p",{"data-slot":"form-message",id:l,className:(0,i.cn)("text-destructive text-sm",e),...t,children:n}):null}},72150:(e,t,a)=>{"use strict";a.d(t,{default:()=>eo});var s=a(60687),l=a(1303),n=a(29523),r=a(43210),i=a.n(r),o=a(56896),c=a(34208),d=a(96834),u=a(48730),m=a(41862),g=a(5336),x=a(35071),h=a(97840);function p({status:e,className:t}){let a=(e=>{switch(e.toLowerCase()){case"pending":return{variant:"secondary",icon:u.A,label:"Pending",className:"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"};case"running":return{variant:"default",icon:m.A,label:"Running",className:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"};case"completed":return{variant:"default",icon:g.A,label:"Completed",className:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"};case"failed":return{variant:"destructive",icon:x.A,label:"Failed",className:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"};case"cancelled":return{variant:"outline",icon:x.A,label:"Cancelled",className:"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"};default:return{variant:"outline",icon:h.A,label:e,className:"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"}}})(e),l=a.icon;return(0,s.jsxs)(d.E,{variant:a.variant,className:`${a.className} ${t||""}`,children:[(0,s.jsx)(l,{className:`mr-1 h-3 w-3 ${"running"===e.toLowerCase()?"animate-spin":""}`}),a.label]})}var j=a(93661),v=a(31158),f=a(10022),b=a(96362),N=a(11860),y=a(21342),C=a(63503),w=a(20140),k=a(90631),S=a(59321);function A({execution:e,onDeleted:t}){let[a,l]=(0,r.useState)(!1),[i,o]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),{toast:u}=(0,w.d)(),g=async()=>{o(!0);try{console.log("Delete execution:",e.id),u({title:"Delete Simulation",description:"Delete execution API not yet implemented."}),l(!1),t&&t()}catch(e){console.error("Delete failed:",e),u({title:"Delete Failed",description:"Failed to delete execution. Please try again.",variant:"destructive"})}finally{o(!1)}},x=async()=>{if(!e.hasLogs){u({title:"No Logs Available",description:"This execution does not have logs available for download.",variant:"destructive"});return}d(!0);try{let t=`execution_${e.id.substring(0,8)}_logs.log`;await (0,k.iX)(e.id,t),u({title:"Download Started",description:"Execution logs download has started."})}catch(e){console.error("Failed to download logs:",e),u((0,S.m4)(e))}finally{d(!1)}},h=e=>{e.stopPropagation()};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(y.rI,{children:[(0,s.jsx)(y.ty,{asChild:!0,onClick:h,children:(0,s.jsx)(n.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted","aria-label":"Open menu",onClick:h,children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(y.SQ,{align:"end",className:"w-[180px]",onClick:h,onPointerDown:h,onMouseDown:h,children:[(0,s.jsxs)(y._2,{onClick:e=>{e.stopPropagation(),x()},disabled:!e.hasLogs||c,children:[c?(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin","aria-hidden":"true"}):e.hasLogs?(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}):(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,s.jsx)("span",{children:c?"Downloading...":e.hasLogs?"View Logs":"No Logs"})]}),(0,s.jsx)(y.mB,{}),(0,s.jsxs)(y._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e.stopPropagation(),l(!0)},children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Delete"})]})]})]}),(0,s.jsx)(C.lG,{open:a,onOpenChange:l,children:(0,s.jsxs)(C.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsxs)(C.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(N.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,s.jsx)(C.c7,{children:(0,s.jsx)(C.L3,{children:"Confirm Delete"})}),(0,s.jsxs)("div",{children:["Are you sure you want to delete this execution? ",(0,s.jsx)("br",{}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Execution ID: ",(0,s.jsxs)("b",{children:[e.id.substring(0,8),"..."]})]})]}),(0,s.jsxs)(C.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:e=>{e.stopPropagation(),l(!1)},disabled:i,children:"Cancel"}),(0,s.jsxs)(n.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:e=>{e.stopPropagation(),g()},disabled:i,children:[i&&(0,s.jsx)(m.A,{className:"animate-spin w-4 h-4 mr-2"}),i?"Deleting...":"Delete"]})]})]})})]})}let F=({onDeleted:e}={})=>[{id:"select",header:({table:e})=>(0,s.jsx)(o.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>{let t=e=>{e.stopPropagation()};return(0,s.jsx)("span",{onClick:t,onMouseDown:t,onPointerDown:t,children:(0,s.jsx)(o.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]",onClick:t})})},enableSorting:!1,enableHiding:!1},{id:"actions",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Actions"}),cell:({row:t})=>(0,s.jsx)(A,{execution:t.original,onDeleted:e}),enableSorting:!1,enableHiding:!1},{accessorKey:"packageName",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Package Name"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:"font-medium",children:e.getValue("packageName")||"N/A"})})},{accessorKey:"Version",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Package Version"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("Version")||"N/A"})})},{accessorKey:"agent",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Agent"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("agent")})})},{accessorKey:"state",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"State"}),cell:({row:e})=>{let t=String(e.getValue("state"));return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(p,{status:t})})}},{accessorKey:"startTime",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Start Time"}),cell:({row:e})=>{let t=e.getValue("startTime");return console.log("StartTime column value:",{value:t,type:typeof t,rowId:e.original.id}),(0,s.jsx)("span",{children:"string"==typeof t?t:""})}},{accessorKey:"endTime",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"End Time"}),cell:({row:e})=>{let t=e.getValue("endTime");return console.log("EndTime column value:",{value:t,type:typeof t,rowId:e.original.id}),(0,s.jsx)("span",{children:"string"==typeof t?t:""})}},{accessorKey:"source",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Source"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("source")})})},{accessorKey:"command",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Command"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("command")})})},{accessorKey:"schedules",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Schedules"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("schedules")})})},{accessorKey:"taskId",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Task Id"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("taskId")})})}];function V({execution:e,onDeleted:t}){let[a,l]=(0,r.useState)(!1),[i,o]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),{toast:u}=(0,w.d)(),g=async()=>{o(!0);try{console.log("Delete execution:",e.id),u({title:"Delete Simulation",description:"Delete execution API not yet implemented."}),l(!1),t&&t()}catch(e){console.error("Delete failed:",e),u({title:"Delete Failed",description:"Failed to delete execution. Please try again.",variant:"destructive"})}finally{o(!1)}},x=async()=>{if(!e.hasLogs){u({title:"No Logs Available",description:"This execution does not have logs available for download.",variant:"destructive"});return}d(!0);try{let t=`execution_${e.id.substring(0,8)}_logs.log`;await (0,k.iX)(e.id,t),u({title:"Download Started",description:"Execution logs download has started."})}catch(e){console.error("Failed to download logs:",e),u((0,S.m4)(e))}finally{d(!1)}},h=e=>{e.stopPropagation()};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(y.rI,{children:[(0,s.jsx)(y.ty,{asChild:!0,onClick:h,children:(0,s.jsx)(n.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted","aria-label":"Open menu",onClick:h,children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(y.SQ,{align:"end",className:"w-[180px]",onClick:h,onPointerDown:h,onMouseDown:h,children:[(0,s.jsxs)(y._2,{onClick:e=>{e.stopPropagation(),x()},disabled:!e.hasLogs||c,children:[c?(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin","aria-hidden":"true"}):e.hasLogs?(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}):(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,s.jsx)("span",{children:c?"Downloading...":e.hasLogs?"Download Logs":"No Logs"})]}),(0,s.jsx)(y.mB,{}),(0,s.jsxs)(y._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e.stopPropagation(),l(!0)},children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Delete"})]})]})]}),(0,s.jsx)(C.lG,{open:a,onOpenChange:l,children:(0,s.jsxs)(C.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsxs)(C.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(N.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,s.jsx)(C.c7,{children:(0,s.jsx)(C.L3,{children:"Confirm Delete"})}),(0,s.jsxs)("div",{children:["Are you sure you want to delete this execution? ",(0,s.jsx)("br",{}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Execution ID: ",(0,s.jsxs)("b",{children:[e.id.substring(0,8),"..."]})]})]}),(0,s.jsxs)(C.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:e=>{e.stopPropagation(),l(!1)},disabled:i,children:"Cancel"}),(0,s.jsxs)(n.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:e=>{e.stopPropagation(),g()},disabled:i,children:[i&&(0,s.jsx)(m.A,{className:"animate-spin w-4 h-4 mr-2"}),i?"Deleting...":"Delete"]})]})]})})]})}F();var P=a(27590);let $=({onDeleted:e}={})=>[{id:"select",header:({table:e})=>(0,s.jsx)(o.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>{let t=e=>{e.stopPropagation()};return(0,s.jsx)("span",{onClick:t,onMouseDown:t,onPointerDown:t,children:(0,s.jsx)(o.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]",onClick:t})})},enableSorting:!1,enableHiding:!1},{id:"actions",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Actions"}),cell:({row:t})=>(0,s.jsx)(V,{execution:t.original,onDeleted:e}),enableSorting:!1,enableHiding:!1},{accessorKey:"packageName",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Package Name"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:"font-medium",children:e.getValue("packageName")||"N/A"})})},{accessorKey:"Version",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Package Version"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("Version")||"N/A"})})},{accessorKey:"agent",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Agent"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("agent")})})},{accessorKey:"state",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"State"}),cell:({row:e})=>{let t=String(e.getValue("state"));return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(p,{status:t})})}},{accessorKey:"startTime",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Start Time"}),cell:({row:e})=>{let t=e.getValue("startTime"),a=(0,P.Ej)(t,{fallback:""});return(0,s.jsx)("span",{children:a})}},{accessorKey:"endTime",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"End Time"}),cell:({row:e})=>{let t=e.getValue("endTime"),a=(0,P.Ej)(t,{fallback:""});return(0,s.jsx)("span",{children:a})}},{accessorKey:"source",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Source"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("source")})})},{accessorKey:"command",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Command"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("command")})})},{accessorKey:"schedules",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Schedules"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("schedules")})})},{accessorKey:"taskId",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Task Id"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("taskId")})})}];$();let I=[{id:"select",header:({table:e})=>(0,s.jsx)(o.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>(0,s.jsx)(o.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1},{accessorKey:"packageName",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Package name"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("packageName")})})},{accessorKey:"agent",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Agent"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("agent")})})},{accessorKey:"nextRunTime",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Next Run Time"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("nextRunTime")})})},{accessorKey:"schedule",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Schedule"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("schedule")})})}];var z=a(50723),D=a(27605),R=a(63442),T=a(45880),E=a(71669),M=a(15079),L=a(31207),q=a(70891),O=a(37337),_=a(39582);let K=T.Ik({packageId:T.Yj().min(1,"Package is required"),version:T.Yj().min(1,"Version is required"),botAgentId:T.Yj().min(1,"Agent is required")});function G({isOpen:e,onClose:t,onSuccess:a}){let{toast:l}=(0,w.d)(),{data:i,error:o}=(0,L.Ay)(e?q.DC.packages():null,O.s9),{data:c,error:d}=(0,L.Ay)(e?q.DC.agents():null,_.NA),[u,g]=(0,r.useState)(!1),x=(0,r.useMemo)(()=>i?.filter(e=>e.isActive)??[],[i]),p=(0,r.useMemo)(()=>c?.filter(e=>"Disconnected"!==e.status)??[],[c]),j=!i||!c,v=(0,D.mN)({resolver:(0,R.u)(K),defaultValues:{packageId:"",version:"",botAgentId:""}}),f=v.watch("packageId"),b=i?.find(e=>e.id===f),N=b?.versions||[],y=e=>{let t=c?.find(t=>t.id===e);return t?"Disconnected"===t.status?(l({variant:"destructive",title:"Agent Disconnected",description:"Selected agent is disconnected and cannot execute packages"}),null):("Busy"===t.status&&l({title:"Agent Busy",description:"Selected agent is currently busy. The execution will be queued."}),t):(l({variant:"destructive",title:"Agent Not Found",description:"Selected agent not found"}),null)},S=(e,t)=>{let a=i?.find(t=>t.id===e),s=a?.versions.find(e=>e.versionNumber===t);return a&&s?s.isActive?{selectedPackage:a,selectedVersion:s}:(l({variant:"destructive",title:"Version Inactive",description:"Selected package version is not active"}),null):(l({variant:"destructive",title:"Invalid Selection",description:"Invalid package or version selected"}),null)},A=e=>{console.error("Error triggering execution:",e)},F=async e=>{g(!0);try{let s=y(e.botAgentId);if(!s)return;let n=S(e.packageId,e.version);if(!n)return;let{selectedPackage:r}=n,i={botAgentId:e.botAgentId,packageId:e.packageId,packageName:r.name,version:e.version},o=await (0,k.RT)(i);l({title:"Execution Started",description:`Execution started successfully (ID: ${o.id.substring(0,8)}...)`}),v.reset(),t(),a?.({id:o.id,packageName:r.name,botAgentName:s.name})}catch(e){A(e)}finally{g(!1)}},V=()=>{v.reset(),t()};return(0,s.jsx)(C.lG,{open:e,onOpenChange:V,children:(0,s.jsxs)(C.Cf,{className:"sm:max-w-[500px]",children:[(0,s.jsxs)(C.c7,{children:[(0,s.jsxs)(C.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-5 w-5"}),"Create New Execution"]}),(0,s.jsx)(C.rr,{children:"Select a package, version, and agent to execute immediately."})]}),j?(0,s.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,s.jsx)(m.A,{className:"h-6 w-6 animate-spin"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading packages and agents..."})]}):(0,s.jsx)(E.lV,{...v,children:(0,s.jsxs)("form",{onSubmit:v.handleSubmit(F),className:"space-y-4",children:[(0,s.jsx)(E.zB,{control:v.control,name:"packageId",render:({field:e})=>(0,s.jsxs)(E.eI,{children:[(0,s.jsx)(E.lR,{children:"Package"}),(0,s.jsxs)(M.l6,{onValueChange:e.onChange,value:e.value,children:[(0,s.jsx)(E.MJ,{children:(0,s.jsx)(M.bq,{children:(0,s.jsx)(M.yv,{placeholder:"Select a package"})})}),(0,s.jsx)(M.gC,{children:x.map(e=>(0,s.jsx)(M.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"font-medium",children:e.name}),e.description&&(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:e.description})]})},e.id))})]}),(0,s.jsx)(E.C5,{})]})}),(0,s.jsx)(E.zB,{control:v.control,name:"version",render:({field:e})=>(0,s.jsxs)(E.eI,{children:[(0,s.jsx)(E.lR,{children:"Version"}),(0,s.jsxs)(M.l6,{onValueChange:e.onChange,value:e.value,disabled:!f,children:[(0,s.jsx)(E.MJ,{children:(0,s.jsx)(M.bq,{children:(0,s.jsx)(M.yv,{placeholder:"Select a version"})})}),(0,s.jsx)(M.gC,{children:N.map(e=>(0,s.jsx)(M.eb,{value:e.versionNumber,children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("span",{className:"font-medium",children:["v",e.versionNumber]}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Uploaded ",(0,P.Ej)(e.uploadedAt,{dateStyle:"medium",timeStyle:void 0,fallback:"Unknown date"})]})]})},e.id))})]}),(0,s.jsx)(E.C5,{})]})}),(0,s.jsx)(E.zB,{control:v.control,name:"botAgentId",render:({field:e})=>(0,s.jsxs)(E.eI,{children:[(0,s.jsx)(E.lR,{children:"Agent"}),(0,s.jsxs)(M.l6,{onValueChange:e.onChange,value:e.value,children:[(0,s.jsx)(E.MJ,{children:(0,s.jsx)(M.bq,{children:(0,s.jsx)(M.yv,{placeholder:"Select an agent"})})}),(0,s.jsx)(M.gC,{children:p.map(e=>(0,s.jsx)(M.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"font-medium",children:e.name}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:e.machineName})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`h-2 w-2 rounded-full ${"Available"===e.status?"bg-green-500":"Busy"===e.status?"bg-yellow-500":"bg-red-500"}`}),(0,s.jsx)("span",{className:`text-xs font-medium ${"Available"===e.status?"text-green-600":"Busy"===e.status?"text-yellow-600":"text-red-600"}`,children:e.status})]})]})},e.id))})]}),(0,s.jsx)(E.C5,{})]})}),(0,s.jsxs)(C.Es,{children:[(0,s.jsx)(n.$,{type:"button",variant:"outline",onClick:V,children:"Cancel"}),(0,s.jsx)(n.$,{type:"submit",disabled:u,children:u?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Starting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Start Execution"]})})]})]})})]})})}var B=a(16189),H=a(99270),U=a(80462),J=a(89667),W=a(53984),X=a(40988),Q=a(48754),Y=a(36966),Z=a(40228),ee=a(73437);function et({table:e,statuses:t,onSearch:a,onStatusChange:l,searchValue:o="",isFiltering:c=!1,isPending:u=!1,searchPlaceholder:g="Search by ID or Agent..."}){let x=e.getState().columnFilters.length>0,[h,p]=i().useState(void 0),j=e.getState().columnFilters.length,v=(0,r.useRef)(null),f=(0,r.useRef)(null),b=t=>{v.current&&(f.current=v.current.selectionStart),a?a(t):e.getColumn("name")?.setFilterValue(t)};return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,s.jsx)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md space-y-2",children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(X.AM,{children:[(0,s.jsx)(X.Wv,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",className:(0,Y.cn)("w-full justify-start text-left font-normal",!h&&"text-muted-foreground"),disabled:c,children:[(0,s.jsx)(Z.A,{className:"mr-2 h-4 w-4"}),h?(0,ee.GP)(h,"PPP"):"Select date"]})}),(0,s.jsx)(X.hl,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(Q.V,{mode:"single",selected:h,onSelect:t=>{p(t);let a=t?(0,ee.GP)(t,"yyyy-MM-dd"):"";e.getColumn("createdAt")?.setFilterValue(a)},initialFocus:!0})})]})})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(J.p,{ref:v,placeholder:g,value:o,onChange:e=>b(e.target.value),className:"h-10 pl-8 pr-8 w-full",disabled:c,onFocus:()=>{v.current&&(f.current=v.current.selectionStart)}}),(0,s.jsx)(H.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground pointer-events-none"}),c&&(0,s.jsx)(m.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!c&&""!==o&&(0,s.jsx)(N.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>b("")})]}),e.getColumn("agent")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("agent")?.setFilterValue(""):e.getColumn("agent")?.setFilterValue(t)},value:e.getColumn("agent")?.getFilterValue()||"all",disabled:c||u,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter agent"}),e.getColumn("agent")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All Agents"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),e.getColumn("packageName")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("packageName")?.setFilterValue(""):e.getColumn("packageName")?.setFilterValue(t)},value:e.getColumn("packageName")?.getFilterValue()||"all",disabled:c||u,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter package"}),e.getColumn("packageName")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All Packages"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),e.getColumn("state")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("state")?.setFilterValue(""):e.getColumn("state")?.setFilterValue(t)},value:e.getColumn("state")?.getFilterValue()||"all",disabled:c||u,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter state"}),e.getColumn("state")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All States"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),j>0&&(0,s.jsxs)(d.E,{variant:"secondary",className:"rounded-sm px-1",children:[j," active ",1===j?"filter":"filters"]}),x&&(0,s.jsxs)(n.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),a&&a("")},className:"h-8 px-2 lg:px-3",disabled:c,children:["Reset",(0,s.jsx)(N.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(W.i,{table:e})]})}function ea({table:e,statuses:t,onSearch:a,onStatusChange:l,searchValue:i="",isFiltering:o=!1,isPending:c=!1,searchPlaceholder:u="Search by ID or Agent..."}){let g=e.getState().columnFilters.length>0,x=e.getState().columnFilters.length,h=(0,r.useRef)(null),p=(0,r.useRef)(null),j=t=>{h.current&&(p.current=h.current.selectionStart),a?a(t):e.getColumn("name")?.setFilterValue(t)};return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,s.jsx)(H.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(J.p,{ref:h,placeholder:u,value:i,onChange:e=>j(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:o,onFocus:()=>{h.current&&(p.current=h.current.selectionStart)}}),o&&(0,s.jsx)(m.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!o&&""!==i&&(0,s.jsx)(N.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>j("")})]}),e.getColumn("agent")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("agent")?.setFilterValue(""):e.getColumn("agent")?.setFilterValue(t)},value:e.getColumn("agent")?.getFilterValue()||"all",disabled:o||c,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter agent"}),e.getColumn("agent")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All Agents"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),e.getColumn("packageName")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("packageName")?.setFilterValue(""):e.getColumn("packageName")?.setFilterValue(t)},value:e.getColumn("packageName")?.getFilterValue()||"all",disabled:o||c,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter package"}),e.getColumn("packageName")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All Packages"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),e.getColumn("state")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("state")?.setFilterValue(""):e.getColumn("state")?.setFilterValue(t)},value:e.getColumn("state")?.getFilterValue()||"all",disabled:o||c,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter state"}),e.getColumn("state")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All States"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),x>0&&(0,s.jsxs)(d.E,{variant:"secondary",className:"rounded-sm px-1",children:[x," active ",1===x?"filter":"filters"]}),g&&(0,s.jsxs)(n.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),a&&a("")},className:"h-8 px-2 lg:px-3",disabled:o,children:["Reset",(0,s.jsx)(N.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(W.i,{table:e})]})}function es({table:e,statuses:t,onSearch:a,onStatusChange:l,searchValue:i="",isFiltering:o=!1,isPending:c=!1,searchPlaceholder:u="Search by ID or Agent..."}){let g=e.getState().columnFilters.length>0,x=e.getState().columnFilters.length,h=(0,r.useRef)(null),p=(0,r.useRef)(null),j=t=>{h.current&&(p.current=h.current.selectionStart),a?a(t):e.getColumn("name")?.setFilterValue(t)};return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,s.jsx)(H.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(J.p,{ref:h,placeholder:u,value:i,onChange:e=>j(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:o,onFocus:()=>{h.current&&(p.current=h.current.selectionStart)}}),o&&(0,s.jsx)(m.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!o&&""!==i&&(0,s.jsx)(N.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>j("")})]}),e.getColumn("agent")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("agent")?.setFilterValue(""):e.getColumn("agent")?.setFilterValue(t)},value:e.getColumn("agent")?.getFilterValue()||"all",disabled:o||c,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter agent"}),e.getColumn("agent")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All Agents"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),e.getColumn("packageName")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("packageName")?.setFilterValue(""):e.getColumn("packageName")?.setFilterValue(t)},value:e.getColumn("packageName")?.getFilterValue()||"all",disabled:o||c,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter package"}),e.getColumn("packageName")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All Packages"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),e.getColumn("state")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(M.l6,{onValueChange:t=>{l?l(t):"all"===t?e.getColumn("state")?.setFilterValue(""):e.getColumn("state")?.setFilterValue(t)},value:e.getColumn("state")?.getFilterValue()||"all",disabled:o||c,children:[(0,s.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(M.yv,{placeholder:"Filter state"}),e.getColumn("state")?.getFilterValue()&&(0,s.jsx)(d.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(M.gC,{children:[(0,s.jsx)(M.eb,{value:"all",children:"All States"}),t.map(e=>(0,s.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),x>0&&(0,s.jsxs)(d.E,{variant:"secondary",className:"rounded-sm px-1",children:[x," active ",1===x?"filter":"filters"]}),g&&(0,s.jsxs)(n.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),a&&a("")},className:"h-8 px-2 lg:px-3",disabled:o,children:["Reset",(0,s.jsx)(N.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(W.i,{table:e})]})}var el=a(42300),en=a(14583);a(52974),a(22918),a(97011),a(38605);var er=a(56090),ei=a(93772);function eo(){let e=(0,B.useRouter)(),t=(0,B.usePathname)(),a=(0,B.useSearchParams)(),{updateUrl:i}=(0,el.z)(),{toast:o}=(0,w.d)(),[c,d]=(0,r.useState)(!1),[u,m]=(0,r.useState)("inprogress"),[g,x]=(0,r.useState)({}),[h,p]=(0,r.useState)({}),[j,v]=(0,r.useState)(0),f=(0,r.useRef)(0),[b,N]=(0,r.useState)(!1),[y,C]=(0,r.useState)(!1),[S,A]=(0,r.useState)(!1),V=(0,r.useRef)(null);(0,r.useRef)(!0);let[D,R]=(0,r.useState)(()=>{let e=[],t=a.get("search");t&&("inprogress"===u||"historical"===u?e.push({id:"agent",value:t}):e.push({id:"packageName",value:t}));let s=a.get("status");return s&&e.push({id:"state",value:s}),e}),[T,E]=(0,r.useState)(()=>{let e=a.get("sort"),t=a.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[M,O]=(0,r.useState)(()=>{let e=a.get("page"),t=a.get("size"),s=t?Math.max(1,parseInt(t)):10;return console.log(`Initializing pagination from URL: page=${e}, size=${t}, pageSize=${s}`),{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:s}}),[_,K]=(0,r.useState)(a.get("search")??""),H=t.split("/")[1],U=function(e,t){let[a,s]=(0,r.useState)({});return(0,r.useRef)(null),a}(0),J=(0,r.useCallback)(()=>{let e={$top:M.pageSize,$skip:M.pageIndex*M.pageSize,$count:!0};if(T.length>0&&(e.$orderby=T.map(e=>`${e.id} ${e.desc?"desc":"asc"}`).join(",")),D.length>0){let t=D.filter(e=>"string"!=typeof e.value||""!==e.value).map(e=>{let t=e.id,a=e.value;return"string"==typeof a?"agent"===t&&a||"packageName"===t&&a?`contains(tolower(botAgentName), '${a.toLowerCase()}')`:"state"===t&&a?`state eq '${a}'`:`contains(tolower(${t}), '${a.toLowerCase()}')`:Array.isArray(a)?a.map(e=>`${t} eq '${e}'`).join(" or "):""}).filter(Boolean);t.length>0&&(e.$filter=t.join(" and "))}let t="";switch(u){case"inprogress":t="status eq 'Running' or status eq 'Pending'";break;case"sheduled":t="status eq 'Scheduled'";break;case"historical":t="status eq 'Completed' or status eq 'Failed' or status eq 'Cancelled'"}return t&&(e.$filter=e.$filter?`(${e.$filter}) and (${t})`:t),e},[M,T,D,u])(),W=(0,r.useMemo)(()=>q.DC.executionsWithOData(J),[J]),{data:X,error:Q,isLoading:Y,mutate:Z}=(0,L.Ay)(W,()=>(0,k.bF)(J),{dedupingInterval:0,revalidateOnFocus:!1,revalidateIfStale:!1,keepPreviousData:!1}),{data:ee}=(0,L.Ay)(X?.value?.length===0||Q?q.DC.executions():null,k.vH),eo=Y||Q&&!ee,ec=(0,r.useCallback)(e=>{let t=U[e.id],a=t?.status||e.status,s=e=>(0,P.Ej)(e,{fallback:""}),l=s(e.startTime),n=s(e.endTime);return console.log("Execution transformation:",{id:e.id,startTime:e.startTime,endTime:e.endTime,formattedStartTime:l,formattedEndTime:n}),{id:e.id,Version:e.packageVersion||"",Agent:e.botAgentName||"",State:a,"Start Time":l,"End Time":n,Source:"Manual",Command:"execute",Schedules:"Once","Task Id":e.id,"Created Date":(0,P.Ej)(e.startTime,{dateStyle:"medium",timeStyle:void 0,fallback:""}),"Created By":"Current User",name:e.packageName||"",type:"execution",value:e.packageVersion||"",createdBy:"Current User",label:e.botAgentName||"",status:a,agent:e.botAgentName||"",state:a,startTime:l,endTime:n,source:"Manual",command:"execute",schedules:"Once",taskId:e.id,createdDate:(0,P.Ej)(e.startTime,{dateStyle:"medium",timeStyle:void 0,fallback:""}),packageName:e.packageName||"",hasLogs:e.hasLogs||!1}},[U]),ed=(0,r.useMemo)(()=>$({onDeleted:()=>Z()}),[Z]),eu=(0,r.useMemo)(()=>F({onDeleted:()=>Z()}),[Z]),em=(0,r.useMemo)(()=>{if(ee&&(!X?.value||0===X.value.length)){console.log("Using fallback data with pagination:",M);let e=ee.map(e=>ec(e));e=e.filter(e=>"inprogress"===u?"Running"===e.state||"Pending"===e.state:"sheduled"===u?"Scheduled"===e.state:"historical"!==u||"Completed"===e.state||"Failed"===e.state||"Cancelled"===e.state),_&&(e=e.filter(e=>e.agent&&e.agent.toLowerCase().includes(_.toLowerCase())));let t=D.find(e=>"state"===e.id)?.value;t&&(e=e.filter(e=>e.state===t));let a=e.length,s=M.pageIndex*M.pageSize,l=s+M.pageSize;console.log(`Slicing fallback data from ${s} to ${l} out of ${e.length} items`);let n=e.slice(s,l);return console.log(`Returning ${n.length} items from fallback data`),j!==a&&setTimeout(()=>{v(a),f.current=a},0),n}return X?.value?(console.log(`Returning ${X.value.length} items from OData response`),X.value.map(e=>ec(e))):(console.log("No data available from OData or fallback"),[])},[X,ee,ec,u,_,D,M,j]),eg=(0,r.useMemo)(()=>em,[em]),ex=e=>e+1,eh=(e,t)=>Math.max(1,Math.ceil(e/t)),ep=(0,r.useMemo)(()=>{let e=eh(j,M.pageSize),t=eg.length===M.pageSize&&j<=M.pageSize*(M.pageIndex+1),a=ex(M.pageIndex);return t?Math.max(a,e,M.pageIndex+2):Math.max(a,e)},[M.pageSize,M.pageIndex,eg.length,j]),ej=(0,r.useMemo)(()=>!S&&eg.length===M.pageSize,[S,eg.length,M.pageSize]),ev=(0,er.N4)({data:eg,columns:"inprogress"===u?ed:"sheduled"===u?I:eu,state:{sorting:T,columnVisibility:h,rowSelection:g,columnFilters:D,pagination:M},enableRowSelection:!0,onRowSelectionChange:x,onSortingChange:e=>{let a="function"==typeof e?e(T):e;E(a),a.length>0?i(t,{sort:a[0].id,order:a[0].desc?"desc":"asc",page:"1"}):i(t,{sort:null,order:null,page:"1"}),Z()},onColumnFiltersChange:R,onColumnVisibilityChange:p,onPaginationChange:e=>{console.log("Pagination change triggered");let a="function"==typeof e?e(M):e;console.log("Current pagination:",M,"New pagination:",a),O(a),i(t,{page:(a.pageIndex+1).toString(),size:a.pageSize.toString()}),console.log("Forcing data reload for pagination change"),Z()},getCoreRowModel:(0,ei.HT)(),getFilteredRowModel:(0,ei.hM)(),getPaginationRowModel:(0,ei.kW)(),getSortedRowModel:(0,ei.h5)(),getFacetedRowModel:(0,ei.kQ)(),getFacetedUniqueValues:(0,ei.oS)(),manualPagination:!0,pageCount:ep,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.id}),ef=(0,r.useCallback)(e=>{K(e),N(!0),V.current&&clearTimeout(V.current),V.current=setTimeout(()=>{let a="sheduled"===u?"packageName":"agent",s=ev.getColumn(a);s&&(s.setFilterValue(e),i(t,{search:e||null,page:"1"}),Z()),N(!1)},500)},[ev,i,t,u,Z]),eb=(0,r.useCallback)(e=>{let a=ev.getColumn("state");if(a){let s="all"===e?"":e;a.setFilterValue(s),i(t,{status:s||null,page:"1"})}},[ev,i,t]),eN=(0,r.useCallback)(e=>{e&&Z(t=>{if(!t)return t;if("value"in t&&Array.isArray(t.value)){let a={id:e.id,packageName:e.packageName,botAgentName:e.botAgentName,status:"Pending",startTime:new Date().toISOString(),endTime:void 0,packageVersion:void 0,errorMessage:void 0,logOutput:void 0,botAgentId:"",packageId:""};return{...t,value:[a,...t.value],"@odata.count":(t["@odata.count"]||0)+1}}return t},!1),setTimeout(()=>{Z()},2e3)},[Z]),ey=a=>{let s=t.startsWith("/admin")?`/admin/executions/${a.id}`:`/${H}/executions/${a.id}`;e.push(s)},eC=e=>{m(e),O(e=>({...e,pageIndex:0})),i(t,{page:"1"}),Z()};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,s.jsx)("div",{className:"mb-4 border-b w-full",children:(0,s.jsxs)("nav",{className:"flex space-x-8","aria-label":"Tabs",children:[(0,s.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"inprogress"===u,type:"button",onClick:()=>eC("inprogress"),children:"In Progress"}),(0,s.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"sheduled"===u,type:"button",onClick:()=>eC("sheduled"),children:"Scheduled"}),(0,s.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"historical"===u,type:"button",onClick:()=>eC("historical"),children:"Historical"})]})}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Executions"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[j>0&&(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,s.jsxs)("span",{children:["Total: ",j," execution",1!==j?"s":""]})}),"inprogress"===u&&(0,s.jsxs)(n.$,{onClick:()=>{d(!0)},className:"flex items-center justify-center",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Create Execution"]})]})]}),Q&&!ee&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,s.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load executions. Please try again."}),(0,s.jsx)(n.$,{variant:"outline",className:"mt-2",onClick:()=>Z(),children:"Retry"})]}),"inprogress"===u&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(ea,{table:ev,statuses:[{value:"Running",label:"Running"},{value:"Pending",label:"Pending"}],onSearch:ef,onStatusChange:eb,searchValue:_,isFiltering:eo,isPending:b}),(0,s.jsx)(z.b,{data:eg,columns:ed,onRowClick:ey,table:ev,isLoading:eo,totalCount:j})]}),"sheduled"===u&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(es,{table:ev,statuses:[{value:"Scheduled",label:"Scheduled"}],onSearch:ef,onStatusChange:eb,searchValue:_,isFiltering:eo,isPending:b}),(0,s.jsx)(z.b,{data:eg,columns:I,onRowClick:ey,table:ev,isLoading:eo,totalCount:j})]}),"historical"===u&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(et,{table:ev,statuses:[{value:"Completed",label:"Completed"},{value:"Failed",label:"Failed"},{value:"Cancelled",label:"Cancelled"}],onSearch:ef,onStatusChange:eb,searchValue:_,isFiltering:eo,isPending:b}),(0,s.jsx)(z.b,{data:eg,columns:eu,onRowClick:ey,table:ev,isLoading:eo,totalCount:j})]}),(0,s.jsx)(en.d,{currentPage:M.pageIndex+1,pageSize:M.pageSize,totalCount:j,totalPages:ep,isLoading:eo,isChangingPageSize:y,isUnknownTotalCount:ej,onPageChange:e=>{console.log(`Page change requested to page ${e}`),O(t=>({...t,pageIndex:e-1})),i(t,{page:e.toString()}),console.log("Reloading data after page change"),setTimeout(()=>{Z()},0)},onPageSizeChange:e=>{console.log(`Page size change requested to ${e}`),C(!0);let a=Math.floor(M.pageIndex*M.pageSize/e);O({pageSize:e,pageIndex:a}),i(t,{size:e.toString(),page:(a+1).toString()}),console.log("Reloading data after page size change"),setTimeout(()=>{Z()},0)}})]}),(0,s.jsx)(G,{isOpen:c,onClose:()=>d(!1),onSuccess:eN})]})}T.z.object({id:T.z.string(),name:T.z.string(),type:T.z.string(),value:T.z.string(),createdBy:T.z.string(),label:T.z.string(),status:T.z.string(),Version:T.z.string().optional(),Agent:T.z.string().optional(),State:T.z.string().optional(),"Start Time":T.z.string().optional(),"End Time":T.z.string().optional(),Source:T.z.string().optional(),Command:T.z.string().optional(),Schedules:T.z.string().optional(),"Task Id":T.z.string().optional(),"Created Date":T.z.string().optional(),"Created By":T.z.string().optional(),agent:T.z.string().optional(),state:T.z.string().optional(),startTime:T.z.string().optional(),endTime:T.z.string().optional(),source:T.z.string().optional(),command:T.z.string().optional(),schedules:T.z.string().optional(),taskId:T.z.string().optional(),createdDate:T.z.string().optional(),packageName:T.z.string().optional(),hasLogs:T.z.boolean().optional()})},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var s=a(60687);a(43210);var l=a(61170),n=a(36966);function r({className:e,...t}){return(0,s.jsx)(l.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80462:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},83111:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r,metadata:()=>n});var s=a(37413),l=a(48587);let n={title:"Automation",description:"Agent management page"};function r(){return(0,s.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,s.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,s.jsx)(l.default,{})]})}},90631:(e,t,a)=>{"use strict";a.d(t,{RT:()=>n,bF:()=>i,iX:()=>c,vH:()=>r});var s=a(51787);let l=()=>"default",n=async e=>{let t=l();return await s.F.post(`${t}/api/executions/trigger`,e)},r=async()=>{let e=l();try{return await s.F.get(`${e}/api/executions`)}catch(e){return console.error("Error fetching all executions:",e),[]}},i=async e=>{let t=l(),a={...e};(void 0===a.$top||a.$top<=0)&&(a.$top=10);let n=new Date().getTime(),r=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(a),i=`${t}/odata/Executions`;r?i+=`?${r}&_t=${n}`:i+=`?_t=${n}`,console.log(`Fetching executions with endpoint: ${i}`),console.log(`Page: ${a.$skip?a.$skip/a.$top+1:1}, Size: ${a.$top}`);try{let e=await s.F.get(i),t=function(e){return e&&"object"==typeof e?{value:Array.isArray(e.value)?e.value:[],"@odata.count":"number"==typeof e["@odata.count"]?e["@odata.count"]:void 0,"@odata.nextLink":"string"==typeof e["@odata.nextLink"]?e["@odata.nextLink"]:void 0}:{value:[]}}(e);return a.$top&&t.value.length>a.$top&&(console.warn(`OData returned ${t.value.length} items but only ${a.$top} were requested. Trimming results.`),t.value=t.value.slice(0,a.$top)),console.log(`Received ${t.value.length} executions from OData`),t}catch(e){return console.error("Error fetching executions with OData:",e),{value:[]}}},o=async e=>{let t=l();return await s.F.get(`${t}/api/executions/${e}/logs/download`)},c=async(e,t)=>{try{let{downloadUrl:a}=await o(e),s=t||`execution_${e}_logs.log`;try{let e=await fetch(a,{method:"GET",headers:{Accept:"application/octet-stream"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let t=await e.blob(),l=window.URL.createObjectURL(t),n=document.createElement("a");n.href=l,n.download=s,n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(l)}catch(t){console.warn("Fetch method failed, falling back to direct link:",t);let e=document.createElement("a");e.href=a,e.download=s,e.target="_blank",e.rel="noopener noreferrer",e.style.display="none",document.body.appendChild(e),e.click(),document.body.removeChild(e)}}catch(e){throw console.error("Error downloading execution logs:",e),e}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,7966,5584,5156,4654,6467,5880,1694,6945,8759,7943,3437,1637,4503,6763,519,4881,6295],()=>a(64993));module.exports=s})();