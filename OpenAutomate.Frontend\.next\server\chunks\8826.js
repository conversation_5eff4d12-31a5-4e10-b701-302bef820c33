"use strict";exports.id=8826,exports.ids=[8826],exports.modules={10590:(e,t,s)=>{s.d(t,{Header:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\layout\\header.tsx","Header")},11365:(e,t,s)=>{s.d(t,{F:()=>M});var a=s(85397),r=s(11860),n=s(41862),o=s(47033),i=s(14952),l=s(96362),d=s(84027),c=s(85778),u=s(81904),m=s(96474),x=s(65668),f=s(58869),h=s(70334),p=s(47282),g=s(29104),v=s(363),j=s(34410),b=s(62157),N=s(72575),w=s(13964),y=s(78464),A=s(10022),k=s(9005),C=s(97840),F=s(36058),z=s(32192),_=s(24541),L=s(22915),I=s(40083),$=s(78122),H=s(45547),S=s(52069),U=s(41550);let M={logo:a.A,close:r.A,Spinner:n.A,chevronLeft:o.A,chevronRight:i.A,trash:l.A,settings:d.A,billing:c.A,ellipsis:u.A,add:m.A,warning:x.A,user:f.A,arrowRight:h.A,help:x.A,pizza:p.A,sun:g.A,moon:v.A,laptop:j.A,gitHub:b.A,twitter:N.A,check:w.A,file:y.A,fileText:A.A,image:k.A,play:C.A,pause:F.A,home:z.A,chart:_.A,cog:L.A,logout:I.A,refresh:$.A,about:H.A,guide:S.A,contact:U.A}},21342:(e,t,s)=>{s.d(t,{I:()=>c,SQ:()=>d,_2:()=>u,hO:()=>m,lp:()=>x,mB:()=>f,rI:()=>i,ty:()=>l});var a=s(60687);s(43210);var r=s(4654),n=s(13964),o=s(36966);function i({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dropdown-menu",...e})}function l({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...e})}function d({className:e,sideOffset:t=4,...s}){return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function c({...e}){return(0,a.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...e})}function u({className:e,inset:t,variant:s="default",...n}){return(0,a.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function m({className:e,children:t,checked:s,...i}){return(0,a.jsxs)(r.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:s,...i,children:[(0,a.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),t]})}function x({className:e,inset:t,...s}){return(0,a.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...s})}function f({className:e,...t}){return(0,a.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},64147:(e,t,s)=>{s.d(t,{Header:()=>k});var a=s(60687),r=s(85814),n=s.n(r),o=s(16189),i=s(36966);function l({user:e,...t}){let s=(0,o.usePathname)();return(0,a.jsx)("div",{className:"mr-4 hidden md:flex",...t,children:(0,a.jsx)("nav",{className:"flex items-center space-x-6 text-sm font-medium",children:[{title:"About Us",href:"/about"},{title:"Guides",href:"/guide"},{title:"Contact Us",href:"/contact"}].map(({title:e,href:t})=>(0,a.jsx)(n(),{href:t,className:(0,i.cn)("relative px-2 py-1.5 transition-all duration-200 hover:text-orange-600",s===t?"text-orange-600 font-semibold after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:bg-orange-600 after:rounded-full":"text-foreground/60 hover:text-orange-600"),children:e},t))})})}var d=s(43210),c=s(11365),u=s(29523),m=s(63756);function x({className:e,children:t,...s}){return(0,a.jsxs)(m.bL,{"data-slot":"scroll-area",className:(0,i.cn)("relative",e),...s,children:[(0,a.jsx)(m.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:t}),(0,a.jsx)(f,{}),(0,a.jsx)(m.OK,{})]})}function f({className:e,orientation:t="vertical",...s}){return(0,a.jsx)(m.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,i.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...s,children:(0,a.jsx)(m.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}var h=s(67146);function p({user:e}){let t=(0,o.usePathname)(),[s,r]=d.useState(!1),l=[{title:"About Us",href:"/about",icon:(0,a.jsx)(c.F.about,{className:"mr-2 h-4 w-4"}),requiresAuth:!0},{title:"Guides",href:"/guide",icon:(0,a.jsx)(c.F.guide,{className:"mr-2 h-4 w-4"}),requiresAuth:!0},{title:"Contact Us",href:"/contact",icon:(0,a.jsx)(c.F.contact,{className:"mr-2 h-4 w-4"}),requiresAuth:!0}];return(0,a.jsxs)(h.cj,{open:s,onOpenChange:r,children:[(0,a.jsx)(h.CG,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-orange-600/10 hover:text-orange-600 focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden",children:[(0,a.jsxs)("svg",{strokeWidth:"1.5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",children:[(0,a.jsx)("path",{d:"M3 5H11",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M3 12H16",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M3 19H21",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,a.jsxs)(h.h,{side:"left",className:"pr-0",children:[(0,a.jsx)("div",{className:"px-7",children:(0,a.jsx)(n(),{href:"/",className:"flex items-center text-lg font-bold hover:text-orange-600 transition-colors",onClick:()=>r(!1),children:"OpenAutomate"})}),(0,a.jsx)(x,{className:"my-4 h-[calc(100vh-8rem)] pb-10 pl-6",children:(0,a.jsx)("div",{className:"pl-1 pr-7",children:(0,a.jsxs)("nav",{className:"flex flex-col space-y-2",children:[l.map(({title:e,href:s,icon:o})=>(0,a.jsxs)(n(),{href:s,onClick:()=>r(!1),className:(0,i.cn)("flex items-center rounded-md px-3 py-2 text-sm font-medium transition-all duration-200",t===s?"bg-orange-600/10 text-orange-600":"hover:bg-orange-600/10 hover:text-orange-600"),children:[o,e]},s)),!e&&(0,a.jsxs)(n(),{href:"/login",onClick:()=>r(!1),className:"flex items-center rounded-md px-3 py-2 text-sm font-medium mt-4 bg-orange-600 text-white hover:bg-orange-700 transition-colors",children:[(0,a.jsx)(c.F.user,{className:"mr-2 h-4 w-4"}),"Sign In"]})]})})})]})]})}var g=s(75139),v=s(21342),j=s(75593);function b({className:e,...t}){return(0,a.jsx)(j.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function N({className:e,...t}){return(0,a.jsx)(j._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",e),...t})}function w({className:e,...t}){return(0,a.jsx)(j.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var y=s(86522);function A({user:e}){let{logout:t}=(0,y.A)(),s=(0,o.useParams)(),r=s?.tenant;if(!e)return null;let i=e=>r?`/${r}${e}`:"/tenant-selector";return(0,a.jsxs)(v.rI,{children:[(0,a.jsx)(v.ty,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsxs)(b,{className:"h-8 w-8",children:[(0,a.jsx)(N,{src:"/avatars/01.png",alt:e.firstName}),(0,a.jsx)(w,{children:e?`${e.firstName?.[0]||""}${e.lastName?.[0]||""}`.toUpperCase():"??"})]})})}),(0,a.jsxs)(v.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,a.jsx)(v.lp,{className:"font-normal",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsxs)("p",{className:"text-sm font-medium leading-none",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:e.email})]})}),(0,a.jsx)(v.mB,{}),(0,a.jsxs)(v.I,{children:[(0,a.jsx)(v._2,{asChild:!0,children:(0,a.jsxs)(n(),{href:i("/tenant-selector"),children:[(0,a.jsx)(c.F.home,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Dashboard"})]})}),(0,a.jsx)(v._2,{asChild:!0,children:(0,a.jsxs)(n(),{href:i("/profile"),children:[(0,a.jsx)(c.F.user,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Profile"})]})}),(0,a.jsx)(v._2,{asChild:!0,children:(0,a.jsxs)(n(),{href:i("/settings"),children:[(0,a.jsx)(c.F.settings,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Settings"})]})}),(0,a.jsx)(v._2,{asChild:!0,children:(0,a.jsxs)(n(),{href:"/tenant-selector",children:[(0,a.jsx)(c.F.settings,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Switch Organization"})]})})]}),(0,a.jsx)(v.mB,{}),(0,a.jsxs)(v._2,{onClick:()=>t(),className:"cursor-pointer",children:[(0,a.jsx)(c.F.logout,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Log out"})]})]})]})}function k(){let{user:e,isAuthenticated:t}=(0,y.A)();return(0,a.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background backdrop-blur supports-[backdrop-filter]:bg-background",children:(0,a.jsxs)("div",{className:"container flex h-16 items-center",children:[(0,a.jsx)(n(),{href:"/",className:"flex items-center space-x-2",children:(0,a.jsx)("span",{className:" font-bold text-xl text-orange-600",children:"OpenAutomate"})}),(0,a.jsx)("div",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",children:(0,a.jsx)(l,{user:e})}),(0,a.jsx)(p,{user:e}),(0,a.jsx)("div",{className:"flex flex-1 items-center justify-end space-x-4",children:(0,a.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.U,{}),t?(0,a.jsx)(A,{user:e}):(0,a.jsx)(n(),{href:"/login",children:(0,a.jsx)(u.$,{variant:"outline",size:"sm",className:"transition-all duration-300 hover:translate-y-[-2px]",children:"Sign In"})})]})})]})})}},67146:(e,t,s)=>{s.d(t,{CG:()=>l,Fm:()=>m,Qs:()=>f,cj:()=>i,h:()=>u,qp:()=>x});var a=s(60687);s(43210);var r=s(88562),n=s(11860),o=s(36966);function i({...e}){return(0,a.jsx)(r.bL,{"data-slot":"sheet",...e})}function l({...e}){return(0,a.jsx)(r.l9,{"data-slot":"sheet-trigger",...e})}function d({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"sheet-portal",...e})}function c({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"sheet-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,side:s="right",...i}){return(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(r.UC,{"data-slot":"sheet-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...i,children:[t,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(n.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,o.cn)("flex flex-col gap-1.5 p-4",e),...t})}function x({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"sheet-title",className:(0,o.cn)("text-foreground font-semibold",e),...t})}function f({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"sheet-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}},71669:(e,t,s)=>{s.d(t,{C5:()=>v,MJ:()=>p,Rr:()=>g,eI:()=>f,lR:()=>h,lV:()=>d,zB:()=>u});var a=s(60687),r=s(43210),n=s(11329),o=s(27605),i=s(36966),l=s(80013);let d=o.Op,c=r.createContext({}),u=({...e})=>(0,a.jsx)(c.Provider,{value:{name:e.name},children:(0,a.jsx)(o.xI,{...e})}),m=()=>{let e=r.useContext(c),t=r.useContext(x),{getFieldState:s}=(0,o.xW)(),a=(0,o.lN)({name:e.name}),n=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}},x=r.createContext({});function f({className:e,...t}){let s=r.useId();return(0,a.jsx)(x.Provider,{value:{id:s},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",e),...t})})}function h({className:e,...t}){let{error:s,formItemId:r}=m();return(0,a.jsx)(l.J,{"data-slot":"form-label","data-error":!!s,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:r,...t})}function p({...e}){let{error:t,formItemId:s,formDescriptionId:r,formMessageId:o}=m();return(0,a.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":t?`${r} ${o}`:`${r}`,"aria-invalid":!!t,...e})}function g({className:e,...t}){let{formDescriptionId:s}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function v({className:e,...t}){let{error:s,formMessageId:r}=m(),n=s?String(s?.message??""):t.children;return n?(0,a.jsx)("p",{"data-slot":"form-message",id:r,className:(0,i.cn)("text-destructive text-sm",e),...t,children:n}):null}},75139:(e,t,s)=>{s.d(t,{U:()=>l});var a=s(60687);s(43210);var r=s(10218),n=s(29523),o=s(11365),i=s(21342);function l(){let{setTheme:e}=(0,r.D)();return(0,a.jsxs)(i.rI,{children:[(0,a.jsx)(i.ty,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 px-0",children:[(0,a.jsx)(o.F.sun,{className:"rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(o.F.moon,{className:"absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,a.jsxs)(i.SQ,{align:"end",children:[(0,a.jsxs)(i._2,{onClick:()=>e("light"),children:[(0,a.jsx)(o.F.sun,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Light"})]}),(0,a.jsxs)(i._2,{onClick:()=>e("dark"),children:[(0,a.jsx)(o.F.moon,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Dark"})]}),(0,a.jsxs)(i._2,{onClick:()=>e("system"),children:[(0,a.jsx)(o.F.laptop,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"System"})]})]})]})}},80013:(e,t,s)=>{s.d(t,{J:()=>o});var a=s(60687);s(43210);var r=s(61170),n=s(36966);function o({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},89667:(e,t,s)=>{s.d(t,{p:()=>n});var a=s(60687);s(43210);var r=s(36966);function n({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},91821:(e,t,s)=>{s.d(t,{Fc:()=>l,TN:()=>c,XL:()=>d});var a=s(60687),r=s(43210),n=s(24224),o=s(36966);let i=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),l=r.forwardRef(({className:e,variant:t,...s},r)=>(0,a.jsx)("div",{ref:r,role:"alert",className:(0,o.cn)(i({variant:t}),e),...s}));l.displayName="Alert";let d=r.forwardRef(({className:e,children:t,...s},r)=>(t||console.warn("AlertTitle must have content for accessibility"),(0,a.jsx)("h5",{ref:r,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",e),...s,children:t})));d.displayName="AlertTitle";let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",e),...t}));c.displayName="AlertDescription"}};