exports.id=7288,exports.ids=[7288],exports.modules={6091:(e,t,a)=>{"use strict";a.d(t,{HF:()=>n,S8:()=>o,eV:()=>i,gO:()=>l});var r=a(51787);let s=()=>"",i={getAvailableResources:async()=>{let e=s();return(0,r.fetchApi)(`${e}/api/author/resources`)},getAllRoles:async()=>{let e=s();return(0,r.fetchApi)(`${e}/api/author/authorities`)},getRoleById:async e=>{let t=s();return(0,r.fetchApi)(`${t}/api/author/authority/${e}`)},createRole:async e=>{let t=s();return(0,r.fetchApi)(`${t}/api/author/authority`,{method:"POST"},e)},updateRole:async(e,t)=>{let a=s();return(0,r.fetchApi)(`${a}/api/author/authority/${e}`,{method:"PUT"},t)},deleteRole:async e=>{let t=s();return(0,r.fetchApi)(`${t}/api/author/authority/${e}`,{method:"DELETE"})},getUserAuthorities:async e=>{let t=s();return(0,r.fetchApi)(`${t}/api/author/user/${e}`)},assignRoleToUser:async(e,t)=>{let a=s();return(0,r.fetchApi)(`${a}/api/author/user/${e}`,{method:"POST"},{authorityId:t})},removeRoleFromUser:async(e,t)=>{let a=s();return(0,r.fetchApi)(`${a}/api/author/user/${e}/authority/${t}`,{method:"DELETE"})}},n={NO_ACCESS:0,VIEW:1,CREATE:2,UPDATE:3,DELETE:4},l=e=>{switch(e){case n.NO_ACCESS:return"No Access";case n.VIEW:return"View Only";case n.CREATE:return"View & Create";case n.UPDATE:return"View, Create & Update (includes Execute)";case n.DELETE:return"Full Administrative Access";default:return"Invalid Permission"}},o=e=>e>=n.NO_ACCESS&&e<=n.DELETE},31599:(e,t,a)=>{"use strict";a.d(t,{c:()=>o});var r=a(43210),s=a(39989),i=a(16189),n=a(31207),l=a(70891);function o(){let e=(0,i.useRouter)(),{data:t,error:a,isLoading:o,mutate:d}=(0,n.Ay)(l.DC.organizationUnits(),()=>s.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:o,error:a?"Failed to fetch organization units. Please try again later.":null,refresh:d,selectOrganizationUnit:(0,r.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},39989:(e,t,a)=>{"use strict";a.d(t,{K:()=>s});var r=a(51787);let s={getMyOrganizationUnits:async()=>await r.F.get("/api/ou/my-ous"),getBySlug:async e=>await r.F.get(`/api/ou/slug/${e}`),getById:async e=>await r.F.get(`/api/ou/${e}`),create:async e=>await r.F.post("/api/ou/create",e),update:async(e,t)=>await r.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await r.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await r.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await r.F.get(`/api/ou/${e}/deletion-status`)}},61018:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},61170:(e,t,a)=>{"use strict";a.d(t,{b:()=>c});var r=a(43210);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}a(51215);var i=a(60687),n=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===n}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...i}=e;if(r.isValidElement(a)){var n;let e,l;let o=(n=a,(l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(l=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),d=function(e,t){let a={...t};for(let r in t){let s=e[r],i=t[r];/^on[A-Z]/.test(r)?s&&i?a[r]=(...e)=>{i(...e),s(...e)}:s&&(a[r]=s):"style"===r?a[r]={...s,...i}:"className"===r&&(a[r]=[s,i].filter(Boolean).join(" "))}return{...e,...a}}(i,a.props);return a.type!==r.Fragment&&(d.ref=t?function(...e){return t=>{let a=!1,r=e.map(e=>{let r=s(e,t);return a||"function"!=typeof r||(a=!0),r});if(a)return()=>{for(let t=0;t<r.length;t++){let a=r[t];"function"==typeof a?a():s(e[t],null)}}}}(t,o):o),r.cloneElement(a,d)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=r.forwardRef((e,a)=>{let{children:s,...n}=e,o=r.Children.toArray(s),d=o.find(l);if(d){let e=d.props.children,s=o.map(t=>t!==d?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...n,ref:a,children:r.isValidElement(e)?r.cloneElement(e,void 0,s):null})}return(0,i.jsx)(t,{...n,ref:a,children:s})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),n=r.forwardRef((e,r)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?a:t,{...n,ref:r})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),d=r.forwardRef((e,t)=>(0,i.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>p,Es:()=>f,HM:()=>u,L3:()=>x,c7:()=>h,lG:()=>o,rr:()=>g,zM:()=>d});var r=a(60687),s=a(43210),i=a(88562),n=a(11860),l=a(36966);let o=i.bL,d=i.l9,c=i.ZL,u=i.bm,m=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(i.hJ,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));m.displayName=i.hJ.displayName;let p=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(c,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(i.UC,{ref:s,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...a,children:[t,(0,r.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=i.UC.displayName;let h=({className:e,...t})=>(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});h.displayName="DialogHeader";let f=({className:e,...t})=>(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});f.displayName="DialogFooter";let x=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(i.hE,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=i.hE.displayName;let g=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(i.VY,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=i.VY.displayName},64656:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(37413),s=a(48974),i=a(31057),n=a(50417),l=a(92588),o=a(61018),d=a(2505);function c({children:e}){return(0,r.jsx)(o.TenantGuard,{children:(0,r.jsx)(d.ChatProvider,{children:(0,r.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,r.jsxs)(n.SidebarProvider,{className:"flex flex-col",children:[(0,r.jsx)(i.SiteHeader,{}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)(s.AppSidebar,{}),(0,r.jsx)(n.SidebarInset,{children:(0,r.jsx)(l.SearchProvider,{children:(0,r.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>o});var r=a(60687),s=a(43210),i=a(16189),n=a(31599),l=a(31568);function o({children:e}){let{tenant:t}=(0,i.useParams)();(0,i.useRouter)();let{isAuthenticated:a,isLoading:o}=(0,l.A)(),{organizationUnits:d,isLoading:c}=(0,n.c)(),[u,m]=(0,s.useState)(!0);return o||c||u?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,r.jsx)(r.Fragment,{children:e})}},72826:(e,t,a)=>{Promise.resolve().then(a.bind(a,69231)),Promise.resolve().then(a.bind(a,83847)),Promise.resolve().then(a.bind(a,78526)),Promise.resolve().then(a.bind(a,97597)),Promise.resolve().then(a.bind(a,98641)),Promise.resolve().then(a.bind(a,80110))},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var r=a(60687);a(43210);var s=a(61170),i=a(36966);function n({className:e,...t}){return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80391:(e,t,a)=>{"use strict";a.d(t,{I:()=>v});var r=a(60687),s=a(43210),i=a(29523),n=a(63503),l=a(80013),o=a(89667),d=a(36966);let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("textarea",{className:(0,d.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));c.displayName="Textarea";var u=a(15079),m=a(96834),p=a(88233),h=a(20140),f=a(6091),x=a(31207),g=a(70891);function v({isOpen:e,onClose:t,editingRole:a}){let{toast:d}=(0,h.d)(),[v,y]=(0,s.useState)(""),[b,j]=(0,s.useState)(""),[N,E]=(0,s.useState)(!1),{data:w,error:C,isLoading:A}=(0,x.Ay)(e?g.DC.availableResources():null,f.eV.getAvailableResources),[S,R]=(0,s.useState)(a?.name??""),[P,F]=(0,s.useState)(a?.description??""),[$,D]=(0,s.useState)(()=>a?.permissions&&w?a.permissions.map(e=>{let t=w.find(t=>t.resourceName===e.resourceName);return{resourceName:e.resourceName,permission:e.permission,displayName:t?.displayName??e.resourceName}}):[]),T=e=>{D(t=>t.filter(t=>t.resourceName!==e))},V=()=>S.trim()?S.length<2||S.length>50?"Role name must be between 2 and 50 characters.":P.length>200?"Description cannot exceed 200 characters.":null:"Role name is required.",O=e=>e&&"object"==typeof e&&"message"in e?e.message.includes("already exists")?`A role with the name "${S}" already exists. Please choose a different name.`:e.message.includes("validation")?"Please check your input and try again.":e.message.includes("permission")?"You do not have permission to perform this action.":e.message:"Failed to save role. Please try again.",k=async()=>{let e={name:S.trim(),description:P.trim(),resourcePermissions:$.map(e=>({resourceName:e.resourceName,permission:e.permission}))};a?(console.log("Updating role with data:",e),await f.eV.updateRole(a.id,e),d({title:"Success",description:"Role updated successfully."})):(console.log("Creating role with data:",e),await f.eV.createRole(e),d({title:"Success",description:"Role created successfully."}))},U=async e=>{e.preventDefault();let r=V();if(r){d({title:"Validation Error",description:r,variant:"destructive"});return}E(!0);try{await k(),t(!0)}catch(e){console.error("Failed to save role:",e),d({title:a?"Update Failed":"Creation Failed",description:O(e),variant:"destructive"})}finally{E(!1)}},L=w?.filter(e=>!$.some(t=>t.resourceName===e.resourceName))??[];return(0,r.jsx)(n.lG,{open:e,onOpenChange:()=>t(),children:(0,r.jsx)(n.Cf,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:(0,r.jsxs)("form",{onSubmit:U,children:[(0,r.jsx)(n.c7,{children:(0,r.jsx)(n.L3,{children:a?"Edit Role":"Create New Role"})}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(l.J,{htmlFor:"roleName",children:"Name *"}),(0,r.jsx)(o.p,{id:"roleName",value:S,onChange:e=>R(e.target.value),placeholder:"Enter role name",maxLength:50,required:!0}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:[S.length,"/50 characters"]})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(l.J,{htmlFor:"roleDescription",children:"Description"}),(0,r.jsx)(c,{id:"roleDescription",value:P,onChange:e=>F(e.target.value),placeholder:"Enter role description (optional)",maxLength:200,rows:3}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:[P.length,"/200 characters"]})]}),(0,r.jsxs)("div",{className:"grid gap-4",children:[(0,r.jsx)(l.J,{children:"Resource Permissions"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,r.jsx)("div",{children:(0,r.jsxs)(u.l6,{value:v,onValueChange:y,children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select resource"})}),(0,r.jsx)(u.gC,{children:A?(0,r.jsx)(u.eb,{value:"loading",disabled:!0,children:"Loading..."}):L.map(e=>(0,r.jsx)(u.eb,{value:e.resourceName,children:e.displayName},e.resourceName))})]})}),(0,r.jsx)("div",{children:(0,r.jsxs)(u.l6,{value:b,onValueChange:j,children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Permission level"})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:f.HF.NO_ACCESS.toString(),children:(0,f.gO)(f.HF.NO_ACCESS)}),(0,r.jsx)(u.eb,{value:f.HF.VIEW.toString(),children:(0,f.gO)(f.HF.VIEW)})," ",(0,r.jsx)(u.eb,{value:f.HF.CREATE.toString(),children:(0,f.gO)(f.HF.CREATE)}),(0,r.jsx)(u.eb,{value:f.HF.UPDATE.toString(),children:(0,f.gO)(f.HF.UPDATE)}),(0,r.jsx)(u.eb,{value:f.HF.DELETE.toString(),children:(0,f.gO)(f.HF.DELETE)})]})]})}),(0,r.jsx)(i.$,{type:"button",onClick:()=>{if(!v||!b){d({title:"Validation Error",description:"Please select both a resource and permission level.",variant:"destructive"});return}let e=parseInt(b);if(!(0,f.S8)(e)){d({title:"Validation Error",description:"Invalid permission level selected.",variant:"destructive"});return}let t=$.findIndex(e=>e.resourceName===v),a=w?.find(e=>e.resourceName===v),r={resourceName:v,permission:e,displayName:a?.displayName??v};if(t>=0){let e=[...$];e[t]=r,D(e)}else D([...$,r]);y(""),j("")},disabled:!v||!b,variant:"outline",children:"Add Permission"})]}),(0,r.jsx)("div",{className:"space-y-2",children:$.length>0?(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:"Current Permissions:"}),$.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"font-medium",children:e.displayName}),(0,r.jsx)(m.E,{variant:"outline",children:(0,f.gO)(e.permission)})]}),(0,r.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>T(e.resourceName),className:"text-destructive hover:text-destructive",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})]},e.resourceName))]}):(0,r.jsx)("div",{className:"text-sm text-muted-foreground p-4 text-center border rounded-lg border-dashed",children:"No permissions assigned. Add permissions above to define what this role can access."})})]})]}),(0,r.jsxs)(n.Es,{children:[(0,r.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>t(),children:"Cancel"}),(0,r.jsx)(i.$,{type:"submit",disabled:N||A,children:N?"Saving...":a?"Update Role":"Create Role"})]})]})})})}},83442:(e,t,a)=>{Promise.resolve().then(a.bind(a,61018)),Promise.resolve().then(a.bind(a,2505)),Promise.resolve().then(a.bind(a,92588)),Promise.resolve().then(a.bind(a,48974)),Promise.resolve().then(a.bind(a,31057)),Promise.resolve().then(a.bind(a,50417))},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};