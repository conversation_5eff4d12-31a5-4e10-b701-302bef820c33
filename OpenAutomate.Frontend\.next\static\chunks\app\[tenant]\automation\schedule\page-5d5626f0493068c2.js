(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7589],{1493:(e,a,t)=>{Promise.resolve().then(t.bind(t,63718))},63718:(e,a,t)=>{"use strict";t.d(a,{default:()=>ev});var n=t(95155),l=t(49103),s=t(30285),r=t(12115),i=t(47262),c=t(26126),d=t(98627),o=t(36928);function u(e){let{className:a,...t}=e;return(0,n.jsx)(d.bL,{"data-slot":"switch",className:(0,o.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,n.jsx)(d.zi,{"data-slot":"switch-thumb",className:(0,o.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}var h=t(51154),m=t(87570),g=t(66094),x=t(5623),p=t(13717),v=t(59629),y=t(27265),j=t(74126),b=t(54416),f=t(44838),N=t(54165),w=t(88262),S=t(12187);function k(e){let{schedule:a,onDeleted:t,onToggleEnabled:l,onEdit:i}=e,[c,d]=(0,r.useState)(!1),[o,u]=(0,r.useState)(!1),[m,k]=(0,r.useState)(!1),{toast:C}=(0,w.d)(),M=()=>{i&&i()},D=async()=>{u(!0);try{await (0,g.VD)(a.id),C({title:"Schedule Deleted",description:'Schedule "'.concat(a.name,'" has been deleted successfully.')}),d(!1),t&&t()}catch(e){console.error("Delete failed:",e),C((0,S.m4)(e))}finally{u(!1)}},T=async()=>{if(l){await l(a);return}k(!0);try{let e=a.isEnabled?await (0,g.g8)(a.id):await (0,g.H4)(a.id);C({title:"Schedule ".concat(e.isEnabled?"Enabled":"Disabled"),description:'Schedule "'.concat(a.name,'" has been ').concat(e.isEnabled?"enabled":"disabled",".")})}catch(e){console.error("Toggle enable failed:",e),C((0,S.m4)(e))}finally{k(!1)}},A=e=>{e.stopPropagation()};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(f.rI,{children:[(0,n.jsx)(f.ty,{asChild:!0,onClick:A,children:(0,n.jsx)(s.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted","aria-label":"Open menu",onClick:A,children:(0,n.jsx)(x.A,{className:"h-4 w-4"})})}),(0,n.jsxs)(f.SQ,{align:"start",className:"w-[180px]",onClick:A,onPointerDown:A,onMouseDown:A,children:[(0,n.jsxs)(f._2,{onClick:e=>{e.stopPropagation(),M()},children:[(0,n.jsx)(p.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,n.jsx)("span",{children:"Edit"})]}),(0,n.jsxs)(f._2,{onClick:e=>{e.stopPropagation(),T()},disabled:m,children:[m?(0,n.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin","aria-hidden":"true"}):a.isEnabled?(0,n.jsx)(v.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}):(0,n.jsx)(y.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,n.jsx)("span",{children:m?"".concat(a.isEnabled?"Disabling":"Enabling","..."):a.isEnabled?"Disable":"Enable"})]}),(0,n.jsx)(f.mB,{}),(0,n.jsxs)(f._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e.stopPropagation(),d(!0)},children:[(0,n.jsx)(j.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,n.jsx)("span",{children:"Delete"})]})]})]}),(0,n.jsx)(N.lG,{open:c,onOpenChange:d,children:(0,n.jsxs)(N.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,n.jsxs)(N.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(b.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,n.jsx)(N.c7,{children:(0,n.jsx)(N.L3,{children:"Confirm Delete"})}),(0,n.jsxs)("div",{children:["Are you sure you want to delete this schedule? ",(0,n.jsx)("br",{}),(0,n.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Schedule: ",(0,n.jsx)("b",{children:a.name})]}),(0,n.jsx)("br",{}),(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:"This action cannot be undone."})]}),(0,n.jsxs)(N.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,n.jsx)(s.$,{variant:"outline",onClick:e=>{e.stopPropagation(),d(!1)},disabled:o,children:"Cancel"}),(0,n.jsxs)(s.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:e=>{e.stopPropagation(),D()},disabled:o,children:[o&&(0,n.jsx)(h.A,{className:"animate-spin w-4 h-4 mr-2"}),o?"Deleting...":"Delete"]})]})]})})]})}var C=t(15426);let M=e=>{let{schedule:a,onToggleEnabled:t}=e,[l,s]=(0,r.useState)(!1),i=async()=>{if(t&&!l){console.log("Toggling schedule ".concat(a.name," from ").concat(a.isEnabled," to ").concat(!a.isEnabled)),s(!0);try{await t(a),console.log("Successfully toggled schedule ".concat(a.name))}catch(e){console.error("Toggle failed:",e)}finally{s(!1)}}};return(0,n.jsx)("div",{className:"flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:(0,n.jsxs)("div",{className:"relative",children:[l&&(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-background/80 rounded-full z-10",children:(0,n.jsx)(h.A,{className:"h-3 w-3 animate-spin text-muted-foreground"})}),(0,n.jsx)(u,{checked:a.isEnabled,onCheckedChange:i,disabled:l,"aria-label":"".concat(a.isEnabled?"Disable":"Enable",' schedule "').concat(a.name,'"'),className:"transition-all duration-200 ".concat(l?"opacity-50":"hover:scale-105"," cursor-pointer")})]})})},D=function(){let{onDeleted:e,onToggleEnabled:a,onEdit:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return[{id:"select",header:e=>{let{table:a}=e;return(0,n.jsx)(i.S,{checked:a.getIsAllPageRowsSelected()||a.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>a.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:a}=e,t=e=>{e.stopPropagation()};return(0,n.jsx)("span",{onClick:t,onMouseDown:t,onPointerDown:t,children:(0,n.jsx)(i.S,{checked:a.getIsSelected(),onCheckedChange:e=>a.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]",onClick:t})})},enableSorting:!1,enableHiding:!1},{id:"actions",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Actions"})},cell:l=>{let{row:s}=l;return(0,n.jsx)(k,{schedule:s.original,onDeleted:e,onToggleEnabled:a,onEdit:t?()=>t(s.original):void 0})},enableSorting:!1,enableHiding:!1},{accessorKey:"name",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Schedule Name"})},cell:e=>{let{row:a}=e,t=a.original;return(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("span",{className:"font-medium",children:t.name}),t.description&&(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:t.description})]})}},{accessorKey:"isEnabled",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Status"})},cell:e=>{let{row:t}=e,l=t.original;return(0,n.jsx)(M,{schedule:l,onToggleEnabled:a})}},{accessorKey:"recurrenceType",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Recurrence"})},cell:e=>{let{row:a}=e,t=a.getValue("recurrenceType");return(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)(c.E,{variant:"outline",children:(0,g.MK)(t)})})}},{accessorKey:"automationPackageName",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Package"})},cell:e=>{let{row:a}=e;return(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("span",{children:a.getValue("automationPackageName")||"N/A"})})}},{accessorKey:"botAgentName",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Agent"})},cell:e=>{let{row:a}=e;return(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("span",{children:a.getValue("botAgentName")||"N/A"})})}},{accessorKey:"nextRunTime",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Next Run"})},cell:e=>{let{row:a}=e,t=a.getValue("nextRunTime");return(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("span",{className:"text-sm",children:(0,g.po)(t)})})}},{accessorKey:"timeZoneId",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Timezone"})},cell:e=>{let{row:a}=e;return(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:a.getValue("timeZoneId")||"UTC"})})}},{accessorKey:"createdAt",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Created"})},cell:e=>{let{row:a}=e,t=a.getValue("createdAt"),l=(0,C.Ej)(t,{fallback:"-"});return(0,n.jsx)("span",{className:"text-sm",children:l})}},{accessorKey:"cronExpression",header:e=>{let{column:a}=e;return(0,n.jsx)(m.w,{column:a,title:"Cron Expression"})},cell:e=>{let{row:a}=e,t=a.getValue("cronExpression");return a.original.recurrenceType===g.V5.Advanced&&t?(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("code",{className:"text-xs bg-muted px-2 py-1 rounded",children:t})}):(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:"-"})}}]};D();var T=t(54333),A=t(34953),E=t(62523),V=t(59409),O=t(83856),I=t(35695);function H(e){let{className:a,useUrlParams:t=!1,paramName:l="tab",value:s,onValueChange:i,...c}=e,d=(0,I.useRouter)(),u=(0,I.usePathname)(),h=(0,I.useSearchParams)(),m=r.useCallback(e=>{if(i&&i(e),t){let a=new URLSearchParams(h.toString());a.set(l,e),d.push("".concat(u,"?").concat(a.toString()),{scroll:!1})}},[i,t,l,u,h,d]),g=t&&h.get(l)||void 0;return(0,n.jsx)(O.bL,{"data-slot":"tabs",className:(0,o.cn)("flex flex-col gap-2",a),value:g||s,onValueChange:m,...c})}function z(e){let{className:a,...t}=e;return(0,n.jsx)(O.bL,{"data-slot":"tabs",className:(0,o.cn)("flex flex-col gap-2",a),...t})}function F(e){return e.useUrlParams?(0,n.jsx)(H,{...e}):(0,n.jsx)(z,{...e})}function P(e){let{className:a,...t}=e;return(0,n.jsx)(O.B8,{"data-slot":"tabs-list",className:(0,o.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...t})}function L(e){let{className:a,...t}=e;return(0,n.jsx)(O.l9,{"data-slot":"tabs-trigger",className:(0,o.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...t})}function R(e){let{className:a,...t}=e;return(0,n.jsx)(O.UC,{"data-slot":"tabs-content",className:(0,o.cn)("flex-1 outline-none",a),...t})}var U=t(32771),W=t(70449),$=t(85511),q=t(14636),_=t(66474);function J(e){let{recurrence:a,onUpdate:t}=e,[l,s]=(0,r.useState)(!1),[i,c]=(0,r.useState)(!1);return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"recurrence",className:"text-sm font-medium",children:["Recurrence",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)(V.l6,{value:a.type,onValueChange:e=>t({type:e}),children:[(0,n.jsx)(V.bq,{children:(0,n.jsx)(V.yv,{})}),(0,n.jsxs)(V.gC,{children:[(0,n.jsx)(V.eb,{value:g.V5.Once,children:"Once"}),(0,n.jsx)(V.eb,{value:g.V5.Minutes,children:"Minutes"}),(0,n.jsx)(V.eb,{value:g.V5.Hourly,children:"Hourly"}),(0,n.jsx)(V.eb,{value:g.V5.Daily,children:"Daily"}),(0,n.jsx)(V.eb,{value:g.V5.Weekly,children:"Weekly"}),(0,n.jsx)(V.eb,{value:g.V5.Monthly,children:"Monthly"})]})]})]}),a.type===g.V5.Once&&(0,n.jsx)(K,{recurrence:a,onUpdate:t,startDateOpen:l,setStartDateOpen:s}),a.type===g.V5.Daily&&(0,n.jsx)(Z,{recurrence:a,onUpdate:t,startDateOpen:l,setStartDateOpen:s,endDateOpen:i,setEndDateOpen:c}),a.type===g.V5.Weekly&&(0,n.jsx)(B,{recurrence:a,onUpdate:t,updateDaySelection:(e,n)=>{var l;let s=null!==(l=a.selectedDays)&&void 0!==l?l:[];n?t({selectedDays:[...s,e]}):t({selectedDays:s.filter(a=>a!==e)})},startDateOpen:l,setStartDateOpen:s,endDateOpen:i,setEndDateOpen:c}),a.type===g.V5.Monthly&&(0,n.jsx)(Y,{recurrence:a,onUpdate:t,updateMonthSelection:(e,n)=>{var l;let s=null!==(l=a.selectedMonths)&&void 0!==l?l:[];n?t({selectedMonths:[...s,e]}):t({selectedMonths:s.filter(a=>a!==e)})},startDateOpen:l,setStartDateOpen:s,endDateOpen:i,setEndDateOpen:c}),(a.type===g.V5.Minutes||a.type===g.V5.Hourly)&&(0,n.jsx)(G,{recurrence:a,onUpdate:t,startDateOpen:l,setStartDateOpen:s,endDateOpen:i,setEndDateOpen:c})]})}function K(e){var a,t;let{recurrence:l,onUpdate:s,startDateOpen:r,setStartDateOpen:i}=e;return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(Q,{label:"Start Date",required:!0,date:l.startDate,onDateChange:e=>s({startDate:e}),isOpen:r,setIsOpen:i}),(0,n.jsx)(X,{label:"At:",hour:null!==(a=l.dailyHour)&&void 0!==a?a:"09",minute:null!==(t=l.dailyMinute)&&void 0!==t?t:"00",onHourChange:e=>s({dailyHour:e}),onMinuteChange:e=>s({dailyMinute:e})})]})}function Z(e){var a,t;let{recurrence:l,onUpdate:s}=e;return(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsx)(X,{label:"At:",hour:null!==(a=l.dailyHour)&&void 0!==a?a:"09",minute:null!==(t=l.dailyMinute)&&void 0!==t?t:"00",onHourChange:e=>s({dailyHour:e}),onMinuteChange:e=>s({dailyMinute:e})})})}function B(e){var a,t,l;let{recurrence:s,onUpdate:r,updateDaySelection:i}=e,c=null!==(a=s.selectedDays)&&void 0!==a?a:[];return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(X,{label:"Every day at:",hour:null!==(t=s.weeklyHour)&&void 0!==t?t:"17",minute:null!==(l=s.weeklyMinute)&&void 0!==l?l:"00",onHourChange:e=>r({weeklyHour:e}),onMinuteChange:e=>r({weeklyMinute:e})}),(0,n.jsx)(ee,{days:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],selectedDays:c,onDayToggle:i})]})}function Y(e){var a,t,l;let{recurrence:s,onUpdate:r,updateMonthSelection:i}=e,c=null!==(a=s.selectedMonths)&&void 0!==a?a:[];return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(X,{label:"At:",hour:null!==(t=s.monthlyHour)&&void 0!==t?t:"00",minute:null!==(l=s.monthlyMinute)&&void 0!==l?l:"00",onHourChange:e=>r({monthlyHour:e}),onMinuteChange:e=>r({monthlyMinute:e})}),(0,n.jsx)(et,{recurrence:s,onUpdate:r}),(0,n.jsx)(ea,{months:["January","February","March","April","May","June","July","August","September","October","November","December"],selectedMonths:c,onMonthToggle:i})]})}function G(e){let{recurrence:a,onUpdate:t}=e;return(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm",children:"Every"}),(0,n.jsxs)(V.l6,{value:a.value,onValueChange:e=>t({value:e}),children:[(0,n.jsx)(V.bq,{className:"w-20",children:(0,n.jsx)(V.yv,{})}),(0,n.jsxs)(V.gC,{children:[(0,n.jsx)(V.eb,{value:"1",children:"1"}),(0,n.jsx)(V.eb,{value:"5",children:"5"}),(0,n.jsx)(V.eb,{value:"10",children:"10"}),(0,n.jsx)(V.eb,{value:"15",children:"15"}),(0,n.jsx)(V.eb,{value:"30",children:"30"})]})]}),(0,n.jsx)("span",{className:"text-sm",children:a.type===g.V5.Hourly?"Hours":"minute(s)"})]})})}function Q(e){let{label:a,required:t,date:l,onDateChange:r,isOpen:i,setIsOpen:c,minDate:d}=e;return(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:[a," ",t&&(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)(q.AM,{open:i,onOpenChange:c,children:[(0,n.jsx)(q.Wv,{asChild:!0,children:(0,n.jsxs)(s.$,{variant:"outline",className:"w-full justify-between font-normal",children:[l?l.toLocaleDateString():"Select date",(0,n.jsx)(_.A,{className:"h-4 w-4"})]})}),(0,n.jsx)(q.hl,{className:"w-auto overflow-hidden p-0",align:"start",children:(0,n.jsx)($.V,{mode:"single",selected:l,captionLayout:"dropdown",disabled:e=>{let a=new Date(new Date().setHours(0,0,0,0));return e<(d?new Date(d):a)},onSelect:e=>{r(e||void 0),c(!1)}})})]})]})}function X(e){let{label:a,hour:t,minute:l,onHourChange:s,onMinuteChange:r}=e;return(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm",children:a}),(0,n.jsxs)(V.l6,{value:t,onValueChange:s,children:[(0,n.jsx)(V.bq,{className:"w-20",children:(0,n.jsx)(V.yv,{})}),(0,n.jsx)(V.gC,{children:Array.from({length:24},(e,a)=>(0,n.jsx)(V.eb,{value:a.toString().padStart(2,"0"),children:a.toString().padStart(2,"0")},a))})]}),(0,n.jsx)("span",{className:"text-sm",children:":"}),(0,n.jsxs)(V.l6,{value:l,onValueChange:r,children:[(0,n.jsx)(V.bq,{className:"w-20",children:(0,n.jsx)(V.yv,{})}),(0,n.jsx)(V.gC,{children:Array.from({length:60},(e,a)=>(0,n.jsx)(V.eb,{value:a.toString().padStart(2,"0"),children:a.toString().padStart(2,"0")},a))})]})]})}function ee(e){let{days:a,selectedDays:t,onDayToggle:l}=e;return(0,n.jsx)("div",{className:"space-y-2",children:(0,n.jsx)("div",{className:"grid grid-cols-4 gap-4",children:a.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(i.S,{id:e,checked:t.includes(e),onCheckedChange:a=>l(e,!!a)}),(0,n.jsx)("label",{htmlFor:e,className:"text-sm font-medium",children:e})]},e))})})}function ea(e){let{months:a,selectedMonths:t,onMonthToggle:l}=e;return(0,n.jsx)("div",{className:"space-y-2",children:(0,n.jsx)("div",{className:"grid grid-cols-3 gap-4",children:a.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(i.S,{id:e,checked:t.includes(e),onCheckedChange:a=>l(e,!!a)}),(0,n.jsx)("label",{htmlFor:e,className:"text-sm font-medium",children:e})]},e))})})}function et(e){var a,t,l;let{recurrence:s,onUpdate:r}=e;return(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"On:"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"radio",id:"day-option",name:"monthly-on",checked:"day"===s.monthlyOnType,onChange:()=>r({monthlyOnType:"day"})}),(0,n.jsx)("label",{htmlFor:"day-option",className:"text-sm font-medium",children:"Day"})]}),(0,n.jsxs)(V.l6,{value:null!==(a=s.selectedDay)&&void 0!==a?a:"31",onValueChange:e=>r({selectedDay:e}),disabled:"day"!==s.monthlyOnType,children:[(0,n.jsx)(V.bq,{className:"w-40",children:(0,n.jsx)(V.yv,{placeholder:"Select day"})}),(0,n.jsx)(V.gC,{children:Array.from({length:31},(e,a)=>(0,n.jsx)(V.eb,{value:(a+1).toString(),children:a+1},a+1))})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"radio",id:"the-option",name:"monthly-on",checked:"the"===s.monthlyOnType,onChange:()=>r({monthlyOnType:"the"})}),(0,n.jsx)("label",{htmlFor:"the-option",className:"text-sm font-medium",children:"The"})]}),(0,n.jsxs)(V.l6,{value:null!==(t=s.selectedOrdinal)&&void 0!==t?t:"2nd",onValueChange:e=>r({selectedOrdinal:e}),disabled:"the"!==s.monthlyOnType,children:[(0,n.jsx)(V.bq,{className:"w-20",children:(0,n.jsx)(V.yv,{})}),(0,n.jsxs)(V.gC,{children:[(0,n.jsx)(V.eb,{value:"1st",children:"1st"}),(0,n.jsx)(V.eb,{value:"2nd",children:"2nd"}),(0,n.jsx)(V.eb,{value:"3rd",children:"3rd"}),(0,n.jsx)(V.eb,{value:"4th",children:"4th"}),(0,n.jsx)(V.eb,{value:"5th",children:"5th"})]})]}),(0,n.jsxs)(V.l6,{value:null!==(l=s.selectedWeekday)&&void 0!==l?l:"Wednesday",onValueChange:e=>r({selectedWeekday:e}),disabled:"the"!==s.monthlyOnType,children:[(0,n.jsx)(V.bq,{className:"w-32",children:(0,n.jsx)(V.yv,{})}),(0,n.jsxs)(V.gC,{children:[(0,n.jsx)(V.eb,{value:"Monday",children:"Monday"}),(0,n.jsx)(V.eb,{value:"Tuesday",children:"Tuesday"}),(0,n.jsx)(V.eb,{value:"Wednesday",children:"Wednesday"}),(0,n.jsx)(V.eb,{value:"Thursday",children:"Thursday"}),(0,n.jsx)(V.eb,{value:"Friday",children:"Friday"}),(0,n.jsx)(V.eb,{value:"Saturday",children:"Saturday"}),(0,n.jsx)(V.eb,{value:"Sunday",children:"Sunday"})]})]})]})]})]})}var en=t(76517),el=t(25487),es=t(94449),er=t(47924),ei=t(86490);function ec(e){let{selectedAgentId:a,onAgentSelect:t}=e,[l,s]=(0,r.useState)(""),{data:i=[],error:d,isLoading:o}=(0,A.Ay)(W.DC.agents(),ei.NA),u=(0,r.useMemo)(()=>{let e=i.filter(e=>e.status&&"disconnected"!==e.status.toLowerCase()&&"offline"!==e.status.toLowerCase());return l?e.filter(e=>{var a;return e.name.toLowerCase().includes(l.toLowerCase())||(null===(a=e.machineName)||void 0===a?void 0:a.toLowerCase().includes(l.toLowerCase()))}):e},[i,l]),h=e=>{switch(e.toLowerCase()){case"available":case"online":return{variant:"default",icon:en.A,className:"bg-green-100 text-green-700"};case"busy":case"running":return{variant:"secondary",icon:el.A,className:"bg-yellow-100 text-yellow-700"};default:return{variant:"destructive",icon:es.A,className:"bg-red-100 text-red-700"}}},m=e=>{t&&t(a===e?"":e)};return o?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:["Execution Agent",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading agents..."})]}),(0,n.jsx)("div",{className:"h-32 bg-muted/20 rounded-md animate-pulse"})]}):d?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:["Execution Agent",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"text-sm text-destructive",children:"Failed to load agents"})]}),(0,n.jsx)("div",{className:"p-3 bg-destructive/10 rounded-md border border-destructive/20",children:(0,n.jsx)("div",{className:"text-sm text-destructive",children:"Unable to connect to the server. Please try again."})})]}):(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:["Execution Agent",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Select an agent to execute this schedule (only connected agents shown)"})]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(er.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,n.jsx)(E.p,{placeholder:"Search agents by name or machine...",value:l,onChange:e=>s(e.target.value),className:"pl-10"})]}),(0,n.jsxs)("div",{className:"border rounded-md",children:[(0,n.jsxs)("div",{className:"bg-muted/50 grid grid-cols-12 py-3 px-4 text-sm font-medium border-b",children:[(0,n.jsx)("div",{className:"col-span-1",children:"Select"}),(0,n.jsx)("div",{className:"col-span-4",children:"Agent Name"}),(0,n.jsx)("div",{className:"col-span-4",children:"Machine Name"}),(0,n.jsx)("div",{className:"col-span-3",children:"Status"})]}),(0,n.jsx)("div",{className:"max-h-64 overflow-y-auto",children:0===u.length?(0,n.jsxs)("div",{className:"py-8 text-center text-sm text-muted-foreground",children:[l?"No available agents found matching your search.":"No available agents found.",i.length>0&&(0,n.jsx)("div",{className:"text-xs mt-1",children:"Only connected agents are shown for selection."})]}):u.map(e=>{let t=h(e.status),l=t.icon,s=a===e.id;return(0,n.jsxs)("div",{className:"grid grid-cols-12 py-3 px-4 border-b last:border-b-0 hover:bg-muted/30 transition-colors cursor-pointer ".concat(s?"bg-primary/10 border-primary/20":""),onClick:()=>m(e.id),children:[(0,n.jsx)("div",{className:"col-span-1 flex items-center",children:(0,n.jsx)("div",{className:"w-4 h-4 rounded-full border-2 flex items-center justify-center ".concat(s?"border-primary bg-primary":"border-muted-foreground"),children:s&&(0,n.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})}),(0,n.jsx)("div",{className:"col-span-4 flex items-center",children:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),e.description&&(0,n.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})}),(0,n.jsx)("div",{className:"col-span-4 flex items-center",children:(0,n.jsx)("span",{className:"text-sm",children:e.machineName||"N/A"})}),(0,n.jsx)("div",{className:"col-span-3 flex items-center",children:(0,n.jsxs)(c.E,{variant:t.variant,className:t.className,children:[(0,n.jsx)(l,{className:"w-3 h-3 mr-1"}),e.status]})})]},e.id)})})]}),a&&(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Selected Agent"}),(0,n.jsx)("div",{className:"p-3 bg-primary/5 rounded-md border border-primary/20",children:(()=>{let e=i.find(e=>e.id===a);if(!e)return(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Agent not found"});let t=h(e.status),l=t.icon;return(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:e.machineName})]}),(0,n.jsxs)(c.E,{variant:t.variant,className:t.className,children:[(0,n.jsx)(l,{className:"w-3 h-3 mr-1"}),e.status]})]})})()})]}),(0,n.jsx)("div",{className:"text-xs text-muted-foreground",children:"Only connected agents are available for selection. Click an agent to select it for schedule execution."})]})}function ed(e){var a,t,l,i,c,d,o,u,h,m,x,p,v,y,j,b,f,k,C,M,D,T,A,E,V,O,I,H,z,U,W,$,q,_,K;let{isOpen:Z,onClose:B,mode:Y,editingSchedule:G,onSuccess:Q}=e,{toast:X}=(0,w.d)(),[ee,ea]=(0,r.useState)(!1),[et,en]=(0,r.useState)("trigger"),[el,es]=(0,r.useState)({name:null!==(b=null==G?void 0:G.name)&&void 0!==b?b:"",packageId:null!==(f=null==G?void 0:G.packageId)&&void 0!==f?f:"",packageVersion:null!==(k=null==G?void 0:G.packageVersion)&&void 0!==k?k:"latest",agentId:null!==(C=null==G?void 0:G.agentId)&&void 0!==C?C:"",timezone:null!==(M=null==G?void 0:G.timezone)&&void 0!==M?M:"Asia/Ho_Chi_Minh",recurrence:{type:null!==(D=null==G?void 0:null===(a=G.recurrence)||void 0===a?void 0:a.type)&&void 0!==D?D:g.V5.Daily,value:null!==(T=null==G?void 0:null===(t=G.recurrence)||void 0===t?void 0:t.value)&&void 0!==T?T:"1",startTime:null!==(A=null==G?void 0:null===(l=G.recurrence)||void 0===l?void 0:l.startTime)&&void 0!==A?A:"09:00",dailyHour:null!==(E=null==G?void 0:null===(i=G.recurrence)||void 0===i?void 0:i.dailyHour)&&void 0!==E?E:"09",dailyMinute:null!==(V=null==G?void 0:null===(c=G.recurrence)||void 0===c?void 0:c.dailyMinute)&&void 0!==V?V:"00",weeklyHour:null!==(O=null==G?void 0:null===(d=G.recurrence)||void 0===d?void 0:d.weeklyHour)&&void 0!==O?O:"09",weeklyMinute:null!==(I=null==G?void 0:null===(o=G.recurrence)||void 0===o?void 0:o.weeklyMinute)&&void 0!==I?I:"00",selectedDays:null!==(H=null==G?void 0:null===(u=G.recurrence)||void 0===u?void 0:u.selectedDays)&&void 0!==H?H:["Monday","Tuesday","Wednesday","Thursday","Friday"],monthlyHour:null!==(z=null==G?void 0:null===(h=G.recurrence)||void 0===h?void 0:h.monthlyHour)&&void 0!==z?z:"09",monthlyMinute:null!==(U=null==G?void 0:null===(m=G.recurrence)||void 0===m?void 0:m.monthlyMinute)&&void 0!==U?U:"00",monthlyOnType:null!==(W=null==G?void 0:null===(x=G.recurrence)||void 0===x?void 0:x.monthlyOnType)&&void 0!==W?W:"day",selectedDay:null!==($=null==G?void 0:null===(p=G.recurrence)||void 0===p?void 0:p.selectedDay)&&void 0!==$?$:"1",selectedOrdinal:null!==(q=null==G?void 0:null===(v=G.recurrence)||void 0===v?void 0:v.selectedOrdinal)&&void 0!==q?q:"1st",selectedWeekday:null!==(_=null==G?void 0:null===(y=G.recurrence)||void 0===y?void 0:y.selectedWeekday)&&void 0!==_?_:"Monday",selectedMonths:null!==(K=null==G?void 0:null===(j=G.recurrence)||void 0===j?void 0:j.selectedMonths)&&void 0!==K?K:["January","February","March","April","May","June","July","August","September","October","November","December"]}});(0,r.useEffect)(()=>{if("edit"===Y&&G){var e,a,t,n,l,s,r,i,c,d,o,u,h,m,x,p,v,y,j,b;let f;let N=null!==(e=G.recurrence)&&void 0!==e?e:{},w=null!==(a=N.dailyHour)&&void 0!==a?a:"09",S=null!==(t=N.dailyMinute)&&void 0!==t?t:"00";if(N.type===g.V5.Once&&G.oneTimeExecution)try{let e=new Date(G.oneTimeExecution);f=new Date(e.getFullYear(),e.getMonth(),e.getDate()),w=e.getHours().toString().padStart(2,"0"),S=e.getMinutes().toString().padStart(2,"0")}catch(e){console.error("Failed to parse date:",e)}es({name:null!==(n=G.name)&&void 0!==n?n:"",packageId:null!==(l=G.packageId)&&void 0!==l?l:"",packageVersion:null!==(s=G.packageVersion)&&void 0!==s?s:"latest",agentId:null!==(r=G.agentId)&&void 0!==r?r:"",timezone:null!==(i=G.timezone)&&void 0!==i?i:"Asia/Ho_Chi_Minh",recurrence:{type:null!==(c=N.type)&&void 0!==c?c:g.V5.Daily,value:null!==(d=N.value)&&void 0!==d?d:"1",startDate:f,dailyHour:w,dailyMinute:S,startTime:"".concat(w,":").concat(S),weeklyHour:null!==(o=N.weeklyHour)&&void 0!==o?o:"09",weeklyMinute:null!==(u=N.weeklyMinute)&&void 0!==u?u:"00",selectedDays:null!==(h=N.selectedDays)&&void 0!==h?h:["Monday","Tuesday","Wednesday","Thursday","Friday"],monthlyHour:null!==(m=N.monthlyHour)&&void 0!==m?m:"09",monthlyMinute:null!==(x=N.monthlyMinute)&&void 0!==x?x:"00",monthlyOnType:null!==(p=N.monthlyOnType)&&void 0!==p?p:"day",selectedDay:null!==(v=N.selectedDay)&&void 0!==v?v:"1",selectedOrdinal:null!==(y=N.selectedOrdinal)&&void 0!==y?y:"1st",selectedWeekday:null!==(j=N.selectedWeekday)&&void 0!==j?j:"Monday",selectedMonths:null!==(b=N.selectedMonths)&&void 0!==b?b:["January","February","March","April","May","June","July","August","September","October","November","December"]}})}"create"===Y&&es({name:"",packageId:"",packageVersion:"latest",agentId:"",timezone:"Asia/Ho_Chi_Minh",recurrence:{type:g.V5.Daily,value:"1",startTime:"09:00",dailyHour:"09",dailyMinute:"00",weeklyHour:"09",weeklyMinute:"00",selectedDays:["Monday","Tuesday","Wednesday","Thursday","Friday"],monthlyHour:"09",monthlyMinute:"00",monthlyOnType:"day",selectedDay:"1",selectedOrdinal:"1st",selectedWeekday:"Monday",selectedMonths:["January","February","March","April","May","June","July","August","September","October","November","December"]}})},[G,Y,Z]);let er=e=>{es(a=>({...a,...e}))},ei=()=>{if(!el.name.trim())return{isValid:!1,error:"Schedule name is required"};if(!el.packageId)return{isValid:!1,error:"Package selection is required"};if(!el.agentId)return{isValid:!1,error:"Agent selection is required"};if(!el.timezone)return{isValid:!1,error:"Timezone selection is required"};if(el.recurrence.type===g.V5.Once){if(!el.recurrence.startDate)return{isValid:!1,error:"Please select a date for one-time schedule"};if(!el.recurrence.dailyHour||!el.recurrence.dailyMinute)return{isValid:!1,error:"Please select a time for one-time schedule"}}return{isValid:!0}},ed=()=>{let e,a;let{recurrence:t}=el;switch(t.type){case g.V5.Once:if(t.startDate&&t.dailyHour&&t.dailyMinute){let e=new Date(t.startDate);e.setHours(parseInt(t.dailyHour,10),parseInt(t.dailyMinute,10),0,0),a=e.toISOString()}else throw Error("Please select both date and time for one-time schedule");break;case g.V5.Daily:e="0 ".concat(t.dailyMinute," ").concat(t.dailyHour," * * *");break;case g.V5.Weekly:if(t.selectedDays&&t.selectedDays.length>0){let a={Sunday:"0",Monday:"1",Tuesday:"2",Wednesday:"3",Thursday:"4",Friday:"5",Saturday:"6"},n=t.selectedDays.map(e=>a[e]).join(",");e="0 ".concat(t.weeklyMinute," ").concat(t.weeklyHour," * * ").concat(n)}break;case g.V5.Monthly:"day"===t.monthlyOnType&&t.selectedDay&&(e="0 ".concat(t.monthlyMinute," ").concat(t.monthlyHour," ").concat(t.selectedDay," * *"));break;case g.V5.Hourly:e="0 0 */".concat(t.value," * * *");break;case g.V5.Minutes:e="0 */".concat(t.value," * * * *")}return{name:el.name.trim(),description:"",isEnabled:!0,recurrenceType:t.type,cronExpression:e,oneTimeExecution:a,timeZoneId:el.timezone,automationPackageId:el.packageId,botAgentId:el.agentId}},eu=async()=>{let e=ei();if(!e.isValid){X({title:"Validation Error",description:e.error,variant:"destructive"});return}ea(!0);try{let e=ed();if("edit"===Y&&(null==G?void 0:G.id)){let a=await (0,g.Fs)(G.id,e);X({title:"Success",description:'Schedule "'.concat(a.name,'" updated successfully')}),Q&&Q({id:a.id,name:a.name})}else{let a=await (0,g.sF)(e);X({title:"Success",description:'Schedule "'.concat(a.name,'" created successfully')}),Q&&Q({id:a.id,name:a.name})}B(!0)}catch(e){console.error("".concat(Y," schedule failed:"),e),X((0,S.m4)(e))}finally{ea(!1)}},eh=()=>{B()};return(0,n.jsx)(N.lG,{open:Z,onOpenChange:eh,children:(0,n.jsxs)(N.Cf,{className:"sm:max-w-[800px]",children:[(0,n.jsx)(N.c7,{children:(0,n.jsx)(N.L3,{children:"edit"===Y?"Edit Schedule":"Create Schedule"})}),(0,n.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,n.jsx)(eo,{formData:el,onUpdate:er}),(0,n.jsxs)(F,{value:et,onValueChange:en,className:"w-full",children:[(0,n.jsxs)(P,{className:"grid grid-cols-2 mb-4",children:[(0,n.jsx)(L,{value:"trigger",children:"Trigger"}),(0,n.jsx)(L,{value:"executionTarget",children:"Execution Target"})]}),(0,n.jsx)(R,{value:"trigger",children:(0,n.jsx)(J,{recurrence:el.recurrence,onUpdate:e=>{es(a=>({...a,recurrence:{...a.recurrence,...e}}))}})}),(0,n.jsx)(R,{value:"executionTarget",children:(0,n.jsx)(ec,{selectedAgentId:el.agentId,onAgentSelect:e=>er({agentId:e})})})]})]}),(0,n.jsxs)(N.Es,{children:[(0,n.jsx)(s.$,{variant:"outline",onClick:eh,disabled:ee,children:"Cancel"}),(0,n.jsx)(s.$,{onClick:eu,disabled:ee,children:ee?"Saving...":"edit"===Y?"Update":"Create"})]})]})})}function eo(e){let{formData:a,onUpdate:t}=e,{data:l=[],error:s,isLoading:i}=(0,A.Ay)(W.DC.packages(),U.s9),c=(0,r.useMemo)(()=>{if(!a.packageId)return[{value:"latest",label:"Latest"}];let e=l.find(e=>e.id===a.packageId);return(null==e?void 0:e.versions)?[{value:"latest",label:"Latest"},...e.versions.map(e=>({value:e.versionNumber,label:"v".concat(e.versionNumber)}))]:[{value:"latest",label:"Latest"}]},[l,a.packageId]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"name",className:"text-sm font-medium",children:["Name",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)(E.p,{id:"name",value:a.name,onChange:e=>t({name:e.target.value}),placeholder:"Enter schedule name"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"package",className:"text-sm font-medium",children:["Package",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),i?(0,n.jsx)("div",{className:"h-10 bg-muted rounded-md animate-pulse"}):s?(0,n.jsx)("div",{className:"text-sm text-destructive",children:"Failed to load packages"}):(0,n.jsxs)(V.l6,{value:a.packageId,onValueChange:e=>t({packageId:e,packageVersion:"latest"}),children:[(0,n.jsx)(V.bq,{children:(0,n.jsx)(V.yv,{placeholder:"Choose package"})}),(0,n.jsx)(V.gC,{children:l.map(e=>(0,n.jsx)(V.eb,{value:e.id,children:e.name},e.id))})]})]}),(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"packageVersion",className:"text-sm font-medium",children:["Package Version",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)(V.l6,{value:a.packageVersion,onValueChange:e=>t({packageVersion:e}),disabled:!a.packageId,children:[(0,n.jsx)(V.bq,{children:(0,n.jsx)(V.yv,{placeholder:"Select version"})}),(0,n.jsx)(V.gC,{children:c.map(e=>(0,n.jsx)(V.eb,{value:e.value,children:e.label},e.value))})]})]})]}),(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)("label",{htmlFor:"timezone",className:"text-sm font-medium",children:["Time Zone",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)(V.l6,{value:a.timezone,onValueChange:e=>t({timezone:e}),children:[(0,n.jsx)(V.bq,{children:(0,n.jsx)(V.yv,{placeholder:"Select time zone"})}),(0,n.jsxs)(V.gC,{children:[(0,n.jsx)(V.eb,{value:"Asia/Ho_Chi_Minh",children:"(UTC+7:00) Asia/Ho Chi Minh"}),(0,n.jsx)(V.eb,{value:"America/New_York",children:"(UTC-5:00) America/New York"}),(0,n.jsx)(V.eb,{value:"Europe/London",children:"(UTC+0:00) Europe/London"}),(0,n.jsx)(V.eb,{value:"Asia/Tokyo",children:"(UTC+9:00) Asia/Tokyo"}),(0,n.jsx)(V.eb,{value:"Australia/Sydney",children:"(UTC+10:00) Australia/Sydney"}),(0,n.jsx)(V.eb,{value:"UTC",children:"(UTC+0:00) UTC"})]})]})]})]})}var eu=t(11832);function eh(e){let{table:a,statuses:t,recurrenceTypes:l,onSearch:r,onStatusChange:i,onRecurrenceTypeChange:c,searchValue:d="",isFiltering:o=!1,isPending:u=!1,searchPlaceholder:m="Search schedules...",totalCount:g=0}=e,x=a.getState().columnFilters.length>0;return(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,n.jsxs)("div",{className:"relative flex items-center",children:[(0,n.jsx)(er.A,{className:"absolute left-3 h-4 w-4 text-muted-foreground"}),(0,n.jsx)(E.p,{placeholder:m,value:d,onChange:e=>{let a=e.target.value;r&&r(a)},className:"h-8 w-[200px] pl-10 lg:w-[300px]",disabled:o}),u&&(0,n.jsx)(h.A,{className:"absolute right-3 h-4 w-4 animate-spin text-muted-foreground"}),d&&!u&&(0,n.jsxs)(s.$,{variant:"ghost",onClick:()=>{r&&r("")},className:"absolute right-1 h-6 w-6 p-0 hover:bg-transparent",children:[(0,n.jsx)(b.A,{className:"h-3 w-3"}),(0,n.jsx)("span",{className:"sr-only",children:"Clear search"})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Status:"}),(0,n.jsxs)(V.l6,{onValueChange:e=>{i&&i(e)},defaultValue:"all",children:[(0,n.jsx)(V.bq,{className:"h-8 w-[120px]",children:(0,n.jsx)(V.yv,{placeholder:"All"})}),(0,n.jsxs)(V.gC,{children:[(0,n.jsx)(V.eb,{value:"all",children:"All"}),t.map(e=>(0,n.jsx)(V.eb,{value:e.value,children:e.label},e.value))]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Type:"}),(0,n.jsxs)(V.l6,{onValueChange:e=>{c&&c(e)},defaultValue:"all",children:[(0,n.jsx)(V.bq,{className:"h-8 w-[140px]",children:(0,n.jsx)(V.yv,{placeholder:"All Types"})}),(0,n.jsxs)(V.gC,{children:[(0,n.jsx)(V.eb,{value:"all",children:"All Types"}),l.map(e=>(0,n.jsx)(V.eb,{value:e.value,children:e.label},e.value))]})]})]}),x&&(0,n.jsxs)(s.$,{variant:"ghost",onClick:()=>{a.resetColumnFilters(),r&&r(""),i&&i("all"),c&&c("all")},className:"h-8 px-2 lg:px-3",children:["Reset",(0,n.jsx)(b.A,{className:"ml-2 h-4 w-4"})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[g>0&&(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,n.jsxs)("span",{children:[g," schedule",1!==g?"s":""]})}),(0,n.jsx)(eu.i,{table:a})]})]})}var em=t(62668),eg=t(29797),ex=t(36268),ep=t(11032);function ev(){var e,a,t;let i=(0,I.useRouter)(),c=(0,I.usePathname)(),d=(0,I.useSearchParams)(),{updateUrl:o}=(0,em.z)(),{toast:u}=(0,w.d)(),[h,m]=(0,r.useState)(!1),[x,p]=(0,r.useState)(null),[v,y]=(0,r.useState)({}),[j,b]=(0,r.useState)({}),[f,N]=(0,r.useState)(0),k=(0,r.useRef)(0),[C,M]=(0,r.useState)(!1),[E,V]=(0,r.useState)(!1),[O,H]=(0,r.useState)(!1),z=(0,r.useRef)(null),F=(0,r.useRef)(!0),[P,L]=(0,r.useState)(()=>{let e=[],a=d.get("search");a&&e.push({id:"name",value:a});let t=d.get("status");t&&e.push({id:"isEnabled",value:t});let n=d.get("type");return n&&e.push({id:"recurrenceType",value:n}),e}),[R,U]=(0,r.useState)(()=>{let e=d.get("sort"),a=d.get("order");return e&&("asc"===a||"desc"===a)?[{id:e,desc:"desc"===a}]:[]}),[$,q]=(0,r.useState)(()=>{let e=d.get("page"),a=d.get("size"),t=a?Math.max(1,parseInt(a)):10;return console.log("Initializing pagination from URL: page=".concat(e,", size=").concat(a,", pageSize=").concat(t)),{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:t}}),[_,J]=(0,r.useState)(null!==(a=d.get("search"))&&void 0!==a?a:""),K=c.split("/")[1],Z=(0,r.useCallback)(()=>{let e={$top:$.pageSize,$skip:$.pageIndex*$.pageSize,$count:!0};if(R.length>0&&(e.$orderby=R.map(e=>"".concat(e.id," ").concat(e.desc?"desc":"asc")).join(",")),P.length>0){let a=P.filter(e=>"string"!=typeof e.value||""!==e.value).map(e=>{let a=e.id,t=e.value;return"string"==typeof t?"name"===a&&t?"contains(tolower(name), '".concat(t.toLowerCase(),"')"):"isEnabled"===a&&t?"isEnabled eq ".concat("enabled"===t?"true":"false"):"recurrenceType"===a&&t?"recurrenceType eq '".concat(t,"'"):"contains(tolower(".concat(a,"), '").concat(t.toLowerCase(),"')"):Array.isArray(t)?t.map(e=>"".concat(a," eq '").concat(e,"'")).join(" or "):""}).filter(Boolean);a.length>0&&(e.$filter=a.join(" and "))}return e},[$,R,P])(),B=(0,r.useMemo)(()=>W.DC.schedulesWithOData(Z),[Z]),{data:Y,error:G,isLoading:Q,mutate:X}=(0,A.Ay)(B,()=>(0,g.ye)(Z),{dedupingInterval:0,revalidateOnFocus:!1,revalidateIfStale:!0,keepPreviousData:!1}),{data:ee,mutate:ea}=(0,A.Ay)((null==Y?void 0:null===(e=Y.value)||void 0===e?void 0:e.length)===0||G?W.DC.schedules():null,g.Os),et=Q||G&&!ee,en=(0,r.useMemo)(()=>{if(ee&&(!(null==Y?void 0:Y.value)||0===Y.value.length)){var e,a;console.log("Using fallback data with pagination:",$);let t=ee;_&&(t=t.filter(e=>e.name&&e.name.toLowerCase().includes(_.toLowerCase())));let n=null===(e=P.find(e=>"isEnabled"===e.id))||void 0===e?void 0:e.value;n&&"all"!==n&&(t=t.filter(e=>"enabled"===n?e.isEnabled:"disabled"!==n||!e.isEnabled));let l=null===(a=P.find(e=>"recurrenceType"===e.id))||void 0===a?void 0:a.value;l&&"all"!==l&&(t=t.filter(e=>e.recurrenceType===l));let s=t.length,r=$.pageIndex*$.pageSize,i=r+$.pageSize;console.log("Slicing fallback data from ".concat(r," to ").concat(i," out of ").concat(t.length," items"));let c=t.slice(r,i);return console.log("Returning ".concat(c.length," items from fallback data")),f!==s&&setTimeout(()=>{N(s),k.current=s},0),c}return(null==Y?void 0:Y.value)?(console.log("Returning ".concat(Y.value.length," items from OData response")),Y.value):(console.log("No data available from OData or fallback"),[])},[Y,ee,_,P,$,f]),el=(0,r.useCallback)(e=>{let a=$.pageIndex*$.pageSize+e.length;a>k.current&&(N(a),k.current=a),e.length===$.pageSize&&0===$.pageIndex&&(N(a+1),k.current=a+1),H(!1)},[$.pageIndex,$.pageSize]),es=(0,r.useCallback)(e=>{if(console.log("OData response received:",e),"number"==typeof e["@odata.count"]){N(e["@odata.count"]),k.current=e["@odata.count"],H(!0);return}Array.isArray(e.value)&&el(e.value)},[el]),er=(0,r.useCallback)(()=>{var e;if(!ee||0===ee.length)return;console.log("Using fallback data count:",ee.length);let a=ee.length;_&&(a=ee.filter(e=>e.name&&e.name.toLowerCase().includes(_.toLowerCase())).length);let t=null===(e=P.find(e=>"isEnabled"===e.id))||void 0===e?void 0:e.value;t&&"all"!==t&&(a=ee.filter(e=>"enabled"===t?e.isEnabled:"disabled"!==t||!e.isEnabled).length),console.log("Filtered fallback count:",a),N(a),k.current=a,H(!0)},[ee,_,P]);(0,r.useEffect)(()=>{Y?es(Y):er()},[Y,es,er]),(0,r.useEffect)(()=>{if((null==Y?void 0:Y.value)&&0===Y.value.length&&k.current>0&&$.pageIndex>0){let e=Math.max(1,Math.ceil(k.current/$.pageSize));$.pageIndex>=e&&(q(e=>({...e,pageIndex:0})),o(c,{page:"1"}))}},[Y,$.pageIndex,$.pageSize,k,o,c]),(0,r.useEffect)(()=>{if(F.current){F.current=!1;let e=d.get("page"),a=d.get("size");e&&a||o(c,{page:null!=e?e:"1",size:null!=a?a:"10"})}},[d,o,c]),(0,r.useEffect)(()=>{let e=d.get("page"),a=d.get("size");if(e&&a){let t=Math.max(0,parseInt(e)-1),n=parseInt(a);(t!==$.pageIndex||n!==$.pageSize)&&(console.log("URL changed: page=".concat(e,", size=").concat(a,". Updating pagination state.")),q({pageIndex:t,pageSize:n}))}},[d,$.pageIndex,$.pageSize]);let ei=e=>e+1,ec=(e,a)=>Math.max(1,Math.ceil(e/a)),eo=(0,r.useMemo)(()=>{let e=ec(f,$.pageSize),a=en.length===$.pageSize&&f<=$.pageSize*($.pageIndex+1),t=ei($.pageIndex);return a?Math.max(t,e,$.pageIndex+2):Math.max(t,e)},[$.pageSize,$.pageIndex,en.length,f]),eu=(0,r.useMemo)(()=>!O&&en.length===$.pageSize,[O,en.length,$.pageSize]),ev=(0,r.useMemo)(()=>D({onDeleted:()=>{X(),ea()},onToggleEnabled:async e=>{try{let a=e.isEnabled?await (0,g.g8)(e.id):await (0,g.H4)(e.id);u({title:"Schedule ".concat(a.isEnabled?"Enabled":"Disabled"),description:'"'.concat(e.name,'" is now ').concat(a.isEnabled?"active":"inactive","."),duration:3e3}),console.log("Refreshing both data sources after toggle..."),await Promise.all([X(),ea()]),console.log("Data refresh completed")}catch(e){throw console.error("Toggle enable failed:",e),u((0,S.m4)(e)),e}},onEdit:async e=>{p(function(e){let a=e.recurrenceType,t={type:a};e.cronExpression&&(a===g.V5.Daily?t={...t,...function(e){let a=e.split(" "),t=a[1];return{dailyHour:a[2],dailyMinute:t}}(e.cronExpression)}:a===g.V5.Weekly?t={...t,...function(e){var a;let t=e.split(" "),n=t[1];return{weeklyHour:t[2],weeklyMinute:n,selectedDays:null===(a=t[5])||void 0===a?void 0:a.split(",").map(e=>({0:"Sunday",1:"Monday",2:"Tuesday",3:"Wednesday",4:"Thursday",5:"Friday",6:"Saturday"})[e]).filter(Boolean)}}(e.cronExpression)}:a===g.V5.Monthly?t={...t,...function(e){let a=e.split(" "),t=a[1];return{monthlyHour:a[2],monthlyMinute:t,monthlyOnType:"day",selectedDay:a[3]}}(e.cronExpression)}:a===g.V5.Hourly?t={...t,...function(e){let a=e.split(" ");return{value:a[2].startsWith("*/")?a[2].replace("*/",""):"1"}}(e.cronExpression)}:a===g.V5.Minutes&&(t={...t,...function(e){let a=e.split(" ");return{value:a[1].startsWith("*/")?a[1].replace("*/",""):"1"}}(e.cronExpression)}));let n={id:e.id,name:e.name,packageId:e.automationPackageId,packageVersion:"latest",agentId:e.botAgentId,timezone:e.timeZoneId,recurrence:t};return a===g.V5.Once&&e.oneTimeExecution&&(n.oneTimeExecution=e.oneTimeExecution),n}(e)),m(!0)}}),[X,ea,u]),ey=(0,ex.N4)({data:en,columns:ev,state:{sorting:R,columnVisibility:j,rowSelection:v,columnFilters:P,pagination:$},enableRowSelection:!0,onRowSelectionChange:y,onSortingChange:e=>{let a="function"==typeof e?e(R):e;U(a),a.length>0?o(c,{sort:a[0].id,order:a[0].desc?"desc":"asc",page:"1"}):o(c,{sort:null,order:null,page:"1"}),X()},onColumnFiltersChange:L,onColumnVisibilityChange:b,onPaginationChange:e=>{console.log("Pagination change triggered");let a="function"==typeof e?e($):e;console.log("Current pagination:",$,"New pagination:",a),q(a),o(c,{page:(a.pageIndex+1).toString(),size:a.pageSize.toString()}),console.log("Forcing data reload for pagination change"),X()},getCoreRowModel:(0,ep.HT)(),getFilteredRowModel:(0,ep.hM)(),getPaginationRowModel:(0,ep.kW)(),getSortedRowModel:(0,ep.h5)(),getFacetedRowModel:(0,ep.kQ)(),getFacetedUniqueValues:(0,ep.oS)(),manualPagination:!0,pageCount:eo,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.id}),ej=(0,r.useCallback)(e=>{J(e),M(!0),z.current&&clearTimeout(z.current),z.current=setTimeout(()=>{let a=ey.getColumn("name");a&&(a.setFilterValue(e),o(c,{search:e||null,page:"1"}),X()),M(!1)},500)},[ey,o,c,X]),eb=(0,r.useCallback)(e=>{let a=ey.getColumn("isEnabled");if(a){let t="all"===e?"":e;a.setFilterValue(t),o(c,{status:t||null,page:"1"}),X()}},[ey,o,c,X]),ef=(0,r.useCallback)(e=>{let a=ey.getColumn("recurrenceType");if(a){let t="all"===e?"":e;a.setFilterValue(t),o(c,{type:t||null,page:"1"}),X()}},[ey,o,c,X]);(0,r.useEffect)(()=>()=>{z.current&&clearTimeout(z.current)},[]),(0,r.useEffect)(()=>{G&&(console.error("Failed to load schedules:",G),ee||u({title:"Error",description:"Failed to load schedules. Please try again.",variant:"destructive"}))},[G,ee,u]);let eN=(0,r.useCallback)(e=>{e&&X(a=>{if(!a)return a;if("value"in a&&Array.isArray(a.value)){let t={id:e.id,name:e.name,description:"",isEnabled:!0,recurrenceType:g.V5.Daily,timeZoneId:"UTC",automationPackageId:"",botAgentId:"",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return{...a,value:[t,...a.value],"@odata.count":(a["@odata.count"]||0)+1}}return a},!1),setTimeout(()=>{X()},2e3)},[X]),ew=Object.values(g.V5).map(e=>({value:e,label:(0,g.MK)(e)}));return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Schedules"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[f>0&&(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,n.jsxs)("span",{children:["Total: ",f," schedule",1!==f?"s":""]})}),(0,n.jsxs)(s.$,{onClick:()=>{p(null),m(!0)},className:"flex items-center justify-center",children:[(0,n.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Create Schedule"]})]})]}),G&&!ee&&(0,n.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,n.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load schedules. Please try again."}),(0,n.jsx)(s.$,{variant:"outline",className:"mt-2",onClick:()=>X(),children:"Retry"})]}),(0,n.jsx)(eh,{table:ey,statuses:[{value:"enabled",label:"Enabled"},{value:"disabled",label:"Disabled"}],recurrenceTypes:ew,onSearch:ej,onStatusChange:eb,onRecurrenceTypeChange:ef,searchValue:_,isFiltering:et,isPending:C,searchPlaceholder:"Search schedules by name...",totalCount:f}),(0,n.jsx)(T.b,{data:en,columns:ev,onRowClick:e=>{let a=c.startsWith("/admin")?"/admin/schedules/".concat(e.id):"/".concat(K,"/automation/schedule/").concat(e.id);i.push(a)},table:ey,isLoading:et,totalCount:f}),(0,n.jsx)(eg.d,{currentPage:$.pageIndex+1,pageSize:$.pageSize,totalCount:f,totalPages:eo,isLoading:et,isChangingPageSize:E,isUnknownTotalCount:eu,onPageChange:e=>{console.log("Page change requested to page ".concat(e)),q(a=>({...a,pageIndex:e-1})),o(c,{page:e.toString()}),console.log("Reloading data after page change"),setTimeout(()=>{X()},0)},onPageSizeChange:e=>{console.log("Page size change requested to ".concat(e)),V(!0);let a=Math.floor($.pageIndex*$.pageSize/e);q({pageSize:e,pageIndex:a}),o(c,{size:e.toString(),page:(a+1).toString()}),console.log("Reloading data after page size change"),setTimeout(()=>{X()},0)}})]}),(0,n.jsx)(ed,{isOpen:h,onClose:e=>{m(!1),p(null),e&&X()},mode:x?"edit":"create",editingSchedule:x,onSuccess:eN},null!==(t=null==x?void 0:x.id)&&void 0!==t?t:"new")]})}},66094:(e,a,t)=>{"use strict";t.d(a,{Fs:()=>d,H4:()=>u,MK:()=>g,Os:()=>i,V5:()=>l,VD:()=>o,g8:()=>h,po:()=>m,sF:()=>r,ye:()=>c});var n=t(7283),l=function(e){return e.Once="Once",e.Minutes="Minutes",e.Hourly="Hourly",e.Daily="Daily",e.Weekly="Weekly",e.Monthly="Monthly",e.Advanced="Advanced",e}({});let s=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},r=async e=>{let a=s();return await n.F.post("".concat(a,"/api/schedules"),e)},i=async()=>{let e=s();try{return await n.F.get("".concat(e,"/api/schedules"))}catch(e){return console.error("Error fetching all schedules:",e),[]}},c=async e=>{let a=s(),t={...e};(void 0===t.$top||t.$top<=0)&&(t.$top=10);let l=new Date().getTime(),r=function(e){let a=new URLSearchParams;return a.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[t,n]=e;null!=n&&"$count"!==t&&a.append(t,String(n))}),a.toString()}(t),i="".concat(a,"/odata/Schedules");r?i+="?".concat(r,"&_t=").concat(l):i+="?_t=".concat(l),console.log("Fetching schedules with endpoint: ".concat(i)),console.log("Page: ".concat(t.$skip?t.$skip/t.$top+1:1,", Size: ").concat(t.$top));try{let e=await n.F.get(i),a=function(e){return e&&"object"==typeof e?{value:Array.isArray(e.value)?e.value:[],"@odata.count":"number"==typeof e["@odata.count"]?e["@odata.count"]:void 0,"@odata.nextLink":"string"==typeof e["@odata.nextLink"]?e["@odata.nextLink"]:void 0}:{value:[]}}(e);return t.$top&&a.value.length>t.$top&&(console.warn("OData returned ".concat(a.value.length," items but only ").concat(t.$top," were requested. Trimming results.")),a.value=a.value.slice(0,t.$top)),console.log("Received ".concat(a.value.length," schedules from OData")),a}catch(e){return console.error("Error fetching schedules with OData:",e),{value:[]}}},d=async(e,a)=>{let t=s();return await n.F.put("".concat(t,"/api/schedules/").concat(e),a)},o=async e=>{let a=s();await n.F.delete("".concat(a,"/api/schedules/").concat(e))},u=async e=>{let a=s();return await n.F.post("".concat(a,"/api/schedules/").concat(e,"/enable"))},h=async e=>{let a=s();return await n.F.post("".concat(a,"/api/schedules/").concat(e,"/disable"))},m=e=>{if(!e)return"Not scheduled";try{let a=new Date(e);if(isNaN(a.getTime()))return"Invalid date";return new Intl.DateTimeFormat("en-US",{dateStyle:"medium",timeStyle:"short"}).format(a)}catch(e){return"Invalid date"}},g=e=>{switch(e){case"Once":return"Once";case"Minutes":return"Every few minutes";case"Hourly":return"Hourly";case"Daily":return"Daily";case"Weekly":return"Weekly";case"Monthly":return"Monthly";case"Advanced":return"Custom (Cron)";default:return"Unknown"}}}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,4953,6341,2178,8852,5699,8523,9483,3085,7295,3608,4727,5224,3003,8441,1684,7358],()=>a(1493)),_N_E=e.O()}]);