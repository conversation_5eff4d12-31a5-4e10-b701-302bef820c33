(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8757],{26126:(e,r,t)=>{"use strict";t.d(r,{E:()=>l});var n=t(95155);t(12115);var a=t(66634),s=t(74466),i=t(36928);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:t,asChild:s=!1,...l}=e,d=s?a.DX:"span";return(0,n.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),r),...l})}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>l});var n=t(95155),a=t(12115),s=t(66634),i=t(74466),o=t(36928);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:d=!1,...c}=e,u=d?s.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,o.cn)(l({variant:a,size:i,className:t})),ref:r,...c})});d.displayName="Button"},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,r,t)=>{"use strict";var n=t(18999);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},53904:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},64657:(e,r,t)=>{Promise.resolve().then(t.bind(t,70824))},66634:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i});var n=t(12115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var s=t(95155),i=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){var i;let e,o;let l=(i=t,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,r){let t={...r};for(let n in r){let a=e[n],s=r[n];/^on[A-Z]/.test(n)?a&&s?t[n]=(...e)=>{let r=s(...e);return a(...e),r}:a&&(t[n]=a):"style"===n?t[n]={...a,...s}:"className"===n&&(t[n]=[a,s].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(d.ref=r?function(...e){return r=>{let t=!1,n=e.map(e=>{let n=a(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():a(e[r],null)}}}}(r,l):l),n.cloneElement(t,d)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:a,...i}=e,o=n.Children.toArray(a),d=o.find(l);if(d){let e=d.props.children,a=o.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(r,{...i,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}("Slot"),o=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>i,wL:()=>c});var n=t(95155);t(12115);var a=t(36928);function s(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function o(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function l(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}function c(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},70824:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var n=t(95155),a=t(35695),s=t(30285),i=t(66695),o=t(26126),l=t(53904),d=t(35169),c=t(91788),u=t(12115),m=t(86490);t(67938);var v=t(75420),f=t(34953),g=t(70449),p=t(88262);let h=e=>"Disconnected"===e?"bg-red-100 text-red-600 border-none":"Busy"===e?"bg-yellow-100 text-yellow-600 border-none":"Available"===e?"bg-green-100 text-green-600 border-none":"bg-gray-100 text-gray-600 border-none";function x(e){var r,t;let{id:x}=e,y=(0,a.useRouter)(),j=(0,a.useParams)(),N=null==j?void 0:j.tenant,w=(0,v.d)(N),{toast:k}=(0,p.d)(),{data:A,error:R,isLoading:C}=(0,f.Ay)(x?g.DC.agentById(x):null,()=>(0,m.Ri)(x)),E=(0,u.useMemo)(()=>A?{...A,botAgentId:A.id}:null,[A]);(0,u.useEffect)(()=>{R&&(console.error("Failed to load agent details:",R),k({title:"Error",description:"Failed to load agent details.",variant:"destructive"}))},[R,k]);let _="".concat(window.location.protocol,"//").concat(window.location.host);(0,u.useEffect)(()=>{let e=console.error;return console.error=function(){for(var r=arguments.length,t=Array(r),n=0;n<r;n++)t[n]=arguments[n];if(t.length>0&&"string"==typeof t[0]&&(t[0].includes("Server timeout")||t[0].includes("SignalR")||t[0].includes("connection")||t[0].includes("Connection")||t[0].includes("Failed to start")||t[0].includes("negotiation"))){console.debug("[Suppressed SignalR Error]",...t);return}e(...t)},()=>{console.error=e}},[]);let P=null===(r=w[x])||void 0===r?void 0:r.status,S=null!==(t=null!=P?P:null==E?void 0:E.status)&&void 0!==t?t:"";return C?(0,n.jsx)("div",{className:"flex items-center justify-center h-full py-10",children:(0,n.jsx)("div",{className:"animate-spin text-primary",children:(0,n.jsx)(l.A,{className:"h-10 w-10"})})}):R?(0,n.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,n.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load agent details."}),(0,n.jsx)(s.$,{variant:"outline",className:"mt-2",onClick:()=>window.location.reload(),children:"Retry"})]}):E?(0,n.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,n.jsxs)(i.Zp,{className:"border rounded-md shadow-sm",children:[(0,n.jsxs)(i.aR,{className:"flex items-center justify-between border-b p-4",children:[(0,n.jsxs)(s.$,{variant:"ghost",size:"sm",className:"gap-1",onClick:()=>{y.back()},children:[(0,n.jsx)(d.A,{className:"h-4 w-4"}),"Back"]}),(0,n.jsxs)(s.$,{variant:"outline",size:"sm",className:"gap-1",onClick:()=>{window.location.href="https://openautomate-agent.s3.ap-southeast-1.amazonaws.com/OpenAutomate.BotAgent.Installer.msi"},children:[(0,n.jsx)(c.A,{className:"h-4 w-4"}),"Download Agent"]})]}),(0,n.jsxs)(i.Wu,{className:"p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(b,{label:"Name",children:E.name}),(0,n.jsx)(b,{label:"Machine name",children:E.machineName})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsx)(b,{label:"Status",children:(0,n.jsx)(o.E,{variant:"outline",className:h(S),children:S})})})]}),(0,n.jsxs)("div",{className:"mt-8 pt-6 border-t",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Connection Information"}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4",children:(0,n.jsxs)("div",{className:"bg-muted p-4 rounded-md",children:[(0,n.jsx)("div",{className:"flex justify-between items-center mb-2",children:(0,n.jsx)("span",{className:"text-sm font-medium",children:"Connection URL"})}),(0,n.jsx)("div",{className:"bg-card p-2 rounded border text-sm font-mono overflow-x-auto",children:"".concat(_,"/").concat(N)}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Use this URL to connect your bot agent to the OpenAutomate platform."})]})})]})]})]})}):(0,n.jsx)("div",{children:"Agent not found"})}function b(e){let{label:r,children:t}=e;return(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:r}),(0,n.jsx)("div",{className:"text-base font-medium pb-1 border-b",children:t})]})}function y(){let e=(0,a.useParams)().id;return(0,n.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,n.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,n.jsx)(x,{id:e})]})}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var n=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,i=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return s(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:o}=r,l=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],n=null==o?void 0:o[e];if(null===r)return null;let s=a(r)||a(n);return i[e][s]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return s(e,l,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...d}[r]):({...o,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,4953,3701,4727,4046,8441,1684,7358],()=>r(64657)),_N_E=e.O()}]);