(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1309],{5560:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("life-buoy",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.93 4.93 4.24 4.24",key:"1ymg45"}],["path",{d:"m14.83 9.17 4.24-4.24",key:"1cb5xl"}],["path",{d:"m14.83 14.83 4.24 4.24",key:"q42g0n"}],["path",{d:"m9.17 14.83-4.24 4.24",key:"bqpfvv"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]])},10081:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},14186:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},22432:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},22503:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("file-key-2",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v6",key:"rc0qvx"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"4",cy:"16",r:"2",key:"1ehqvc"}],["path",{d:"m10 10-4.5 4.5",key:"7fwrp6"}],["path",{d:"m9 11 1 1",key:"wa6s5q"}]])},23227:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},25657:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},28890:(e,t,r)=>{"use strict";r.d(t,{Ke:()=>A,R6:()=>M,bL:()=>S});var n=r(12115),a=r(95155),i=r(12640),l=globalThis?.document?n.useLayoutEffect:()=>{};function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function u(...e){return n.useCallback(s(...e),e)}r(47650);var c=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var i;let e,l;let o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{i(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(u.ref=t?s(t,o):o),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...l}=e,o=n.Children.toArray(i),s=o.find(d);if(s){let e=s.props.children,i=o.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,a.jsx)(t,{...l,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:t,{...l,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),m=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[a,i]=n.useState(),o=n.useRef({}),s=n.useRef(e),u=n.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=p(o.current);u.current="mounted"===c?e:"none"},[c]),l(()=>{let t=o.current,r=s.current;if(r!==e){let n=u.current,a=p(t);e?d("MOUNT"):"none"===a||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==a?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),l(()=>{if(a){var e;let t;let r=null!==(e=a.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=p(o.current).includes(e.animationName);if(e.target===a&&n&&(d("ANIMATION_END"),!s.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},i=e=>{e.target===a&&(u.current=p(o.current))};return a.addEventListener("animationstart",i),a.addEventListener("animationcancel",n),a.addEventListener("animationend",n),()=>{r.clearTimeout(t),a.removeEventListener("animationstart",i),a.removeEventListener("animationcancel",n),a.removeEventListener("animationend",n)}}d("ANIMATION_END")},[a,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{e&&(o.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),o=u(a.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,a=n&&"isReactWarning"in n&&n.isReactWarning;return a?e.ref:(a=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||a.isPresent?n.cloneElement(i,{ref:o}):null};function p(e){return(null==e?void 0:e.animationName)||"none"}m.displayName="Presence";var h=r(52496),y="Collapsible",[v,g]=function(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let a=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return i.scopeName=e,[function(t,i){let l=n.createContext(i),o=r.length;r=[...r,i];let s=t=>{let{scope:r,children:i,...s}=t,u=r?.[e]?.[o]||l,c=n.useMemo(()=>s,Object.values(s));return(0,a.jsx)(u.Provider,{value:c,children:i})};return s.displayName=t+"Provider",[s,function(r,a){let s=a?.[e]?.[o]||l,u=n.useContext(s);if(u)return u;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=r.reduce((t,{useScope:r,scopeName:n})=>{let a=r(e)[`__scope${n}`];return{...t,...a}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return r.scopeName=t.scopeName,r}(i,...t)]}(y),[b,k]=v(y),x=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:l,defaultOpen:o,disabled:s,onOpenChange:u,...c}=e,[d,m]=(0,i.i)({prop:l,defaultProp:null!=o&&o,onChange:u,caller:y});return(0,a.jsx)(b,{scope:r,disabled:s,contentId:(0,h.B)(),open:d,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),children:(0,a.jsx)(f.div,{"data-state":E(d),"data-disabled":s?"":void 0,...c,ref:t})})});x.displayName=y;var w="CollapsibleTrigger",M=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,i=k(w,r);return(0,a.jsx)(f.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":E(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...n,ref:t,onClick:function(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}(e.onClick,i.onOpenToggle)})});M.displayName=w;var N="CollapsibleContent",A=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,i=k(N,e.__scopeCollapsible);return(0,a.jsx)(m,{present:r||i.open,children:e=>{let{present:r}=e;return(0,a.jsx)(C,{...n,ref:t,present:r})}})});A.displayName=N;var C=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:i,children:o,...s}=e,c=k(N,r),[d,m]=n.useState(i),p=n.useRef(null),h=u(t,p),y=n.useRef(0),v=y.current,g=n.useRef(0),b=g.current,x=c.open||d,w=n.useRef(x),M=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(e)},[]),l(()=>{let e=p.current;if(e){M.current=M.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();y.current=t.height,g.current=t.width,w.current||(e.style.transitionDuration=M.current.transitionDuration,e.style.animationName=M.current.animationName),m(i)}},[c.open,i]),(0,a.jsx)(f.div,{"data-state":E(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!x,...s,ref:h,style:{"--radix-collapsible-content-height":v?"".concat(v,"px"):void 0,"--radix-collapsible-content-width":b?"".concat(b,"px"):void 0,...e.style},children:x&&o})});function E(e){return e?"open":"closed"}var S=x},34869:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47330:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},51362:(e,t,r)=>{"use strict";r.d(t,{D:()=>u,N:()=>c});var n=r(12115),a=(e,t,r,n,a,i,l,o)=>{let s=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&i?a.map(e=>i[e]||e):a;r?(s.classList.remove(...n),s.classList.add(i&&i[t]?i[t]:t)):s.setAttribute(e,t)}),r=t,o&&u.includes(r)&&(s.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},i=["light","dark"],l="(prefers-color-scheme: dark)",o=n.createContext(void 0),s={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(o))?e:s},c=e=>n.useContext(o)?n.createElement(n.Fragment,null,e.children):n.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:a=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:c=d,defaultTheme:f=a?"system":"light",attribute:v="data-theme",value:g,children:b,nonce:k,scriptProps:x}=e,[w,M]=n.useState(()=>p(u,f)),[N,A]=n.useState(()=>"system"===w?y():w),C=g?Object.values(g):c,E=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&a&&(t=y());let n=g?g[t]:t,l=r?h(k):null,o=document.documentElement,u=e=>{"class"===e?(o.classList.remove(...C),n&&o.classList.add(n)):e.startsWith("data-")&&(n?o.setAttribute(e,n):o.removeAttribute(e))};if(Array.isArray(v)?v.forEach(u):u(v),s){let e=i.includes(f)?f:null,r=i.includes(t)?t:e;o.style.colorScheme=r}null==l||l()},[k]),S=n.useCallback(e=>{let t="function"==typeof e?e(w):e;M(t);try{localStorage.setItem(u,t)}catch(e){}},[w]),R=n.useCallback(e=>{A(y(e)),"system"===w&&a&&!t&&E("system")},[w,t]);n.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(R),R(e),()=>e.removeListener(R)},[R]),n.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?M(e.newValue):S(f))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),n.useEffect(()=>{E(null!=t?t:w)},[t,w]);let D=n.useMemo(()=>({theme:w,setTheme:S,forcedTheme:t,resolvedTheme:"system"===w?N:w,themes:a?[...c,"system"]:c,systemTheme:a?N:void 0}),[w,S,t,N,a,c]);return n.createElement(o.Provider,{value:D},n.createElement(m,{forcedTheme:t,storageKey:u,attribute:v,enableSystem:a,enableColorScheme:s,defaultTheme:f,value:g,themes:c,nonce:k,scriptProps:x}),b)},m=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:i,enableSystem:l,enableColorScheme:o,defaultTheme:s,value:u,themes:c,nonce:d,scriptProps:f}=e,m=JSON.stringify([i,r,s,t,c,u,l,o]).slice(1,-1);return n.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(a.toString(),")(").concat(m,")")}})}),p=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},h=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},53311:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},73783:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},75819:()=>{},84109:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("refresh-ccw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]])},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86986:(e,t,r)=>{"use strict";r.d(t,{b:()=>f});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(47650);var i=r(95155),l=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,o;let s=(l=r,(o=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(o=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),u=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{i(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...l}=e,s=n.Children.toArray(a),u=s.find(o);if(u){let e=u.props.children,a=s.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:a,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?r:t,{...l,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),u="horizontal",c=["horizontal","vertical"],d=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:a=u,...l}=e,o=(r=a,c.includes(r))?a:u;return(0,i.jsx)(s.div,{"data-orientation":o,...n?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...l,ref:t})});d.displayName="Separator";var f=d},90498:(e,t,r)=>{"use strict";r.d(t,{m:()=>c});var n=r(92084),a=r(35476);function i(e,t){let r=(0,a.a)(e),n=(0,a.a)(t),i=r.getTime()-n.getTime();return i<0?-1:i>0?1:i}var l=r(41876),o=r(53072),s=r(36199),u=r(43461);function c(e,t){return function(e,t,r){var n,c;let d,f,m;let p=(0,s.q)(),h=null!==(c=null!==(n=null==r?void 0:r.locale)&&void 0!==n?n:p.locale)&&void 0!==c?c:o.c,y=i(e,t);if(isNaN(y))throw RangeError("Invalid time value");let v=Object.assign({},r,{addSuffix:null==r?void 0:r.addSuffix,comparison:y});y>0?(d=(0,a.a)(t),f=(0,a.a)(e)):(d=(0,a.a)(e),f=(0,a.a)(t));let g=function(e,t,r){var n;let i=(+(0,a.a)(e)-+(0,a.a)(t))/1e3;return(n=null==void 0?void 0:(void 0).roundingMethod,e=>{let t=(n?Math[n]:Math.trunc)(e);return 0===t?0:t})(i)}(f,d),b=Math.round((g-((0,u.G)(f)-(0,u.G)(d))/1e3)/60);if(b<2){if(null==r?void 0:r.includeSeconds){if(g<5)return h.formatDistance("lessThanXSeconds",5,v);if(g<10)return h.formatDistance("lessThanXSeconds",10,v);if(g<20)return h.formatDistance("lessThanXSeconds",20,v);else if(g<40)return h.formatDistance("halfAMinute",0,v);else if(g<60)return h.formatDistance("lessThanXMinutes",1,v);else return h.formatDistance("xMinutes",1,v)}return 0===b?h.formatDistance("lessThanXMinutes",1,v):h.formatDistance("xMinutes",b,v)}if(b<45)return h.formatDistance("xMinutes",b,v);if(b<90)return h.formatDistance("aboutXHours",1,v);if(b<l.F6){let e=Math.round(b/60);return h.formatDistance("aboutXHours",e,v)}if(b<2520)return h.formatDistance("xDays",1,v);else if(b<l.Nw){let e=Math.round(b/l.F6);return h.formatDistance("xDays",e,v)}else if(b<2*l.Nw)return m=Math.round(b/l.Nw),h.formatDistance("aboutXMonths",m,v);if((m=function(e,t){let r;let n=(0,a.a)(e),l=(0,a.a)(t),o=i(n,l),s=Math.abs(function(e,t){let r=(0,a.a)(e),n=(0,a.a)(t);return 12*(r.getFullYear()-n.getFullYear())+(r.getMonth()-n.getMonth())}(n,l));if(s<1)r=0;else{1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-o*s);let t=i(n,l)===-o;(function(e){let t=(0,a.a)(e);return+function(e){let t=(0,a.a)(e);return t.setHours(23,59,59,999),t}(t)==+function(e){let t=(0,a.a)(e),r=t.getMonth();return t.setFullYear(t.getFullYear(),r+1,0),t.setHours(23,59,59,999),t}(t)})((0,a.a)(e))&&1===s&&1===i(e,l)&&(t=!1),r=o*(s-Number(t))}return 0===r?0:r}(f,d))<12){let e=Math.round(b/l.Nw);return h.formatDistance("xMonths",e,v)}{let e=m%12,t=Math.trunc(m/12);return e<3?h.formatDistance("aboutXYears",t,v):e<9?h.formatDistance("overXYears",t,v):h.formatDistance("almostXYears",t+1,v)}}(e,(0,n.w)(e,Date.now()),t)}},99853:(e,t,r)=>{"use strict";r.d(t,{bL:()=>d,Qg:()=>u});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(47650);var i=r(95155),l=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,o;let s=(l=r,(o=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(o=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),u=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{i(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...l}=e,s=n.Children.toArray(a),u=s.find(o);if(u){let e=u.props.children,a=s.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:a,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?r:t,{...l,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),u=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),c=n.forwardRef((e,t)=>(0,i.jsx)(s.span,{...e,ref:t,style:{...u,...e.style}}));c.displayName="VisuallyHidden";var d=c}}]);