"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5699],{13052:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18289:(e,n,r)=>{r.d(n,{H_:()=>nt,UC:()=>e8,ty:()=>eH,YJ:()=>ne,q7:()=>nr,VF:()=>no,JU:()=>nn,ZL:()=>e7,bL:()=>e3,wv:()=>nl,l9:()=>e4});var t=r(12115);function o(e,n,{checkForDefaultPrevented:r=!0}={}){return function(t){if(e?.(t),!1===r||!t.defaultPrevented)return n?.(t)}}function l(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function i(...e){return n=>{let r=!1,t=e.map(e=>{let t=l(e,n);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let n=0;n<t.length;n++){let r=t[n];"function"==typeof r?r():l(e[n],null)}}}}var u=r(95155),a=r(12640),c=r(47650),s=Symbol("radix.slottable");function d(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{let r=function(e){let n=function(e){let n=t.forwardRef((e,n)=>{let{children:r,...o}=e;if(t.isValidElement(r)){var l;let e,u;let a=(l=r,(u=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(u=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,n){let r={...n};for(let t in n){let o=e[t],l=n[t];/^on[A-Z]/.test(t)?o&&l?r[t]=(...e)=>{l(...e),o(...e)}:o&&(r[t]=o):"style"===t?r[t]={...o,...l}:"className"===t&&(r[t]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==t.Fragment&&(c.ref=n?i(n,a):a),t.cloneElement(r,c)}return t.Children.count(r)>1?t.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}(e),r=t.forwardRef((e,r)=>{let{children:o,...l}=e,i=t.Children.toArray(o),a=i.find(d);if(a){let e=a.props.children,o=i.map(n=>n!==a?n:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,u.jsx)(n,{...l,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,o):null})}return(0,u.jsx)(n,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}(`Primitive.${n}`),o=t.forwardRef((e,t)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?r:n,{...l,ref:t})});return o.displayName=`Primitive.${n}`,{...e,[n]:o}},{});function p(e,n,{checkForDefaultPrevented:r=!0}={}){return function(t){if(e?.(t),!1===r||!t.defaultPrevented)return n?.(t)}}var m=r(60671);function v(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function h(...e){return n=>{let r=!1,t=e.map(e=>{let t=v(e,n);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let n=0;n<t.length;n++){let r=t[n];"function"==typeof r?r():v(e[n],null)}}}}function g(...e){return t.useCallback(h(...e),e)}var w=t.createContext(void 0),y=r(7166),x=r(92293),b=r(12307),C=r(52496),R=r(13227),M=r(962),N=globalThis?.document?t.useLayoutEffect:()=>{},_=e=>{let{present:n,children:r}=e,o=function(e){var n,r;let[o,l]=t.useState(),i=t.useRef({}),u=t.useRef(e),a=t.useRef("none"),[c,s]=(n=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,n)=>{let t=r[e][n];return null!=t?t:e},n));return t.useEffect(()=>{let e=j(i.current);a.current="mounted"===c?e:"none"},[c]),N(()=>{let n=i.current,r=u.current;if(r!==e){let t=a.current,o=j(n);e?s("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?s("UNMOUNT"):r&&t!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),N(()=>{if(o){var e;let n;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,t=e=>{let t=j(i.current).includes(e.animationName);if(e.target===o&&t&&(s("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(a.current=j(i.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(n),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}s("ANIMATION_END")},[o,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{e&&(i.current=getComputedStyle(e)),l(e)},[])}}(n),l="function"==typeof r?r({present:o.isPresent}):t.Children.only(r),i=g(o.ref,function(e){var n,r;let t=null===(n=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===n?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(o=(t=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?t.cloneElement(l,{ref:i}):null};function j(e){return(null==e?void 0:e.animationName)||"none"}function E(e){let n=function(e){let n=t.forwardRef((e,n)=>{let{children:r,...o}=e;if(t.isValidElement(r)){var l;let e,i;let u=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),a=function(e,n){let r={...n};for(let t in n){let o=e[t],l=n[t];/^on[A-Z]/.test(t)?o&&l?r[t]=(...e)=>{l(...e),o(...e)}:o&&(r[t]=o):"style"===t?r[t]={...o,...l}:"className"===t&&(r[t]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==t.Fragment&&(a.ref=n?h(n,u):u),t.cloneElement(r,a)}return t.Children.count(r)>1?t.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}(e),r=t.forwardRef((e,r)=>{let{children:o,...l}=e,i=t.Children.toArray(o),a=i.find(P);if(a){let e=a.props.children,o=i.map(n=>n!==a?n:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,u.jsx)(n,{...l,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,o):null})}return(0,u.jsx)(n,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}_.displayName="Presence";var D=Symbol("radix.slottable");function P(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===D}var I=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{let r=E(`Primitive.${n}`),o=t.forwardRef((e,t)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?r:n,{...l,ref:t})});return o.displayName=`Primitive.${n}`,{...e,[n]:o}},{}),O=r(82353);function k(e){let n=t.useRef(e);return t.useEffect(()=>{n.current=e}),t.useMemo(()=>(...e)=>n.current?.(...e),[])}var S=r(38168),T=r(93795),A=["Enter"," "],F=["ArrowUp","PageDown","End"],L=["ArrowDown","PageUp","Home",...F],$={ltr:[...A,"ArrowRight"],rtl:[...A,"ArrowLeft"]},K={ltr:["ArrowLeft"],rtl:["ArrowRight"]},U="Menu",[V,W,G]=(0,m.N)(U),[B,q]=function(e,n=[]){let r=[],o=()=>{let n=r.map(e=>t.createContext(e));return function(r){let o=r?.[e]||n;return t.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(n,o){let l=t.createContext(o),i=r.length;r=[...r,o];let a=n=>{let{scope:r,children:o,...a}=n,c=r?.[e]?.[i]||l,s=t.useMemo(()=>a,Object.values(a));return(0,u.jsx)(c.Provider,{value:s,children:o})};return a.displayName=n+"Provider",[a,function(r,u){let a=u?.[e]?.[i]||l,c=t.useContext(a);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((n,{useScope:r,scopeName:t})=>{let o=r(e)[`__scope${t}`];return{...n,...o}},{});return t.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return r.scopeName=n.scopeName,r}(o,...n)]}(U,[G,R.Bk,O.RG]),Z=(0,R.Bk)(),X=(0,O.RG)(),[z,H]=B(U),[Y,J]=B(U),Q=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:l,onOpenChange:i,modal:a=!0}=e,c=Z(n),[s,d]=t.useState(null),f=t.useRef(!1),p=k(i),m=function(e){let n=t.useContext(w);return e||n||"ltr"}(l);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,u.jsx)(R.bL,{...c,children:(0,u.jsx)(z,{scope:n,open:r,onOpenChange:p,content:s,onContentChange:d,children:(0,u.jsx)(Y,{scope:n,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:a,children:o})})})};Q.displayName=U;var ee=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=Z(r);return(0,u.jsx)(R.Mz,{...o,...t,ref:n})});ee.displayName="MenuAnchor";var en="MenuPortal",[er,et]=B(en,{forceMount:void 0}),eo=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,l=H(en,n);return(0,u.jsx)(er,{scope:n,forceMount:r,children:(0,u.jsx)(_,{present:r||l.open,children:(0,u.jsx)(M.Z,{asChild:!0,container:o,children:t})})})};eo.displayName=en;var el="MenuContent",[ei,eu]=B(el),ea=t.forwardRef((e,n)=>{let r=et(el,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,l=H(el,e.__scopeMenu),i=J(el,e.__scopeMenu);return(0,u.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,u.jsx)(_,{present:t||l.open,children:(0,u.jsx)(V.Slot,{scope:e.__scopeMenu,children:i.modal?(0,u.jsx)(ec,{...o,ref:n}):(0,u.jsx)(es,{...o,ref:n})})})})}),ec=t.forwardRef((e,n)=>{let r=H(el,e.__scopeMenu),o=t.useRef(null),l=g(n,o);return t.useEffect(()=>{let e=o.current;if(e)return(0,S.Eq)(e)},[]),(0,u.jsx)(ef,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:p(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),es=t.forwardRef((e,n)=>{let r=H(el,e.__scopeMenu);return(0,u.jsx)(ef,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ed=E("MenuContent.ScrollLock"),ef=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:o=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:a,disableOutsidePointerEvents:c,onEntryFocus:s,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:v,onDismiss:h,disableOutsideScroll:w,...C}=e,M=H(el,r),N=J(el,r),_=Z(r),j=X(r),E=W(r),[D,P]=t.useState(null),I=t.useRef(null),k=g(n,I,M.onContentChange),S=t.useRef(0),A=t.useRef(""),$=t.useRef(0),K=t.useRef(null),U=t.useRef("right"),V=t.useRef(0),G=w?T.A:t.Fragment,B=e=>{var n,r;let t=A.current+e,o=E().filter(e=>!e.disabled),l=document.activeElement,i=null===(n=o.find(e=>e.ref.current===l))||void 0===n?void 0:n.textValue,u=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,l=(t=Math.max(r?e.indexOf(r):-1,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(l=l.filter(e=>e!==r));let i=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}(o.map(e=>e.textValue),t,i),a=null===(r=o.find(e=>e.textValue===u))||void 0===r?void 0:r.ref.current;!function e(n){A.current=n,window.clearTimeout(S.current),""!==n&&(S.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};t.useEffect(()=>()=>window.clearTimeout(S.current),[]),(0,x.Oh)();let q=t.useCallback(e=>{var n,r;return U.current===(null===(n=K.current)||void 0===n?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,l=n.length-1;e<n.length;l=e++){let i=n[e],u=n[l],a=i.x,c=i.y,s=u.x,d=u.y;c>t!=d>t&&r<(s-a)*(t-c)/(d-c)+a&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null===(r=K.current)||void 0===r?void 0:r.area)},[]);return(0,u.jsx)(ei,{scope:r,searchRef:A,onItemEnter:t.useCallback(e=>{q(e)&&e.preventDefault()},[q]),onItemLeave:t.useCallback(e=>{var n;q(e)||(null===(n=I.current)||void 0===n||n.focus(),P(null))},[q]),onTriggerLeave:t.useCallback(e=>{q(e)&&e.preventDefault()},[q]),pointerGraceTimerRef:$,onPointerGraceIntentChange:t.useCallback(e=>{K.current=e},[]),children:(0,u.jsx)(G,{...w?{as:ed,allowPinchZoom:!0}:void 0,children:(0,u.jsx)(b.n,{asChild:!0,trapped:l,onMountAutoFocus:p(i,e=>{var n;e.preventDefault(),null===(n=I.current)||void 0===n||n.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:(0,u.jsx)(y.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:v,onDismiss:h,children:(0,u.jsx)(O.bL,{asChild:!0,...j,dir:N.dir,orientation:"vertical",loop:o,currentTabStopId:D,onCurrentTabStopIdChange:P,onEntryFocus:p(s,e=>{N.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,u.jsx)(R.UC,{role:"menu","aria-orientation":"vertical","data-state":eL(M.open),"data-radix-menu-content":"",dir:N.dir,..._,...C,ref:k,style:{outline:"none",...C.style},onKeyDown:p(C.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&B(e.key));let o=I.current;if(e.target!==o||!L.includes(e.key))return;e.preventDefault();let l=E().filter(e=>!e.disabled).map(e=>e.ref.current);F.includes(e.key)&&l.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(l)}),onBlur:p(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(S.current),A.current="")}),onPointerMove:p(e.onPointerMove,eU(e=>{let n=e.target,r=V.current!==e.clientX;e.currentTarget.contains(n)&&r&&(U.current=e.clientX>V.current?"right":"left",V.current=e.clientX)}))})})})})})})});ea.displayName=el;var ep=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,u.jsx)(I.div,{role:"group",...t,ref:n})});ep.displayName="MenuGroup";var em=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,u.jsx)(I.div,{...t,ref:n})});em.displayName="MenuLabel";var ev="MenuItem",eh="menu.itemSelect",eg=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:o,...l}=e,i=t.useRef(null),a=J(ev,e.__scopeMenu),s=eu(ev,e.__scopeMenu),d=g(n,i),f=t.useRef(!1);return(0,u.jsx)(ew,{...l,ref:d,disabled:r,onClick:p(e.onClick,()=>{let e=i.current;if(!r&&e){let n=new CustomEvent(eh,{bubbles:!0,cancelable:!0});e.addEventListener(eh,e=>null==o?void 0:o(e),{once:!0}),function(e,n){e&&c.flushSync(()=>e.dispatchEvent(n))}(e,n),n.defaultPrevented?f.current=!1:a.onClose()}}),onPointerDown:n=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,n),f.current=!0},onPointerUp:p(e.onPointerUp,e=>{var n;f.current||null===(n=e.currentTarget)||void 0===n||n.click()}),onKeyDown:p(e.onKeyDown,e=>{let n=""!==s.searchRef.current;!r&&(!n||" "!==e.key)&&A.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eg.displayName=ev;var ew=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:o=!1,textValue:l,...i}=e,a=eu(ev,r),c=X(r),s=t.useRef(null),d=g(n,s),[f,m]=t.useState(!1),[v,h]=t.useState("");return t.useEffect(()=>{let e=s.current;if(e){var n;h((null!==(n=e.textContent)&&void 0!==n?n:"").trim())}},[i.children]),(0,u.jsx)(V.ItemSlot,{scope:r,disabled:o,textValue:null!=l?l:v,children:(0,u.jsx)(O.q7,{asChild:!0,...c,focusable:!o,children:(0,u.jsx)(I.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...i,ref:d,onPointerMove:p(e.onPointerMove,eU(e=>{o?a.onItemLeave(e):(a.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:p(e.onPointerLeave,eU(e=>a.onItemLeave(e))),onFocus:p(e.onFocus,()=>m(!0)),onBlur:p(e.onBlur,()=>m(!1))})})})}),ey=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...o}=e;return(0,u.jsx)(ej,{scope:e.__scopeMenu,checked:r,children:(0,u.jsx)(eg,{role:"menuitemcheckbox","aria-checked":e$(r)?"mixed":r,...o,ref:n,"data-state":eK(r),onSelect:p(o.onSelect,()=>null==t?void 0:t(!!e$(r)||!r),{checkForDefaultPrevented:!1})})})});ey.displayName="MenuCheckboxItem";var ex="MenuRadioGroup",[eb,eC]=B(ex,{value:void 0,onValueChange:()=>{}}),eR=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,l=k(t);return(0,u.jsx)(eb,{scope:e.__scopeMenu,value:r,onValueChange:l,children:(0,u.jsx)(ep,{...o,ref:n})})});eR.displayName=ex;var eM="MenuRadioItem",eN=t.forwardRef((e,n)=>{let{value:r,...t}=e,o=eC(eM,e.__scopeMenu),l=r===o.value;return(0,u.jsx)(ej,{scope:e.__scopeMenu,checked:l,children:(0,u.jsx)(eg,{role:"menuitemradio","aria-checked":l,...t,ref:n,"data-state":eK(l),onSelect:p(t.onSelect,()=>{var e;return null===(e=o.onValueChange)||void 0===e?void 0:e.call(o,r)},{checkForDefaultPrevented:!1})})})});eN.displayName=eM;var e_="MenuItemIndicator",[ej,eE]=B(e_,{checked:!1}),eD=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,l=eE(e_,r);return(0,u.jsx)(_,{present:t||e$(l.checked)||!0===l.checked,children:(0,u.jsx)(I.span,{...o,ref:n,"data-state":eK(l.checked)})})});eD.displayName=e_;var eP=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,u.jsx)(I.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});eP.displayName="MenuSeparator";var eI=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=Z(r);return(0,u.jsx)(R.i3,{...o,...t,ref:n})});eI.displayName="MenuArrow";var[eO,ek]=B("MenuSub"),eS="MenuSubTrigger",eT=t.forwardRef((e,n)=>{let r=H(eS,e.__scopeMenu),o=J(eS,e.__scopeMenu),l=ek(eS,e.__scopeMenu),i=eu(eS,e.__scopeMenu),a=t.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:s}=i,d={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),s(null)}},[c,s]),(0,u.jsx)(ee,{asChild:!0,...d,children:(0,u.jsx)(ew,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eL(r.open),...e,ref:h(n,l.onTriggerChange),onClick:n=>{var t;null===(t=e.onClick)||void 0===t||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:p(e.onPointerMove,eU(n=>{i.onItemEnter(n),n.defaultPrevented||e.disabled||r.open||a.current||(i.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100))})),onPointerLeave:p(e.onPointerLeave,eU(e=>{var n,t;f();let o=null===(n=r.content)||void 0===n?void 0:n.getBoundingClientRect();if(o){let n=null===(t=r.content)||void 0===t?void 0:t.dataset.side,l="right"===n,u=o[l?"left":"right"],a=o[l?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:u,y:o.top},{x:a,y:o.top},{x:a,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:p(e.onKeyDown,n=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&$[o.dir].includes(n.key)){var l;r.onOpenChange(!0),null===(l=r.content)||void 0===l||l.focus(),n.preventDefault()}})})})});eT.displayName=eS;var eA="MenuSubContent",eF=t.forwardRef((e,n)=>{let r=et(el,e.__scopeMenu),{forceMount:o=r.forceMount,...l}=e,i=H(el,e.__scopeMenu),a=J(el,e.__scopeMenu),c=ek(eA,e.__scopeMenu),s=t.useRef(null),d=g(n,s);return(0,u.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,u.jsx)(_,{present:o||i.open,children:(0,u.jsx)(V.Slot,{scope:e.__scopeMenu,children:(0,u.jsx)(ef,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:d,align:"start",side:"rtl"===a.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;a.isUsingKeyboardRef.current&&(null===(n=s.current)||void 0===n||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:p(e.onFocusOutside,e=>{e.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:p(e.onEscapeKeyDown,e=>{a.onClose(),e.preventDefault()}),onKeyDown:p(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=K[a.dir].includes(e.key);if(n&&r){var t;i.onOpenChange(!1),null===(t=c.trigger)||void 0===t||t.focus(),e.preventDefault()}})})})})})});function eL(e){return e?"open":"closed"}function e$(e){return"indeterminate"===e}function eK(e){return e$(e)?"indeterminate":e?"checked":"unchecked"}function eU(e){return n=>"mouse"===n.pointerType?e(n):void 0}eF.displayName=eA;var eV="DropdownMenu",[eW,eG]=function(e,n=[]){let r=[],o=()=>{let n=r.map(e=>t.createContext(e));return function(r){let o=r?.[e]||n;return t.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(n,o){let l=t.createContext(o),i=r.length;r=[...r,o];let a=n=>{let{scope:r,children:o,...a}=n,c=r?.[e]?.[i]||l,s=t.useMemo(()=>a,Object.values(a));return(0,u.jsx)(c.Provider,{value:s,children:o})};return a.displayName=n+"Provider",[a,function(r,u){let a=u?.[e]?.[i]||l,c=t.useContext(a);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((n,{useScope:r,scopeName:t})=>{let o=r(e)[`__scope${t}`];return{...n,...o}},{});return t.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return r.scopeName=n.scopeName,r}(o,...n)]}(eV,[q]),eB=q(),[eq,eZ]=eW(eV),eX=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:l,defaultOpen:i,onOpenChange:c,modal:s=!0}=e,d=eB(n),f=t.useRef(null),[p,m]=(0,a.i)({prop:l,defaultProp:null!=i&&i,onChange:c,caller:eV});return(0,u.jsx)(eq,{scope:n,triggerId:(0,C.B)(),triggerRef:f,contentId:(0,C.B)(),open:p,onOpenChange:m,onOpenToggle:t.useCallback(()=>m(e=>!e),[m]),modal:s,children:(0,u.jsx)(Q,{...d,open:p,onOpenChange:m,dir:o,modal:s,children:r})})};eX.displayName=eV;var ez="DropdownMenuTrigger",eH=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...l}=e,a=eZ(ez,r),c=eB(r);return(0,u.jsx)(ee,{asChild:!0,...c,children:(0,u.jsx)(f.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:i(n,a.triggerRef),onPointerDown:o(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:o(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eH.displayName=ez;var eY=e=>{let{__scopeDropdownMenu:n,...r}=e,t=eB(n);return(0,u.jsx)(eo,{...t,...r})};eY.displayName="DropdownMenuPortal";var eJ="DropdownMenuContent",eQ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...l}=e,i=eZ(eJ,r),a=eB(r),c=t.useRef(!1);return(0,u.jsx)(ea,{id:i.contentId,"aria-labelledby":i.triggerId,...a,...l,ref:n,onCloseAutoFocus:o(e.onCloseAutoFocus,e=>{var n;c.current||null===(n=i.triggerRef.current)||void 0===n||n.focus(),c.current=!1,e.preventDefault()}),onInteractOutside:o(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!i.modal||t)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eQ.displayName=eJ;var e0=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(ep,{...o,...t,ref:n})});e0.displayName="DropdownMenuGroup";var e1=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(em,{...o,...t,ref:n})});e1.displayName="DropdownMenuLabel";var e6=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(eg,{...o,...t,ref:n})});e6.displayName="DropdownMenuItem";var e5=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(ey,{...o,...t,ref:n})});e5.displayName="DropdownMenuCheckboxItem",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(eR,{...o,...t,ref:n})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(eN,{...o,...t,ref:n})}).displayName="DropdownMenuRadioItem";var e2=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(eD,{...o,...t,ref:n})});e2.displayName="DropdownMenuItemIndicator";var e9=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(eP,{...o,...t,ref:n})});e9.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(eI,{...o,...t,ref:n})}).displayName="DropdownMenuArrow",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(eT,{...o,...t,ref:n})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eB(r);return(0,u.jsx)(eF,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e3=eX,e4=eH,e7=eY,e8=eQ,ne=e0,nn=e1,nr=e6,nt=e5,no=e2,nl=e9},42355:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},51154:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},82353:(e,n,r)=>{r.d(n,{q7:()=>O,bL:()=>I,RG:()=>C});var t=r(12115);function o(e,n,{checkForDefaultPrevented:r=!0}={}){return function(t){if(e?.(t),!1===r||!t.defaultPrevented)return n?.(t)}}var l=r(60671);function i(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function u(...e){return n=>{let r=!1,t=e.map(e=>{let t=i(e,n);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let n=0;n<t.length;n++){let r=t[n];"function"==typeof r?r():i(e[n],null)}}}}var a=r(95155),c=r(52496);r(47650);var s=Symbol("radix.slottable");function d(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{let r=function(e){let n=function(e){let n=t.forwardRef((e,n)=>{let{children:r,...o}=e;if(t.isValidElement(r)){var l;let e,i;let a=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,n){let r={...n};for(let t in n){let o=e[t],l=n[t];/^on[A-Z]/.test(t)?o&&l?r[t]=(...e)=>{l(...e),o(...e)}:o&&(r[t]=o):"style"===t?r[t]={...o,...l}:"className"===t&&(r[t]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==t.Fragment&&(c.ref=n?u(n,a):a),t.cloneElement(r,c)}return t.Children.count(r)>1?t.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}(e),r=t.forwardRef((e,r)=>{let{children:o,...l}=e,i=t.Children.toArray(o),u=i.find(d);if(u){let e=u.props.children,o=i.map(n=>n!==u?n:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,a.jsx)(n,{...l,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,o):null})}return(0,a.jsx)(n,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}(`Primitive.${n}`),o=t.forwardRef((e,t)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?r:n,{...l,ref:t})});return o.displayName=`Primitive.${n}`,{...e,[n]:o}},{}),p=r(12640),m=t.createContext(void 0),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[w,y,x]=(0,l.N)(g),[b,C]=function(e,n=[]){let r=[],o=()=>{let n=r.map(e=>t.createContext(e));return function(r){let o=r?.[e]||n;return t.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(n,o){let l=t.createContext(o),i=r.length;r=[...r,o];let u=n=>{let{scope:r,children:o,...u}=n,c=r?.[e]?.[i]||l,s=t.useMemo(()=>u,Object.values(u));return(0,a.jsx)(c.Provider,{value:s,children:o})};return u.displayName=n+"Provider",[u,function(r,u){let a=u?.[e]?.[i]||l,c=t.useContext(a);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((n,{useScope:r,scopeName:t})=>{let o=r(e)[`__scope${t}`];return{...n,...o}},{});return t.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return r.scopeName=n.scopeName,r}(o,...n)]}(g,[x]),[R,M]=b(g),N=t.forwardRef((e,n)=>(0,a.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(_,{...e,ref:n})})}));N.displayName=g;var _=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,orientation:l,loop:i=!1,dir:c,currentTabStopId:s,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:b=!1,...C}=e,M=t.useRef(null),N=function(...e){return t.useCallback(u(...e),e)}(n,M),_=function(e){let n=t.useContext(m);return e||n||"ltr"}(c),[j,E]=(0,p.i)({prop:s,defaultProp:null!=d?d:null,onChange:w,caller:g}),[D,I]=t.useState(!1),O=function(e){let n=t.useRef(e);return t.useEffect(()=>{n.current=e}),t.useMemo(()=>(...e)=>n.current?.(...e),[])}(x),k=y(r),S=t.useRef(!1),[T,A]=t.useState(0);return t.useEffect(()=>{let e=M.current;if(e)return e.addEventListener(v,O),()=>e.removeEventListener(v,O)},[O]),(0,a.jsx)(R,{scope:r,orientation:l,dir:_,loop:i,currentTabStopId:j,onItemFocus:t.useCallback(e=>E(e),[E]),onItemShiftTab:t.useCallback(()=>I(!0),[]),onFocusableItemAdd:t.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>A(e=>e-1),[]),children:(0,a.jsx)(f.div,{tabIndex:D||0===T?-1:0,"data-orientation":l,...C,ref:N,style:{outline:"none",...e.style},onMouseDown:o(e.onMouseDown,()=>{S.current=!0}),onFocus:o(e.onFocus,e=>{let n=!S.current;if(e.target===e.currentTarget&&n&&!D){let n=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=k().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),b)}}S.current=!1}),onBlur:o(e.onBlur,()=>I(!1))})})}),j="RovingFocusGroupItem",E=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,focusable:l=!0,active:i=!1,tabStopId:u,children:s,...d}=e,p=(0,c.B)(),m=u||p,v=M(j,r),h=v.currentTabStopId===m,g=y(r),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:C}=v;return t.useEffect(()=>{if(l)return x(),()=>b()},[l,x,b]),(0,a.jsx)(w.ItemSlot,{scope:r,id:m,focusable:l,active:i,children:(0,a.jsx)(f.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...d,ref:n,onMouseDown:o(e.onMouseDown,e=>{l?v.onItemFocus(m):e.preventDefault()}),onFocus:o(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:o(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let n=function(e,n,r){var t;let o=(t=e.key,"rtl"!==r?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return D[o]}(e,v.orientation,v.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)r.reverse();else if("prev"===n||"next"===n){"prev"===n&&r.reverse();let t=r.indexOf(e.currentTarget);r=v.loop?function(e,n){return e.map((r,t)=>e[(n+t)%e.length])}(r,t+1):r.slice(t+1)}setTimeout(()=>P(r))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=C}):s})})});E.displayName=j;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let t of e)if(t===r||(t.focus({preventScroll:n}),document.activeElement!==r))return}var I=N,O=E}}]);