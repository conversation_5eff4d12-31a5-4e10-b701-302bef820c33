<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OpenAutomate.API</name>
    </assembly>
    <members>
        <member name="T:OpenAutomate.API.Attributes.EnableResponseCacheAttribute">
            <summary>
            Attribute to enable response caching for API endpoints using Redis.
            Caches the response based on request path, query parameters, and user context.
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Attributes.EnableResponseCacheAttribute.#ctor(System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the EnableResponseCacheAttribute.
            </summary>
            <param name="durationInSeconds">Cache duration in seconds</param>
            <param name="varyByUser">Whether to vary cache by user ID</param>
            <param name="varyByTenant">Whether to vary cache by tenant ID</param>
        </member>
        <member name="T:OpenAutomate.API.Attributes.EnableResponseCacheAttribute.CachedApiResponse">
            <summary>
            Represents a cached API response
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Attributes.RequirePermissionAttribute">
            <summary>
            Requires a specific permission on a resource for authorization
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Attributes.RequirePermissionAttribute.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Attributes.RequirePermissionAttribute"/> class
            </summary>
            <param name="resource">The resource type</param>
            <param name="permission">The permission required</param>
        </member>
        <member name="M:OpenAutomate.API.Attributes.RequirePermissionAttribute.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Executes the permission check before the action executes
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Attributes.SubscriptionOperationType">
            <summary>
            Operation types for subscription requirements
            </summary>
        </member>
        <member name="F:OpenAutomate.API.Attributes.SubscriptionOperationType.Read">
            <summary>
            Read operations (GET requests) - allowed for expired trials
            </summary>
        </member>
        <member name="F:OpenAutomate.API.Attributes.SubscriptionOperationType.Write">
            <summary>
            Write operations (POST, PUT, DELETE, PATCH) - requires active subscription
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Attributes.RequireSubscriptionAttribute">
            <summary>
            Requires an active subscription (trial or paid) for authorization
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Attributes.RequireSubscriptionAttribute.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Attributes.RequireSubscriptionAttribute"/> class
            </summary>
            <param name="allowTrial">Whether to allow trial subscriptions (default: true)</param>
        </member>
        <member name="M:OpenAutomate.API.Attributes.RequireSubscriptionAttribute.#ctor(OpenAutomate.API.Attributes.SubscriptionOperationType,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Attributes.RequireSubscriptionAttribute"/> class with operation type
            </summary>
            <param name="operationType">The type of operation (Read allows expired trials, Write requires active subscription)</param>
            <param name="allowTrial">Whether to allow trial subscriptions for write operations (default: true)</param>
        </member>
        <member name="M:OpenAutomate.API.Attributes.RequireSubscriptionAttribute.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Executes the subscription check before the action executes
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.AccountController">
            <summary>
            Controller for handling user account self-service operations
            </summary>
            <remarks>
            Provides endpoints for all self-service account operations including:
            - Retrieving user profile information with permissions
            - Updating personal information (first name, last name)
            - Changing password
            All operations are performed on the currently authenticated user's own account.
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AccountController.#ctor(OpenAutomate.Core.IServices.IAccountService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.AccountController})">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.AccountController"/> class
            </summary>
            <param name="accountService">The account service for account operations</param>
            <param name="logger">The logger for recording account operations</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AccountController.GetProfile">
            <summary>
            Gets the complete user profile with permissions across all organization units
            </summary>
            <returns>Complete user profile including system role, organization units, and permissions</returns>
            <remarks>
            This endpoint returns comprehensive profile information including:
            - Basic user information (name, email, system role)
            - All organization units the user belongs to
            - All permissions for each organization unit (resource + permission level)
            
            The permissions are aggregated to show the highest permission level for each resource
            across all roles the user has within each organization unit.
            </remarks>
            <response code="200">Profile retrieved successfully</response>
            <response code="401">User is not authenticated</response>
            <response code="404">User not found</response>
            <response code="500">Server error during profile retrieval</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AccountController.UpdateInfo(OpenAutomate.Core.Dto.UserDto.UpdateUserInfoRequest)">
            <summary>
            Updates the current user's personal information (first name and last name)
            </summary>
            <param name="request">The update request containing new first name and last name</param>
            <returns>The updated user information</returns>
            <response code="200">User information updated successfully</response>
            <response code="400">Invalid request data</response>
            <response code="401">User is not authenticated</response>
            <response code="500">Server error during update process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AccountController.ChangePassword(OpenAutomate.Core.Dto.UserDto.ChangePasswordRequest)">
            <summary>
            Changes the current user's password
            </summary>
            <param name="request">The password change request containing current password and new password</param>
            <returns>Success message if password was changed</returns>
            <response code="200">Password changed successfully</response>
            <response code="400">Invalid request data or current password is incorrect</response>
            <response code="401">User is not authenticated</response>
            <response code="500">Server error during password change process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AccountController.GetCurrentUserIdSafe">
            <summary>
            Safely gets the current user ID without throwing exceptions
            </summary>
            <returns>The current user ID or "unknown" if not available</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminController.GetAllUsers">
            <summary>
            Gets a list of all users in the system. Only accessible by administrators.
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminController.GetUserById(System.Guid)">
            <summary>
            Gets detailed information for a specific user by their ID. Only accessible by administrators.
            </summary>
            <param name="userId">The ID of the user to retrieve.</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminController.UpdateUserInfo(System.Guid,OpenAutomate.Core.Dto.UserDto.UpdateUserInfoRequest)">
            <summary>
            Updates the first and last name of a user. Only accessible by administrators.
            </summary>
            <param name="userId">The ID of the user to update.</param>
            <param name="request">The new first and last name information.</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminController.ChangePassword(System.Guid,OpenAutomate.Core.Dto.AdminDto.AdminChangePasswordRequest)">
            <summary>
            Changes the password of a user. Only accessible by administrators.
            </summary>
            <param name="userId">The ID of the user whose password will be changed.</param>
            <param name="request">The new password information.</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminController.GetAllOrganizationUnit">
            <summary>
            Gets a list of all Organization Unit in the system. Only accessible by administrators.
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.AdminRevenueController">
            <summary>
            Controller for admin revenue reporting and analytics
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminRevenueController.GetRevenueMetrics">
            <summary>
            Gets comprehensive revenue metrics and analytics
            </summary>
            <returns>Revenue metrics including total revenue, MRR, subscription counts, etc.</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminRevenueController.GetMonthlyRevenue(System.Int32)">
            <summary>
            Gets monthly revenue breakdown for a specific time period
            </summary>
            <param name="months">Number of months to include (default: 12)</param>
            <returns>Monthly revenue breakdown</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminRevenueController.GetSubscriptionAnalytics">
            <summary>
            Gets subscription analytics and insights
            </summary>
            <returns>Subscription analytics including active trials, conversions, churn, etc.</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminRevenueController.IsUserAdmin">
            <summary>
            Checks if the current user has admin role
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AdminRevenueController.GetCurrentUserId">
            <summary>
            Gets the current user's ID from JWT claims
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.RevenueMetricsResponse">
            <summary>
            Response model for comprehensive revenue metrics
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.TotalRevenue">
            <summary>
            Total revenue from all payments
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.MonthlyRecurringRevenue">
            <summary>
            Monthly Recurring Revenue (based on active subscriptions)
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.CurrentMonthRevenue">
            <summary>
            Revenue for the current month
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.PreviousMonthRevenue">
            <summary>
            Revenue for the previous month
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.RevenueGrowthPercentage">
            <summary>
            Month-over-month revenue growth percentage
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.ActiveSubscriptions">
            <summary>
            Total number of active subscriptions
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.TrialSubscriptions">
            <summary>
            Total number of trial subscriptions
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.TotalPayments">
            <summary>
            Total number of payments processed
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.AverageRevenuePerUser">
            <summary>
            Average revenue per user
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.TotalSubscribedOrganizations">
            <summary>
            Total number of organization units with subscriptions
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.RevenueMetricsResponse.LastUpdated">
            <summary>
            Last updated timestamp
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.MonthlyRevenueResponse">
            <summary>
            Response model for monthly revenue breakdown
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueResponse.MonthlyData">
            <summary>
            Monthly revenue data points
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueResponse.TotalRevenue">
            <summary>
            Total revenue across all months
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueResponse.AverageMonthlyRevenue">
            <summary>
            Average monthly revenue
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.MonthlyRevenueData">
            <summary>
            Monthly revenue data point
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueData.Year">
            <summary>
            Year of the data point
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueData.Month">
            <summary>
            Month of the data point (1-12)
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueData.MonthName">
            <summary>
            Month name (e.g., "January")
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueData.Revenue">
            <summary>
            Total revenue for the month
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueData.PaymentCount">
            <summary>
            Number of payments in the month
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.MonthlyRevenueData.NewSubscriptions">
            <summary>
            Number of new subscriptions started in the month
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse">
            <summary>
            Response model for subscription analytics
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.ActiveSubscriptions">
            <summary>
            Total active subscriptions
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.TrialSubscriptions">
            <summary>
            Total trial subscriptions
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.ExpiredSubscriptions">
            <summary>
            Total expired subscriptions
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.CancelledSubscriptions">
            <summary>
            Total cancelled subscriptions
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.TrialConversionRate">
            <summary>
            Trial to paid conversion rate (percentage)
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.MonthlyChurnRate">
            <summary>
            Monthly churn rate (percentage)
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.TrialsExpiringThisWeek">
            <summary>
            Trials expiring in the next 7 days
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.SubscriptionsRenewingThisWeek">
            <summary>
            Subscriptions renewing in the next 7 days
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.SubscriptionAnalyticsResponse.AverageSubscriptionLifetime">
            <summary>
            Average subscription lifetime in days
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.AssetController">
            <summary>
            Controller for Asset management operations
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.#ctor(OpenAutomate.Core.IServices.IAssetService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.AssetController})">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.AssetController"/> class
            </summary>
            <param name="assetService">The Asset service</param>
            <param name="logger">The logger</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.CreateAsset(OpenAutomate.Core.Dto.Asset.CreateAssetDto)">
            <summary>
            Creates a new Asset
            </summary>
            <param name="dto">The Asset creation data (Key, Value, etc.)</param>
            <returns>The created Asset</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.GetAllAssets">
            <summary>
            Gets all Assets for the current tenant
            </summary>
            <returns>Collection of Assets</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.GetAssetById(System.Guid)">
            <summary>
            Gets an Asset by its ID
            </summary>
            <param name="id">The Asset ID</param>
            <returns>The Asset if found</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.GetAssetByKey(System.String)">
            <summary>
            Gets an Asset by its key
            </summary>
            <param name="key">The Asset key</param>
            <returns>The Asset if found</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.UpdateAsset(System.Guid,OpenAutomate.Core.Dto.Asset.UpdateAssetDto)">
            <summary>
            Updates an existing Asset
            </summary>
            <param name="id">The Asset ID</param>
            <param name="dto">The updated Asset data</param>
            <returns>The updated Asset</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.DeleteAsset(System.Guid)">
            <summary>
            Deletes an Asset
            </summary>
            <param name="id">The Asset ID</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.GetAuthorizedBotAgents(System.Guid)">
            <summary>
            Gets all Bot Agents authorized to access an Asset
            </summary>
            <param name="id">The Asset ID</param>
            <returns>Collection of authorized Bot Agents</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.UpdateAuthorizedBotAgents(System.Guid,OpenAutomate.Core.Dto.Asset.AssetBotAgentDto)">
            <summary>
            Updates the Bot Agents authorized to access an Asset
            </summary>
            <param name="id">The Asset ID</param>
            <param name="dto">The list of Bot Agent IDs</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.AuthorizeBotAgent(System.Guid,System.Guid)">
            <summary>
            Authorizes a Bot Agent to access an Asset
            </summary>
            <param name="id">The Asset ID</param>
            <param name="botAgentId">The Bot Agent ID</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AssetController.RevokeBotAgent(System.Guid,System.Guid)">
            <summary>
            Revokes a Bot Agent's access to an Asset
            </summary>
            <param name="id">The Asset ID</param>
            <param name="botAgentId">The Bot Agent ID</param>
        </member>
        <member name="T:OpenAutomate.API.Controllers.AuthController">
            <summary>
            Controller for handling user authentication and identity operations
            </summary>
            <remarks>
            Provides endpoints for user registration, login, token refresh, token revocation,
            and password recovery operations. All authentication-related functionality is 
            consolidated in this controller.
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.#ctor(OpenAutomate.Core.IServices.IAuthService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.AuthController},OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.AuthController"/> class
            </summary>
            <param name="authService">The auth service for authentication operations</param>
            <param name="logger">The logger for recording authentication events</param>
            <param name="tenantContext">The tenant context for current tenant information</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.Register(OpenAutomate.Core.Dto.UserDto.RegistrationRequest)">
            <summary>
            Registers a new user in the system
            </summary>
            <param name="request">The registration information containing email, password, and other user details</param>
            <returns>User registration confirmation with authentication tokens</returns>
            <response code="200">Registration successful</response>
            <response code="400">Invalid registration data or email already exists</response>
            <response code="500">Server error during registration process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.Login(OpenAutomate.Core.Dto.UserDto.AuthenticationRequest)">
            <summary>
            Authenticates a user and provides access tokens
            </summary>
            <param name="request">The authentication request containing email and password</param>
            <returns>Authentication response with access token and refresh token</returns>
            <response code="200">Authentication successful</response>
            <response code="400">Invalid credentials or account disabled</response>
            <response code="500">Server error during authentication process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.RefreshToken">
            <summary>
            Generates a new access token using a valid refresh token
            </summary>
            <returns>Authentication response with new access token and refresh token</returns>
            <remarks>
            The refresh token is extracted from the HTTP-only cookie set during login
            </remarks>
            <response code="200">Token refresh successful</response>
            <response code="400">Refresh token missing or invalid</response>
            <response code="500">Server error during token refresh process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.RevokeToken(OpenAutomate.Core.Dto.UserDto.RevokeTokenRequest)">
            <summary>
            Revokes an active refresh token to prevent future use
            </summary>
            <param name="request">Optional revocation request containing token and reason</param>
            <returns>Confirmation of token revocation</returns>
            <remarks>
            The refresh token can either be provided in request body or extracted from the cookie.
            This endpoint requires authentication.
            </remarks>
            <response code="200">Token successfully revoked</response>
            <response code="400">Token is missing</response>
            <response code="404">Token not found or already revoked</response>
            <response code="500">Server error during revocation process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.GetCurrentUser">
            <summary>
            Gets the profile information for the currently authenticated user
            </summary>
            <returns>User profile details of the current user</returns>
            <remarks>
            This endpoint requires authentication.
            </remarks>
            <response code="200">User information retrieved successfully</response>
            <response code="401">User is not authenticated</response>
            <response code="500">Server error while retrieving user information</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.ForgotPassword(OpenAutomate.Core.Dto.UserDto.ForgotPasswordRequest)">
            <summary>
            Sends a password reset email to the user
            </summary>
            <param name="request">The forgot password request containing user's email</param>
            <returns>Success message if reset email was sent</returns>
            <response code="200">Reset email sent successfully</response>
            <response code="400">Invalid request</response>
            <response code="500">Server error</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.ResetPassword(OpenAutomate.Core.Dto.UserDto.ResetPasswordRequest)">
            <summary>
            Resets the user's password using a valid token
            </summary>
            <param name="request">The reset password request containing email, token, and new password</param>
            <returns>Success message if password was reset successfully</returns>
            <response code="200">Password reset successful</response>
            <response code="400">Invalid request or token</response>
            <response code="500">Server error</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.SetRefreshTokenCookie(System.String,System.DateTime)">
            <summary>
            Sets the refresh token in an HTTP-only cookie
            </summary>
            <param name="token">The refresh token value</param>
            <param name="expires">The expiration date for the token</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.GetIpAddress">
            <summary>
            Gets the client's IP address from request headers or connection information
            </summary>
            <returns>The client's IP address or "unknown" if not available</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.GetForwardedForHeader(System.String)">
            <summary>
            Uses model binding to retrieve the X-Forwarded-For header
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthController.EnsureDefaultTenant">
            <summary>
            Ensures a default tenant is set in the tenant context for authentication operations
            </summary>
            <remarks>
            Authentication operations do not rely on tenant-specific data
            but services may require a valid tenant context
            </remarks>
            <exception cref="T:System.InvalidOperationException">Thrown when tenant context could not be set</exception>
        </member>
        <member name="T:OpenAutomate.API.Controllers.AuthorController">
            <summary>
            Controller for managing user authorities and permissions within an organization unit
            </summary>
            <remarks>
            Provides endpoints for creating roles, assigning permissions, and managing user authorities.
            All operations are scoped to the current organization unit (tenant).
            Permission levels: 0=No Access, 1=View, 2=Create, 3=Update (includes Execute), 4=Delete/Full Admin
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.#ctor(OpenAutomate.Core.IServices.IAuthorizationManager,OpenAutomate.Core.IServices.IOrganizationUnitService,OpenAutomate.Core.IServices.ICacheInvalidationService,OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.AuthorController"/> class
            </summary>
            <param name="authorizationManager">The authorization manager service</param>
            <param name="organizationUnitService">The organization unit service</param>
            <param name="cacheInvalidationService">The cache invalidation service</param>
            <param name="tenantContext">The tenant context</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.CreateAuthority(OpenAutomate.Core.Dto.Authority.CreateAuthorityDto)">
            <summary>
            Creates a new authority (role) within the organization unit
            Only OWNER can create new authorities
            </summary>
            <param name="dto">The authority creation details</param>
            <returns>The created authority details</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.GetAllAuthorities">
            <summary>
            Gets all authorities within the current organization unit
            </summary>
            <returns>List of authorities with their permissions</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.GetAuthority(System.Guid)">
            <summary>
            Gets a specific authority by ID with its permissions
            </summary>
            <param name="authorityId">The authority ID</param>
            <returns>Authority details with permissions</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.UpdateAuthority(System.Guid,OpenAutomate.Core.Dto.Authority.UpdateAuthorityDto)">
            <summary>
            Updates an existing authority's details and permissions
            Only OWNER can update authorities
            </summary>
            <param name="authorityId">The authority ID</param>
            <param name="dto">Updated authority details</param>
            <returns>Success response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.DeleteAuthority(System.Guid)">
            <summary>
            Deletes an authority (role) from the organization unit
            Only OWNER can delete authorities. Cannot delete system authorities (OWNER, etc.)
            </summary>
            <param name="authorityId">The authority ID</param>
            <returns>Success response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.GetUserAuthorities(System.Guid)">
            <summary>
            Gets all authorities assigned to a specific user
            </summary>
            <param name="userId">The unique identifier of the user</param>
            <returns>A collection of authority names assigned to the user</returns>
            <response code="200">Authorities successfully retrieved</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.AssignAuthorityToUser(System.Guid,OpenAutomate.Core.Dto.Authority.AssignAuthorityDto)">
            <summary>
            Assigns an authority to a user
            Requires UPDATE permission on OrganizationUnit
            </summary>
            <param name="userId">The unique identifier of the user</param>
            <param name="dto">The authority assignment details containing the authority ID</param>
            <returns>A success response if the assignment is successful</returns>
            <response code="200">Authority successfully assigned to user</response>
            <response code="400">Invalid request data</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">User or authority not found</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.RemoveAuthorityFromUser(System.Guid,System.Guid)">
            <summary>
            Removes an authority from a user
            </summary>
            <param name="userId">The unique identifier of the user</param>
            <param name="authorityId">The ID of the authority to remove</param>
            <returns>A success response if the removal is successful</returns>
            <response code="200">Authority successfully removed from user</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">User or authority assignment not found</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.GetAvailableResources">
            <summary>
            Gets all available resources and their permission levels
            Used for role creation UI
            </summary>
            <returns>List of resources with available permission levels</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.AddResourcePermission(OpenAutomate.Core.Dto.Authority.ResourcePermissionDto)">
            <summary>
            Adds a resource permission to an authority
            </summary>
            <param name="dto">The resource permission details containing authority name, resource name, and permission</param>
            <returns>A success response if the permission is successfully added</returns>
            <response code="200">Resource permission successfully added</response>
            <response code="400">Invalid resource permission data</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">Authority or resource not found</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.RemoveResourcePermission(System.String,System.String)">
            <summary>
            Removes all permissions for a resource from an authority
            </summary>
            <param name="authorityName">The name of the authority</param>
            <param name="resourceName">The name of the resource</param>
            <returns>A success response if the permission is successfully removed</returns>
            <response code="200">Resource permissions successfully removed</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">Authority, resource, or permission not found</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AuthorController.AssignAuthoritiesToUserBulk(System.String,System.Guid,OpenAutomate.Core.Dto.Authority.AssignAuthoritiesDto)">
            <summary>
            Assigns multiple authorities (roles) to a user in one request
            </summary>
            <param name="tenant">The tenant slug</param>
            <param name="userId">The unique identifier of the user</param>
            <param name="dto">The authority assignment details containing the list of authority IDs</param>
            <returns>A success response if the assignment is successful</returns>
            <response code="200">Authorities successfully assigned to user</response>
            <response code="400">Invalid request data</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">User or authority not found</response>
        </member>
        <member name="T:OpenAutomate.API.Controllers.AutomationPackageController">
            <summary>
            Controller for Automation Package management
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.#ctor(OpenAutomate.Core.IServices.IAutomationPackageService,OpenAutomate.Core.IServices.IPackageMetadataService,OpenAutomate.Core.IServices.IBotAgentService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.AutomationPackageController})">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.AutomationPackageController"/> class
            </summary>
            <param name="packageService">The automation package service</param>
            <param name="metadataService">The package metadata service</param>
            <param name="botAgentService">The bot agent service</param>
            <param name="logger">The logger</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.CreatePackage(OpenAutomate.Core.Dto.Package.CreateAutomationPackageDto)">
            <summary>
            Creates a new automation package
            </summary>
            <param name="dto">Package creation data</param>
            <returns>Created package response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.UploadPackageWithAutoCreation(OpenAutomate.API.Controllers.UploadPackageWithMetadataRequest)">
            <summary>
            Uploads a package file and automatically creates the package with extracted metadata
            </summary>
            <param name="request">Upload request containing the package file</param>
            <returns>Created package with first version</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.GetPackageById(System.Guid)">
            <summary>
            Gets a package by ID
            </summary>
            <param name="id">Package ID</param>
            <returns>Package response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.GetAllPackages">
            <summary>
            Gets all packages for the current tenant
            </summary>
            <returns>Collection of package responses</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.UploadPackageVersion(System.Guid,OpenAutomate.API.Controllers.UploadPackageVersionRequest)">
            <summary>
            Uploads a new version of a package
            </summary>
            <param name="id">Package ID</param>
            <param name="request">Upload request containing file and version</param>
            <returns>Package version response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.GetPackageDownloadUrl(System.Guid,System.String)">
            <summary>
            Gets a download URL for a specific package version
            </summary>
            <param name="id">Package ID</param>
            <param name="version">Version number</param>
            <returns>Download URL response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.GetAgentDownloadUrl(System.Guid,System.String,System.String)">
            <summary>
            Gets a secure download URL for a package version (for bot agents)
            </summary>
            <param name="id">Package ID</param>
            <param name="version">Version number</param>
            <param name="machineKey">Bot agent machine key for authentication</param>
            <returns>Download URL response with expiration</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.DeletePackage(System.Guid)">
            <summary>
            Deletes a package and all its versions
            </summary>
            <param name="id">Package ID</param>
            <returns>No content response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.AutomationPackageController.DeletePackageVersion(System.Guid,System.String)">
            <summary>
            Deletes a specific package version
            </summary>
            <param name="id">Package ID</param>
            <param name="version">Version number</param>
            <returns>No content response</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.UploadPackageVersionRequest">
            <summary>
            Request model for uploading package versions
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.UploadPackageVersionRequest.File">
            <summary>
            Package file to upload
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.UploadPackageVersionRequest.Version">
            <summary>
            Version number for the package
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.UploadPackageWithMetadataRequest">
            <summary>
            Request model for uploading packages with automatic metadata extraction
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.UploadPackageWithMetadataRequest.File">
            <summary>
            Package file to upload
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.UploadPackageWithMetadataRequest.Name">
            <summary>
            Optional: Override the extracted package name
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.UploadPackageWithMetadataRequest.Description">
            <summary>
            Optional: Override the extracted package description
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.UploadPackageWithMetadataRequest.Version">
            <summary>
            Optional: Override the extracted package version
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.BotAgentAssetController">
            <summary>
            Controller for Bot Agent asset retrieval operations
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentAssetController.#ctor(OpenAutomate.Core.IServices.IAssetService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.BotAgentAssetController})">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.BotAgentAssetController"/> class
            </summary>
            <param name="assetService">The Asset service</param>
            <param name="logger">The logger</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentAssetController.GetAssetValueByKey(System.String,OpenAutomate.Core.Dto.Asset.BotAgentAssetDto)">
            <summary>
            Gets an Asset value by its key using machine key authentication
            </summary>
            <param name="key">The Asset key</param>
            <param name="request">The request containing the machine key</param>
            <returns>The Asset value if found and bot agent is authorized</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentAssetController.GetAccessibleAssets(OpenAutomate.Core.Dto.Asset.BotAgentKeyDto)">
            <summary>
            Gets all Assets accessible by a bot agent using machine key authentication
            </summary>
            <param name="request">The request containing the machine key</param>
            <returns>Collection of accessible Assets</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.BotAgentConnectionController">
             <summary>
             Controller for Bot Agent real-time connection and command operations
            
             DEPRECATED: This controller is deprecated as of the direct connection refactor.
             Bot Agents now connect directly to the backend SignalR hub using the discovery endpoint.
             This controller is kept temporarily for backward compatibility.
             </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentConnectionController.#ctor(OpenAutomate.Core.IServices.IBotAgentService,Microsoft.AspNetCore.SignalR.IHubContext{OpenAutomate.API.Hubs.BotAgentHub},Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.BotAgentConnectionController},OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.BotAgentConnectionController"/> class
            </summary>
            <param name="botAgentService">The Bot Agent service</param>
            <param name="hubContext">The SignalR hub context</param>
            <param name="logger">The logger instance</param>
            <param name="tenantContext">The tenant context</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentConnectionController.ConnectBotAgent(OpenAutomate.Core.Dto.BotAgent.BotAgentConnectionRequest)">
            <summary>
            Authenticates a Bot Agent for real-time communication
            </summary>
            <param name="connectionRequest">The connection request with machine key</param>
            <returns>Connection details for the SignalR hub</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentConnectionController.SendCommandToBotAgent(System.Guid,OpenAutomate.Core.Dto.BotAgent.BotAgentCommandDto)">
            <summary>
            Sends a command to a Bot Agent via SignalR
            </summary>
            <param name="id">The Bot Agent ID</param>
            <param name="command">The command data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentConnectionController.GetBotAgentStatus(System.Guid)">
            <summary>
            Gets the connection status of a Bot Agent
            </summary>
            <param name="id">The Bot Agent ID</param>
            <returns>The connection status</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentConnectionController.BroadcastNotification(OpenAutomate.Core.Dto.BotAgent.BroadcastNotificationDto)">
            <summary>
            Broadcasts a notification to all connected clients in the current tenant
            </summary>
            <param name="notification">The notification data</param>
            <returns>Success status</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.BotAgentController">
            <summary>
            Controller for Bot Agent CRUD management operations
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentController.#ctor(OpenAutomate.Core.IServices.IBotAgentService,OpenAutomate.Core.IServices.ICacheInvalidationService,OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.BotAgentController"/> class
            </summary>
            <param name="botAgentService">The Bot Agent service</param>
            <param name="cacheInvalidationService">The cache invalidation service</param>
            <param name="tenantContext">The tenant context</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentController.CreateBotAgent(OpenAutomate.Core.Dto.BotAgent.CreateBotAgentDto)">
            <summary>
            Creates a new Bot Agent and generates a machine key
            </summary>
            <param name="dto">The Bot Agent creation data</param>
            <returns>The CreatedAtBot Agent with machine key</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentController.GetBotAgentById(System.Guid)">
            <summary>
            Gets a Bot Agent by its ID
            </summary>
            <param name="id">The Bot Agent ID</param>
            <returns>The Bot Agent if found</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentController.GetAllBotAgents">
            <summary>
            Gets all Bot Agents for the current tenant
            </summary>
            <returns>Collection of Bot Agents</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentController.RegenerateMachineKey(System.Guid)">
            <summary>
            Regenerates the machine key for a Bot Agent
            </summary>
            <param name="id">The Bot Agent ID</param>
            <returns>The updated Bot Agent with new machine key</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentController.DeactivateBotAgent(System.Guid)">
            <summary>
            Deactivates a Bot Agent
            </summary>
            <param name="id">The Bot Agent ID</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentController.DeleteBotAgent(System.Guid)">
            <summary>
            Deletes a Bot Agent.
            </summary>
            <param name="id">The Bot Agent ID</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.BotAgentController.UpdateBotAgent(System.Guid,OpenAutomate.Core.Dto.BotAgent.UpdateBotAgentDto)">
            <summary>
            Updates a Bot Agent's editable fields
            </summary>
            <param name="id">The Bot Agent ID</param>
            <param name="dto">The update data</param>
            <returns>The updated Bot Agent</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.CustomControllerBase">
            <summary>
            Base controller that provides common functionality for all API controllers
            </summary>
        </member>
        <member name="P:OpenAutomate.API.Controllers.CustomControllerBase.currentUser">
            <summary>
            Gets the current authenticated user from the HttpContext
            </summary>
            <remarks>Returns null if no user is authenticated</remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.CustomControllerBase.GetCurrentUserId">
            <summary>
            Gets the ID of the currently authenticated user
            </summary>
            <returns>The user's ID</returns>
            <exception cref="T:System.UnauthorizedAccessException">Thrown if no user is authenticated</exception>
        </member>
        <member name="M:OpenAutomate.API.Controllers.EmailTestController.GetEmailStatus">
            <summary>
            Gets the email configuration status
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.EmailTestController.SendWelcomeEmailGet(System.String)">
            <summary>
            Sends a test welcome email to the specified recipient (GET method)
            </summary>
            <param name="email">Email address of the recipient</param>
            <returns>Result of the operation</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.EmailTestController.SendWelcomeEmailPost(System.String)">
            <summary>
            Sends a test welcome email to the specified recipient (POST method)
            </summary>
            <param name="email">Email address of the recipient</param>
            <returns>Result of the operation</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.EmailTestController.SendWelcomeEmailInternal(System.String)">
            <summary>
            Internal method to send welcome email
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.EmailVerificationController">
            <summary>
            Controller for handling email verification
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.EmailVerificationController.#ctor(OpenAutomate.Core.IServices.IUserService,OpenAutomate.Core.IServices.ITokenService,OpenAutomate.Core.IServices.INotificationService,Microsoft.Extensions.Options.IOptions{OpenAutomate.Core.Configurations.AppSettings},Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.EmailVerificationController})">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.EmailVerificationController"/> class
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.EmailVerificationController.VerifyEmail(System.String)">
            <summary>
            Verifies a user's email using a verification token
            </summary>
            <param name="token">The verification token</param>
            <returns>Redirect to the frontend with success or error status</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.EmailVerificationController.ResendVerification">
            <summary>
            Resends a verification email to the currently authenticated user
            </summary>
            <returns>Status of the operation</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.EmailVerificationController.ResendVerificationPublic(OpenAutomate.Core.Dto.UserDto.ResendEmailVerificationRequest)">
            <summary>
            Resends a verification email to the specified email address (public, no login required)
            </summary>
            <param name="request">Request containing the email address to resend verification to.</param>
            <returns>
            200 OK: Verification email sent successfully or email is already verified.
            400 Bad Request: If the email is missing/invalid or sending email failed.
            404 Not Found: If the email address does not exist in the system.
            500 Internal Server Error: If an unexpected error occurs.
            </returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.ExecutionController">
            <summary>
            Controller for managing bot executions
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.#ctor(OpenAutomate.Core.IServices.IExecutionService,OpenAutomate.Core.IServices.IBotAgentService,OpenAutomate.Core.IServices.IAutomationPackageService,OpenAutomate.Core.IServices.ILogStorageService,Microsoft.AspNetCore.SignalR.IHubContext{OpenAutomate.API.Hubs.BotAgentHub},OpenAutomate.Core.IServices.ITenantContext,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.ExecutionController})">
            <summary>
            Initializes a new instance of the ExecutionController
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.TriggerExecution(OpenAutomate.Core.Dto.Execution.TriggerExecutionDto)">
            <summary>
            Triggers a new execution
            </summary>
            <param name="dto">Execution trigger data</param>
            <returns>Created execution response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.UploadExecutionLogs(System.Guid,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Uploads a log file for a specific execution
            </summary>
            <param name="id">Execution ID</param>
            <param name="logFile">Log file to upload</param>
            <returns>Success response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.GetExecutionLogDownloadUrl(System.Guid)">
            <summary>
            Gets a secure download URL for execution logs
            </summary>
            <param name="id">Execution ID</param>
            <returns>Download URL</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.GetAllExecutions">
            <summary>
            Gets all executions for the current tenant
            </summary>
            <returns>List of executions</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.GetExecutionById(System.Guid)">
            <summary>
            Gets an execution by ID
            </summary>
            <param name="id">Execution ID</param>
            <returns>Execution details</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.UpdateExecutionStatus(System.Guid,OpenAutomate.Core.Dto.Execution.UpdateExecutionStatusDto)">
            <summary>
            Updates execution status (typically called by bot agents)
            </summary>
            <param name="id">Execution ID</param>
            <param name="updateDto">Status update data</param>
            <returns>Updated execution</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.CancelExecution(System.Guid)">
            <summary>
            Cancels an execution
            </summary>
            <param name="id">Execution ID</param>
            <returns>Updated execution</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ExecutionController.MapToResponseDto(OpenAutomate.Core.Domain.Entities.Execution)">
            <summary>
            Maps execution entity to response DTO
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.HealthController">
            <summary>
            Health check controller to verify system components
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.HealthController.Get">
            <summary>
            Basic health check
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.HealthController.CheckRedis">
            <summary>
            Redis health check with cache test
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.HealthController.CreateDemoCache">
            <summary>
            Demo endpoint to create a persistent cache entry showing InstanceName prefix
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.LemonsqueezyWebhookController">
            <summary>
            Controller for receiving and processing Lemon Squeezy webhooks
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.LemonsqueezyWebhookController.ProcessWebhook">
            <summary>
            Receives webhook events from Lemon Squeezy
            </summary>
            <returns>200 OK if processed successfully, 401 if signature invalid, 400 if processing failed</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.LemonsqueezyWebhookController.Health">
            <summary>
            Health check endpoint for webhook URL validation
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.AssetsController">
            <summary>
            OData controller for querying Assets
            </summary>
            <remarks>
            This controller provides OData query capabilities for Assets
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.AssetsController.Get">
            <summary>
            Gets all Assets with OData query support
            </summary>
            <returns>Collection of Assets that can be queried using OData</returns>
            <remarks>
            Example queries:
            GET /tenant/odata/Assets?$filter=Type eq 'String'
            GET /tenant/odata/Assets?$select=Id,Key,Description
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.AssetsController.Get(System.Guid)">
            <summary>
            Gets a specific Asset by ID with OData query support
            </summary>
            <param name="key">The Asset ID</param>
            <returns>The Asset if found</returns>
            <remarks>
            Example query:
            GET /tenant/odata/Assets(guid'12345678-1234-1234-1234-123456789012')?$select=Key,Description
            </remarks>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.AuthoritiesController">
            <summary>
            OData controller for querying Authorities (Roles)
            </summary>
            <remarks>
            This controller provides OData query capabilities for Authorities/Roles within a tenant context.
            All data is automatically filtered by the current tenant (organization unit).
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.AuthoritiesController.#ctor(OpenAutomate.Core.IServices.IAuthorizationManager,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.OData.AuthoritiesController},OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.OData.AuthoritiesController"/> class
            </summary>
            <param name="authorizationManager">The authorization manager service</param>
            <param name="logger">The logger</param>
            <param name="tenantContext">The tenant context</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.AuthoritiesController.Get">
            <summary>
            Gets all Authorities (Roles) with OData query support
            </summary>
            <returns>Collection of Authorities that can be queried using OData</returns>
            <remarks>
            Example queries:
            GET /{tenant}/odata/Roles?$filter=IsSystemAuthority eq false
            GET /{tenant}/odata/Roles?$select=Id,Name,Description
            GET /{tenant}/odata/Roles?$expand=Permissions
            GET /{tenant}/odata/Roles?$filter=contains(Name,'Admin')
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.AuthoritiesController.Get(System.Guid)">
            <summary>
            Gets a specific Authority (Role) by ID with OData query support
            </summary>
            <param name="key">The Authority ID</param>
            <returns>The Authority if found</returns>
            <remarks>
            Example query:
            GET /{tenant}/odata/Roles(guid'12345678-1234-1234-1234-123456789012')?$select=Name,Description
            GET /{tenant}/odata/Roles(guid'12345678-1234-1234-1234-123456789012')?$expand=Permissions
            </remarks>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.AutomationPackagesController">
            <summary>
            OData controller for querying Automation Packages
            </summary>
            <remarks>
            This controller provides OData query capabilities for Automation Packages
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.AutomationPackagesController.#ctor(OpenAutomate.Core.IServices.IAutomationPackageService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.OData.AutomationPackagesController},OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.OData.AutomationPackagesController"/> class
            </summary>
            <param name="packageService">The automation package service</param>
            <param name="logger">The logger</param>
            <param name="tenantContext">The tenant context</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.AutomationPackagesController.Get">
            <summary>
            Gets all Automation Packages with OData query support
            </summary>
            <returns>Collection of Automation Packages that can be queried using OData</returns>
            <remarks>
            Example queries:
            GET /tenant/odata/AutomationPackages?$filter=IsActive eq true
            GET /tenant/odata/AutomationPackages?$select=Id,Name,Description,CreatedAt
            GET /tenant/odata/AutomationPackages?$expand=Versions
            GET /tenant/odata/AutomationPackages?$filter=contains(Name,'automation') and CreatedAt gt 2024-01-01T00:00:00Z
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.AutomationPackagesController.Get(System.Guid)">
            <summary>
            Gets a specific Automation Package by ID with OData query support
            </summary>
            <param name="key">The Automation Package ID</param>
            <returns>The Automation Package if found</returns>
            <remarks>
            Example queries:
            GET /tenant/odata/AutomationPackages(guid'12345678-1234-1234-1234-123456789012')?$select=Name,Description
            GET /tenant/odata/AutomationPackages(guid'12345678-1234-1234-1234-123456789012')?$expand=Versions
            </remarks>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.BotAgentsController">
            <summary>
            OData controller for querying Bot Agents
            </summary>
            <remarks>
            This controller provides OData query capabilities for Bot Agents
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.BotAgentsController.#ctor(OpenAutomate.Core.IServices.IBotAgentService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.OData.BotAgentsController},OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.OData.BotAgentsController"/> class
            </summary>
            <param name="botAgentService">The Bot Agent service</param>
            <param name="logger">The logger</param>
            <param name="tenantContext">The tenant context</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.BotAgentsController.Get">
            <summary>
            Gets all Bot Agents with OData query support
            </summary>
            <returns>Collection of Bot Agents that can be queried using OData</returns>
            <remarks>
            Example queries:
            GET /tenant/odata/BotAgents?$filter=Status eq 'Active'
            GET /tenant/odata/BotAgents?$select=Id,Name,Status
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.BotAgentsController.Get(System.Guid)">
            <summary>
            Gets a specific Bot Agent by ID with OData query support
            </summary>
            <param name="key">The Bot Agent ID</param>
            <returns>The Bot Agent if found</returns>
            <remarks>
            Example query:
            GET /tenant/odata/BotAgents(guid'12345678-1234-1234-1234-123456789012')?$select=Name,Status
            </remarks>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.ExecutionsController">
            <summary>
            OData controller for querying Executions
            </summary>
            <remarks>
            This controller provides OData query capabilities for Executions
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.ExecutionsController.#ctor(OpenAutomate.Core.IServices.IExecutionService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.OData.ExecutionsController},OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.OData.ExecutionsController"/> class
            </summary>
            <param name="executionService">The execution service</param>
            <param name="logger">The logger</param>
            <param name="tenantContext">The tenant context</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.ExecutionsController.Get">
            <summary>
            Gets all Executions with OData query support
            </summary>
            <returns>Collection of Executions that can be queried using OData</returns>
            <remarks>
            Example queries:
            GET /tenant/odata/Executions?$filter=Status eq 'Running'
            GET /tenant/odata/Executions?$select=Id,Status,StartTime,EndTime
            GET /tenant/odata/Executions?$expand=BotAgent,Package
            GET /tenant/odata/Executions?$filter=Status eq 'Completed' and StartTime gt 2024-01-01T00:00:00Z
            GET /tenant/odata/Executions?$filter=contains(BotAgent/Name,'Agent1') and Status ne 'Failed'
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.ExecutionsController.Get(System.Guid)">
            <summary>
            Gets a specific Execution by ID with OData query support
            </summary>
            <param name="key">The Execution ID</param>
            <returns>The Execution if found</returns>
            <remarks>
            Example queries:
            GET /tenant/odata/Executions(guid'12345678-1234-1234-1234-123456789012')?$select=Status,StartTime,EndTime
            GET /tenant/odata/Executions(guid'12345678-1234-1234-1234-123456789012')?$expand=BotAgent,Package
            </remarks>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.OrganizationUnitInvitationsController">
            <summary>
            OData controller for querying Organization Unit Invitations
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.OrganizationUnitInvitationsController.Get">
            <summary>
            Gets all Organization Unit Invitations with OData query support
            </summary>
            <returns>Collection of OrganizationUnitInvitationDto</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.OrganizationUnitUsersController">
            <summary>
            OData controller for querying Organization Unit Users
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.OrganizationUnitUsersController.Get">
            <summary>
            Gets all Organization Unit Users with OData query support
            </summary>
            <returns>Collection of OrganizationUnitUserDetailDto</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.PackageVersionsController">
            <summary>
            OData controller for querying Package Versions
            </summary>
            <remarks>
            This controller provides OData query capabilities for Package Versions across all packages
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.PackageVersionsController.#ctor(OpenAutomate.Core.IServices.IAutomationPackageService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.OData.PackageVersionsController},OpenAutomate.Core.IServices.ITenantContext)">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.OData.PackageVersionsController"/> class
            </summary>
            <param name="packageService">The automation package service</param>
            <param name="logger">The logger</param>
            <param name="tenantContext">The tenant context</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.PackageVersionsController.Get">
            <summary>
            Gets all Package Versions with OData query support
            </summary>
            <returns>Collection of Package Versions that can be queried using OData</returns>
            <remarks>
            Example queries:
            GET /tenant/odata/PackageVersions?$filter=IsActive eq true
            GET /tenant/odata/PackageVersions?$select=Id,VersionNumber,FileName,FileSize,UploadedAt
            GET /tenant/odata/PackageVersions?$filter=FileSize gt 1000000 and UploadedAt gt 2024-01-01T00:00:00Z
            GET /tenant/odata/PackageVersions?$filter=contains(VersionNumber,'1.0') and ContentType eq 'application/zip'
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.PackageVersionsController.Get(System.Guid)">
            <summary>
            Gets a specific Package Version by ID with OData query support
            </summary>
            <param name="key">The Package Version ID</param>
            <returns>The Package Version if found</returns>
            <remarks>
            Example queries:
            GET /tenant/odata/PackageVersions(guid'12345678-1234-1234-1234-123456789012')?$select=VersionNumber,FileSize
            </remarks>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.SchedulesController">
            <summary>
            OData controller for querying schedules
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.SchedulesController.#ctor(OpenAutomate.Core.IServices.IScheduleService)">
            <summary>
            Initializes a new instance of the SchedulesController
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.SchedulesController.Get">
            <summary>
            Gets schedules with OData query support
            </summary>
            <returns>Queryable collection of schedules</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OData.UsersController">
            <summary>
            OData controller for querying Users
            </summary>
            <remarks>
            This controller provides OData query capabilities for Users
            Only accessible by administrators
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OData.UsersController.#ctor(OpenAutomate.Core.IServices.IAdminService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.OData.UsersController})">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.OData.UsersController"/> class
            </summary>
            <param name="adminService">The admin service</param>
            <param name="logger">The logger</param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:OpenAutomate.API.Controllers.OData.UsersController.Get" -->
        <member name="M:OpenAutomate.API.Controllers.OData.UsersController.Get(System.Guid)">
            <summary>
            Gets a specific User by ID with OData query support
            </summary>
            <param name="key">The User ID</param>
            <returns>The User if found</returns>
            <remarks>
            Example query:
            GET /tenant/odata/Users(guid'12345678-1234-1234-1234-123456789012')?$select=Email,FirstName,LastName
            </remarks>
        </member>
        <member name="T:OpenAutomate.API.Controllers.OrganizationUnitController">
            <summary>
            Controller for managing organization units (tenants) in the system
            </summary>
            <remarks>
            Provides endpoints for creating, retrieving, and updating organization units.
            Some endpoints require specific permissions.
            </remarks>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.#ctor(OpenAutomate.Core.IServices.IOrganizationUnitService,OpenAutomate.Core.IServices.ICacheInvalidationService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.OrganizationUnitController})">
            <summary>
            Initializes a new instance of the <see cref="T:OpenAutomate.API.Controllers.OrganizationUnitController"/> class
            </summary>
            <param name="organizationUnitService">The organization unit service</param>
            <param name="cacheInvalidationService">The cache invalidation service</param>
            <param name="logger">The logger</param>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.Create(OpenAutomate.Core.Dto.OrganizationUnit.CreateOrganizationUnitDto)">
            <summary>
            Creates a new organization unit with default authorities
            </summary>
            <param name="dto">The organization unit creation data</param>
            <returns>The newly CreatedAtorganization unit details</returns>
            <remarks>
            This endpoint creates a new organization unit (tenant) and automatically sets up
            default authorities (OWNER, MANAGER, DEVELOPER, USER). The authenticated user
            will be assigned as an OWNER of the new organization unit.
            
            The slug is automatically generated from the name and does not need to be provided in the request.
            </remarks>
            <response code="201">Organization unit successfully created</response>
            <response code="400">Invalid organization unit data</response>
            <response code="401">User is not authenticated</response>
            <response code="500">Server error during creation process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.GetMyOrganizationUnits">
            <summary>
            Gets all organization units that the current user belongs to
            </summary>
            <returns>A collection of organization units the user belongs to and the total count</returns>
            <remarks>
            This endpoint retrieves all organization units that the authenticated user belongs to,
            regardless of their role (OWNER, MANAGER, etc.) within those units.
            </remarks>
            <response code="200">Organization units retrieved successfully</response>
            <response code="401">User is not authenticated</response>
            <response code="500">Server error during retrieval process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.GetById(System.Guid)">
            <summary>
            Gets an organization unit by its unique identifier
            </summary>
            <param name="id">The unique identifier of the organization unit</param>
            <returns>The organization unit details</returns>
            <response code="200">Organization unit found and returned</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">Organization unit not found</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.GetBySlug(System.String)">
            <summary>
            Gets an organization unit by its URL-friendly slug
            </summary>
            <param name="slug">The URL-friendly identifier (slug) of the organization unit</param>
            <returns>The organization unit details</returns>
            <response code="200">Organization unit found and returned</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">Organization unit not found</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.GetAll">
            <summary>
            Gets all organization units in the system
            </summary>
            <returns>A collection of all organization units</returns>
            <remarks>
            This endpoint retrieves all organization units that the user has permission to view.
            For regular users, this will include only the organization units they belong to.
            For administrators, this may include all organization units in the system.
            </remarks>
            <response code="200">List of organization units retrieved successfully</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.Update(System.Guid,OpenAutomate.Core.Dto.OrganizationUnit.CreateOrganizationUnitDto)">
            <summary>
            Updates an existing organization unit
            </summary>
            <param name="id">The unique identifier of the organization unit to update</param>
            <param name="dto">The updated organization unit data</param>
            <returns>The updated organization unit details</returns>
            <remarks>
            This endpoint updates organization unit properties such as name and description.
            The slug is automatically regenerated when the name changes.
            Note that changing the name will impact URL routing to this organization unit as the slug will change.
            </remarks>
            <response code="200">Organization unit updated successfully</response>
            <response code="400">Invalid organization unit data</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">Organization unit not found</response>
            <response code="500">Server error during update process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.CheckNameChange(System.Guid,System.String)">
            <summary>
            Checks the potential impact of changing an organization unit's name
            </summary>
            <param name="id">The unique identifier of the organization unit</param>
            <param name="newName">The proposed new name for the organization unit</param>
            <returns>A warning object containing information about the impact of the name change</returns>
            <remarks>
            This endpoint analyzes the impact of a name change, particularly on the slug (URL-friendly identifier).
            It helps users understand potential routing or access issues before committing to a name change.
            </remarks>
            <response code="200">Impact analysis completed successfully</response>
            <response code="400">New name is invalid or missing</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">Organization unit not found</response>
            <response code="500">Server error during analysis process</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.RequestDeletion(System.Guid)">
            <summary>
            Request deletion of organization unit (Owner only)
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.CancelDeletion(System.Guid)">
            <summary>
            Cancel pending deletion
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitController.GetDeletionStatus(System.Guid)">
            <summary>
            Get deletion status with countdown
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitInvitationController.InviteUser(System.String,OpenAutomate.Core.Dto.OrganizationUnitInvitation.InviteUserRequest)">
            <summary>
            Sends an invitation to a user to join the specified organization.
            </summary>
            <param name="tenant">The organization slug.</param>
            <param name="request">The invitation request containing the recipient's email.</param>
            <returns>
            200 OK: Returns the created invitation information.<br/>
            404 Not Found: Organization not found.
            </returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitInvitationController.AcceptInvitation(OpenAutomate.Core.Dto.OrganizationUnitInvitation.AcceptInvitationRequest)">
            <summary>
            Accepts an organization unit invitation using the provided token.
            </summary>
            <param name="request">The accept invitation request containing the invitation token.</param>
            <returns>
            200 OK: Invitation accepted, returns success and invited email.<br/>
            403 Forbidden: User is not invited to this organization unit.<br/>
            404 Not Found: Invitation or user not found.<br/>
            410 Gone: Invitation has expired.<br/>
            500 Internal Server Error: Unknown error.
            </returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitInvitationController.CheckInvitation(System.String,System.String)">
            <summary>
            Checks if an email has a pending invitation to the specified organization.
            </summary>
            <param name="tenant">The organization slug.</param>
            <param name="email">The email address to check.</param>
            <returns>
            200 OK: Returns invited status and invitation status if found.<br/>
            404 Not Found: Organization not found.
            </returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitInvitationController.CheckInvitationToken(System.String)">
            <summary>
            Checks the validity and status of an invitation token.
            </summary>
            <param name="token">The invitation token to check.</param>
            <returns>
            200 OK: Returns invitation status, recipient email, expiration, and organization unit ID.<br/>
            400 Bad Request: Token is required.<br/>
            404 Not Found: Invitation not found or token invalid.
            </returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitInvitationController.ListInvitations(System.String)">
            <summary>
            Lists all invitations for the specified organization unit.
            </summary>
            <param name="tenant">The organization slug.</param>
            <returns>
            200 OK: Returns a list of invitations for the organization unit.<br/>
            404 Not Found: Organization not found.
            </returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitUserController.GetUsersInOrganizationUnit(System.String)">
            <summary>
            Gets all users in a specific organization unit by tenant slug
            </summary>
            <param name="tenant">The slug of the organization unit (tenant)</param>
            <returns>List of users in the organization unit</returns>
            <response code="200">List of users retrieved successfully</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">Organization unit not found</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitUserController.DeleteUser(System.String,System.Guid)">
            <summary>
            Deletes a user from the organization unit by tenant slug
            </summary>
            <param name="tenant">The slug of the organization unit (tenant)</param>
            <param name="userId">The ID of the user to remove</param>
            <returns>No content if successful</returns>
            <response code="204">User removed successfully</response>
            <response code="404">User or organization unit not found</response>
        </member>
        <member name="M:OpenAutomate.API.Controllers.OrganizationUnitUserController.GetRolesInOrganizationUnit(System.String)">
            <summary>
            Gets all roles in a specific organization unit by tenant slug
            </summary>
            <param name="tenant">The slug of the organization unit (tenant)</param>
            <returns>List of roles in the organization unit</returns>
            <response code="200">List of roles retrieved successfully</response>
            <response code="401">User is not authenticated</response>
            <response code="403">User lacks required permissions</response>
            <response code="404">Organization unit not found</response>
        </member>
        <member name="T:OpenAutomate.API.Controllers.ScheduleController">
            <summary>
            Controller for schedule management operations
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.#ctor(OpenAutomate.Core.IServices.IScheduleService,Microsoft.Extensions.Logging.ILogger{OpenAutomate.API.Controllers.ScheduleController})">
            <summary>
            Initializes a new instance of the ScheduleController
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.CreateSchedule(OpenAutomate.Core.Dto.Schedule.CreateScheduleDto)">
            <summary>
            Creates a new schedule
            </summary>
            <param name="dto">Schedule creation data</param>
            <returns>Created schedule response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.GetScheduleById(System.Guid)">
            <summary>
            Gets a schedule by ID
            </summary>
            <param name="id">Schedule ID</param>
            <returns>Schedule response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.GetAllSchedules">
            <summary>
            Gets all schedules for the current tenant
            </summary>
            <returns>Collection of schedules</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.UpdateSchedule(System.Guid,OpenAutomate.Core.Dto.Schedule.UpdateScheduleDto)">
            <summary>
            Updates an existing schedule
            </summary>
            <param name="id">Schedule ID</param>
            <param name="dto">Schedule update data</param>
            <returns>Updated schedule response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.DeleteSchedule(System.Guid)">
            <summary>
            Deletes a schedule
            </summary>
            <param name="id">Schedule ID</param>
            <returns>No content response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.EnableSchedule(System.Guid)">
            <summary>
            Enables a schedule
            </summary>
            <param name="id">Schedule ID</param>
            <returns>Updated schedule response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.DisableSchedule(System.Guid)">
            <summary>
            Disables a schedule
            </summary>
            <param name="id">Schedule ID</param>
            <returns>Updated schedule response</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.ScheduleController.GetUpcomingRunTimes(System.Guid,System.Int32)">
            <summary>
            Gets upcoming run times for a schedule
            </summary>
            <param name="id">Schedule ID</param>
            <param name="count">Number of upcoming run times to return (default: 5)</param>
            <returns>List of upcoming run times in UTC</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.SubscriptionController">
            <summary>
            Controller for managing subscriptions and checkout processes
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Controllers.SubscriptionController.GetCheckoutUrl(System.String)">
            <summary>
            Generates a Lemon Squeezy checkout URL for the current tenant
            </summary>
            <param name="redirectUrl">Optional redirect URL after successful payment</param>
            <returns>Checkout URL for the user to complete payment</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.SubscriptionController.GetSubscriptionStatus">
            <summary>
            Gets the current subscription status for the tenant
            </summary>
            <returns>Current subscription status and details</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.SubscriptionController.StartTrial">
            <summary>
            Starts a trial subscription for the current organization unit
            </summary>
            <returns>Response indicating whether trial was started successfully</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.SubscriptionController.GetCustomerPortal">
            <summary>
            Gets the customer portal URL for managing subscription
            </summary>
            <returns>Customer portal URL from Lemon Squeezy</returns>
        </member>
        <member name="M:OpenAutomate.API.Controllers.SubscriptionController.DetermineUserTrialStatusAsync(System.Guid,System.Guid,OpenAutomate.Core.IServices.SubscriptionStatus)">
            <summary>
            Determines the user's trial status based on current subscription and user's trial history
            </summary>
            <param name="organizationUnitId">The current organization unit ID</param>
            <param name="userId">The user ID</param>
            <param name="currentSubscriptionStatus">The current subscription status</param>
            <returns>The user's trial status</returns>
        </member>
        <member name="T:OpenAutomate.API.Controllers.TrialStatus">
            <summary>
            Enum representing the trial status for a user
            </summary>
        </member>
        <member name="F:OpenAutomate.API.Controllers.TrialStatus.Eligible">
            <summary>
            User is eligible to start a trial
            </summary>
        </member>
        <member name="F:OpenAutomate.API.Controllers.TrialStatus.Active">
            <summary>
            User has an active trial
            </summary>
        </member>
        <member name="F:OpenAutomate.API.Controllers.TrialStatus.Used">
            <summary>
            User has used their trial (on this or another organization unit)
            </summary>
        </member>
        <member name="F:OpenAutomate.API.Controllers.TrialStatus.NotEligible">
            <summary>
            User is not eligible for a trial
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.CheckoutResponse">
            <summary>
            Response model for checkout URL generation
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.SubscriptionStatusResponse">
            <summary>
            Response model for subscription status
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.StartTrialResponse">
            <summary>
            Response model for starting a trial subscription
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Controllers.CustomerPortalResponse">
            <summary>
            Response model for customer portal URL
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Extensions.BotAgentServiceExtensions">
            <summary>
            Extension methods for the BotAgentService to interact with SignalR
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Extensions.BotAgentServiceExtensions.SendCommandToBotAgentAsync(OpenAutomate.Core.IServices.IBotAgentService,Microsoft.AspNetCore.SignalR.IHubContext{OpenAutomate.API.Hubs.BotAgentHub},System.Guid,System.String,System.Object,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Sends a command to a bot agent via SignalR
            </summary>
            <param name="botAgentService">The bot agent service</param>
            <param name="hubContext">The SignalR hub context</param>
            <param name="botAgentId">ID of the target bot agent</param>
            <param name="command">Command to execute</param>
            <param name="payload">Additional data for the command</param>
            <param name="logger">Optional logger for the operation</param>
        </member>
        <member name="M:OpenAutomate.API.Extensions.BotAgentServiceExtensions.SendTenantNotificationAsync(OpenAutomate.Core.IServices.IBotAgentService,Microsoft.AspNetCore.SignalR.IHubContext{OpenAutomate.API.Hubs.BotAgentHub},System.Guid,System.String,System.Object,OpenAutomate.Core.IServices.ITenantContext,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Sends a notification to all frontend clients in a tenant
            </summary>
            <param name="botAgentService">The bot agent service</param>
            <param name="hubContext">The SignalR hub context</param>
            <param name="tenantId">ID of the tenant</param>
            <param name="notificationType">Type of notification</param>
            <param name="data">Notification data</param>
            <param name="tenantContext">Optional tenant context for validation</param>
            <param name="logger">Optional logger for the operation</param>
        </member>
        <member name="T:OpenAutomate.API.Extensions.HttpContextExtensions">
            <summary>
            Extension methods for HttpContext
            </summary>
        </member>
        <member name="F:OpenAutomate.API.Extensions.HttpContextExtensions.SystemEndpoints">
            <summary>
            System endpoints that don't require tenant resolution
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Extensions.HttpContextExtensions.GetCurrentUser(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Gets the current user from the HttpContext items
            </summary>
            <param name="context">The HttpContext</param>
            <returns>The current User or null if not authenticated</returns>
        </member>
        <member name="M:OpenAutomate.API.Extensions.HttpContextExtensions.GetTenantSlug(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Extracts the tenant slug from the request path
            </summary>
            <param name="context">The HTTP context</param>
            <returns>The tenant slug or null if not found</returns>
        </member>
        <member name="M:OpenAutomate.API.Extensions.HttpContextExtensions.GetTenantSlugFromPath(System.String)">
            <summary>
            Helper method to extract tenant slug from the request path
            </summary>
            <param name="path">The request path</param>
            <returns>The tenant slug or null if not found</returns>
        </member>
        <member name="T:OpenAutomate.API.Extensions.JwtAuthenticationMiddlewareExtensions">
            <summary>
            Extension methods for the JwtAuthenticationMiddleware
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Extensions.ODataExtensions">
            <summary>
            Extensions for configuring OData in the application
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Extensions.ODataExtensions.GetEdmModel">
            <summary>
            Builds and returns the Entity Data Model for OData
            </summary>
            <returns>The configured EDM model</returns>
        </member>
        <member name="T:OpenAutomate.API.Hubs.BotAgentHub">
            <summary>
            SignalR hub for real-time communication with bot agents
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Hubs.BotAgentHub.OnConnectedAsync">
            <summary>
            Handles connection of a bot agent
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Hubs.BotAgentHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            Handles disconnection of a bot agent
            </summary>
            <param name="exception">Optional exception that caused the disconnection</param>
        </member>
        <member name="M:OpenAutomate.API.Hubs.BotAgentHub.SendStatusUpdate(System.String,System.String)">
            <summary>
            Method for bot agent to send status updates
            </summary>
            <param name="status">Current status of the bot agent</param>
            <param name="executionId">Optional execution ID if the status is related to a specific execution</param>
        </member>
        <member name="M:OpenAutomate.API.Hubs.BotAgentHub.SendExecutionStatusUpdate(System.String,System.String,System.String)">
            <summary>
            Method for bot agent to send execution-specific status updates
            </summary>
            <param name="executionId">ID of the execution</param>
            <param name="status">Current status of the execution</param>
            <param name="message">Optional status message</param>
        </member>
        <member name="M:OpenAutomate.API.Hubs.BotAgentHub.KeepAlive">
            <summary>
            Lightweight method for bot agent to keep the connection alive without sending status updates
            </summary>
        </member>
        <member name="M:OpenAutomate.API.Hubs.BotAgentHub.SendCommandToBotAgent(System.String,System.String,System.Object)">
            <summary>
            Method for server to send commands to bot agents
            </summary>
            <param name="botAgentId">ID of the target bot agent</param>
            <param name="command">Command to execute</param>
            <param name="payload">Additional data for the command</param>
        </member>
        <member name="T:OpenAutomate.API.Middleware.JwtAuthenticationMiddleware">
            <summary>
            Middleware that validates JWT token and retrieves the corresponding user
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Middleware.TenantResolutionMiddleware">
            <summary>
            Middleware that resolves the current tenant from the URL path
            </summary>
        </member>
        <member name="T:OpenAutomate.API.Middleware.TenantResolutionMiddlewareExtensions">
            <summary>
            Extension methods for the TenantResolutionMiddleware
            </summary>
        </member>
    </members>
</doc>
