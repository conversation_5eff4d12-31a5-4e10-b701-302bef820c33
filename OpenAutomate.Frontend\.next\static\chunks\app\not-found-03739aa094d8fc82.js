(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4345],{16646:(e,r,t)=>{Promise.resolve().then(t.bind(t,99543))},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>o});var n=t(95155),a=t(12115),s=t(66634),l=t(74466),i=t(36928);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:l,asChild:d=!1,...c}=e,u=d?s.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:l,className:t})),ref:r,...c})});d.displayName="Button"},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},36928:(e,r,t)=>{"use strict";t.d(r,{cn:()=>s});var n=t(52596),a=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,n.$)(r))}},57340:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},66634:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l});var n=t(12115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var s=t(95155),l=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){var l;let e,i;let o=(l=t,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,r){let t={...r};for(let n in r){let a=e[n],s=r[n];/^on[A-Z]/.test(n)?a&&s?t[n]=(...e)=>{let r=s(...e);return a(...e),r}:a&&(t[n]=a):"style"===n?t[n]={...a,...s}:"className"===n&&(t[n]=[a,s].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(d.ref=r?function(...e){return r=>{let t=!1,n=e.map(e=>{let n=a(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():a(e[r],null)}}}}(r,o):o),n.cloneElement(t,d)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:a,...l}=e,i=n.Children.toArray(a),d=i.find(o);if(d){let e=d.props.children,a=i.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(r,{...l,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}("Slot"),i=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>i,Zp:()=>s,aR:()=>l,wL:()=>c});var n=t(95155);t(12115);var a=t(36928);function s(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function i(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}function c(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>l});var n=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,l=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return s(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:i}=r,o=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let s=a(r)||a(n);return l[e][s]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return s(e,o,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},99543:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var n=t(95155),a=t(30285),s=t(66695),l=t(35169),i=t(57340),o=t(6874),d=t.n(o);function c(){return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-gray-100 flex items-center justify-center p-4",children:(0,n.jsx)(s.Zp,{className:"w-full max-w-md text-center shadow-lg",children:(0,n.jsxs)(s.Wu,{className:"pt-8 pb-8",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("div",{className:"text-6xl font-bold text-gray-300 mb-2",children:"404"}),(0,n.jsx)("div",{className:"w-24 h-1 bg-orange-600 mx-auto rounded-full"})]}),(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Not Found"}),(0,n.jsx)("p",{className:"text-gray-600 mb-8 leading-relaxed",children:"Sorry, we could not find the page you are looking for. It might have been moved, deleted, or you entered the wrong URL."}),(0,n.jsx)("div",{className:"space-y-3",children:(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(a.$,{variant:"outline",className:"flex-1",onClick:()=>window.history.back(),children:[(0,n.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Go Back"]}),(0,n.jsx)(a.$,{asChild:!0,className:"flex-1 bg-orange-600 hover:bg-orange-700",children:(0,n.jsxs)(d(),{href:"/",children:[(0,n.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Go Home"]})})]})})]})})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,6874,8441,1684,7358],()=>r(16646)),_N_E=e.O()}]);