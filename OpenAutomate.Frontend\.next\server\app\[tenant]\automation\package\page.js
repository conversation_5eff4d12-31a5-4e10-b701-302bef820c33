(()=>{var e={};e.id=5016,e.ids=[5016],e.modules={1303:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5714:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l,metadata:()=>r});var s=a(37413),n=a(96971);let r={title:"Automation Packages",description:"Manage your automation packages and versions"};function l(){return(0,s.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,s.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,s.jsx)(n.default,{})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16023:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27590:(e,t,a)=>{"use strict";function s(e,t={}){let{dateStyle:a="medium",timeStyle:n="short",fallback:r="N/A",customFormat:l,locale:i="en-US"}=t;if(!e)return r;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug(`Corrected UTC date format: ${e} -> ${a}`)),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn(`Invalid date provided to formatUtcToLocal: ${e}`),r;if(l)return function(e,t){let a=e.getFullYear(),s=e.getMonth()+1,n=e.getDate(),r=e.getHours(),l=e.getMinutes(),i={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][s-1],dd:n.toString().padStart(2,"0"),h:(r%12||12).toString(),mm:l.toString().padStart(2,"0"),a:r>=12?"PM":"AM"},o=t;return Object.entries(i).forEach(([e,t])=>{o=o.replace(RegExp(e,"g"),t)}),o}(t,l);return new Intl.DateTimeFormat(i,{dateStyle:a,timeStyle:n}).format(t)}catch(t){return console.error(`Error formatting date ${e}:`,t),r}}a.d(t,{Ej:()=>s})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},31599:(e,t,a)=>{"use strict";a.d(t,{c:()=>o});var s=a(43210),n=a(39989),r=a(16189),l=a(31207),i=a(70891);function o(){let e=(0,r.useRouter)(),{data:t,error:a,isLoading:o,mutate:c}=(0,l.Ay)(i.DC.organizationUnits(),()=>n.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:o,error:a?"Failed to fetch organization units. Please try again later.":null,refresh:c,selectOrganizationUnit:(0,s.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},37337:(e,t,a)=>{"use strict";a.d(t,{AW:()=>d,Cb:()=>u,QQ:()=>o,ae:()=>r,jm:()=>c,oy:()=>i,s9:()=>l});var s=a(51787);let n=()=>"default",r=async e=>{let t=n();return await s.F.get(`${t}/api/packages/${e}`)},l=async()=>{let e=n();return await s.F.get(`${e}/api/packages`)},i=async e=>{let t=n(),a=new FormData;return a.append("file",e.file),e.name&&a.append("name",e.name),e.description&&a.append("description",e.description),e.version&&a.append("version",e.version),await s.F.post(`${t}/api/packages/upload`,a)},o=async(e,t)=>{let a=n();return await s.F.get(`${a}/api/packages/${e}/versions/${t}/download`)},c=async e=>{let t=n();await s.F.delete(`${t}/api/packages/${e}`)},d=async(e,t)=>{let a=n();await s.F.delete(`${a}/api/packages/${e}/versions/${t}`)},u=async e=>{let t=n(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(([e,a])=>{null!=a&&"$count"!==e&&t.append(e,String(a))}),t.toString()}(e),r=`${t}/odata/AutomationPackages`;a&&(r+=`?${a}`),console.log("OData query endpoint:",r);try{let e=await s.F.get(r);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log(`Received ${e.value.length} packages from OData. Total count: ${e["@odata.count"]}`),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log(`Found array property "${a}" in response`);let s=e[a],n=e["@odata.count"];return{value:s,"@odata.count":"number"==typeof n?n:s.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},39989:(e,t,a)=>{"use strict";a.d(t,{K:()=>n});var s=a(51787);let n={getMyOrganizationUnits:async()=>await s.F.get("/api/ou/my-ous"),getBySlug:async e=>await s.F.get(`/api/ou/slug/${e}`),getById:async e=>await s.F.get(`/api/ou/${e}`),create:async e=>await s.F.post("/api/ou/create",e),update:async(e,t)=>await s.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await s.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await s.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await s.F.get(`/api/ou/${e}/deletion-status`)}},42300:(e,t,a)=>{"use strict";a.d(t,{z:()=>r});var s=a(16189),n=a(43210);function r(){let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),a=(0,n.useCallback)(e=>{let a=new URLSearchParams(t.toString());return Object.entries(e).forEach(([e,t])=>{null===t?a.delete(e):a.set(e,t)}),a.toString()},[t]),r=(0,n.useCallback)((t,s)=>{let n=a(s);e.push(`${t}?${n}`,{scroll:!1})},[a,e]);return{createQueryString:a,updateUrl:r}}},53984:(e,t,a)=>{"use strict";a.d(t,{i:()=>o});var s=a(60687),n=a(4654),r=a(56476),l=a(29523),i=a(21342);function o({table:e}){return(0,s.jsxs)(i.rI,{children:[(0,s.jsx)(n.ty,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,s.jsx)(r.A,{}),"View"]})}),(0,s.jsxs)(i.SQ,{align:"end",className:"w-[150px]",children:[(0,s.jsx)(i.lp,{children:"Toggle columns"}),(0,s.jsx)(i.mB,{}),e.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,s.jsx)(i.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},57175:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},61018:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>g,Es:()=>h,HM:()=>u,L3:()=>x,c7:()=>m,lG:()=>o,rr:()=>f,zM:()=>c});var s=a(60687),n=a(43210),r=a(88562),l=a(11860),i=a(36966);let o=r.bL,c=r.l9,d=r.ZL,u=r.bm,p=n.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));p.displayName=r.hJ.displayName;let g=n.forwardRef(({className:e,children:t,...a},n)=>(0,s.jsxs)(d,{children:[(0,s.jsx)(p,{}),(0,s.jsxs)(r.UC,{ref:n,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...a,children:[t,(0,s.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));g.displayName=r.UC.displayName;let m=({className:e,...t})=>(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let h=({className:e,...t})=>(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});h.displayName="DialogFooter";let x=n.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=r.hE.displayName;let f=n.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=r.VY.displayName},64291:(e,t,a)=>{"use strict";a.d(t,{default:()=>_});var s=a(60687),n=a(41862),r=a(1303),l=a(29523),i=a(96834),o=a(56896),c=a(34208),d=a(43210),u=a(93661),p=a(31158),g=a(57175),m=a(96362),h=a(63503),x=a(21342),f=a(20140);function v({row:e,onRefresh:t}){let[a,n]=(0,d.useState)(!1),[r,i]=(0,d.useState)(!1),[o,c]=(0,d.useState)(""),[v,j]=(0,d.useState)(!1),{toast:y}=(0,f.d)(),b=async()=>{j(!0);try{y({title:"Package deleted",description:`Package ${e.original.name} has been deleted.`}),n(!1),t&&t()}catch(e){n(!1),e instanceof Error?c(e.message):c("Failed to delete package."),i(!0)}finally{j(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(x.rI,{children:[(0,s.jsx)(x.ty,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,s.jsxs)(x.SQ,{align:"end",className:"w-[160px]",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)(x._2,{onClick:t=>{t&&t.stopPropagation();let a=e.original.versions;if(a&&a.length>0){let t=a.sort((e,t)=>new Date(t.uploadedAt).getTime()-new Date(e.uploadedAt).getTime())[0];y({title:"Downloading package",description:`Downloading package: ${e.original.name} version: ${t.versionNumber}`})}else c("No versions available to download."),i(!0)},children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Download"})]}),(0,s.jsxs)(x._2,{onClick:t=>{t&&t.stopPropagation(),y({title:"Edit package",description:`Editing package: ${e.original.name}`})},children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Edit"})]}),(0,s.jsx)(x.mB,{}),(0,s.jsxs)(x._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e&&e.stopPropagation(),n(!0)},children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Delete"})]})]})]}),(0,s.jsx)(h.lG,{open:a,onOpenChange:n,children:(0,s.jsxs)(h.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(h.c7,{children:(0,s.jsx)(h.L3,{children:"Confirm Delete"})}),(0,s.jsxs)("div",{children:["Are you sure you want to delete package ",(0,s.jsx)("b",{children:e.original.name}),"?"]}),(0,s.jsxs)(h.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:()=>n(!1),disabled:v,children:"Cancel"}),(0,s.jsx)(l.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:b,disabled:v,children:"Delete"})]})]})}),(0,s.jsx)(h.lG,{open:r,onOpenChange:i,children:(0,s.jsxs)(h.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(h.c7,{children:(0,s.jsx)(h.L3,{children:"Error"})}),(0,s.jsx)("div",{children:o}),(0,s.jsx)(h.Es,{children:(0,s.jsx)(l.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:()=>i(!1),children:"OK"})})]})})]})}var j=a(27590);let y=e=>[{id:"select",header:({table:e})=>(0,s.jsx)(o.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>(0,s.jsx)(o.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1},{id:"actions",cell:({row:t})=>(0,s.jsx)(v,{row:t,onRefresh:e}),header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Actions"})},{accessorKey:"name",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Name"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex space-x-2",children:(0,s.jsx)("span",{className:"max-w-[500px] truncate font-medium",children:e.getValue("name")})})},{accessorKey:"description",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Description"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:"max-w-md truncate",title:e.getValue("description"),children:e.getValue("description")})})},{accessorKey:"versions",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Latest Version"}),cell:({row:e})=>{let t=e.getValue("versions"),a=t&&t.length>0?[...t].sort((e,t)=>new Date(t.uploadedAt).getTime()-new Date(e.uploadedAt).getTime())[0]:null;return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(i.E,{variant:"secondary",children:a?a.versionNumber:"No versions"})})},enableSorting:!1},{accessorKey:"versions",id:"versionCount",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Version Count"}),cell:({row:e})=>{let t=e.getValue("versions"),a=t?t.length:0;return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:a})})},enableSorting:!1},{accessorKey:"isActive",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Status"}),cell:({row:e})=>{let t=e.getValue("isActive");return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${t?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}`,children:t?"Active":"Inactive"})})}},{accessorKey:"createdAt",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Created Date"}),cell:({row:e})=>{let t=e.getValue("createdAt"),a=(0,j.Ej)(t,{dateStyle:"medium",timeStyle:void 0,fallback:"Invalid date"});return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:a})})}}];y();var b=a(50723),w=a(91821),k=a(16023),C=a(37337),N=a(59321);function A({isOpen:e,onClose:t,mode:a,onSuccess:r}){let{toast:i}=(0,f.d)(),[o,c]=(0,d.useState)(null),[u,p]=(0,d.useState)(""),[g,m]=(0,d.useState)(!1),[x,v]=(0,d.useState)(null),j=async()=>{try{if(m(!0),v(null),!o){let e="Please select a package file";v(e),i({title:"Validation Error",description:e,variant:"destructive"});return}let e=await (0,C.oy)({file:o,version:u.trim()||void 0});i({title:"Success",description:`Package "${e.name}" uploaded successfully`,variant:"default"}),y(),r?.()}catch(e){console.error("Error creating package:",e),v((0,N.PE)(e)),i((0,N.m4)(e))}finally{m(!1)}},y=()=>{c(null),p(""),v(null),t()};return(0,s.jsx)(h.lG,{open:e,onOpenChange:y,children:(0,s.jsxs)(h.Cf,{className:"sm:max-w-[600px] p-6",children:[(0,s.jsx)(h.c7,{children:(0,s.jsx)(h.L3,{children:"edit"===a?"Edit Package":"Create New Automation Package"})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsxs)("label",{htmlFor:"file-upload",className:"flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-md cursor-pointer hover:bg-gray-50 transition-colors",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,s.jsx)(k.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:o?o.name:"Click to select a bot package file (.zip)"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"ZIP files containing bot.py are supported"})]}),(0,s.jsx)("input",{id:"file-upload",type:"file",accept:".zip",className:"hidden",onChange:e=>{e.target.files&&e.target.files[0]&&(c(e.target.files[0]),v(null))}})]})}),x&&(0,s.jsx)(w.Fc,{variant:"destructive",children:(0,s.jsx)(w.TN,{children:x})})]}),(0,s.jsxs)(h.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:y,disabled:g,children:"Cancel"}),(0,s.jsx)(l.$,{onClick:j,disabled:!o||g,children:g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create Package"})]})]})})}var S=a(45880),P=a(16189),$=a(99270),F=a(11860),z=a(80462),R=a(89667),M=a(53984),O=a(15079);function D({table:e,statuses:t,onSearch:a,onStatusChange:r,searchValue:o="",isFiltering:c=!1,isPending:u=!1,totalCount:p=0}){let g=e.getState().columnFilters.length>0,m=e.getState().columnFilters.length,h=(0,d.useRef)(null),x=(0,d.useRef)(null),f=t=>{h.current&&(x.current=h.current.selectionStart),a?a(t):e.getColumn("name")?.setFilterValue(t)};return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,s.jsx)($.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(R.p,{ref:h,placeholder:"Search by name or description...",value:o,onChange:e=>f(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:c,onFocus:()=>{h.current&&(x.current=h.current.selectionStart)}}),c&&(0,s.jsx)(n.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!c&&""!==o&&(0,s.jsx)(F.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>f("")})]}),e.getColumn("isActive")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(O.l6,{onValueChange:t=>{r?r(t):e.getColumn("isActive")?.setFilterValue("all"===t?"":t)},value:e.getColumn("isActive")?.getFilterValue()||"all",disabled:c||u,children:[(0,s.jsx)(O.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(z.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(O.yv,{placeholder:""}),e.getColumn("isActive")?.getFilterValue()&&(0,s.jsx)(i.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsx)(O.gC,{children:t.map(e=>(0,s.jsx)(O.eb,{value:e.value,children:e.label},e.value))})]})}),p>0&&(0,s.jsx)("div",{className:"text-sm font-medium ml-2",children:(0,s.jsxs)("span",{children:["Total: ",p," package",1!==p?"s":""]})}),m>0&&(0,s.jsxs)(i.E,{variant:"secondary",className:"rounded-sm px-1",children:[m," active ",1===m?"filter":"filters"]}),g&&(0,s.jsxs)(l.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),a&&a("")},className:"h-8 px-2 lg:px-3",disabled:c,children:["Reset",(0,s.jsx)(F.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(M.i,{table:e})]})}var I=a(56090),E=a(93772),V=a(42300),T=a(14583),G=a(31207),L=a(70891);function _(){let e=(0,P.useRouter)(),t=(0,P.usePathname)(),a=(0,P.useSearchParams)(),{updateUrl:i}=(0,V.z)(),{toast:o}=(0,f.d)(),[c,u]=(0,d.useState)(!1),[p,g]=(0,d.useState)("create"),[m,h]=(0,d.useState)({}),[x,v]=(0,d.useState)({}),[j,w]=(0,d.useState)(0);(0,d.useRef)(0);let[k,N]=(0,d.useState)(!1),[S,$]=(0,d.useState)(!1),[F,z]=(0,d.useState)(!1),R=(0,d.useRef)(null);(0,d.useRef)(!0);let[M,O]=(0,d.useState)(()=>{let e=[],t=a.get("name");t&&e.push({id:"name",value:t});let s=a.get("isActive");return s&&e.push({id:"isActive",value:s}),e}),[_,q]=(0,d.useState)(()=>{let e=a.get("sort"),t=a.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[{id:"createdAt",desc:!0}]}),[U,H]=(0,d.useState)(()=>{let e=a.get("page"),t=a.get("size");return{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:t?parseInt(t):10}}),[K,B]=(0,d.useState)(a.get("name")??""),J=(0,d.useCallback)(()=>{let e={$top:U.pageSize,$skip:U.pageIndex*U.pageSize,$count:!0,$expand:"Versions"};if(_.length>0&&(e.$orderby=_.map(e=>`${e.id} ${e.desc?"desc":"asc"}`).join(",")),M.length>0){let t=M.filter(e=>"string"!=typeof e.value||""!==e.value).map(e=>{let t=e.id,a=e.value;return"string"==typeof a?"name"===t&&a?`(contains(tolower(name), '${a.toLowerCase()}') or contains(tolower(description), '${a.toLowerCase()}'))`:"isActive"===t?`isActive eq ${"true"===a}`:`contains(tolower(${t}), '${a.toLowerCase()}')`:Array.isArray(a)?a.map(e=>`${t} eq '${e}'`).join(" or "):""}).filter(Boolean);t.length>0&&(e.$filter=t.join(" and "))}return e},[U,_,M])(),{data:Q,error:W,isLoading:Z,mutate:Y}=(0,G.Ay)(L.DC.packagesWithOData(J),()=>(0,C.Cb)(J)),X=(0,d.useMemo)(()=>Q?.value?Q.value:[],[Q]),ee=(0,d.useCallback)(async()=>{N(!1),$(!1),await Y()},[Y]),et=e=>e+1,ea=(e,t)=>Math.max(1,Math.ceil(e/t)),es=(0,d.useMemo)(()=>{let e=ea(j,U.pageSize),t=X.length===U.pageSize&&j<=U.pageSize*(U.pageIndex+1),a=et(U.pageIndex);return t?Math.max(a,e,U.pageIndex+2):Math.max(a,e)},[U.pageSize,U.pageIndex,X.length,j]),en=(0,d.useMemo)(()=>!F&&X.length===U.pageSize,[F,X.length,U.pageSize]),er=(0,I.N4)({data:X,columns:y(ee),state:{sorting:_,columnVisibility:x,rowSelection:m,columnFilters:M,pagination:U},enableRowSelection:!0,onRowSelectionChange:h,onSortingChange:e=>{let a="function"==typeof e?e(_):e;q(a),a.length>0?i(t,{sort:a[0].id,order:a[0].desc?"desc":"asc",page:"1"}):i(t,{sort:null,order:null,page:"1"})},onColumnFiltersChange:O,onColumnVisibilityChange:v,onPaginationChange:e=>{let a="function"==typeof e?e(U):e;H(a),i(t,{page:(a.pageIndex+1).toString(),size:a.pageSize.toString()})},getCoreRowModel:(0,E.HT)(),getFilteredRowModel:(0,E.hM)(),getPaginationRowModel:(0,E.kW)(),getSortedRowModel:(0,E.h5)(),getFacetedRowModel:(0,E.kQ)(),getFacetedUniqueValues:(0,E.oS)(),manualPagination:!0,pageCount:es,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.id}),el=(0,d.useCallback)(e=>{B(e),N(!0),R.current&&clearTimeout(R.current),R.current=setTimeout(()=>{let a=er.getColumn("name");a&&(a.setFilterValue(e),i(t,{name:e||null,page:"1"})),N(!1)},300)},[er,i,t]),ei=(0,d.useCallback)(e=>{let a=er.getColumn("isActive");if(a){let s="all"===e?"":e;a.setFilterValue(s),i(t,{isActive:s||null,page:"1"})}},[er,i,t]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Automation Packages"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(l.$,{variant:"outline",onClick:ee,disabled:Z||k,children:[Z?(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Refresh"]}),(0,s.jsxs)(l.$,{onClick:()=>{g("create"),u(!0)},className:"flex items-center justify-center",children:[(0,s.jsx)(r.A,{className:"mr-2 h-4 w-4"}),"Create"]})]})]}),W&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,s.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load packages. Please try again."}),(0,s.jsx)(l.$,{variant:"outline",className:"mt-2",onClick:()=>Y(),children:"Retry"})]}),(0,s.jsx)(D,{table:er,statuses:[{value:"all",label:"Show All"},{value:"true",label:"Active"},{value:"false",label:"Inactive"}],onSearch:el,onStatusChange:ei,searchValue:K,isFiltering:Z,isPending:k,totalCount:j}),(0,s.jsx)(b.b,{data:X,columns:y(ee),onRowClick:a=>{let s=t.match(/^\/([^\/]+)/),n=s?s[1]:"default",r=`/${n}/automation/package/${a.id}`;e.push(r)},table:er,isLoading:Z,totalCount:j}),(0,s.jsx)(T.d,{currentPage:U.pageIndex+1,pageSize:U.pageSize,totalCount:j,totalPages:es,isLoading:Z,isChangingPageSize:S,isUnknownTotalCount:en,rowsLabel:"packages",onPageChange:e=>{e!==U.pageIndex+1&&(H({...U,pageIndex:e-1}),i(t,{page:e.toString()}))},onPageSizeChange:e=>{if(e!==U.pageSize){$(!0);let a=Math.floor(U.pageIndex*U.pageSize/e);H({pageSize:e,pageIndex:a}),i(t,{size:e.toString(),page:(a+1).toString()})}}})]}),(0,s.jsx)(A,{isOpen:c,onClose:()=>{u(!1)},mode:p,onSuccess:ee})]})}S.z.object({id:S.z.string(),name:S.z.string(),description:S.z.string(),isActive:S.z.boolean(),createdAt:S.z.string()})},64656:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var s=a(37413),n=a(48974),r=a(31057),l=a(50417),i=a(92588),o=a(61018),c=a(2505);function d({children:e}){return(0,s.jsx)(o.TenantGuard,{children:(0,s.jsx)(c.ChatProvider,{children:(0,s.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,s.jsxs)(l.SidebarProvider,{className:"flex flex-col",children:[(0,s.jsx)(r.SiteHeader,{}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)(n.AppSidebar,{}),(0,s.jsx)(l.SidebarInset,{children:(0,s.jsx)(i.SearchProvider,{children:(0,s.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>o});var s=a(60687),n=a(43210),r=a(16189),l=a(31599),i=a(31568);function o({children:e}){let{tenant:t}=(0,r.useParams)();(0,r.useRouter)();let{isAuthenticated:a,isLoading:o}=(0,i.A)(),{organizationUnits:c,isLoading:d}=(0,l.c)(),[u,p]=(0,n.useState)(!0);return o||d||u?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,s.jsx)(s.Fragment,{children:e})}},70047:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>r.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=a(65239),n=a(48088),r=a(31369),l=a(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let o={children:["",{children:["[tenant]",{children:["automation",{children:["package",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,5714)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\automation\\package\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\automation\\package\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[tenant]/automation/package/page",pathname:"/[tenant]/automation/package",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},70357:(e,t,a)=>{Promise.resolve().then(a.bind(a,64291))},72826:(e,t,a)=>{Promise.resolve().then(a.bind(a,69231)),Promise.resolve().then(a.bind(a,83847)),Promise.resolve().then(a.bind(a,78526)),Promise.resolve().then(a.bind(a,97597)),Promise.resolve().then(a.bind(a,98641)),Promise.resolve().then(a.bind(a,80110))},80085:(e,t,a)=>{Promise.resolve().then(a.bind(a,96971))},80462:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},83442:(e,t,a)=>{Promise.resolve().then(a.bind(a,61018)),Promise.resolve().then(a.bind(a,2505)),Promise.resolve().then(a.bind(a,92588)),Promise.resolve().then(a.bind(a,48974)),Promise.resolve().then(a.bind(a,31057)),Promise.resolve().then(a.bind(a,50417))},91821:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>o,TN:()=>d,XL:()=>c});var s=a(60687),n=a(43210),r=a(24224),l=a(36966);let i=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),o=n.forwardRef(({className:e,variant:t,...a},n)=>(0,s.jsx)("div",{ref:n,role:"alert",className:(0,l.cn)(i({variant:t}),e),...a}));o.displayName="Alert";let c=n.forwardRef(({className:e,children:t,...a},n)=>(t||console.warn("AlertTitle must have content for accessibility"),(0,s.jsx)("h5",{ref:n,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...a,children:t})));c.displayName="AlertTitle";let d=n.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},93661:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},96971:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var s=a(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call packageSchema() from the server but packageSchema is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\automation\\package\\package.tsx","packageSchema");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\automation\\\\package\\\\package.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\automation\\package\\package.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,7966,5584,5156,4654,6467,5880,1694,6945,8759,6763,519,4881],()=>a(70047));module.exports=s})();