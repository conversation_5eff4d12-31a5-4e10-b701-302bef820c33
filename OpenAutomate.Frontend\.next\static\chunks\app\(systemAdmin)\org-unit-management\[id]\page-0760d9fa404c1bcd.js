(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4170],{11145:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>U});var n=a(95155),r=a(35695),s=a(30285),o=a(66695),i=a(26126),l=a(54165),c=a(53904),d=a(35169),u=a(62525),g=a(25657),p=a(42148),h=a(17580),m=a(37108),y=a(75525),f=a(12115),v=a(96365),x=a(45995),b=a(86490),w=a(81053),j=a(32771),k=a(19040),A=a(34953),N=a(70449),D=a(88262),O=a(15426);function F(){return(0,n.jsx)("div",{className:"flex items-center justify-center h-full py-10",children:(0,n.jsx)("div",{className:"animate-spin text-primary",children:(0,n.jsx)(c.A,{className:"h-10 w-10"})})})}function C(e){let{onRetry:t}=e;return(0,n.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,n.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load organization unit details."}),(0,n.jsx)(s.$,{variant:"outline",className:"mt-2",onClick:t,children:"Retry"})]})}function z(e){let{countdown:t,deletionStatusData:a,onCancelClick:r,formatTimeRemaining:o}=e;return(0,n.jsxs)("div",{className:"flex items-center justify-between dark:bg-orange-950/50 bg-orange-50 border border-orange-300 dark:border-orange-800/50 rounded-lg px-4 py-3",children:[(0,n.jsx)("div",{className:"text-orange-700 dark:text-orange-400 font-semibold",children:"number"==typeof t&&t>0?"This organization unit will be deleted in ".concat(o(t)):"Deleting organization unit..."}),(null==a?void 0:a.canCancel)&&(0,n.jsx)(s.$,{variant:"outline",className:"ml-4 border-orange-600 text-orange-700 hover:bg-orange-100",onClick:r,children:"Cancel Deletion"})]})}function S(e){let{title:t,count:a,icon:r,isLoading:s}=e;return(0,n.jsx)(o.Zp,{children:(0,n.jsx)(o.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"p-2 bg-primary/10 rounded-lg",children:r}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:t}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:s?(0,n.jsx)("div",{className:"animate-pulse bg-muted h-8 w-12 rounded"}):null!=a?a:0})]})]})})})}function R(e){let{id:t}=e,a=(0,r.useRouter)(),R=(0,r.useParams)(),U=null==R?void 0:R.tenant,{toast:M}=(0,D.d)(),{data:I,error:T,isLoading:L}=function(e){let t=(0,f.useCallback)(async()=>{if(!e)return Promise.reject(Error("No ID provided"));try{let t=(await x.i.getAllOrganizationUnits()).find(t=>t.id===e);if(!t)throw Error("Organization unit with ID ".concat(e," not found"));return t}catch(t){return console.error("Failed to fetch organization unit via admin API, trying regular API:",t),v.K.getById(e)}},[e]);return(0,A.Ay)(e?"organization-unit-".concat(e):null,t)}(t),{agentsData:P,agentsLoading:B,assetsData:H,assetsLoading:$,packagesData:q,packagesLoading:V,usersData:W,usersLoading:_,rolesData:J,rolesLoading:G}=function(e){let t=(0,f.useCallback)(async()=>{let e=await (0,b.NA)();return{length:e.length,data:e}},[]),a=(0,f.useCallback)(async()=>{let e=await (0,w.NH)();return{length:e.length,data:e}},[]),n=(0,f.useCallback)(async()=>{let e=await (0,j.s9)();return{length:e.length,data:e}},[]),r=(0,f.useCallback)(async()=>{if(!e)return{length:0,data:[]};let t=await k.H1.getUsers(e);return{length:t.length,data:t}},[e]),s=(0,f.useCallback)(async()=>{if(!e)return{length:0,data:[]};let t=await k.H1.getRolesInOrganizationUnit(e);return{length:t.length,data:t}},[e]),{data:o,isLoading:i}=(0,A.Ay)(e?"agents-count-".concat(e):null,t),{data:l,isLoading:c}=(0,A.Ay)(e?"assets-count-".concat(e):null,a),{data:d,isLoading:u}=(0,A.Ay)(e?"packages-count-".concat(e):null,n),{data:g,isLoading:p}=(0,A.Ay)(e?"users-count-".concat(e):null,r),{data:h,isLoading:m}=(0,A.Ay)(e?"roles-count-".concat(e):null,s);return{agentsData:o,agentsLoading:i,assetsData:l,assetsLoading:c,packagesData:d,packagesLoading:u,usersData:g,usersLoading:p,rolesData:h,rolesLoading:m}}(U),{deletionStatusData:Z,mutateDeletionStatus:K,countdown:Y,showDeletionStatus:Q}=function(e){let t=(0,f.useRef)(!1),[a,n]=(0,f.useState)(null),r=async()=>{var t,a,n;if(!e)throw Error("Missing ID");let r=await v.K.getDeletionStatus(e),s=null!==(a=null!==(t=r.isPendingDeletion)&&void 0!==t?t:r.isDeletionPending)&&void 0!==a&&a,o=null;return"number"==typeof r.remainingSeconds?o=r.remainingSeconds:"number"==typeof r.hoursUntilDeletion&&(o=3600*r.hoursUntilDeletion),{isPendingDeletion:s,remainingSeconds:o,scheduledDeletionAt:r.scheduledDeletionAt,canCancel:null!==(n=r.canCancel)&&void 0!==n&&n}},{data:s,mutate:o}=(0,A.Ay)(e?N.DC.organizationUnitDeletionStatus(e):null,r,{refreshInterval:6e4,refreshWhenHidden:!0});return(0,f.useEffect)(()=>{if(!(null==s?void 0:s.isPendingDeletion)){n(null),t.current=!1;return}!t.current&&"number"==typeof s.remainingSeconds&&s.remainingSeconds>=0&&(n(s.remainingSeconds),t.current=!0);let e=setInterval(()=>{n(e=>null===e||e<=0?0:e-1)},1e3);return()=>clearInterval(e)},[null==s?void 0:s.isPendingDeletion,null==s?void 0:s.remainingSeconds]),{deletionStatusData:s,mutateDeletionStatus:o,countdown:a,showDeletionStatus:!!(null==s?void 0:s.isPendingDeletion)}}(t),[X,ee]=(0,f.useState)(!1),[et,ea]=(0,f.useState)(!1),[en,er]=(0,f.useState)(!1),[es,eo]=(0,f.useState)(!1);(0,f.useEffect)(()=>{T&&(console.error("Failed to load organization unit details:",T),M({title:"Error",description:"Failed to load organization unit details.",variant:"destructive"}))},[T,M]);let ei=async()=>{er(!0);try{await x.i.deleteOrganizationUnit(t),M({title:"Success",description:'Organization unit "'.concat(null==I?void 0:I.name,'" has been deleted successfully.')}),a.push("/system-admin/org-unit-management")}catch(e){console.error("Failed to delete organization unit:",e),M({title:"Error",description:"Failed to delete organization unit. Please try again.",variant:"destructive"})}finally{er(!1),ee(!1)}},el=async()=>{eo(!0);try{await v.K.cancelDeletion(t),await K(),M({title:"Deletion Cancelled",description:"Organization unit deletion has been cancelled."})}catch(e){M({title:"Error",description:"Failed to cancel deletion.",variant:"destructive"})}finally{eo(!1),ea(!1)}},ec=(0,f.useCallback)(e=>{if(e<=0)return"Deleting...";let t=Math.floor(e/86400),a=Math.floor(e%86400/3600),n=Math.floor(e%3600/60),r=e%60,s=[];return t>0&&s.push("".concat(t," day").concat(t>1?"s":"")),a>0&&s.push("".concat(a," hour").concat(a>1?"s":"")),n>0&&s.push("".concat(n," minute").concat(n>1?"s":"")),r>0&&0===s.length&&s.push("".concat(r," second").concat(r>1?"s":"")),s.join(", ")},[]);return L?(0,n.jsx)(F,{}):T?(0,n.jsx)(C,{onRetry:()=>window.location.reload()}):I?(0,n.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,n.jsxs)(o.Zp,{className:"border rounded-md shadow-sm",children:[(0,n.jsxs)(o.aR,{className:"flex items-center justify-between border-b p-4",children:[(0,n.jsxs)(s.$,{variant:"ghost",size:"sm",className:"gap-1",onClick:()=>{a.back()},children:[(0,n.jsx)(d.A,{className:"h-4 w-4"}),"Back"]}),!Q&&(0,n.jsxs)(s.$,{variant:"destructive",size:"sm",onClick:()=>{ee(!0)},className:"gap-1",children:[(0,n.jsx)(u.A,{className:"h-4 w-4"}),"Delete"]})]}),(0,n.jsxs)(o.Wu,{className:"p-6 space-y-6",children:[Q&&(0,n.jsx)(z,{countdown:Y,deletionStatusData:Z,onCancelClick:()=>ea(!0),formatTimeRemaining:ec}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(E,{label:"Name",children:I.name}),(0,n.jsx)(E,{label:"Description",children:I.description||"No description"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(E,{label:"Slug",children:I.slug}),(0,n.jsx)(E,{label:"Status",children:(0,n.jsx)(i.E,{variant:"outline",className:I.isActive?"bg-green-100 text-green-600 border-none":"bg-red-100 text-red-600 border-none",children:I.isActive?"Active":"Inactive"})})]})]}),(0,n.jsxs)("div",{className:"mt-8 pt-6 border-t",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Organization Statistics"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4",children:[(0,n.jsx)(S,{title:"Total Agents",count:null==P?void 0:P.length,icon:(0,n.jsx)(g.A,{className:"h-5 w-5 text-primary"}),isLoading:B}),(0,n.jsx)(S,{title:"Total Assets",count:null==H?void 0:H.length,icon:(0,n.jsx)(p.A,{className:"h-5 w-5 text-primary"}),isLoading:$}),(0,n.jsx)(S,{title:"Total Users",count:null==W?void 0:W.length,icon:(0,n.jsx)(h.A,{className:"h-5 w-5 text-primary"}),isLoading:_}),(0,n.jsx)(S,{title:"Total Packages",count:null==q?void 0:q.length,icon:(0,n.jsx)(m.A,{className:"h-5 w-5 text-primary"}),isLoading:V}),(0,n.jsx)(S,{title:"Total Roles",count:null==J?void 0:J.length,icon:(0,n.jsx)(y.A,{className:"h-5 w-5 text-primary"}),isLoading:G})]})]}),(0,n.jsxs)("div",{className:"mt-8 pt-6 border-t",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Additional Information"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsx)(E,{label:"Created At",children:(0,O.Ej)(I.createdAt)}),I.updatedAt&&(0,n.jsx)(E,{label:"Last Updated",children:(0,O.Ej)(I.updatedAt)})]})]})]})]}),(0,n.jsx)(l.lG,{open:X,onOpenChange:ee,children:(0,n.jsxs)(l.Cf,{children:[(0,n.jsxs)(l.c7,{children:[(0,n.jsx)(l.L3,{children:"Delete Organization Unit"}),(0,n.jsxs)(l.rr,{children:['Are you sure you want to delete the organization unit "',null==I?void 0:I.name,'"? It will be deleted in 7 days.']})]}),(0,n.jsxs)(l.Es,{children:[(0,n.jsx)(s.$,{variant:"outline",onClick:()=>ee(!1),disabled:en,children:"Cancel"}),(0,n.jsxs)(s.$,{variant:"destructive",onClick:ei,disabled:en,className:"gap-1",children:[en?(0,n.jsx)(c.A,{className:"h-4 w-4 animate-spin"}):(0,n.jsx)(u.A,{className:"h-4 w-4"}),en?"Processing...":"Delete"]})]})]})}),(0,n.jsx)(l.lG,{open:et,onOpenChange:ea,children:(0,n.jsxs)(l.Cf,{children:[(0,n.jsx)(l.c7,{children:(0,n.jsx)(l.L3,{children:"Cancel Deletion"})}),(0,n.jsx)("div",{children:"Are you sure you want to cancel the deletion of this organization unit?"}),(0,n.jsxs)(l.Es,{children:[(0,n.jsx)(s.$,{variant:"outline",onClick:()=>ea(!1),disabled:es,children:"No"}),(0,n.jsx)(s.$,{onClick:el,className:"bg-[#FF6A1A] text-white hover:bg-orange-500",disabled:es,children:"Cancel Deletion"})]})]})})]}):(0,n.jsx)("div",{children:"Organization unit not found"})}function E(e){let{label:t,children:a}=e;return(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:t}),(0,n.jsx)("div",{className:"text-base font-medium pb-1 border-b",children:a})]})}function U(){let e=(0,r.useParams)().id;return(0,n.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,n.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,n.jsx)(R,{id:e})]})}},15426:(e,t,a)=>{"use strict";function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{dateStyle:a="medium",timeStyle:n="short",fallback:r="N/A",customFormat:s,locale:o=navigator.language||"en-US"}=t;if(!e)return r;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug("Corrected UTC date format: ".concat(e," -> ").concat(a))),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn("Invalid date provided to formatUtcToLocal: ".concat(e)),r;if(s)return function(e,t){let a=e.getFullYear(),n=e.getMonth()+1,r=e.getDate(),s=e.getHours(),o=e.getMinutes(),i={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][n-1],dd:r.toString().padStart(2,"0"),h:(s%12||12).toString(),mm:o.toString().padStart(2,"0"),a:s>=12?"PM":"AM"},l=t;return Object.entries(i).forEach(e=>{let[t,a]=e;l=l.replace(RegExp(t,"g"),a)}),l}(t,s);return new Intl.DateTimeFormat(o,{dateStyle:a,timeStyle:n}).format(t)}catch(t){return console.error("Error formatting date ".concat(e,":"),t),r}}a.d(t,{Ej:()=>n})},17580:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},19040:(e,t,a)=>{"use strict";a.d(t,{Bx:()=>s,Gg:()=>i,H1:()=>o});var n=a(7283);function r(){{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return console.log("Current tenant:",e[1]),e[1]}return console.log("Using default tenant"),"default"}let s=async e=>{let t=r(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&t.append(a,String(n))}),t.toString()}(e),s="".concat(t,"/odata/OrganizationUnitUsers");return a&&(s+="?".concat(a)),function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:{value:[]}}(await n.F.get(s))},o={getUsers:async e=>(await n.F.get("".concat(e,"/api/ou/users"))).users,getRolesInOrganizationUnit:async e=>n.F.get("".concat(e,"/api/ou/users/roles")),assignRolesBulk:async(e,t)=>{let a=r(),s=t.map(e=>e.trim()),o="".concat(a,"/api/author/user/").concat(e,"/assign-multiple-roles");try{return await n.F.post(o,{authorityIds:s})}catch(e){throw console.error("Error assigning roles:",e),e}}},i=async e=>{let t=r();await n.F.delete("".concat(t,"/api/ou/users/").concat(e))}},25657:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var n=a(95155);a(12115);var r=a(66634),s=a(74466),o=a(36928);let i=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:s=!1,...l}=e,c=s?r.DX:"span";return(0,n.jsx)(c,{"data-slot":"badge",className:(0,o.cn)(i({variant:a}),t),...l})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>c,r:()=>l});var n=a(95155),r=a(12115),s=a(66634),o=a(74466),i=a(36928);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:a,variant:r,size:o,asChild:c=!1,...d}=e,u=c?s.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:o,className:a})),ref:t,...d})});c.displayName="Button"},32771:(e,t,a)=>{"use strict";a.d(t,{AW:()=>d,Cb:()=>u,QQ:()=>l,ae:()=>s,jm:()=>c,oy:()=>i,s9:()=>o});var n=a(7283);let r=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},s=async e=>{let t=r();return await n.F.get("".concat(t,"/api/packages/").concat(e))},o=async()=>{let e=r();return await n.F.get("".concat(e,"/api/packages"))},i=async e=>{let t=r(),a=new FormData;return a.append("file",e.file),e.name&&a.append("name",e.name),e.description&&a.append("description",e.description),e.version&&a.append("version",e.version),await n.F.post("".concat(t,"/api/packages/upload"),a)},l=async(e,t)=>{let a=r();return await n.F.get("".concat(a,"/api/packages/").concat(e,"/versions/").concat(t,"/download"))},c=async e=>{let t=r();await n.F.delete("".concat(t,"/api/packages/").concat(e))},d=async(e,t)=>{let a=r();await n.F.delete("".concat(a,"/api/packages/").concat(e,"/versions/").concat(t))},u=async e=>{let t=r(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&"$count"!==a&&t.append(a,String(n))}),t.toString()}(e),s="".concat(t,"/odata/AutomationPackages");a&&(s+="?".concat(a)),console.log("OData query endpoint:",s);try{let e=await n.F.get(s);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," packages from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log('Found array property "'.concat(a,'" in response'));let n=e[a],r=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof r?r:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37108:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},42148:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("laptop",[["path",{d:"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16",key:"tarvll"}]])},45995:(e,t,a)=>{"use strict";a.d(t,{i:()=>r});var n=a(7283);let r={getAllUsers:async()=>(0,n.fetchApi)("api/admin/user/get-all",{method:"GET"}),getUserById:async e=>(0,n.fetchApi)("api/admin/user/detail/".concat(e),{method:"GET"}),updateUserInfo:async(e,t)=>(0,n.fetchApi)("api/admin/user/update-detail/".concat(e),{method:"PUT",body:JSON.stringify(t)}),changeUserPassword:async(e,t)=>(0,n.fetchApi)("api/admin/user/change-password/".concat(e),{method:"POST",body:JSON.stringify(t)}),getAllOrganizationUnits:async()=>await n.F.get("/api/admin/organization-unit/get-all"),deleteOrganizationUnit:async e=>{await n.F.delete("/api/admin/organization-unit/".concat(e))}}},53904:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>p,Es:()=>m,HM:()=>u,L3:()=>y,c7:()=>h,lG:()=>l,rr:()=>f,zM:()=>c});var n=a(95155),r=a(12115),s=a(59096),o=a(54416),i=a(36928);let l=s.bL,c=s.l9,d=s.ZL,u=s.bm,g=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",a),...r})});g.displayName=s.hJ.displayName;let p=r.forwardRef((e,t)=>{let{className:a,children:r,...l}=e;return(0,n.jsxs)(d,{children:[(0,n.jsx)(g,{}),(0,n.jsxs)(s.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",a),...l,children:[r,(0,n.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(o.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});p.displayName=s.UC.displayName;let h=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};h.displayName="DialogHeader";let m=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};m.displayName="DialogFooter";let y=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});y.displayName=s.hE.displayName;let f=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...r})});f.displayName=s.VY.displayName},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>s,aR:()=>o,wL:()=>d});var n=a(95155);a(12115);var r=a(36928);function s(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},70449:(e,t,a)=>{"use strict";a.d(t,{DC:()=>i,EJ:()=>o,IS:()=>l,bb:()=>s});var n=a(7283),r=a(15874);function s(){return{fetcher:e=>(0,n.fetchApi)(e),onError:function(e){(0,r.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let o={fetcher:e=>(0,n.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},i={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},l=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},75525:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},81053:(e,t,a)=>{"use strict";a.d(t,{$o:()=>s,Lm:()=>l,NH:()=>i,deleteAsset:()=>c,gT:()=>o,j0:()=>u,mK:()=>g,qi:()=>d});var n=a(7283);let r=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},s=async e=>{let t=r();return n.F.post("".concat(t,"/api/assets"),e)},o=async(e,t,a)=>{let s=r(),o=await n.F.put("".concat(s,"/api/assets/").concat(e),t);return await n.F.put("".concat(s,"/api/assets/").concat(e,"/bot-agents"),{botAgentIds:a}),o},i=async()=>{let e=r();return await n.F.get("".concat(e,"/api/assets"))},l=async e=>{let t=r(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&t.append(a,String(n))}),t.toString()}(e),s="".concat(t,"/odata/Assets");a&&(s+="?".concat(a)),console.log("OData query endpoint:",s);try{let e=await n.F.get(s);return console.log("Raw OData response:",e),function(e){var t,a;if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," items from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":null!==(t=e["@odata.count"])&&void 0!==t?t:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let n=t[0];console.log('Found array property "'.concat(n,'" in response'));let r=e[n],s=e["@odata.count"];return{value:r,"@odata.count":null!==(a="number"==typeof s?s:void 0)&&void 0!==a?a:r.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching assets with OData:",e),{value:[]}}},c=async e=>{let t=r();await n.F.delete("".concat(t,"/api/assets/").concat(e))},d=async e=>{let t=r();return n.F.get("".concat(t,"/api/assets/").concat(e))},u=async e=>{let t=r();return n.F.get("".concat(t,"/api/assets/").concat(e,"/bot-agents"))},g=async()=>{let e=r();return n.F.get("".concat(e,"/api/agents"))}},86490:(e,t,a)=>{"use strict";a.d(t,{NA:()=>i,Qk:()=>d,Ri:()=>o,dT:()=>l,kz:()=>c,xR:()=>s});var n=a(7283);let r=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},s=async e=>{let t=r();return await n.F.post("".concat(t,"/api/agents/create"),e)},o=async e=>{let t=r();return await n.F.get("".concat(t,"/api/agents/").concat(e))},i=async()=>{let e=r();return await n.F.get("".concat(e,"/api/agents"))},l=async e=>{let t=r(),a=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,n]=e;null!=n&&"$count"!==a&&t.append(a,String(n))}),t.toString()}(e),s="".concat(t,"/odata/BotAgents");a&&(s+="?".concat(a)),console.log("OData query endpoint:",s);try{let e=await n.F.get(s);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," items from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let a=t[0];console.log('Found array property "'.concat(a,'" in response'));let n=e[a],r=e["@odata.count"];return{value:n,"@odata.count":"number"==typeof r?r:n.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching agents with OData:",e),{value:[]}}},c=async e=>{let t=r();await n.F.delete("".concat(t,"/api/agents/").concat(e))},d=async(e,t)=>{let a=r();return await n.F.put("".concat(a,"/api/agents/").concat(e),t)}},88262:(e,t,a)=>{"use strict";a.d(t,{$:()=>r,d:()=>s});var n=a(12115);let r=(0,n.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function s(){let e=(0,n.useContext)(r);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}},96365:(e,t,a)=>{"use strict";a.d(t,{K:()=>r});var n=a(7283);let r={getMyOrganizationUnits:async()=>await n.F.get("/api/ou/my-ous"),getBySlug:async e=>await n.F.get("/api/ou/slug/".concat(e)),getById:async e=>await n.F.get("/api/ou/".concat(e)),create:async e=>await n.F.post("/api/ou/create",e),update:async(e,t)=>await n.F.put("/api/ou/".concat(e),t),requestDeletion:async e=>await n.F.post("/api/ou/".concat(e,"/request-deletion"),{}),cancelDeletion:async e=>await n.F.post("/api/ou/".concat(e,"/cancel-deletion"),{}),getDeletionStatus:async e=>await n.F.get("/api/ou/".concat(e,"/deletion-status"))}},98198:(e,t,a)=>{Promise.resolve().then(a.bind(a,11145))}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,2178,4727,8441,1684,7358],()=>t(98198)),_N_E=e.O()}]);