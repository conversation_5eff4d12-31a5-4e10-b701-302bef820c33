(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5016],{5623:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},11832:(e,t,a)=>{"use strict";a.d(t,{i:()=>c});var l=a(95155),s=a(18289),n=a(47330),r=a(30285),i=a(44838);function c(e){let{table:t}=e;return(0,l.jsxs)(i.rI,{children:[(0,l.jsx)(s.ty,{asChild:!0,children:(0,l.jsxs)(r.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,l.jsx)(n.A,{}),"View"]})}),(0,l.jsxs)(i.SQ,{align:"end",className:"w-[150px]",children:[(0,l.jsx)(i.lp,{children:"Toggle columns"}),(0,l.jsx)(i.mB,{}),t.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,l.jsx)(i.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},15426:(e,t,a)=>{"use strict";function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{dateStyle:a="medium",timeStyle:l="short",fallback:s="N/A",customFormat:n,locale:r=navigator.language||"en-US"}=t;if(!e)return s;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug("Corrected UTC date format: ".concat(e," -> ").concat(a))),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn("Invalid date provided to formatUtcToLocal: ".concat(e)),s;if(n)return function(e,t){let a=e.getFullYear(),l=e.getMonth()+1,s=e.getDate(),n=e.getHours(),r=e.getMinutes(),i={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][l-1],dd:s.toString().padStart(2,"0"),h:(n%12||12).toString(),mm:r.toString().padStart(2,"0"),a:n>=12?"PM":"AM"},c=t;return Object.entries(i).forEach(e=>{let[t,a]=e;c=c.replace(RegExp(t,"g"),a)}),c}(t,n);return new Intl.DateTimeFormat(r,{dateStyle:a,timeStyle:l}).format(t)}catch(t){return console.error("Error formatting date ".concat(e,":"),t),s}}a.d(t,{Ej:()=>l})},17561:(e,t,a)=>{"use strict";a.d(t,{default:()=>_});var l=a(95155),s=a(51154),n=a(49103),r=a(30285),i=a(26126),c=a(47262),o=a(87570),d=a(12115),u=a(5623),g=a(91788),m=a(89917),x=a(74126),p=a(54165),h=a(44838),f=a(88262);function v(e){let{row:t,onRefresh:a}=e,[s,n]=(0,d.useState)(!1),[i,c]=(0,d.useState)(!1),[o,v]=(0,d.useState)(""),[j,y]=(0,d.useState)(!1),{toast:k}=(0,f.d)(),b=async()=>{y(!0);try{k({title:"Package deleted",description:"Package ".concat(t.original.name," has been deleted.")}),n(!1),a&&a()}catch(e){n(!1),e instanceof Error?v(e.message):v("Failed to delete package."),c(!0)}finally{y(!1)}};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(h.rI,{children:[(0,l.jsx)(h.ty,{asChild:!0,children:(0,l.jsxs)(r.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,l.jsx)(u.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,l.jsxs)(h.SQ,{align:"end",className:"w-[160px]",onClick:e=>e.stopPropagation(),children:[(0,l.jsxs)(h._2,{onClick:e=>{e&&e.stopPropagation();let a=t.original.versions;if(a&&a.length>0){let e=a.sort((e,t)=>new Date(t.uploadedAt).getTime()-new Date(e.uploadedAt).getTime())[0];k({title:"Downloading package",description:"Downloading package: ".concat(t.original.name," version: ").concat(e.versionNumber)})}else v("No versions available to download."),c(!0)},children:[(0,l.jsx)(g.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,l.jsx)("span",{children:"Download"})]}),(0,l.jsxs)(h._2,{onClick:e=>{e&&e.stopPropagation(),k({title:"Edit package",description:"Editing package: ".concat(t.original.name)})},children:[(0,l.jsx)(m.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,l.jsx)("span",{children:"Edit"})]}),(0,l.jsx)(h.mB,{}),(0,l.jsxs)(h._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e&&e.stopPropagation(),n(!0)},children:[(0,l.jsx)(x.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,l.jsx)("span",{children:"Delete"})]})]})]}),(0,l.jsx)(p.lG,{open:s,onOpenChange:n,children:(0,l.jsxs)(p.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,l.jsx)(p.c7,{children:(0,l.jsx)(p.L3,{children:"Confirm Delete"})}),(0,l.jsxs)("div",{children:["Are you sure you want to delete package ",(0,l.jsx)("b",{children:t.original.name}),"?"]}),(0,l.jsxs)(p.Es,{children:[(0,l.jsx)(r.$,{variant:"outline",onClick:()=>n(!1),disabled:j,children:"Cancel"}),(0,l.jsx)(r.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:b,disabled:j,children:"Delete"})]})]})}),(0,l.jsx)(p.lG,{open:i,onOpenChange:c,children:(0,l.jsxs)(p.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,l.jsx)(p.c7,{children:(0,l.jsx)(p.L3,{children:"Error"})}),(0,l.jsx)("div",{children:o}),(0,l.jsx)(p.Es,{children:(0,l.jsx)(r.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:()=>c(!1),children:"OK"})})]})})]})}var j=a(15426);let y=e=>[{id:"select",header:e=>{let{table:t}=e;return(0,l.jsx)(c.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e;return(0,l.jsx)(c.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]"})},enableSorting:!1,enableHiding:!1},{id:"actions",cell:t=>{let{row:a}=t;return(0,l.jsx)(v,{row:a,onRefresh:e})},header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Actions"})}},{accessorKey:"name",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Name"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex space-x-2",children:(0,l.jsx)("span",{className:"max-w-[500px] truncate font-medium",children:t.getValue("name")})})}},{accessorKey:"description",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Description"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{className:"max-w-md truncate",title:t.getValue("description"),children:t.getValue("description")})})}},{accessorKey:"versions",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Latest Version"})},cell:e=>{let{row:t}=e,a=t.getValue("versions"),s=a&&a.length>0?[...a].sort((e,t)=>new Date(t.uploadedAt).getTime()-new Date(e.uploadedAt).getTime())[0]:null;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)(i.E,{variant:"secondary",children:s?s.versionNumber:"No versions"})})},enableSorting:!1},{accessorKey:"versions",id:"versionCount",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Version Count"})},cell:e=>{let{row:t}=e,a=t.getValue("versions"),s=a?a.length:0;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:s})})},enableSorting:!1},{accessorKey:"isActive",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Status"})},cell:e=>{let{row:t}=e,a=t.getValue("isActive");return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(a?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"),children:a?"Active":"Inactive"})})}},{accessorKey:"createdAt",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Created Date"})},cell:e=>{let{row:t}=e,a=t.getValue("createdAt"),s=(0,j.Ej)(a,{dateStyle:"medium",timeStyle:void 0,fallback:"Invalid date"});return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:s})})}}];y();var k=a(54333),b=a(55365),w=a(29869),S=a(32771),N=a(12187);function C(e){let{isOpen:t,onClose:a,mode:n,onSuccess:i}=e,{toast:c}=(0,f.d)(),[o,u]=(0,d.useState)(null),[g,m]=(0,d.useState)(""),[x,h]=(0,d.useState)(!1),[v,j]=(0,d.useState)(null),y=async()=>{try{if(h(!0),j(null),!o){let e="Please select a package file";j(e),c({title:"Validation Error",description:e,variant:"destructive"});return}let e=await (0,S.oy)({file:o,version:g.trim()||void 0});c({title:"Success",description:'Package "'.concat(e.name,'" uploaded successfully'),variant:"default"}),k(),null==i||i()}catch(e){console.error("Error creating package:",e),j((0,N.PE)(e)),c((0,N.m4)(e))}finally{h(!1)}},k=()=>{u(null),m(""),j(null),a()};return(0,l.jsx)(p.lG,{open:t,onOpenChange:k,children:(0,l.jsxs)(p.Cf,{className:"sm:max-w-[600px] p-6",children:[(0,l.jsx)(p.c7,{children:(0,l.jsx)(p.L3,{children:"edit"===n?"Edit Package":"Create New Automation Package"})}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsxs)("label",{htmlFor:"file-upload",className:"flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-md cursor-pointer hover:bg-gray-50 transition-colors",children:[(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,l.jsx)(w.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:o?o.name:"Click to select a bot package file (.zip)"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"ZIP files containing bot.py are supported"})]}),(0,l.jsx)("input",{id:"file-upload",type:"file",accept:".zip",className:"hidden",onChange:e=>{e.target.files&&e.target.files[0]&&(u(e.target.files[0]),j(null))}})]})}),v&&(0,l.jsx)(b.Fc,{variant:"destructive",children:(0,l.jsx)(b.TN,{children:v})})]}),(0,l.jsxs)(p.Es,{children:[(0,l.jsx)(r.$,{variant:"outline",onClick:k,disabled:x,children:"Cancel"}),(0,l.jsx)(r.$,{onClick:y,disabled:!o||x,children:x?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(s.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create Package"})]})]})})}var A=a(55594),z=a(35695),M=a(47924),I=a(54416),E=a(66932),P=a(62523),F=a(11832),R=a(59409);function V(e){var t,a;let{table:n,statuses:c,onSearch:o,onStatusChange:u,searchValue:g="",isFiltering:m=!1,isPending:x=!1,totalCount:p=0}=e,h=n.getState().columnFilters.length>0,f=n.getState().columnFilters.length,v=(0,d.useRef)(null),j=(0,d.useRef)(null);(0,d.useEffect)(()=>{document.activeElement!==v.current&&null!==j.current&&v.current&&(v.current.focus(),null!==j.current&&v.current.setSelectionRange(j.current,j.current))},[x,m]);let y=e=>{if(v.current&&(j.current=v.current.selectionStart),o)o(e);else{var t;null===(t=n.getColumn("name"))||void 0===t||t.setFilterValue(e)}};return(0,l.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,l.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,l.jsx)(M.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,l.jsx)(P.p,{ref:v,placeholder:"Search by name or description...",value:g,onChange:e=>y(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:m,onFocus:()=>{v.current&&(j.current=v.current.selectionStart)}}),m&&(0,l.jsx)(s.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!m&&""!==g&&(0,l.jsx)(I.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>y("")})]}),n.getColumn("isActive")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(R.l6,{onValueChange:e=>{if(u)u(e);else{var t;null===(t=n.getColumn("isActive"))||void 0===t||t.setFilterValue("all"===e?"":e)}},value:(null===(t=n.getColumn("isActive"))||void 0===t?void 0:t.getFilterValue())||"all",disabled:m||x,children:[(0,l.jsx)(R.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(E.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(R.yv,{placeholder:""}),(null===(a=n.getColumn("isActive"))||void 0===a?void 0:a.getFilterValue())&&(0,l.jsx)(i.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsx)(R.gC,{children:c.map(e=>(0,l.jsx)(R.eb,{value:e.value,children:e.label},e.value))})]})}),p>0&&(0,l.jsx)("div",{className:"text-sm font-medium ml-2",children:(0,l.jsxs)("span",{children:["Total: ",p," package",1!==p?"s":""]})}),f>0&&(0,l.jsxs)(i.E,{variant:"secondary",className:"rounded-sm px-1",children:[f," active ",1===f?"filter":"filters"]}),h&&(0,l.jsxs)(r.$,{variant:"ghost",onClick:()=>{n.resetColumnFilters(),o&&o("")},className:"h-8 px-2 lg:px-3",disabled:m,children:["Reset",(0,l.jsx)(I.A,{className:"ml-2 h-4 w-4"})]})]}),(0,l.jsx)(F.i,{table:n})]})}var D=a(36268),$=a(11032),L=a(62668),T=a(29797),O=a(34953),H=a(70449);function _(){var e;let t=(0,z.useRouter)(),a=(0,z.usePathname)(),i=(0,z.useSearchParams)(),{updateUrl:c}=(0,L.z)(),{toast:o}=(0,f.d)(),[u,g]=(0,d.useState)(!1),[m,x]=(0,d.useState)("create"),[p,h]=(0,d.useState)({}),[v,j]=(0,d.useState)({}),[b,w]=(0,d.useState)(0),N=(0,d.useRef)(0),[A,M]=(0,d.useState)(!1),[I,E]=(0,d.useState)(!1),[P,F]=(0,d.useState)(!1),R=(0,d.useRef)(null),_=(0,d.useRef)(!0),[U,K]=(0,d.useState)(()=>{let e=[],t=i.get("name");t&&e.push({id:"name",value:t});let a=i.get("isActive");return a&&e.push({id:"isActive",value:a}),e}),[q,J]=(0,d.useState)(()=>{let e=i.get("sort"),t=i.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[{id:"createdAt",desc:!0}]}),[G,Z]=(0,d.useState)(()=>{let e=i.get("page"),t=i.get("size");return{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:t?parseInt(t):10}}),[B,Q]=(0,d.useState)(null!==(e=i.get("name"))&&void 0!==e?e:""),W=(0,d.useCallback)(()=>{let e={$top:G.pageSize,$skip:G.pageIndex*G.pageSize,$count:!0,$expand:"Versions"};if(q.length>0&&(e.$orderby=q.map(e=>"".concat(e.id," ").concat(e.desc?"desc":"asc")).join(",")),U.length>0){let t=U.filter(e=>"string"!=typeof e.value||""!==e.value).map(e=>{let t=e.id,a=e.value;return"string"==typeof a?"name"===t&&a?"(contains(tolower(name), '".concat(a.toLowerCase(),"') or contains(tolower(description), '").concat(a.toLowerCase(),"'))"):"isActive"===t?"isActive eq ".concat("true"===a):"contains(tolower(".concat(t,"), '").concat(a.toLowerCase(),"')"):Array.isArray(a)?a.map(e=>"".concat(t," eq '").concat(e,"'")).join(" or "):""}).filter(Boolean);t.length>0&&(e.$filter=t.join(" and "))}return e},[G,q,U])(),{data:Y,error:X,isLoading:ee,mutate:et}=(0,O.Ay)(H.DC.packagesWithOData(W),()=>(0,S.Cb)(W)),ea=(0,d.useMemo)(()=>(null==Y?void 0:Y.value)?Y.value:[],[Y]);(0,d.useEffect)(()=>{X&&(console.error("Failed to load packages:",X),o({title:"Error",description:"Failed to load packages. Please try again.",variant:"destructive"}))},[X,o]),(0,d.useEffect)(()=>{if(!Y)return;if("number"==typeof Y["@odata.count"]){w(Y["@odata.count"]),N.current=Y["@odata.count"],F(!0);return}if(!Array.isArray(Y.value))return;let e=G.pageIndex*G.pageSize+Y.value.length;e>N.current&&(w(e),N.current=e),Y.value.length===G.pageSize&&0===G.pageIndex&&(w(e+1),N.current=e+1),F(!1)},[Y,G.pageIndex,G.pageSize]),(0,d.useEffect)(()=>{if((null==Y?void 0:Y.value)&&0===Y.value.length&&N.current>0&&G.pageIndex>0){let e=Math.max(1,Math.ceil(N.current/G.pageSize));G.pageIndex>=e&&(Z(e=>({...e,pageIndex:0})),c(a,{page:"1"}))}},[Y,G.pageIndex,G.pageSize,N,c,a]);let el=(0,d.useCallback)(async()=>{M(!1),E(!1),await et()},[et]);(0,d.useEffect)(()=>{if(_.current){_.current=!1;let e=i.get("page"),t=i.get("size");e&&t||c(a,{page:null!=e?e:"1",size:null!=t?t:"10"})}},[i,c,a]);let es=e=>e+1,en=(e,t)=>Math.max(1,Math.ceil(e/t)),er=(0,d.useMemo)(()=>{let e=en(b,G.pageSize),t=ea.length===G.pageSize&&b<=G.pageSize*(G.pageIndex+1),a=es(G.pageIndex);return t?Math.max(a,e,G.pageIndex+2):Math.max(a,e)},[G.pageSize,G.pageIndex,ea.length,b]),ei=(0,d.useMemo)(()=>!P&&ea.length===G.pageSize,[P,ea.length,G.pageSize]),ec=(0,D.N4)({data:ea,columns:y(el),state:{sorting:q,columnVisibility:v,rowSelection:p,columnFilters:U,pagination:G},enableRowSelection:!0,onRowSelectionChange:h,onSortingChange:e=>{let t="function"==typeof e?e(q):e;J(t),t.length>0?c(a,{sort:t[0].id,order:t[0].desc?"desc":"asc",page:"1"}):c(a,{sort:null,order:null,page:"1"})},onColumnFiltersChange:K,onColumnVisibilityChange:j,onPaginationChange:e=>{let t="function"==typeof e?e(G):e;Z(t),c(a,{page:(t.pageIndex+1).toString(),size:t.pageSize.toString()})},getCoreRowModel:(0,$.HT)(),getFilteredRowModel:(0,$.hM)(),getPaginationRowModel:(0,$.kW)(),getSortedRowModel:(0,$.h5)(),getFacetedRowModel:(0,$.kQ)(),getFacetedUniqueValues:(0,$.oS)(),manualPagination:!0,pageCount:er,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.id}),eo=(0,d.useCallback)(e=>{Q(e),M(!0),R.current&&clearTimeout(R.current),R.current=setTimeout(()=>{let t=ec.getColumn("name");t&&(t.setFilterValue(e),c(a,{name:e||null,page:"1"})),M(!1)},300)},[ec,c,a]),ed=(0,d.useCallback)(e=>{let t=ec.getColumn("isActive");if(t){let l="all"===e?"":e;t.setFilterValue(l),c(a,{isActive:l||null,page:"1"})}},[ec,c,a]);return(0,d.useEffect)(()=>()=>{R.current&&clearTimeout(R.current)},[]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Automation Packages"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)(r.$,{variant:"outline",onClick:el,disabled:ee||A,children:[ee?(0,l.jsx)(s.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Refresh"]}),(0,l.jsxs)(r.$,{onClick:()=>{x("create"),g(!0)},className:"flex items-center justify-center",children:[(0,l.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Create"]})]})]}),X&&(0,l.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,l.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load packages. Please try again."}),(0,l.jsx)(r.$,{variant:"outline",className:"mt-2",onClick:()=>et(),children:"Retry"})]}),(0,l.jsx)(V,{table:ec,statuses:[{value:"all",label:"Show All"},{value:"true",label:"Active"},{value:"false",label:"Inactive"}],onSearch:eo,onStatusChange:ed,searchValue:B,isFiltering:ee,isPending:A,totalCount:b}),(0,l.jsx)(k.b,{data:ea,columns:y(el),onRowClick:e=>{let l=a.match(/^\/([^\/]+)/),s=l?l[1]:"default",n="/".concat(s,"/automation/package/").concat(e.id);t.push(n)},table:ec,isLoading:ee,totalCount:b}),(0,l.jsx)(T.d,{currentPage:G.pageIndex+1,pageSize:G.pageSize,totalCount:b,totalPages:er,isLoading:ee,isChangingPageSize:I,isUnknownTotalCount:ei,rowsLabel:"packages",onPageChange:e=>{e!==G.pageIndex+1&&(Z({...G,pageIndex:e-1}),c(a,{page:e.toString()}))},onPageSizeChange:e=>{if(e!==G.pageSize){E(!0);let t=Math.floor(G.pageIndex*G.pageSize/e);Z({pageSize:e,pageIndex:t}),c(a,{size:e.toString(),page:(t+1).toString()})}}})]}),(0,l.jsx)(C,{isOpen:u,onClose:()=>{g(!1)},mode:m,onSuccess:el})]})}A.z.object({id:A.z.string(),name:A.z.string(),description:A.z.string(),isActive:A.z.boolean(),createdAt:A.z.string()})},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},47330:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},49103:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>m,Es:()=>p,HM:()=>u,L3:()=>h,c7:()=>x,lG:()=>c,rr:()=>f,zM:()=>o});var l=a(95155),s=a(12115),n=a(59096),r=a(54416),i=a(36928);let c=n.bL,o=n.l9,d=n.ZL,u=n.bm,g=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(n.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",a),...s})});g.displayName=n.hJ.displayName;let m=s.forwardRef((e,t)=>{let{className:a,children:s,...c}=e;return(0,l.jsxs)(d,{children:[(0,l.jsx)(g,{}),(0,l.jsxs)(n.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",a),...c,children:[s,(0,l.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,l.jsx)(r.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.UC.displayName;let x=e=>{let{className:t,...a}=e;return(0,l.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};x.displayName="DialogHeader";let p=e=>{let{className:t,...a}=e;return(0,l.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};p.displayName="DialogFooter";let h=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(n.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...s})});h.displayName=n.hE.displayName;let f=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(n.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...s})});f.displayName=n.VY.displayName},62668:(e,t,a)=>{"use strict";a.d(t,{z:()=>n});var l=a(35695),s=a(12115);function n(){let e=(0,l.useRouter)(),t=(0,l.useSearchParams)(),a=(0,s.useCallback)(e=>{let a=new URLSearchParams(t.toString());return Object.entries(e).forEach(e=>{let[t,l]=e;null===l?a.delete(t):a.set(t,l)}),a.toString()},[t]),n=(0,s.useCallback)((t,l)=>{let s=a(l);e.push("".concat(t,"?").concat(s),{scroll:!1})},[a,e]);return{createQueryString:a,updateUrl:n}}},66932:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},74126:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},89917:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},91788:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},95907:(e,t,a)=>{Promise.resolve().then(a.bind(a,17561))}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,2178,8852,5699,5594,8523,9483,3085,4727,5224,2338,8441,1684,7358],()=>t(95907)),_N_E=e.O()}]);