(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72],{730:(e,r,s)=>{"use strict";s.d(r,{LoginClient:()=>P});var a=s(95155),t=s(12115),n=s(6874),i=s.n(n),l=s(35695),d=s(62177),o=s(90221),c=s(55594),u=s(54629),m=s(30285),x=s(17759),h=s(62523),g=s(67057),f=s(47262),v=s(85339),p=s(67938),b=s(22370),j=s(12187),y=s(43453),w=s(55365);function k(e){let{email:r,onResendSuccess:s}=e,[n,i]=(0,t.useState)(!1),[l,d]=(0,t.useState)("idle"),[o,c]=(0,t.useState)(null),x=async()=>{if(r&&!n){i(!0);try{await b.Z.resendVerificationEmailByEmail(r),d("success"),c(null),s&&s()}catch(r){var e;d("error"),console.error("Failed to resend verification email:",r),c(null!==(e=(0,j.PE)(r))&&void 0!==e?e:"Failed to resend. Please try again.")}finally{i(!1)}}};return(0,a.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,a.jsxs)(m.$,{variant:"outline",className:"w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 py-2 px-4 text-base font-semibold text-gray-700 dark:text-gray-200 shadow-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-150",onClick:x,disabled:n,children:[n&&(0,a.jsx)(u.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),n?"Sending...":"Resend verification email"]}),"success"===l&&(0,a.jsxs)(w.Fc,{variant:"success",className:"animate-fade-in border-green-500 bg-green-50 text-green-800 dark:bg-green-950 dark:border-green-700 dark:text-green-200",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-green-500 dark:text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(w.XL,{className:"font-semibold",children:"Verification email sent!"}),(0,a.jsx)(w.TN,{children:"Please check your inbox."})]})]}),"error"===l&&(0,a.jsxs)(w.Fc,{variant:"destructive",className:"animate-fade-in",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(w.XL,{className:"font-semibold",children:"Failed to resend"}),(0,a.jsx)(w.TN,{children:null!=o?o:"Failed to resend. Please try again."})]})]})]})}let N=c.Ik({email:c.Yj().email("Please enter a valid email"),password:c.Yj().min(6,"Password must be at least 6 characters"),rememberMe:c.zM().optional()});function S(){var e;let r=(0,l.useRouter)(),s=(0,l.useSearchParams)(),{login:n,isSystemAdmin:c}=(0,g.A)(),[b,j]=t.useState(!1),[y,w]=t.useState(null),[S,C]=t.useState(null),[P,E]=t.useState(!1),F=null!==(e=s.get("returnUrl"))&&void 0!==e?e:p.$.paths.auth.organizationSelector,A="true"===s.get("expired"),I="true"===s.get("needVerification"),L=s.get("email"),R=null==F?void 0:F.includes("/invitation/accept"),M=(0,d.mN)({resolver:(0,o.u)(N),defaultValues:{email:L||"",password:"",rememberMe:!1}}),{getValues:z,setValue:_}=M;async function V(e){j(!0),w(null);try{if(await n({email:e.email,password:e.password}),R){r.push(F);return}c?r.push("/dashboard"):F&&!R?r.push(F):r.push("/tenant-selector")}catch(n){let r="Login failed. Please try again.";if("object"==typeof n&&null!==n){var s,a,t,i,l,d;if((r=null!==(d=null!==(l=null===(a=n.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)&&void 0!==l?l:n.message)&&void 0!==d?d:r).toLowerCase().includes("verify")||r.toLowerCase().includes("verification")||r.toLowerCase().includes("email not verified")||(null===(i=n.response)||void 0===i?void 0:null===(t=i.data)||void 0===t?void 0:t.code)==="EMAIL_NOT_VERIFIED"){C(e.email),E(!0),w("Please verify your email address before logging in. Check your inbox for a verification link or request a new one.");return}}E(!1),C(null),w(r)}finally{j(!1)}}return t.useEffect(()=>{A&&w("Your session has expired. Please sign in again."),I&&L&&(C(L),E(!0),w("Please verify your email address before logging in. Check your inbox for a verification link or request a new one."),z("email")!==L&&_("email",L))},[A,I,L,z,_]),(0,a.jsxs)("div",{className:"grid gap-6",children:[y&&(0,a.jsxs)("div",{className:"flex items-center gap-3 p-4 mb-2 rounded-lg border border-red-500 dark:border-red-400 bg-red-100 dark:bg-red-950 shadow-sm fade-in",children:[(0,a.jsx)("span",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-red-200 dark:bg-red-900 text-red-700 dark:text-red-300",children:(0,a.jsx)(v.A,{className:"w-6 h-6"})}),(0,a.jsx)("span",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:y})]}),P&&S&&(0,a.jsx)(k,{email:S}),(0,a.jsx)(x.lV,{...M,children:(0,a.jsxs)("form",{onSubmit:M.handleSubmit(V),className:"space-y-4",children:[(0,a.jsx)(x.zB,{control:M.control,name:"email",render:e=>{let{field:r}=e;return(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(x.lR,{children:"Email"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(h.p,{type:"email",autoComplete:"email",...r,disabled:b})}),(0,a.jsx)(x.C5,{})]})}}),(0,a.jsx)(x.zB,{control:M.control,name:"password",render:e=>{let{field:r}=e;return(0,a.jsxs)(x.eI,{children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)(x.lR,{children:"Password"})}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(h.p,{type:"password",autoComplete:"current-password",...r,disabled:b})}),(0,a.jsx)(x.C5,{})]})}}),(0,a.jsx)(x.zB,{control:M.control,name:"rememberMe",render:e=>{let{field:r}=e;return(0,a.jsxs)(x.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,a.jsx)(x.MJ,{children:(0,a.jsx)(f.S,{checked:r.value,onCheckedChange:r.onChange,disabled:b})}),(0,a.jsxs)("div",{className:"flex justify-between w-full items-center",children:[(0,a.jsx)(x.lR,{className:"text-sm",children:"Remember me"}),(0,a.jsx)(i(),{href:"/forgot-password",className:"text-sm font-medium text-orange-600 hover:text-orange-700",children:"Forgot password?"})]})]})}}),(0,a.jsxs)(m.$,{type:"submit",className:"w-full bg-orange-600 hover:bg-orange-700 transition-all duration-300 hover:translate-y-[-2px]",disabled:b,children:[b&&(0,a.jsx)(u.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Sign In"]})]})})]})}function C(){return(0,a.jsx)("div",{className:"grid gap-6",children:(0,a.jsx)("div",{className:"flex justify-center py-8",children:(0,a.jsx)(u.F.Spinner,{className:"h-8 w-8 animate-spin text-muted-foreground"})})})}function P(){let[e,r]=t.useState("");t.useEffect(()=>{var e;r(null!==(e=new URLSearchParams(window.location.search).get("returnUrl"))&&void 0!==e?e:"")},[]);let s=e?"/register?returnUrl=".concat(encodeURIComponent(e)):"/register";return(0,a.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold tracking-tight text-orange-600",children:"Sign In"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enter your email and password to continue"})]}),(0,a.jsx)(t.Suspense,{fallback:(0,a.jsx)(C,{}),children:(0,a.jsx)(S,{})}),(0,a.jsx)("div",{className:"px-8 text-center space-y-2",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,a.jsx)(i(),{href:s,className:"text-orange-600 underline underline-offset-4 hover:text-orange-700 font-medium transition-all duration-300 hover:underline-offset-8",children:"Sign up"})]})})]})}},18805:(e,r,s)=>{Promise.resolve().then(s.bind(s,730)),Promise.resolve().then(s.bind(s,75074))},43453:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(19946).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},45503:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});var a=s(12115);function t(e){let r=a.useRef({value:e,previous:e});return a.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},47262:(e,r,s)=>{"use strict";s.d(r,{S:()=>l});var a=s(95155);s(12115);var t=s(9483),n=s(5196),i=s(36928);function l(e){let{className:r,...s}=e;return(0,a.jsx)(t.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-orange-600 data-[state=checked]:text-white dark:data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600 focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",r),...s,children:(0,a.jsx)(t.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(n.A,{className:"size-3.5"})})})}},85339:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,6341,2178,8852,5699,5594,9483,6874,8594,1941,3112,7057,8916,5642,8441,1684,7358],()=>r(18805)),_N_E=e.O()}]);