# Cost-Optimized Deployment: OpenAutomate Platform on AWS

## 1. Executive Summary & Cost Optimization Goals

This document provides a **cost-optimized** deployment strategy for OpenAutomate on AWS, designed for **demo environments** and **budget-conscious deployments** while maintaining **auto-scaling capabilities** and **best practices**.

### **Key Cost Optimizations:**
- ❌ **Remove NAT Gateway** ($32/month savings)
- ❌ **Remove RDS & ElastiCache** ($25/month savings) 
- ✅ **Use External SQL Server & Redis** (your existing servers)
- ✅ **Public Subnets Only** (simplified networking)
- ✅ **Minimal Fargate Configuration** (0.25 vCPU, 0.5GB RAM)
- ✅ **Maintain Auto-Scaling** (demo-ready scaling)

### **Target Monthly Cost: ~$25-35/month** (vs $94/month in full deployment)

## 2. Physical View - Deployment Architecture

This diagram shows the **physical deployment topology** of OpenAutomate components across infrastructure nodes, focusing on where each software component runs and how they communicate.

```mermaid
deployment TB
    subgraph Internet["🌐 Internet / External Network"]
        subgraph ExternalInfra["External Infrastructure"]
            SQLServer["🗄️ SQL Server<br/><<database server>><br/>Port: 1433<br/>External IP: xxx.xxx.xxx.xxx"]
            RedisServer["⚡ Redis Server<br/><<cache server>><br/>Port: 6379<br/>External IP: xxx.xxx.xxx.xxx"]
        end
        
        subgraph ClientDevices["Client Devices"]
            WebBrowser["🌐 Web Browser<br/><<client>><br/>End Users"]
            BotAgents["🤖 Bot Agent<br/><<client>><br/>Python/C# Applications"]
        end
    end

    subgraph AWSCloud["☁️ AWS Cloud Region: us-east-1"]
        subgraph Route53Zone["Route 53 DNS Zone"]
            DNSService["🌐 DNS Service<br/><<managed service>><br/>api.domain.com<br/>app.domain.com"]
        end
        
        subgraph VPC["VPC: 10.0.0.0/16"]
            subgraph ECSCluster["ECS Fargate Cluster (VPC-based)"]
                ServiceManager["📋 ECS Service<br/><<orchestrator>><br/>Auto Scaling: 1-3 tasks<br/>Target Group Health Checks"]
                
                subgraph AZ1["Availability Zone 1a"]
                    subgraph PublicSubnet1["Public Subnet: ********/24"]
                        ALBNode1["⚖️ ALB Node 1<br/><<load balancer>><br/>Port: 80, 443"]
                        FargateTask1["🔷 Fargate Task 1<br/><<container>><br/>API Application<br/>CPU: 0.25 vCPU<br/>RAM: 0.5 GB<br/>Port: 80"]
                    end
                end
                
                subgraph AZ2["Availability Zone 1b"]
                    subgraph PublicSubnet2["Public Subnet: ********/24"]
                        ALBNode2["⚖️ ALB Node 2<br/><<load balancer>><br/>Port: 80, 443"]
                        FargateTask2["🔷 Fargate Task 2<br/><<container>><br/>API Application<br/>CPU: 0.25 vCPU<br/>RAM: 0.5 GB<br/>Port: 80"]
                    end
                end
            end
        end
        
        subgraph ManagedServices["AWS Managed Services"]
            AmplifyEdge["🚀 AWS Amplify<br/><<CDN + hosting>><br/>Next.js Frontend<br/>Global Edge Locations"]
            S3Storage["🪣 S3 Bucket<br/><<object storage>><br/>openautomate-assets<br/>us-east-1"]
            ECRRegistry["📦 ECR Repository<br/><<container registry>><br/>openautomate-api<br/>us-east-1"]
            SecretsManager["🔐 Secrets Manager<br/><<secret store>><br/>Connection Strings<br/>us-east-1"]
        end
    end

    %% Physical connections and protocols
    WebBrowser -.->|HTTPS/443| DNSService
    BotAgents -.->|HTTPS/443| DNSService
    DNSService -.->|DNS Resolution| AmplifyEdge
    DNSService -.->|DNS Resolution| ALBNode1
    DNSService -.->|DNS Resolution| ALBNode2
    
    ALBNode1 -->|HTTP/80| FargateTask1
    ALBNode2 -->|HTTP/80| FargateTask2
    
    ServiceManager -.->|Container Orchestration| FargateTask1
    ServiceManager -.->|Container Orchestration| FargateTask2
    ServiceManager -.->|Image Pull| ECRRegistry
    
    FargateTask1 -.->|SQL/1433<br/>TLS Connection| SQLServer
    FargateTask2 -.->|SQL/1433<br/>TLS Connection| SQLServer
    FargateTask1 -.->|Redis/6379<br/>AUTH Protocol| RedisServer
    FargateTask2 -.->|Redis/6379<br/>AUTH Protocol| RedisServer
    
    FargateTask1 -.->|HTTPS/443<br/>AWS SDK| S3Storage
    FargateTask1 -.->|HTTPS/443<br/>AWS SDK| SecretsManager
    FargateTask2 -.->|HTTPS/443<br/>AWS SDK| S3Storage
    FargateTask2 -.->|HTTPS/443<br/>AWS SDK| SecretsManager

    %% Physical deployment styling
    classDef physicalNode fill:#E8F4FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef externalNode fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef managedService fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef clientNode fill:#F3E5F5,stroke:#7B1FA2,stroke-width:2px,color:#000
    classDef networkNode fill:#E1F5FE,stroke:#0277BD,stroke-width:2px,color:#000
    
    class FargateTask1,FargateTask2,ALBNode1,ALBNode2 physicalNode
    class SQLServer,RedisServer externalNode
    class AmplifyEdge,S3Storage,ECRRegistry,SecretsManager,ServiceManager managedService
    class WebBrowser,BotAgents clientNode
    class DNSService networkNode
```

### Physical Deployment Mapping

| **Software Component** | **Physical Node** | **Resource Allocation** | **Network Access** |
|------------------------|-------------------|------------------------|-------------------|
| **OpenAutomate API** | AWS Fargate Tasks (1-3 instances) | 0.25 vCPU, 0.5 GB RAM each | Public IP, Internet Gateway |
| **Next.js Frontend** | AWS Amplify (Global CDN) | Managed scaling | CloudFront Edge Locations |
| **SQL Database** | External SQL Server | Customer-managed | Public IP, Port 1433 |
| **Redis Cache** | External Redis Server | Customer-managed | Public IP, Port 6379 |
| **Load Balancer** | AWS ALB (Multi-AZ) | Managed scaling | Internet-facing |
| **Container Images** | AWS ECR | Managed storage | Private registry |
| **Secrets** | AWS Secrets Manager | Managed encryption | VPC endpoints |
| **File Storage** | AWS S3 | Managed storage | Internet Gateway |

### Network Topology

- **Public Subnets Only**: All Fargate tasks have public IPs (cost optimization)
- **No NAT Gateway**: Direct internet access for cost savings (-$32/month)
- **Multi-AZ Deployment**: Load balancer and tasks distributed across 2 AZs
- **External Dependencies**: Database and Redis accessed via public internet with authentication

## 3. Cost Comparison

| Service | Full Deployment | Cost-Optimized | Savings |
|---------|----------------|----------------|---------|
| RDS SQL Server | $13/month | $0 (External) | **-$13** |
| ElastiCache Redis | $12/month | $0 (External) | **-$12** |
| NAT Gateway | $32/month | $0 (Public only) | **-$32** |
| Fargate (0.5 vCPU, 1GB) | $15/month | $8/month (0.25 vCPU, 0.5GB) | **-$7** |
| ALB | $16/month | $16/month | $0 |
| Amplify | $1/month | $1/month | $0 |
| Route 53 (Optional) | $0.50/month | $0.50/month (if used) | $0 |
| ECR + Secrets + S3 | $5/month | $3/month | **-$2** |
| **TOTAL** | **$94.50/month** | **$28.50/month** | **-$66 (70% savings)** |

## 4. Step-by-Step Cost-Optimized Deployment Guide

### Prerequisites & External Server Setup

#### Step 0.1: Find Your AWS Account ID

Before starting, you'll need your AWS Account ID for ECR and other services:

**Method 1: AWS Console**
1. Log in to [AWS Management Console](https://console.aws.amazon.com/)
2. Click your account name (top right corner)
3. Select "Account"
4. Your **Account ID** is displayed (12-digit number like `************`)

**Method 2: AWS CLI** (if installed)
```bash
aws sts get-caller-identity
```
Look for the `"Account"` field in the output.

**Method 3: AWS Billing Page**
- Go to [AWS Billing Dashboard](https://console.aws.amazon.com/billing/home)
- Account ID is shown at the top

**💡 Note**: Save this Account ID - you'll use it in ECR URIs and other configurations.

#### Step 0.2: Prepare Your External Servers

**SQL Server Setup:**
1. **Install SQL Server** on your external server
2. **Enable TCP/IP connections**:
   ```sql
   -- Enable remote connections
   EXEC sp_configure 'remote access', 1;
   RECONFIGURE;
   
   -- Enable SQL Server Authentication
   EXEC xp_instance_regwrite N'HKEY_LOCAL_MACHINE', 
       N'Software\Microsoft\MSSQLServer\MSSQLServer', 
       N'LoginMode', REG_DWORD, 2;
   ```

3. **Create Database and User**:
   ```sql
   CREATE DATABASE OpenAutomate;
   CREATE LOGIN openautomate_user WITH PASSWORD = 'YourSecurePassword123!';
   USE OpenAutomate;
   CREATE USER openautomate_user FOR LOGIN openautomate_user;
   ALTER ROLE db_owner ADD MEMBER openautomate_user;
   ```

4. **Configure Firewall**:
   ```bash
   # Open SQL Server port (1433) to internet
   # Windows Firewall: Allow port 1433 inbound
   # Router: Port forward 1433 to your SQL Server
   ```

**Redis Setup:**
1. **Install Redis** on your external server:
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install redis-server
   
   # Configure for external access
   sudo nano /etc/redis/redis.conf
   # Change: bind 127.0.0.1 to bind 0.0.0.0
   # Set: requirepass YourRedisPassword123!
   
   sudo systemctl restart redis-server
   ```

2. **Configure Firewall**:
   ```bash
   # Open Redis port (6379) to internet
   sudo ufw allow 6379
   # Router: Port forward 6379 to your Redis server
   ```

**Security Considerations:**
- Use **strong passwords** for both SQL Server and Redis
- Consider **VPN connection** for extra security
- Enable **SSL/TLS** if possible
- Use **non-standard ports** (e.g., 14330 instead of 1433)

### Phase 1: Simplified Network Infrastructure

#### Step 1: Create Simplified VPC
1. **Navigate to VPC Console**:
   - Search "VPC" → Click "VPC"
   - Click "Create VPC"

2. **Simplified VPC Configuration**:
   ```
   Resources to create: VPC and more
   Name tag: openautomate-vpc-simple
   IPv4 CIDR block: 10.0.0.0/16
   IPv6 CIDR block: No IPv6 CIDR block
   Tenancy: Default
   
   Number of Availability Zones: 2
   Number of public subnets: 2
   Number of private subnets: 0 (NONE - Cost Saving!)
   
   NAT gateways: None (MAJOR COST SAVING!)
   VPC endpoints: None
   DNS hostnames: Enable
   DNS resolution: Enable
   ```

3. **Click "Create VPC"** - This creates:
   - 1 VPC
   - 2 Public subnets only
   - 1 Internet Gateway
   - Route tables (no NAT Gateway!)

**💰 Cost Savings: -$32/month (No NAT Gateway)**

#### Step 2: Create Minimal Security Groups
1. **ALB Security Group**:
   ```
   Security group name: openautomate-alb-sg
   Description: Security group for ALB
   VPC: openautomate-vpc-simple
   
   Inbound rules:
   - Type: HTTP, Port: 80, Source: 0.0.0.0/0
   - Type: HTTPS, Port: 443, Source: 0.0.0.0/0
   ```

2. **Fargate Security Group**:
   ```
   Security group name: openautomate-fargate-sg
   Description: Security group for Fargate (Public Subnet)
   VPC: openautomate-vpc-simple
   
   Inbound rules:
   - Type: HTTP, Port: 80, Source: openautomate-alb-sg
   
   Outbound rules:
   - Type: All Traffic, Port: All, Destination: 0.0.0.0/0
   ```

### Phase 2: Minimal AWS Services

#### Step 3: Create ECR Repository (Minimal)
1. **Navigate to ECR Console**:
   - Search "ECR" in AWS Console
   - Click "Elastic Container Registry"
   - Click "Create repository"

2. **Repository Configuration**:
   ```
   Visibility settings: Private
   Repository name: openautomate-api
   Tag immutability: Disabled
   Scan on push: Disabled (cost saving)
   Encryption: AES-256 (default)
   ```

3. **Click "Create repository"**
   - **⏱️ Time**: ~1 minute
   - **💰 Cost**: ~$0.10/GB/month
   - **Save the Repository URI**: You'll need this format:
     `[YOUR-ACCOUNT-ID].dkr.ecr.us-east-1.amazonaws.com/openautomate-api`

#### Step 3.1: Build and Push Docker Image to ECR

**Prerequisites**: Install Docker on your local machine

1. **Dockerfile Location**: The Dockerfile is already created in `OpenAutomate.Backend/` directory with the following configuration:
   ```dockerfile
   FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS base
   WORKDIR /app
   EXPOSE 80
   
   # Install ICU for globalization support
   RUN apk add --no-cache icu-libs
   ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
   ENV LANG=en_US.UTF-8
   ENV ASPNETCORE_URLS=http://+:80
   
   FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
   WORKDIR /src
   COPY ["OpenAutomate.API/OpenAutomate.API.csproj", "OpenAutomate.API/"]
   COPY ["OpenAutomate.Core/OpenAutomate.Core.csproj", "OpenAutomate.Core/"]
   COPY ["OpenAutomate.Infrastructure/OpenAutomate.Infrastructure.csproj", "OpenAutomate.Infrastructure/"]
   RUN dotnet restore "OpenAutomate.API/OpenAutomate.API.csproj"
   COPY . .
   WORKDIR "/src/OpenAutomate.API"
   RUN dotnet build "OpenAutomate.API.csproj" -c Release -o /app/build
   
   FROM build AS publish
   RUN dotnet publish "OpenAutomate.API.csproj" -c Release -o /app/publish
   
   FROM base AS final
   WORKDIR /app
   COPY --from=publish /app/publish .
   ENTRYPOINT ["dotnet", "OpenAutomate.API.dll"]
   ```

2. **Add Health Check Endpoint** to your ASP.NET Core API:
   ```csharp
   // In Program.cs or a Controller
   app.MapGet("/health", () => Results.Ok(new { 
       status = "healthy", 
       timestamp = DateTime.UtcNow 
   }));
   ```

3. **Clean Docker Environment** (recommended for fresh builds):
   ```bash
   # Clean up Docker environment
   docker system prune -a --volumes --force
   
   # Verify clean state
   docker images
   ```

4. **Build Docker Image**:
   ```bash
   # Navigate to your backend directory
   cd OpenAutomate.Backend
   
   # Build the image (use --no-cache for fresh builds)
   docker build --no-cache -t openautomate-api:latest .
   
   # Verify the image was built
   docker image ls openautomate-api
   ```

5. **Authenticate with ECR**:
   ```bash
   # Replace [YOUR-ACCOUNT-ID] and [REGION] with your values
   aws ecr get-login-password --region [REGION] | docker login --username AWS --password-stdin [YOUR-ACCOUNT-ID].dkr.ecr.[REGION].amazonaws.com
   ```

6. **Tag and Push to ECR**:
   ```bash
   # Tag the image (replace [YOUR-ACCOUNT-ID] with your actual AWS account ID)
   docker tag openautomate-api:latest [YOUR-ACCOUNT-ID].dkr.ecr.[REGION].amazonaws.com/openautomate-api:latest
   
   # Push to ECR
   docker push [YOUR-ACCOUNT-ID].dkr.ecr.[REGION].amazonaws.com/openautomate-api:latest
   ```

7. **Understanding ECR Multi-Architecture Images**:
   After pushing, you'll see **3 images** in ECR console:
   - **1 tagged image** (`latest`) - The manifest list (multi-architecture pointer)
   - **2 untagged images** - Architecture-specific images (AMD64 and ARM64)
   
   **This is normal and beneficial because:**
   - ✅ **Cross-platform compatibility** (Intel/AMD and ARM processors)
   - ✅ **AWS Fargate optimization** (automatic architecture selection)
   - ✅ **Future-proof** (ready for Graviton instances)
   - ✅ **Industry standard** (how Docker distributes modern images)

8. **Verify Push Success**:
   - Go back to ECR console
   - Click on your "openautomate-api" repository
   - You should see 1 tagged image and 2 untagged images
   - **Copy the Image URI** - you'll need this for the task definition

**💡 Pro Tips**: 
- The complete ECR URI format is: `[YOUR-ACCOUNT-ID].dkr.ecr.[REGION].amazonaws.com/openautomate-api:latest`
- Keep all 3 images for optimal performance and compatibility
- The untagged images provide minimal storage cost (~$0.50-$1.00/month extra)

#### Step 4: Store Connection Strings in Secrets Manager
1. **External Database Secret**:
   ```
   Secret name: openautomate/external-connections
   Secret type: Other type of secret
   Key/value pairs:
   - DefaultConnection: Server=YOUR_EXTERNAL_IP,1433;Database=OpenAutomate;User Id=openautomate_user;Password=YourSecurePassword123!;TrustServerCertificate=true;
   - RedisConnection: YOUR_EXTERNAL_IP:6379,password=YourRedisPassword123!
   - JwtKey: YourRandomJwtSecretKey123!
   ```

### Phase 3: Minimal Application Load Balancer

#### Step 5: Create Application Load Balancer
```
Name: openautomate-alb-simple
Scheme: Internet-facing
IP address type: IPv4

VPC: openautomate-vpc-simple
Mappings: Select both PUBLIC subnets (no private!)
Security groups: openautomate-alb-sg

Target Group:
- Name: openautomate-api-targets
- Target type: IP addresses
- Protocol: HTTP, Port: 80
- VPC: openautomate-vpc-simple
- Health check: /health
```

### Phase 4: Cost-Optimized ECS Fargate

#### Step 6: Create ECS Cluster (Minimal)
```
Cluster name: openautomate-cluster-simple
Infrastructure: AWS Fargate (serverless)
Monitoring: Disabled (cost saving)
```

#### Step 7: Create Minimal Task Definition
```
Task definition family: openautomate-api-minimal
Launch type: Fargate

Task role: ecsTaskRole (minimal permissions)
Task execution role: ecsTaskExecutionRole

Task size (COST OPTIMIZED):
- CPU: 0.25 vCPU (minimum possible)
- Memory: 0.5 GB (minimum possible)

Container definition:
- Container name: openautomate-api
- Image: [YOUR-ECR-URI]:latest
- Port mappings: 80
- Environment variables:
  * ASPNETCORE_ENVIRONMENT=Production
  * ASPNETCORE_URLS=http://+:80
```

**💰 Cost Savings: -$7/month (Smaller tasks)**

#### Step 8: Create Auto-Scaling Service
```
Service name: openautomate-api-service-minimal
Task definition: openautomate-api-minimal:latest
Launch type: Fargate

Desired tasks: 1
VPC: openautomate-vpc-simple
Subnets: Select PUBLIC subnets (both)
Security groups: openautomate-fargate-sg
Auto-assign public IP: ENABLED (required for public subnet)

Load balancer: openautomate-alb-simple
Target group: openautomate-api-targets

Auto Scaling (Demo-Ready):
- Service auto scaling: ENABLED
- Minimum tasks: 1
- Desired tasks: 1  
- Maximum tasks: 3 (demo scaling)

Scaling policy:
- Target tracking scaling
- Metric: Average CPU utilization
- Target value: 60% (lower threshold for demo)
```

### Phase 5: Domain Configuration (Optional but Recommended)

#### Step 9: Configure Custom Domain with Route 53

**If you have a domain from Namecheap (or other registrar):**

1. **Create Route 53 Hosted Zone**:
   - Go to AWS Route 53 Console
   - Click "Create hosted zone"
   - Domain name: `yourdomain.com` (your actual domain)
   - Type: Public hosted zone
   - Click "Create hosted zone"

2. **Update Namecheap DNS Settings**:
   - Copy the 4 Name Servers from Route 53 hosted zone
   - Go to Namecheap → Domain List → Manage → Custom DNS
   - Replace nameservers with the 4 AWS nameservers:
     ```
     ns-xxx.awsdns-xx.com
     ns-xxx.awsdns-xx.co.uk
     ns-xxx.awsdns-xx.net
     ns-xxx.awsdns-xx.org
     ```

3. **Create DNS Records**:
   ```
   API Subdomain:
   - Name: api.yourdomain.com
   - Type: CNAME
   - Value: [YOUR-ALB-DNS-NAME]
   
   Frontend Subdomain:
   - Name: app.yourdomain.com
   - Type: CNAME
   - Value: [YOUR-AMPLIFY-DOMAIN]
   ```

4. **Request SSL Certificate** (AWS Certificate Manager):
   - Go to AWS Certificate Manager
   - Click "Request certificate"
   - Domain names: 
     * `api.yourdomain.com`
     * `app.yourdomain.com`
   - Validation: DNS validation (recommended)
   - Add CNAME records to Route 53 for validation

5. **Update ALB with SSL Certificate**:
   - Go to EC2 → Load Balancers → Your ALB
   - Click "Listeners" tab
   - Add listener: HTTPS:443
   - Select your SSL certificate
   - Forward to your target group

**Cost Impact**: Route 53 hosted zone costs ~$0.50/month + $0.40 per million queries

#### Step 10: Deploy with AWS Amplify
```
App name: openautomate-frontend
Repository: OpenAutomate.Frontend
Branch: main

Custom Domain (if configured):
- Domain: app.yourdomain.com
- SSL Certificate: Auto-provisioned by Amplify

Environment Variables:
- NEXT_PUBLIC_API_URL: https://api.yourdomain.com (with custom domain)
- NEXT_PUBLIC_API_URL: http://[ALB-DNS-NAME] (without custom domain)
- NEXT_PUBLIC_ENVIRONMENT: production
```

### Phase 6: Application Configuration

#### Step 11: Update Application Configuration
**In your ASP.NET Core appsettings.json:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "#{DefaultConnection}#"
  },
  "Redis": {
    "ConnectionString": "#{RedisConnection}#"
  },
  "Jwt": {
    "Key": "#{JwtKey}#"
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://+:80"
      }
    }
  }
}
```

**Note**: The Dockerfile and container image should already be built and pushed to ECR from Step 3.1 above.

### Testing & Validation

#### Step 12: Demo Auto-Scaling Test
1. **Verify Initial State**:
   - 1 task running
   - CPU utilization < 60%

2. **Generate Load** (simple load test):
   ```bash
   # Install Apache Bench
   sudo apt-get install apache2-utils
   
   # Generate load to trigger auto-scaling
   ab -n 1000 -c 20 http://[YOUR-ALB-DNS]/api/health
   ```

3. **Observe Scaling**:
   - Watch ECS console
   - Should scale from 1 → 2 → 3 tasks
   - Then scale back down after load stops

## 5. Security Best Practices for External Connections

### Database Security
```sql
-- Create limited user for application
CREATE LOGIN openautomate_app WITH PASSWORD = 'SecureAppPassword123!';
USE OpenAutomate;
CREATE USER openautomate_app FOR LOGIN openautomate_app;

-- Grant minimal permissions
ALTER ROLE db_datareader ADD MEMBER openautomate_app;
ALTER ROLE db_datawriter ADD MEMBER openautomate_app;
GRANT EXECUTE TO openautomate_app;
```

### Redis Security
```bash
# redis.conf security settings
requirepass YourRedisPassword123!
bind 0.0.0.0
protected-mode yes
timeout 300
maxclients 100
```

### Network Security
- **Firewall**: Only allow ports 1433 (SQL) and 6379 (Redis)
- **VPN**: Consider site-to-site VPN for production
- **SSL**: Enable SSL for SQL Server connections
- **Monitoring**: Monitor connection logs

## 6. Cost Monitoring & Optimization

### AWS Budgets Setup
1. **Navigate to AWS Budgets**
2. **Create Budget**:
   ```
   Budget name: OpenAutomate-Monthly
   Budget type: Cost budget
   Period: Monthly
   Budget amount: $40
   
   Alerts:
   - 80% of budget ($32)
   - 100% of budget ($40)
   ```

### Further Cost Optimizations
1. **Fargate Spot Tasks** (50% savings):
   ```
   Capacity provider strategy:
   - FARGATE_SPOT: 70%
   - FARGATE: 30%
   ```

2. **Reserved Capacity** (if usage is predictable)
3. **Scheduled Scaling** (scale down nights/weekends)
4. **CloudWatch Logs Retention** (7 days instead of indefinite)

## 7. Production Migration Path

When ready to move to production:

1. **Add Private Subnets** + NAT Gateway
2. **Migrate to RDS** + ElastiCache in VPC
3. **Implement VPN** between AWS and your servers
4. **Add Multi-AZ** deployment
5. **Increase Task Resources** (0.5+ vCPU, 1+ GB RAM)

## 8. Troubleshooting

### ECR Multi-Architecture Images
**Issue**: After pushing to ECR, you see 3 images (1 tagged, 2 untagged)

**Explanation**: This is **normal behavior** for multi-architecture Docker images:
- **Tagged Image** (`latest`): Manifest list pointing to architecture-specific images
- **Untagged Images**: Individual architecture images (AMD64 and ARM64)

**Management Options**:
1. **Keep All Images** (Recommended):
   - ✅ Cross-platform compatibility
   - ✅ Optimal AWS Fargate performance
   - ✅ Minimal cost impact (~$0.50-$1.00/month)

2. **Delete Untagged Images** (Not Recommended):
   - ❌ Loses multi-architecture benefits
   - ❌ May cause deployment issues
   - ❌ Must delete manifest list first

**To Clean Up ECR** (if needed):
```bash
# Delete manifest list first
aws ecr batch-delete-image --repository-name openautomate-api --image-ids imageTag=latest

# Then delete architecture-specific images
aws ecr batch-delete-image --repository-name openautomate-api --image-ids imageDigest=sha256:YOUR-DIGEST-HERE
```

### Docker Build Issues
**Issue**: `docker images` shows no results after successful build

**Solution**: Use `docker image ls` or `docker image ls -a` instead:
```bash
# List all images
docker image ls

# List all images including intermediate
docker image ls -a
```

### External Connection Issues
1. **Connection Timeouts**:
   - Check firewall rules
   - Verify public IP addresses
   - Test telnet connectivity

2. **Authentication Failures**:
   - Verify credentials in Secrets Manager
   - Check SQL Server authentication mode
   - Validate Redis password

3. **Network Connectivity**:
   ```bash
   # Test from Fargate task (if possible)
   telnet YOUR_SQL_IP 1433
   telnet YOUR_REDIS_IP 6379
   ```

### Domain Configuration Issues
**Issue**: Custom domain not working or SSL certificate validation failing

**Common Solutions**:
1. **DNS Propagation**: Wait 24-48 hours for DNS changes to propagate globally
2. **Nameserver Check**: Verify Namecheap is using AWS Route 53 nameservers
3. **SSL Certificate Validation**: 
   - Ensure CNAME records for certificate validation are added to Route 53
   - Check certificate status in AWS Certificate Manager
4. **ALB HTTPS Listener**: Verify HTTPS listener is configured with correct certificate

**Verification Commands**:
```bash
# Check DNS propagation
nslookup api.yourdomain.com

# Check nameservers
nslookup -type=ns yourdomain.com

# Test SSL certificate
curl -I https://api.yourdomain.com
```

### AWS CLI Issues
**Issue**: `aws` command not recognized

**Solutions**:
1. **Install AWS CLI**: Download from [AWS CLI Installation Guide](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
2. **Use AWS Console**: Manually manage ECR through web interface
3. **Use PowerShell**: Some commands may need PowerShell-specific syntax

---

This cost-optimized deployment maintains auto-scaling capabilities while reducing monthly costs by 70% ($94 → $28), making it perfect for demos and budget-conscious deployments! 
This cost-optimized deployment maintains auto-scaling capabilities while reducing monthly costs by 70% ($94 → $28), making it perfect for demos and budget-conscious deployments! 