(()=>{var e={};e.id=9369,e.ids=[9369],e.modules={1303:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},2633:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=a(65239),r=a(48088),n=a(31369),i=a(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let o={children:["",{children:["[tenant]",{children:["administration",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,87931)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\users\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\users\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[tenant]/administration/users/page",pathname:"/[tenant]/administration/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31567:(e,t,a)=>{"use strict";a.d(t,{g:()=>r});var s=a(51787);let r={inviteUser:async(e,t)=>s.F.post(`${e}/api/organization-unit-invitation`,{email:t}),acceptInvitation:async(e,t)=>{try{let a=await s.F.post(`${e}/api/organization-unit-invitation/accept`,{token:t});if(!a)throw Error("Empty response received");if(void 0===a.success)return{success:!0};return a}catch(e){return function(e){if(e?.status===409)return{success:!0};if(e?.response?.data?.message)throw Error(e.response.data.message);if(e?.message)throw Error(e.message);throw Error("Failed to accept invitation.")}(e)}},checkInvitation:async(e,t)=>s.F.get(`${e}/api/organization-unit-invitation/check?email=${encodeURIComponent(t)}`),checkInvitationToken:async(e,t)=>{try{return await s.F.get(`${e}/api/organization-unit-invitation/check-token?token=${encodeURIComponent(t)}`)}catch(e){if(console.error("Error checking invitation token:",e),e?.status===404)return{status:"Invalid",recipientEmail:"",expiresAt:"",organizationUnitId:""};throw e}},listInvitations:async(e,t)=>{let a=`/${e}/odata/OrganizationUnitInvitations`;if(t){let e=new URLSearchParams;Object.entries(t).forEach(([t,a])=>{void 0!==a&&e.append(t,a)}),a+=`?${e.toString()}`}return function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:"object"==typeof e&&null!==e&&"invitations"in e?{value:e.invitations,"@odata.count":e.count}:{value:[]}}(await s.F.get(a))}}},31599:(e,t,a)=>{"use strict";a.d(t,{c:()=>o});var s=a(43210),r=a(39989),n=a(16189),i=a(31207),l=a(70891);function o(){let e=(0,n.useRouter)(),{data:t,error:a,isLoading:o,mutate:c}=(0,i.Ay)(l.DC.organizationUnits(),()=>r.K.getMyOrganizationUnits().then(e=>e.organizationUnits));return{organizationUnits:t??[],isLoading:o,error:a?"Failed to fetch organization units. Please try again later.":null,refresh:c,selectOrganizationUnit:(0,s.useCallback)(t=>{e.push(`/${t}/dashboard`)},[e])}}},33873:e=>{"use strict";e.exports=require("path")},38936:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});var s=a(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call usersSchema() from the server but usersSchema is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\administration\\users\\users.tsx","usersSchema");let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\administration\\\\users\\\\users.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\administration\\users\\users.tsx","default")},39989:(e,t,a)=>{"use strict";a.d(t,{K:()=>r});var s=a(51787);let r={getMyOrganizationUnits:async()=>await s.F.get("/api/ou/my-ous"),getBySlug:async e=>await s.F.get(`/api/ou/slug/${e}`),getById:async e=>await s.F.get(`/api/ou/${e}`),create:async e=>await s.F.post("/api/ou/create",e),update:async(e,t)=>await s.F.put(`/api/ou/${e}`,t),requestDeletion:async e=>await s.F.post(`/api/ou/${e}/request-deletion`,{}),cancelDeletion:async e=>await s.F.post(`/api/ou/${e}/cancel-deletion`,{}),getDeletionStatus:async e=>await s.F.get(`/api/ou/${e}/deletion-status`)}},46392:(e,t,a)=>{Promise.resolve().then(a.bind(a,38936))},61018:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call TenantGuard() from the server but TenantGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\tenant-guard.tsx","TenantGuard")},61170:(e,t,a)=>{"use strict";a.d(t,{b:()=>d});var s=a(43210);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}a(51215);var n=a(60687),i=Symbol("radix.slottable");function l(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:a,...n}=e;if(s.isValidElement(a)){var i;let e,l;let o=(i=a,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let a={...t};for(let s in t){let r=e[s],n=t[s];/^on[A-Z]/.test(s)?r&&n?a[s]=(...e)=>{n(...e),r(...e)}:r&&(a[s]=r):"style"===s?a[s]={...r,...n}:"className"===s&&(a[s]=[r,n].filter(Boolean).join(" "))}return{...e,...a}}(n,a.props);return a.type!==s.Fragment&&(c.ref=t?function(...e){return t=>{let a=!1,s=e.map(e=>{let s=r(e,t);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let t=0;t<s.length;t++){let a=s[t];"function"==typeof a?a():r(e[t],null)}}}}(t,o):o),s.cloneElement(a,c)}return s.Children.count(a)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=s.forwardRef((e,a)=>{let{children:r,...i}=e,o=s.Children.toArray(r),c=o.find(l);if(c){let e=c.props.children,r=o.map(t=>t!==c?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,n.jsx)(t,{...i,ref:a,children:r})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(r?a:t,{...i,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),c=s.forwardRef((e,t)=>(0,n.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var d=c},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>p,Es:()=>h,HM:()=>u,L3:()=>f,c7:()=>x,lG:()=>o,rr:()=>g,zM:()=>c});var s=a(60687),r=a(43210),n=a(88562),i=a(11860),l=a(36966);let o=n.bL,c=n.l9,d=n.ZL,u=n.bm,m=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.hJ,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));m.displayName=n.hJ.displayName;let p=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(d,{children:[(0,s.jsx)(m,{}),(0,s.jsxs)(n.UC,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...a,children:[t,(0,s.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=n.UC.displayName;let x=({className:e,...t})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});x.displayName="DialogHeader";let h=({className:e,...t})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});h.displayName="DialogFooter";let f=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.hE,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));f.displayName=n.hE.displayName;let g=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.VY,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=n.VY.displayName},63600:(e,t,a)=>{"use strict";a.d(t,{Bx:()=>n,Gg:()=>l,H1:()=>i});var s=a(51787);function r(){return console.log("Using default tenant"),"default"}let n=async e=>{let t=r(),a=function(e){if(!e)return"";let t=new URLSearchParams;return Object.entries(e).forEach(([e,a])=>{null!=a&&t.append(e,String(a))}),t.toString()}(e),n=`${t}/odata/OrganizationUnitUsers`;return a&&(n+=`?${a}`),function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:{value:[]}}(await s.F.get(n))},i={getUsers:async e=>(await s.F.get(`${e}/api/ou/users`)).users,getRolesInOrganizationUnit:async e=>s.F.get(`${e}/api/ou/users/roles`),assignRolesBulk:async(e,t)=>{let a=r(),n=t.map(e=>e.trim()),i=`${a}/api/author/user/${e}/assign-multiple-roles`;try{return await s.F.post(i,{authorityIds:n})}catch(e){throw console.error("Error assigning roles:",e),e}}},l=async e=>{let t=r();await s.F.delete(`${t}/api/ou/users/${e}`)}},64656:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var s=a(37413),r=a(48974),n=a(31057),i=a(50417),l=a(92588),o=a(61018),c=a(2505);function d({children:e}){return(0,s.jsx)(o.TenantGuard,{children:(0,s.jsx)(c.ChatProvider,{children:(0,s.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,s.jsxs)(i.SidebarProvider,{className:"flex flex-col",children:[(0,s.jsx)(n.SiteHeader,{}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)(r.AppSidebar,{}),(0,s.jsx)(i.SidebarInset,{children:(0,s.jsx)(l.SearchProvider,{children:(0,s.jsx)("main",{className:"",children:e})})})]})]})})})})}},69231:(e,t,a)=>{"use strict";a.d(t,{TenantGuard:()=>o});var s=a(60687),r=a(43210),n=a(16189),i=a(31599),l=a(31568);function o({children:e}){let{tenant:t}=(0,n.useParams)();(0,n.useRouter)();let{isAuthenticated:a,isLoading:o}=(0,l.A)(),{organizationUnits:c,isLoading:d}=(0,i.c)(),[u,m]=(0,r.useState)(!0);return o||d||u?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Validating access..."})]})}):(0,s.jsx)(s.Fragment,{children:e})}},72826:(e,t,a)=>{Promise.resolve().then(a.bind(a,69231)),Promise.resolve().then(a.bind(a,83847)),Promise.resolve().then(a.bind(a,78526)),Promise.resolve().then(a.bind(a,97597)),Promise.resolve().then(a.bind(a,98641)),Promise.resolve().then(a.bind(a,80110))},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var s=a(60687);a(43210);var r=a(61170),n=a(36966);function i({className:e,...t}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80462:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},80702:(e,t,a)=>{"use strict";a.d(t,{default:()=>V});var s=a(60687),r=a(43210),n=a(1303),i=a(29523),l=a(56896),o=a(76242),c=a(34208),d=a(93661);let u=(0,a(62688).A)("user-check",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);var m=a(96362),p=a(11860),x=a(41862),h=a(63503),f=a(63600),g=a(20140),j=a(21342),v=a(31207),y=a(31898),b=a(6211),w=a(15079);function N({isOpen:e,onClose:t,userId:a,email:n,refreshUsersList:l}){let{toast:c}=(0,g.d)(),{data:d,isLoading:u}=(0,v.Ay)(null,()=>f.H1.getRolesInOrganizationUnit("")),{data:p,isLoading:x}=(0,v.Ay)(null,()=>f.H1.getUsers(""));(0,r.useMemo)(()=>{if(!p)return[];let e=p.find(e=>e.userId===a);return e?(e.roles||[]).map(e=>{let t=d?.find(t=>t.name===e);return t?{id:t.id,name:t.name,description:t.description}:{id:e,name:e,description:""}}):[]},[p,a,d]);let[j,N]=(0,r.useState)(""),[A,C]=(0,r.useState)([]),[S,k]=(0,r.useState)(!1),P=(0,r.useMemo)(()=>d?d.filter(e=>!A.some(t=>t.id===e.id)):[],[d,A]),$=e=>{C(t=>t.filter(t=>t.id!==e))},R=async()=>{k(!0);try{await f.H1.assignRolesBulk(a,A.map(e=>e.id)),c({title:"Success",description:"Roles updated successfully."}),C([]),(0,y.j)(`user-roles-${a}`),(0,y.j)(e=>Array.isArray(e)&&"organization-unit-users"===e[0]),l&&l(),t()}catch(e){console.error("Failed to update roles:",e),c({title:"Error",description:"Failed to update roles.",variant:"destructive"})}finally{k(!1)}};return(0,s.jsx)(h.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(h.Cf,{className:"max-w-[700px]",children:[(0,s.jsx)(h.c7,{children:(0,s.jsxs)(h.L3,{children:["Set Roles for ",n]})}),(0,s.jsxs)("div",{className:"flex gap-2 items-end mb-4",children:[(0,s.jsxs)(w.l6,{value:j,onValueChange:N,children:[(0,s.jsx)(w.bq,{className:"w-64",children:(0,s.jsx)(w.yv,{placeholder:"Select a role to add"})}),(0,s.jsx)(w.gC,{children:P.map(e=>(0,s.jsx)(w.eb,{value:e.id,children:e.name},e.id))})]}),(0,s.jsx)(i.$,{onClick:()=>{if(!j)return;let e=d?.find(e=>e.id===j);e&&!A.some(t=>t.id===e.id)&&(C(t=>[...t,e]),N(""))},disabled:!j||u||x,type:"button",children:"Add"})]}),(0,s.jsx)("div",{className:"w-full max-w-[650px] max-h-[50vh] overflow-y-auto overflow-x-auto",children:(0,s.jsxs)(b.XI,{className:"min-w-full table-fixed",children:[(0,s.jsx)(b.A0,{children:(0,s.jsxs)(b.Hj,{children:[(0,s.jsx)(b.nd,{className:"w-1/3 text-left max-w-[300px] truncate",children:"Role Name"}),(0,s.jsx)(b.nd,{className:"w-1/3 text-left max-w-[200px] truncate",children:"Description"}),(0,s.jsx)(b.nd,{className:"w-1/3 text-right",children:"Action"})]})}),(0,s.jsxs)(b.BF,{children:[x&&(0,s.jsx)(b.Hj,{children:(0,s.jsx)(b.nA,{colSpan:3,className:"text-center text-muted-foreground",children:"Loading..."})}),!x&&0===A.length&&(0,s.jsx)(b.Hj,{children:(0,s.jsx)(b.nA,{colSpan:3,className:"text-center text-muted-foreground",children:"No roles added"})}),!x&&A.length>0&&(0,s.jsx)(s.Fragment,{children:A.map(e=>(0,s.jsxs)(b.Hj,{children:[(0,s.jsx)(b.nA,{className:"w-1/3 text-left max-w-[300px] truncate",children:(0,s.jsx)(o.Bc,{children:(0,s.jsxs)(o.m_,{children:[(0,s.jsx)(o.k$,{asChild:!0,children:(0,s.jsx)("span",{className:"truncate cursor-pointer",children:e.name})}),(0,s.jsx)(o.ZI,{children:e.name})]})})}),(0,s.jsx)(b.nA,{className:"w-1/3 text-left max-w-[200px] truncate",children:e.description?(0,s.jsx)(o.Bc,{children:(0,s.jsxs)(o.m_,{children:[(0,s.jsx)(o.k$,{asChild:!0,children:(0,s.jsx)("span",{className:"truncate cursor-pointer",children:e.description})}),(0,s.jsx)(o.ZI,{children:e.description})]})}):null}),(0,s.jsx)(b.nA,{className:"w-1/3 text-right",children:(0,s.jsx)(i.$,{size:"icon",variant:"ghost",onClick:()=>$(e.id),className:"text-red-400 hover:text-red-600",children:(0,s.jsx)(m.A,{className:"w-4 h-4"})})})]},e.id))})]})]})}),(0,s.jsxs)(h.Es,{children:[(0,s.jsx)(i.$,{variant:"outline",onClick:t,type:"button",children:"Cancel"}),(0,s.jsx)(i.$,{onClick:R,disabled:S||u||x,type:"button",children:S?"Saving...":"Save"})]})]})})}function A({row:e,onDeleted:t}){let[a,n]=(0,r.useState)(!1),[l,o]=(0,r.useState)(!1),[c,v]=(0,r.useState)(!1),{toast:y}=(0,g.d)(),b=async()=>{o(!0);try{await (0,f.Gg)(e.original.userId),n(!1),t&&t()}catch(t){let e="Delete failed!";t&&"object"==typeof t&&"message"in t&&"string"==typeof t.message&&(e=t.message),y({title:"Delete User Failed",description:e,variant:"destructive",style:{background:"#ff6a6a",color:"#fff"}})}finally{o(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(j.rI,{children:[(0,s.jsx)(j.ty,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,s.jsxs)(j.SQ,{align:"start",className:"w-[160px]",children:[(0,s.jsxs)(j._2,{onClick:()=>v(!0),className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"min-w-[1.25rem] flex justify-center",children:(0,s.jsx)(u,{className:"w-4 h-4 text-foreground"})}),(0,s.jsx)("span",{children:"Set Role"})]}),(0,s.jsxs)(j._2,{className:"flex items-center gap-2 text-destructive focus:text-destructive",onClick:()=>n(!0),children:[(0,s.jsx)("span",{className:"min-w-[1.25rem] flex justify-center",children:(0,s.jsx)(m.A,{className:"w-4 h-4 text-destructive","aria-hidden":"true"})}),(0,s.jsx)("span",{children:"Delete"})]})]})]}),(0,s.jsx)(N,{isOpen:c,onClose:()=>v(!1),userId:e.original.userId,email:e.original.email}),(0,s.jsx)(h.lG,{open:a,onOpenChange:n,children:(0,s.jsxs)(h.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsxs)(h.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,s.jsx)(h.c7,{children:(0,s.jsx)(h.L3,{children:"Confirm Delete"})}),(0,s.jsxs)("div",{children:["Are you sure you want to delete this user ",(0,s.jsx)("b",{children:e.original.email}),"?"]}),(0,s.jsxs)(h.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>n(!1),disabled:l,children:"Cancel"}),(0,s.jsxs)(i.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:b,disabled:l,children:[l&&(0,s.jsx)(x.A,{className:"animate-spin w-4 h-4 mr-2"}),l?"Deleting...":"Delete"]})]})]})})]})}let C=[{id:"select",header:({table:e})=>(0,s.jsx)(l.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>(0,s.jsx)(l.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1},{id:"actions",header:"Action",cell:({row:e})=>(0,s.jsx)(A,{row:e})},{accessorKey:"email",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Email"}),cell:({row:e})=>(0,s.jsx)("span",{children:e.getValue("email")})},{accessorKey:"firstName",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"First Name"}),cell:({row:e})=>(0,s.jsx)("span",{children:e.getValue("firstName")})},{accessorKey:"lastName",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Last Name"}),cell:({row:e})=>(0,s.jsx)("span",{children:e.getValue("lastName")})},{accessorKey:"roles",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Roles"}),cell:({row:e})=>{let t=e.getValue("roles"),a=[];return Array.isArray(t)?a=t:"string"==typeof t&&(a=t.split(",").map(e=>e.trim()).filter(Boolean)),(0,s.jsx)(o.Bc,{children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 max-w-[320px]",children:[a.slice(0,3).map(e=>(0,s.jsx)("span",{className:"px-2 py-0.5 rounded text-xs truncate max-w-[90px] bg-muted text-muted-foreground dark:bg-secondary dark:text-secondary-foreground",title:e,children:e},e)),a.length>3&&(0,s.jsxs)(o.m_,{children:[(0,s.jsx)(o.k$,{asChild:!0,children:(0,s.jsxs)("span",{className:"text-xs text-gray-500 cursor-pointer",children:["+",a.length-3," more"]})}),(0,s.jsx)(o.ZI,{children:a.slice(3).join(", ")})]})]})})}},{accessorKey:"joinedAt",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Joined At"}),cell:({row:e})=>(0,s.jsx)("span",{children:e.getValue("joinedAt")})}];var S=a(50723),k=a(80013),P=a(16189),$=a(31567);function R({isOpen:e,onClose:t}){let[a,n]=(0,r.useState)(""),[l,o]=(0,r.useState)(!1),{toast:c}=(0,g.d)(),d=(0,P.useParams)().tenant,u=async e=>{if(e.preventDefault(),!a.trim()){c({title:"No email entered",description:"Please enter an email address.",variant:"destructive"});return}o(!0);try{if((await $.g.checkInvitation(d,a.trim())).invited){c({title:"Email already invited",description:`${a.trim()} has already been invited to this organization.`,variant:"default"}),o(!1);return}await $.g.inviteUser(d,a.trim()),c({title:"Invitation sent",description:`Successfully sent invitation to ${a.trim()}`}),n(""),t()}catch(t){let e="";"object"==typeof t&&null!==t?e=t.details??t.message??"":"string"==typeof t&&(e=t),c({title:"Failed to send invitation",description:e?.includes("already a member of this organization")?"This user is already a member of this organization.":e||"An unknown error occurred",variant:"destructive",style:{background:"#ff6a6a",color:"#fff"}})}finally{o(!1)}};return(0,s.jsx)(h.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(h.Cf,{className:"sm:max-w-[400px] p-6",children:[(0,s.jsx)(h.c7,{children:(0,s.jsx)(h.L3,{children:"Invite User"})}),(0,s.jsxs)("form",{onSubmit:u,children:[(0,s.jsx)("div",{className:"grid gap-4 py-4",children:(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(k.J,{htmlFor:"email",children:"Email address"}),(0,s.jsx)("input",{id:"email",type:"email",value:a,onChange:e=>n(e.target.value),placeholder:"Enter email address",className:"flex-1 bg-background border rounded-md p-2 text-sm",required:!0})]})}),(0,s.jsxs)(h.Es,{className:"pt-4",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:t,type:"button",children:"Cancel"}),(0,s.jsx)(i.$,{type:"submit",disabled:l,children:l?"Sending...":"Send Invitation"})]})]})]})})}var I=a(99270),F=a(80462),E=a(89667),z=a(14583),L=a(96834);let O=[{id:"select",header:({table:e})=>(0,s.jsx)(l.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>(0,s.jsx)(l.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1},{id:"actions",header:"Action",cell:()=>(0,s.jsx)(U,{})},{accessorKey:"recipientEmail",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Email"}),cell:({row:e})=>(0,s.jsx)("span",{children:e.getValue("recipientEmail")}),enableSorting:!0},{accessorKey:"status",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Status"}),cell:({row:e})=>(0,s.jsx)("span",{children:e.getValue("status")}),enableSorting:!0},{accessorKey:"expiresAt",header:({column:e})=>(0,s.jsx)(c.w,{column:e,title:"Expires At"}),cell:({row:e})=>{try{let t;let a=e.getValue("expiresAt");if(!a)return(0,s.jsx)("span",{children:"-"});if("string"==typeof a||"number"==typeof a)t=String(a);else{if(!(a instanceof Date))return console.warn("Invalid date format received:",a),(0,s.jsx)("span",{children:"Invalid format"});t=a.toISOString()}let r=new Date(t);if(isNaN(r.getTime()))return(0,s.jsx)("span",{children:"Invalid date"});return(0,s.jsx)("span",{children:r.toISOString().replace("T"," ").slice(0,10)})}catch(e){return console.error("Error formatting date:",e),(0,s.jsx)("span",{children:"Error"})}},enableSorting:!0}];function U(){return(0,s.jsx)(i.$,{variant:"ghost",size:"icon",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}let M=["All","Pending","Accepted","Expired","Revoked"];function G(){let e=(0,P.useParams)().tenant,[t,a]=(0,r.useState)(""),[l,o]=(0,r.useState)("All"),[c,d]=(0,r.useState)(0),[u,m]=(0,r.useState)(10),[x,h]=(0,r.useState)(!1);(0,r.useRef)(0);let[f,g]=(0,r.useState)(!1),j=(0,r.useRef)(null),y=(0,r.useRef)(null),b={$filter:[t?`contains(tolower(recipientEmail),'${t.toLowerCase()}')`:void 0,"All"!==l?`status eq '${l}'`:void 0].filter(Boolean).join(" and ")||void 0,$top:u,$skip:c*u,$count:!0},{data:N,error:A,isLoading:C,mutate:k}=(0,v.Ay)(["organization-invitations",e,b],()=>$.g.listInvitations(e,b)),U=N?.value??[],G=N?.["@odata.count"]??U.length,D=(0,r.useMemo)(()=>{let e=Math.max(1,Math.ceil(G/u)),t=U.length===u&&G<=u*(c+1),a=c+1;return t?Math.max(a,e,c+2):Math.max(a,e)},[G,u,c,U.length]),_=!f&&U.length===u,q=t||"All"!==l,V=[t,"All"!==l?l:""].filter(Boolean).length;return(0,s.jsxs)("div",{className:"flex flex-col h-full w-full space-y-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center w-full flex-wrap gap-2",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Invitations"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[G>0&&(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,s.jsxs)("span",{children:["Total: ",G," invitation",1!==G?"s":""]})}),(0,s.jsxs)(i.$,{onClick:()=>h(!0),className:"flex items-center justify-center",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"})," Invite User"]})]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mb-2 w-full",children:[(0,s.jsxs)("div",{className:"relative w-48",children:[(0,s.jsx)(I.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(E.p,{ref:j,placeholder:"Search by Email",value:t,onChange:e=>{y.current=e.target.selectionStart,a(e.target.value)},className:"pl-8 pr-8",disabled:C,onFocus:e=>{y.current=e.target.selectionStart}}),t&&(0,s.jsx)(p.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>a("")})]}),(0,s.jsx)("div",{className:"relative w-48",children:(0,s.jsxs)(w.l6,{value:l,onValueChange:o,disabled:C,children:[(0,s.jsx)(w.bq,{className:"pl-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(F.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(w.yv,{placeholder:"Filter Status"})]})}),(0,s.jsxs)(w.gC,{children:[(0,s.jsx)(w.eb,{value:"All",children:"All Statuses"}),M.filter(e=>"All"!==e).map(e=>(0,s.jsx)(w.eb,{value:e,children:e},e))]})]})}),V>0&&(0,s.jsxs)(L.E,{variant:"secondary",className:"rounded-sm px-1",children:[V," active ",1===V?"filter":"filters"]}),q&&(0,s.jsxs)(i.$,{variant:"ghost",onClick:()=>{a(""),o("All")},className:"h-8 px-2 lg:px-3",disabled:C,children:["Reset",(0,s.jsx)(p.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(S.b,{columns:O,data:U,isLoading:C,totalCount:G}),(0,s.jsx)(z.d,{currentPage:c+1,pageSize:u,totalCount:G,totalPages:D,isUnknownTotalCount:_,onPageChange:e=>d(e-1),onPageSizeChange:m}),A&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,s.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load invitations"}),(0,s.jsx)(i.$,{variant:"outline",className:"mt-2",onClick:()=>k(),children:"Retry"})]}),!C&&0===U.length&&!A&&(0,s.jsx)("div",{className:"text-center py-10 text-muted-foreground",children:(0,s.jsx)("p",{children:"No invitations found."})}),(0,s.jsx)(R,{isOpen:x,onClose:()=>{h(!1),k()}})]})}var D=a(45880);function _({searchEmail:e,setSearchEmail:t,searchFirstName:a,setSearchFirstName:n,searchLastName:l,setSearchLastName:c,searchRole:d,setSearchRole:u,roleOptions:m,loading:x=!1,onReset:h}){let f=[e,a,l,"ALL"!==d?d:""].filter(Boolean).length,g=(0,r.useRef)(null),j=(0,r.useRef)(null),v=(0,r.useRef)(null),y=(0,r.useRef)(null),b=(0,r.useRef)(null),N=(0,r.useRef)(null);return(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mb-2",children:[(0,s.jsxs)("div",{className:"relative w-48",children:[(0,s.jsx)(I.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(E.p,{ref:g,placeholder:"Search by Email",value:e,onChange:e=>{y.current=e.target.selectionStart,t(e.target.value)},className:"pl-8 pr-8",disabled:x,onFocus:e=>{y.current=e.target.selectionStart}}),e&&(0,s.jsx)(p.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>t("")})]}),(0,s.jsxs)("div",{className:"relative w-48",children:[(0,s.jsx)(I.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(E.p,{ref:j,placeholder:"Search by First Name",value:a,onChange:e=>{b.current=e.target.selectionStart,n(e.target.value)},className:"pl-8 pr-8",disabled:x,onFocus:e=>{b.current=e.target.selectionStart}}),a&&(0,s.jsx)(p.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>n("")})]}),(0,s.jsxs)("div",{className:"relative w-48",children:[(0,s.jsx)(I.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(E.p,{ref:v,placeholder:"Search by Last Name",value:l,onChange:e=>{N.current=e.target.selectionStart,c(e.target.value)},className:"pl-8 pr-8",disabled:x,onFocus:e=>{N.current=e.target.selectionStart}}),l&&(0,s.jsx)(p.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>c("")})]}),(0,s.jsx)("div",{className:"relative w-48",children:(0,s.jsx)(o.Bc,{children:(0,s.jsxs)(o.m_,{children:[(0,s.jsx)(o.k$,{asChild:!0,children:(0,s.jsxs)(w.l6,{value:d,onValueChange:u,disabled:x,children:[(0,s.jsx)(w.bq,{className:"pl-8 max-w-[180px] truncate",children:(0,s.jsxs)("div",{className:"flex items-center min-w-0",children:[(0,s.jsx)(F.A,{className:"mr-2 h-4 w-4 shrink-0"}),(0,s.jsx)("span",{className:"truncate",title:"ALL"!==d?d:"All Roles",children:(0,s.jsx)(w.yv,{placeholder:"All Roles"})})]})}),(0,s.jsxs)(w.gC,{children:[(0,s.jsx)(w.eb,{value:"ALL",children:"All Roles"}),m.map(e=>(0,s.jsx)(w.eb,{value:e,children:e},e))]})]})}),(0,s.jsx)(o.ZI,{children:"ALL"!==d?d:"All Roles"})]})})}),f>0&&(0,s.jsxs)(L.E,{variant:"secondary",className:"rounded-sm px-1",children:[f," active ",1===f?"filter":"filters"]}),f>0&&(0,s.jsxs)(i.$,{variant:"ghost",onClick:h,className:"h-8 px-2 lg:px-3",disabled:x,children:["Reset",(0,s.jsx)(p.A,{className:"ml-2 h-4 w-4"})]})]})}function q(e,t){let[a,s]=(0,r.useState)(e);return a}function V(){let[e,t]=(0,r.useState)(""),[a,l]=(0,r.useState)(""),[o,c]=(0,r.useState)(""),[d,u]=(0,r.useState)("ALL"),[m,p]=(0,r.useState)(!1),[x,h]=(0,r.useState)("user"),[g,j]=(0,r.useState)(0),[y,b]=(0,r.useState)(10);(0,r.useRef)(0);let[w,N]=(0,r.useState)(!1),k=q(e,400),P=q(a,400),$=q(o,400),{data:I}=(0,v.Ay)(null,()=>f.H1.getRolesInOrganizationUnit("")),F=(0,r.useMemo)(()=>{if(!I)return[];let e=new Map;for(let t of I){let a=t.name.trim().toLowerCase();e.has(a)||e.set(a,t.name)}return Array.from(e.values())},[I]),E={$filter:[k?`contains(tolower(email),'${k.toLowerCase()}')`:void 0,P?`contains(tolower(firstName),'${P.toLowerCase()}')`:void 0,$?`contains(tolower(lastName),'${$.toLowerCase()}')`:void 0,"ALL"!==d?`roles/any(r: tolower(r) eq '${d.toLowerCase()}')`:void 0].filter(Boolean).join(" and ")||void 0,$top:y,$skip:g*y,$count:!0},{data:L,error:O,isLoading:U,mutate:M}=(0,v.Ay)(["organization-unit-users",E],()=>(0,f.Bx)(E),{keepPreviousData:!0}),D=L?.value??[],V=L?.["@odata.count"]??D.length,B=(0,r.useMemo)(()=>{let e=Math.max(1,Math.ceil(V/y)),t=D.length===y&&V<=y*(g+1),a=g+1;return t?Math.max(a,e,g+2):Math.max(a,e)},[V,y,g,D.length]),H=!w&&D.length===y,[T,K]=(0,r.useState)(null),Z=C.map(e=>"actions"===e.id?{...e,cell:({row:e})=>(0,s.jsx)(A,{row:e,onDeleted:M})}:e);return(0,s.jsxs)("div",{className:"flex flex-col h-full w-full space-y-8",children:[(0,s.jsx)("div",{className:"mb-4 border-b w-full",children:(0,s.jsxs)("nav",{className:"flex space-x-8","aria-label":"Tabs",children:[(0,s.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"user"===x,type:"button",onClick:()=>h("user"),children:"User"}),(0,s.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"invitation"===x,type:"button",onClick:()=>h("invitation"),children:"Invitation"})]})}),"user"===x&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center w-full flex-wrap gap-2",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Users"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[V>0&&(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,s.jsxs)("span",{children:["Total: ",V," user",1!==V?"s":""]})}),(0,s.jsxs)(i.$,{onClick:()=>p(!0),className:"flex items-center justify-center",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"})," Invite User"]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,s.jsx)(_,{searchEmail:e,setSearchEmail:t,searchFirstName:a,setSearchFirstName:l,searchLastName:o,setSearchLastName:c,searchRole:d,setSearchRole:u,roleOptions:F,loading:U,onReset:()=>{t(""),l(""),c(""),u("ALL")}}),(0,s.jsx)(S.b,{columns:Z,data:D.map(function(e){return{userId:e.userId,email:e.email,firstName:e.firstName,lastName:e.lastName,roles:Array.isArray(e.roles)?e.roles.join(", "):"",joinedAt:new Date(e.joinedAt).toISOString().replace("T"," ").slice(0,10)}}),isLoading:U,totalCount:V}),(0,s.jsx)(z.d,{currentPage:g+1,pageSize:y,totalCount:V,totalPages:B,isUnknownTotalCount:H,onPageChange:e=>j(e-1),onPageSizeChange:b}),T&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,s.jsx)("p",{className:"text-red-800 dark:text-red-300",children:T}),(0,s.jsx)(i.$,{variant:"outline",className:"mt-2",onClick:()=>M(),children:"Retry"})]}),(0,s.jsx)(R,{isOpen:m,onClose:()=>p(!1)})]})]}),"invitation"===x&&(0,s.jsx)(G,{})]})}D.z.object({email:D.z.string(),firstName:D.z.string(),lastName:D.z.string(),roles:D.z.string(),joinedAt:D.z.string()})},81240:(e,t,a)=>{Promise.resolve().then(a.bind(a,80702))},83442:(e,t,a)=>{Promise.resolve().then(a.bind(a,61018)),Promise.resolve().then(a.bind(a,2505)),Promise.resolve().then(a.bind(a,92588)),Promise.resolve().then(a.bind(a,48974)),Promise.resolve().then(a.bind(a,31057)),Promise.resolve().then(a.bind(a,50417))},87931:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i,metadata:()=>n});var s=a(37413),r=a(38936);let n={title:"Automation",description:"Agent management page"};function i(){return(0,s.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,s.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,s.jsx)(r.default,{})]})}},93661:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,7966,5584,5156,4654,6467,5880,1694,6945,8759,6763,519,4881],()=>a(2633));module.exports=s})();