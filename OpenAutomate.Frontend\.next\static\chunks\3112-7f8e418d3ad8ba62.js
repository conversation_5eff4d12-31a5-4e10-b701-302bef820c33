"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3112],{11758:(e,t,r)=>{r.d(t,{OK:()=>ee,bL:()=>G,VM:()=>L,lr:()=>U,LM:()=>Q});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}r(47650);var a=r(95155),s=n.forwardRef((e,t)=>{let{children:r,...o}=e,l=n.Children.toArray(r),i=l.find(d);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(u,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,a.jsx)(u,{...o,ref:t,children:r})});s.displayName="Slot";var u=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(i.ref=t?l(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});u.displayName="SlotClone";var c=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===c}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,l=n?s:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),p=globalThis?.document?n.useLayoutEffect:()=>{},m=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,l]=n.useState(),i=n.useRef({}),a=n.useRef(e),s=n.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=h(i.current);s.current="mounted"===u?e:"none"},[u]),p(()=>{let t=i.current,r=a.current;if(r!==e){let n=s.current,o=h(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),p(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=h(i.current).includes(e.animationName);if(e.target===o&&n&&(c("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(s.current=h(i.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{e&&(i.current=getComputedStyle(e)),l(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=i(o.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:a}):null};function h(e){return(null==e?void 0:e.animationName)||"none"}function v(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}m.displayName="Presence";var w=n.createContext(void 0);function g(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var y="ScrollArea",[b,S]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),i=r.length;r=[...r,o];let s=t=>{let{scope:r,children:o,...s}=t,u=r?.[e]?.[i]||l,c=n.useMemo(()=>s,Object.values(s));return(0,a.jsx)(u.Provider,{value:c,children:o})};return s.displayName=t+"Provider",[s,function(r,a){let s=a?.[e]?.[i]||l,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(y),[E,C]=b(y),x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:o="hover",dir:l,scrollHideDelay:s=600,...u}=e,[c,d]=n.useState(null),[p,m]=n.useState(null),[h,v]=n.useState(null),[g,y]=n.useState(null),[b,S]=n.useState(null),[C,x]=n.useState(0),[T,N]=n.useState(0),[R,L]=n.useState(!1),[P,_]=n.useState(!1),j=i(t,e=>d(e)),A=function(e){let t=n.useContext(w);return e||t||"ltr"}(l);return(0,a.jsx)(E,{scope:r,type:o,dir:A,scrollHideDelay:s,scrollArea:c,viewport:p,onViewportChange:m,content:h,onContentChange:v,scrollbarX:g,onScrollbarXChange:y,scrollbarXEnabled:R,onScrollbarXEnabledChange:L,scrollbarY:b,onScrollbarYChange:S,scrollbarYEnabled:P,onScrollbarYEnabledChange:_,onCornerWidthChange:x,onCornerHeightChange:N,children:(0,a.jsx)(f.div,{dir:A,...u,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});x.displayName=y;var T="ScrollAreaViewport",N=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:o,nonce:l,...s}=e,u=C(T,r),c=i(t,n.useRef(null),u.onViewportChange);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,a.jsx)(f.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,a.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});N.displayName=T;var R="ScrollAreaScrollbar",L=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=C(R,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=l,u="horizontal"===e.orientation;return n.useEffect(()=>(u?i(!0):s(!0),()=>{u?i(!1):s(!1)}),[u,i,s]),"hover"===l.type?(0,a.jsx)(P,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,a.jsx)(_,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,a.jsx)(j,{...o,ref:t,forceMount:r}):"always"===l.type?(0,a.jsx)(A,{...o,ref:t}):null});L.displayName=R;var P=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=C(R,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,a.jsx)(m,{present:r||i,children:(0,a.jsx)(j,{"data-state":i?"visible":"hidden",...o,ref:t})})}),_=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,i=C(R,e.__scopeScrollArea),s="horizontal"===e.orientation,u=Z(()=>d("SCROLL_END"),100),[c,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>d("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,i.scrollHideDelay,d]),n.useEffect(()=>{let e=i.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(d("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,s,d,u]),(0,a.jsx)(m,{present:o||"hidden"!==c,children:(0,a.jsx)(A,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:t,onPointerEnter:g(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:g(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),j=n.forwardRef((e,t)=>{let r=C(R,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,c=Z(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return J(r.viewport,c),J(r.content,c),(0,a.jsx)(m,{present:o||i,children:(0,a.jsx)(A,{"data-state":i?"visible":"hidden",...l,ref:t})})}),A=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=C(R,e.__scopeScrollArea),i=n.useRef(null),s=n.useRef(0),[u,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=X(u.viewport,u.content),f={...o,sizes:u,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=Y(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return K([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,u,t)}return"horizontal"===r?(0,a.jsx)(O,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=B(l.viewport.scrollLeft,u,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,a.jsx)(D,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=B(l.viewport.scrollTop,u);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),O=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,s=C(R,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=i(t,d,s.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,a.jsx)(W,{"data-orientation":"horizontal",...l,ref:f,sizes:r,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Y(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:$(u.paddingLeft),paddingEnd:$(u.paddingRight)}})}})}),D=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,s=C(R,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=i(t,d,s.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,a.jsx)(W,{"data-orientation":"vertical",...l,ref:f,sizes:r,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Y(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:$(u.paddingTop),paddingEnd:$(u.paddingBottom)}})}})}),[M,I]=b(R),W=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:o,hasThumb:l,onThumbChange:s,onThumbPointerUp:u,onThumbPointerDown:c,onThumbPositionChange:d,onDragScroll:p,onWheelScroll:m,onResize:h,...w}=e,y=C(R,r),[b,S]=n.useState(null),E=i(t,e=>S(e)),x=n.useRef(null),T=n.useRef(""),N=y.viewport,L=o.content-o.viewport,P=v(m),_=v(d),j=Z(h,10);function A(e){x.current&&p({x:e.clientX-x.current.left,y:e.clientY-x.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==b?void 0:b.contains(t))&&P(e,L)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[N,b,L,P]),n.useEffect(_,[o,_]),J(b,j),J(y.content,j),(0,a.jsx)(M,{scope:r,scrollbar:b,hasThumb:l,onThumbChange:v(s),onThumbPointerUp:v(u),onThumbPositionChange:_,onThumbPointerDown:v(c),children:(0,a.jsx)(f.div,{...w,ref:E,style:{position:"absolute",...w.style},onPointerDown:g(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),x.current=b.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),A(e))}),onPointerMove:g(e.onPointerMove,A),onPointerUp:g(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,y.viewport&&(y.viewport.style.scrollBehavior=""),x.current=null})})})}),k="ScrollAreaThumb",U=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=I(k,e.__scopeScrollArea);return(0,a.jsx)(m,{present:r||o.hasThumb,children:(0,a.jsx)(V,{ref:t,...n})})}),V=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:o,...l}=e,s=C(k,r),u=I(k,r),{onThumbPositionChange:c}=u,d=i(t,e=>u.onThumbChange(e)),p=n.useRef(void 0),m=Z(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{m(),p.current||(p.current=q(e,c),c())};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,m,c]),(0,a.jsx)(f.div,{"data-state":u.hasThumb?"visible":"hidden",...l,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:g(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:g(e.onPointerUp,u.onThumbPointerUp)})});U.displayName=k;var H="ScrollAreaCorner",z=n.forwardRef((e,t)=>{let r=C(H,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,a.jsx)(F,{...e,ref:t}):null});z.displayName=H;var F=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...o}=e,l=C(H,r),[i,s]=n.useState(0),[u,c]=n.useState(0),d=!!(i&&u);return J(l.scrollbarX,()=>{var e;let t=(null===(e=l.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;l.onCornerHeightChange(t),c(t)}),J(l.scrollbarY,()=>{var e;let t=(null===(e=l.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;l.onCornerWidthChange(t),s(t)}),d?(0,a.jsx)(f.div,{...o,ref:t,style:{width:i,height:u,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function $(e){return e?parseInt(e,10):0}function X(e,t){let r=e/t;return isNaN(r)?0:r}function Y(e){let t=X(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function B(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=Y(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,i]:[-1*i,0]);return K([0,i],[0,l-n])(a)}function K(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var q=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function Z(e,t){let r=v(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function J(e,t){let r=v(t);p(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var G=x,Q=N,ee=z},22436:(e,t,r)=>{var n=r(12115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=n.useState,i=n.useEffect,a=n.useLayoutEffect,s=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=l({inst:{value:r,getSnapshot:t}}),o=n[0].inst,c=n[1];return a(function(){o.value=r,o.getSnapshot=t,u(o)&&c({inst:o})},[e,r,t]),i(function(){return u(o)&&c({inst:o}),e(function(){u(o)&&c({inst:o})})},[e]),s(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},49033:(e,t,r)=>{e.exports=r(22436)},51362:(e,t,r)=>{r.d(t,{D:()=>u,N:()=>c});var n=r(12115),o=(e,t,r,n,o,l,i,a)=>{let s=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&l?o.map(e=>l[e]||e):o;r?(s.classList.remove(...n),s.classList.add(l&&l[t]?l[t]:t)):s.setAttribute(e,t)}),r=t,a&&u.includes(r)&&(s.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},l=["light","dark"],i="(prefers-color-scheme: dark)",a=n.createContext(void 0),s={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(a))?e:s},c=e=>n.useContext(a)?n.createElement(n.Fragment,null,e.children):n.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:c=d,defaultTheme:f=o?"system":"light",attribute:w="data-theme",value:g,children:y,nonce:b,scriptProps:S}=e,[E,C]=n.useState(()=>m(u,f)),[x,T]=n.useState(()=>"system"===E?v():E),N=g?Object.values(g):c,R=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=v());let n=g?g[t]:t,i=r?h(b):null,a=document.documentElement,u=e=>{"class"===e?(a.classList.remove(...N),n&&a.classList.add(n)):e.startsWith("data-")&&(n?a.setAttribute(e,n):a.removeAttribute(e))};if(Array.isArray(w)?w.forEach(u):u(w),s){let e=l.includes(f)?f:null,r=l.includes(t)?t:e;a.style.colorScheme=r}null==i||i()},[b]),L=n.useCallback(e=>{let t="function"==typeof e?e(E):e;C(t);try{localStorage.setItem(u,t)}catch(e){}},[E]),P=n.useCallback(e=>{T(v(e)),"system"===E&&o&&!t&&R("system")},[E,t]);n.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(P),P(e),()=>e.removeListener(P)},[P]),n.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?C(e.newValue):L(f))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[L]),n.useEffect(()=>{R(null!=t?t:E)},[t,E]);let _=n.useMemo(()=>({theme:E,setTheme:L,forcedTheme:t,resolvedTheme:"system"===E?x:E,themes:o?[...c,"system"]:c,systemTheme:o?x:void 0}),[E,L,t,x,o,c]);return n.createElement(a.Provider,{value:_},n.createElement(p,{forcedTheme:t,storageKey:u,attribute:w,enableSystem:o,enableColorScheme:s,defaultTheme:f,value:g,themes:c,nonce:b,scriptProps:S}),y)},p=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:l,enableSystem:i,enableColorScheme:a,defaultTheme:s,value:u,themes:c,nonce:d,scriptProps:f}=e,p=JSON.stringify([l,r,s,t,c,u,i,a]).slice(1,-1);return n.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),m=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},h=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},53166:(e,t,r)=>{r.d(t,{H4:()=>T,_V:()=>x,bL:()=>C});var n=r(12115),o=r(95155),l=globalThis?.document?n.useLayoutEffect:()=>{};function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(47650);var a=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var l;let e,a;let s=(l=r,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(u.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...i}=e,a=n.Children.toArray(l),u=a.find(s);if(u){let e=u.props.children,l=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...i,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?r:t,{...i,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),c=r(49033);function d(){return()=>{}}var f="Avatar",[p,m]=function(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return l.scopeName=e,[function(t,l){let i=n.createContext(l),a=r.length;r=[...r,l];let s=t=>{let{scope:r,children:l,...s}=t,u=r?.[e]?.[a]||i,c=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:l})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(l,...t)]}(f),[h,v]=p(f),w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...l}=e,[i,a]=n.useState("idle");return(0,o.jsx)(h,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,o.jsx)(u.span,{...l,ref:t})})});w.displayName=f;var g="AvatarImage",y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:i,onLoadingStatusChange:a=()=>{},...s}=e,f=v(g,r),p=function(e,t){let{referrerPolicy:r,crossOrigin:o}=t,i=(0,c.useSyncExternalStore)(d,()=>!0,()=>!1),a=n.useRef(null),s=i?(a.current||(a.current=new window.Image),a.current):null,[u,f]=n.useState(()=>E(s,e));return l(()=>{f(E(s,e))},[s,e]),l(()=>{let e=e=>()=>{f(e)};if(!s)return;let t=e("loaded"),n=e("error");return s.addEventListener("load",t),s.addEventListener("error",n),r&&(s.referrerPolicy=r),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",t),s.removeEventListener("error",n)}},[s,o,r]),u}(i,s),m=function(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}(e=>{a(e),f.onImageLoadingStatusChange(e)});return l(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,o.jsx)(u.img,{...s,ref:t,src:i}):null});y.displayName=g;var b="AvatarFallback",S=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:l,...i}=e,a=v(b,r),[s,c]=n.useState(void 0===l);return n.useEffect(()=>{if(void 0!==l){let e=window.setTimeout(()=>c(!0),l);return()=>window.clearTimeout(e)}},[l]),s&&"loaded"!==a.imageLoadingStatus?(0,o.jsx)(u.span,{...i,ref:t}):null});function E(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}S.displayName=b;var C=w,x=y,T=S}}]);