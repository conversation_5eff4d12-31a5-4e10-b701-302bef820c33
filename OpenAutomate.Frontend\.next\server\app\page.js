(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31568:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});var n=t(43210),o=t(86522);function s(){let e=(0,n.useContext)(o.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},33873:e=>{"use strict";e.exports=require("path")},46867:(e,r,t)=>{Promise.resolve().then(t.bind(t,75694))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75694:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var n=t(60687);t(43210);var o=t(16189),s=t(31568),a=t(4344);function i(){(0,o.useRouter)();let{isAuthenticated:e,isLoading:r,isSystemAdmin:t}=(0,s.A)();return(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"}),(0,n.jsx)("h1",{className:"text-xl font-medium",children:a.$.app.name}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Redirecting..."})]})})}},79435:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.default,__next_app__:()=>u,pages:()=>p,routeModule:()=>l,tree:()=>d});var n=t(65239),o=t(48088),s=t(31369),a=t(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);t.d(r,i);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}],p=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},l=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93723:(e,r,t)=>{Promise.resolve().then(t.bind(t,21204))}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,7966,6763],()=>t(79435));module.exports=n})();