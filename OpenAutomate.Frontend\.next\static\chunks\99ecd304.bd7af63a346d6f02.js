"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5014],{45224:(e,t,r)=>{r.d(t,{createChat:()=>nj});var n,a,s,i,o,l,c,u,h,p,d,f,g,m,b,_,v,k,y,w,E,C,A,x,S,D,L,R,N,M,T,I,F=r(25744),z=r(47896),q=r(23240),O=r(90718);let B={webhookUrl:"http://localhost:5678",webhookConfig:{method:"POST",headers:{}},target:"#n8n-chat",mode:"window",loadPreviousSession:!0,chatInputKey:"chatInput",chatSessionKey:"sessionId",defaultLanguage:"en",showWelcomeScreen:!1,initialMessages:["Hi there! \uD83D\uDC4B","My name is <PERSON>. How can I assist you today?"],i18n:{en:{title:"Hi there! \uD83D\uDC4B",subtitle:"Start a chat. We're here to help you 24/7.",footer:"",getStarted:"New Conversation",inputPlaceholder:"Type your question..",closeButtonTooltip:"Close chat"}},theme:{}},P="n8n-chat/sessionId",$="Chat",j="ChatOptions";for(var U=[],H=0;H<256;++H)U.push((H+256).toString(16).slice(1));var Z,V=new Uint8Array(16);let G={randomUUID:"u">typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function W(e,t,r){if(G.randomUUID&&!e)return G.randomUUID();var n=(e=e||{}).random||(e.rng||function(){if(!Z&&!(Z="u">typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Z(V)})();return n[6]=15&n[6]|64,n[8]=63&n[8]|128,function(e,t=0){return(U[e[t+0]]+U[e[t+1]]+U[e[t+2]]+U[e[t+3]]+"-"+U[e[t+4]]+U[e[t+5]]+"-"+U[e[t+6]]+U[e[t+7]]+"-"+U[e[t+8]]+U[e[t+9]]+"-"+U[e[t+10]]+U[e[t+11]]+U[e[t+12]]+U[e[t+13]]+U[e[t+14]]+U[e[t+15]]).toLowerCase()}(n)}async function X(){return""}async function K(...e){var t,r;let n=await X(),a=null==(t=e[1])?void 0:t.body,s={...n?{authorization:`Bearer ${n}`}:{},...null==(r=e[1])?void 0:r.headers};return a instanceof FormData?delete s["Content-Type"]:s["Content-Type"]="application/json",await (await fetch(e[0],{...e[1],mode:"cors",cache:"no-cache",headers:s})).json()}async function Q(e,t={},r={}){let n=e;return Object.keys(t).length>0&&(n=`${n}?${new URLSearchParams(t).toString()}`),await K(n,{...r,method:"GET"})}async function J(e,t={},r={}){return await K(e,{...r,method:"POST",body:JSON.stringify(t)})}async function Y(e,t={},r=[],n={}){let a=new FormData;for(let e in t)a.append(e,t[e]);for(let e of r)a.append("files",e);return await K(e,{...n,method:"POST",body:a})}async function ee(e,t){var r,n;return await ((null==(r=t.webhookConfig)?void 0:r.method)==="POST"?J:Q)(`${t.webhookUrl}`,{action:"loadPreviousSession",[t.chatSessionKey]:e,...t.metadata?{metadata:t.metadata}:{}},{headers:null==(n=t.webhookConfig)?void 0:n.headers})}async function et(e,t,r,n){var a,s,i;return t.length>0?await Y(`${n.webhookUrl}`,{action:"sendMessage",[n.chatSessionKey]:r,[n.chatInputKey]:e,...n.metadata?{metadata:n.metadata}:{}},t,{headers:null==(a=n.webhookConfig)?void 0:a.headers}):await ((null==(s=n.webhookConfig)?void 0:s.method)==="POST"?J:Q)(`${n.webhookUrl}`,{action:"sendMessage",[n.chatSessionKey]:r,[n.chatInputKey]:e,...n.metadata?{metadata:n.metadata}:{}},{headers:null==(i=n.webhookConfig)?void 0:i.headers})}let er=function(){let e=new Map;function t(t,r){let n=e.get(t);n&&n.splice(n.indexOf(r)>>>0,1)}return{on:function(r,n){let a=e.get(r);return a?a.push(n):a=[n],e.set(r,a),()=>t(r,n)},off:t,emit:function(t,r){let n=e.get(t);n&&n.slice().forEach(async e=>{await e(r)})}}}(),en={install(e,t){e.provide(j,t);let r=(0,F.KR)([]),n=(0,F.KR)(null),a=(0,F.KR)(!1);async function s(e,i=[]){let o={id:W(),text:e,sender:"user",files:i};r.value.push(o),a.value=!0,(0,z.dY)(()=>{er.emit("scrollToBottom")});let l=await et(e,i,n.value,t),c=l.output??l.text??"";if(""===c&&Object.keys(l).length>0)try{c=JSON.stringify(l,null,2)}catch{}let u={id:W(),text:c,sender:"bot"};r.value.push(u),a.value=!1,(0,z.dY)(()=>{er.emit("scrollToBottom")})}async function i(){if(!t.loadPreviousSession)return;let e=localStorage.getItem(P)??W(),a=await ee(e,t);return r.value=((null==a?void 0:a.data)||[]).map((e,t)=>({id:`${t}`,text:e.kwargs.content,sender:e.id.includes("HumanMessage")?"user":"bot"})),r.value.length&&(n.value=e),e}async function o(){n.value=W(),localStorage.setItem(P,n.value)}let l={initialMessages:(0,z.EW)(()=>(t.initialMessages??[]).map(e=>({id:W(),text:e,sender:"bot"}))),messages:r,currentSessionId:n,waitingForResponse:a,loadPreviousSession:i,startNewSession:o,sendMessage:s};e.provide($,l),e.config.globalProperties.$chat=l}};function ea(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}let es=ea(function(){if(tb)return tm;tb=1;class e{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function t(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function r(e,...t){let n=Object.create(null);for(let t in e)n[t]=e[t];return t.forEach(function(e){for(let t in e)n[t]=e[t]}),n}let n=e=>!!e.scope,a=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){let r=e.split(".");return[`${t}${r.shift()}`,...r.map((e,t)=>`${e}${"_".repeat(t+1)}`)].join(" ")}return`${t}${e}`};class s{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=t(e)}openNode(e){if(!n(e))return;let t=a(e.scope,{prefix:this.classPrefix});this.span(t)}closeNode(e){n(e)&&(this.buffer+="</span>")}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}let i=(e={})=>{let t={children:[]};return Object.assign(t,e),t};class o{constructor(){this.rootNode=i(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){let t=i({scope:e});this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return"string"==typeof t?e.addText(t):t.children&&(e.openNode(t),t.children.forEach(t=>this._walk(e,t)),e.closeNode(t)),e}static _collapse(e){"string"!=typeof e&&e.children&&(e.children.every(e=>"string"==typeof e)?e.children=[e.children.join("")]:e.children.forEach(e=>{o._collapse(e)}))}}class l extends o{constructor(e){super(),this.options=e}addText(e){""!==e&&this.add(e)}startScope(e){this.openNode(e)}endScope(){this.closeNode()}__addSublanguage(e,t){let r=e.root;t&&(r.scope=`language:${t}`),this.add(r)}toHTML(){return new s(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function c(e){return e?"string"==typeof e?e:e.source:null}function u(e){return d("(?=",e,")")}function h(e){return d("(?:",e,")*")}function p(e){return d("(?:",e,")?")}function d(...e){return e.map(e=>c(e)).join("")}function f(...e){return"("+(function(e){let t=e[e.length-1];return"object"==typeof t&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}(e).capture?"":"?:")+e.map(e=>c(e)).join("|")+")"}function g(e){return RegExp(e.toString()+"|").exec("").length-1}let m=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function b(e,{joinWith:t}){let r=0;return e.map(e=>{let t=r+=1,n=c(e),a="";for(;n.length>0;){let e=m.exec(n);if(!e){a+=n;break}a+=n.substring(0,e.index),n=n.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?a+="\\"+String(Number(e[1])+t):(a+=e[0],"("===e[0]&&r++)}return a}).map(e=>`(${e})`).join(t)}let _="[a-zA-Z]\\w*",v="[a-zA-Z_]\\w*",k="\\b\\d+(\\.\\d+)?",y="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",w="\\b(0b[01]+)",E={begin:"\\\\[\\s\\S]",relevance:0},C=function(e,t,n={}){let a=r({scope:"comment",begin:e,end:t,contains:[]},n);a.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});let s=f("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return a.contains.push({begin:d(/[ ]+/,"(",s,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),a},A=C("//","$"),x=C("/\\*","\\*/"),S=C("#","$");var D=Object.freeze({__proto__:null,APOS_STRING_MODE:{scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[E]},BACKSLASH_ESCAPE:E,BINARY_NUMBER_MODE:{scope:"number",begin:w,relevance:0},BINARY_NUMBER_RE:w,COMMENT:C,C_BLOCK_COMMENT_MODE:x,C_LINE_COMMENT_MODE:A,C_NUMBER_MODE:{scope:"number",begin:y,relevance:0},C_NUMBER_RE:y,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})},HASH_COMMENT_MODE:S,IDENT_RE:_,MATCH_NOTHING_RE:/\b\B/,METHOD_GUARD:{begin:"\\.\\s*"+v,relevance:0},NUMBER_MODE:{scope:"number",begin:k,relevance:0},NUMBER_RE:k,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},QUOTE_STRING_MODE:{scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[E]},REGEXP_MODE:{scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[E,{begin:/\[/,end:/\]/,relevance:0,contains:[E]}]},RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(e={})=>{let t=/^#![ ]*\//;return e.binary&&(e.begin=d(t,/.*\b/,e.binary,/\b.*/)),r({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{0!==e.index&&t.ignoreMatch()}},e)},TITLE_MODE:{scope:"title",begin:_,relevance:0},UNDERSCORE_IDENT_RE:v,UNDERSCORE_TITLE_MODE:{scope:"title",begin:v,relevance:0}});function L(e,t){"."===e.input[e.index-1]&&t.ignoreMatch()}function R(e,t){void 0!==e.className&&(e.scope=e.className,delete e.className)}function N(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=L,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance&&(e.relevance=0))}function M(e,t){Array.isArray(e.illegal)&&(e.illegal=f(...e.illegal))}function T(e,t){if(e.match){if(e.begin||e.end)throw Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function I(e,t){void 0===e.relevance&&(e.relevance=1)}let F=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw Error("beforeMatch cannot be used with starts");let r=Object.assign({},e);Object.keys(e).forEach(t=>{delete e[t]}),e.keywords=r.keywords,e.begin=d(r.beforeMatch,u(r.begin)),e.starts={relevance:0,contains:[Object.assign(r,{endsParent:!0})]},e.relevance=0,delete r.beforeMatch},z=["of","and","for","in","not","or","if","then","parent","list","value"],q={},O=e=>{console.error(e)},B=(e,...t)=>{console.log(`WARN: ${e}`,...t)},P=(e,t)=>{q[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),q[`${e}/${t}`]=!0)},$=Error();function j(e,t,{key:r}){let n=0,a=e[r],s={},i={};for(let e=1;e<=t.length;e++)i[e+n]=a[e],s[e+n]=!0,n+=g(t[e-1]);e[r]=i,e[r]._emit=s,e[r]._multi=!0}function U(e){e.scope&&"object"==typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope),"string"==typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"==typeof e.endScope&&(e.endScope={_wrap:e.endScope}),function(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw O("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),$;if("object"!=typeof e.beginScope||null===e.beginScope)throw O("beginScope must be object"),$;j(e,e.begin,{key:"beginScope"}),e.begin=b(e.begin,{joinWith:""})}}(e),function(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw O("skip, excludeEnd, returnEnd not compatible with endScope: {}"),$;if("object"!=typeof e.endScope||null===e.endScope)throw O("endScope must be object"),$;j(e,e.end,{key:"endScope"}),e.end=b(e.end,{joinWith:""})}}(e)}class H extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}let Z=Symbol("nomatch"),V=function(n){let a=Object.create(null),s=Object.create(null),i=[],o=!0,m="Could not find the language '{}', did you forget to load/include a language module?",_={disableAutodetect:!0,name:"Plain text",contains:[]},v={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:l};function k(e){return v.noHighlightRe.test(e)}function y(e,t,r){let n="",a="";"object"==typeof t?(n=e,r=t.ignoreIllegals,a=t.language):(P("10.7.0","highlight(lang, code, ...args) has been deprecated."),P("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),a=e,n=t),void 0===r&&(r=!0);let s={code:n,language:a};$("before:highlight",s);let i=s.result?s.result:w(s.language,s.code,r);return i.code=s.code,$("after:highlight",i),i}function w(n,s,i,l){let u=Object.create(null);function h(){if(!D.keywords){q.addText(B);return}let e=0;D.keywordPatternRe.lastIndex=0;let t=D.keywordPatternRe.exec(B),r="";for(;t;){r+=B.substring(e,t.index);let n=C.case_insensitive?t[0].toLowerCase():t[0],a=D.keywords[n];if(a){let[e,s]=a;if(q.addText(r),r="",u[n]=(u[n]||0)+1,u[n]<=7&&(P+=s),e.startsWith("_"))r+=t[0];else{let r=C.classNameAliases[e]||e;d(t[0],r)}}else r+=t[0];e=D.keywordPatternRe.lastIndex,t=D.keywordPatternRe.exec(B)}r+=B.substring(e),q.addText(r)}function p(){null!=D.subLanguage?function(){if(""===B)return;let e=null;if("string"==typeof D.subLanguage){if(!a[D.subLanguage]){q.addText(B);return}e=w(D.subLanguage,B,!0,L[D.subLanguage]),L[D.subLanguage]=e._top}else e=E(B,D.subLanguage.length?D.subLanguage:null);D.relevance>0&&(P+=e.relevance),q.__addSublanguage(e._emitter,e.language)}():h(),B=""}function d(e,t){""!==e&&(q.startScope(t),q.addText(e),q.endScope())}function f(e,t){let r=1,n=t.length-1;for(;r<=n;){if(!e._emit[r]){r++;continue}let n=C.classNameAliases[e[r]]||e[r],a=t[r];n?d(a,n):(B=a,h(),B=""),r++}}function _(e,t){return e.scope&&"string"==typeof e.scope&&q.openNode(C.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(d(B,C.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),B=""):e.beginScope._multi&&(f(e.beginScope,t),B="")),D=Object.create(e,{parent:{value:D}})}let k={};function y(t,r){let a=r&&r[0];if(B+=t,null==a)return p(),0;if("begin"===k.type&&"end"===r.type&&k.index===r.index&&""===a){if(B+=s.slice(r.index,r.index+1),!o){let e=Error(`0 width match regex (${n})`);throw e.languageName=n,e.badRule=k.rule,e}return 1}if(k=r,"begin"===r.type)return function(t){let r=t[0],n=t.rule,a=new e(n);for(let e of[n.__beforeBegin,n["on:begin"]])if(e&&(e(t,a),a.isMatchIgnored))return 0===D.matcher.regexIndex?(B+=r[0],1):(H=!0,0);return n.skip?B+=r:(n.excludeBegin&&(B+=r),p(),n.returnBegin||n.excludeBegin||(B=r)),_(n,t),n.returnBegin?0:r.length}(r);if("illegal"!==r.type||i){if("end"===r.type){let t=function(t){let r=t[0],n=s.substring(t.index),a=function t(r,n,a){let s=function(e,t){let r=e&&e.exec(t);return r&&0===r.index}(r.endRe,a);if(s){if(r["on:end"]){let t=new e(r);r["on:end"](n,t),t.isMatchIgnored&&(s=!1)}if(s){for(;r.endsParent&&r.parent;)r=r.parent;return r}}if(r.endsWithParent)return t(r.parent,n,a)}(D,t,n);if(!a)return Z;let i=D;D.endScope&&D.endScope._wrap?(p(),d(r,D.endScope._wrap)):D.endScope&&D.endScope._multi?(p(),f(D.endScope,t)):i.skip?B+=r:(i.returnEnd||i.excludeEnd||(B+=r),p(),i.excludeEnd&&(B=r));do D.scope&&q.closeNode(),D.skip||D.subLanguage||(P+=D.relevance),D=D.parent;while(D!==a.parent);return a.starts&&_(a.starts,t),i.returnEnd?0:r.length}(r);if(t!==Z)return t}}else{let e=Error('Illegal lexeme "'+a+'" for mode "'+(D.scope||"<unnamed>")+'"');throw e.mode=D,e}if("illegal"===r.type&&""===a)return 1;if(j>1e5&&j>3*r.index)throw Error("potential infinite loop, way more iterations than matches");return B+=a,a.length}let C=S(n);if(!C)throw O(m.replace("{}",n)),Error('Unknown language: "'+n+'"');let A=function(e){function t(t,r){return RegExp(c(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(r?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,t){t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),this.matchAt+=g(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);let e=this.regexes.map(e=>e[1]);this.matcherRe=t(b(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;let t=this.matcherRe.exec(e);if(!t)return null;let r=t.findIndex((e,t)=>t>0&&void 0!==e),n=this.matchIndexes[r];return t.splice(0,r),Object.assign(t,n)}}class a{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];let t=new n;return this.rules.slice(e).forEach(([e,r])=>t.addRule(e,r)),t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){let t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex;let r=t.exec(e);if(this.resumingScanAtSamePosition()&&!(r&&r.index===this.lastIndex)){let t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,r=t.exec(e)}return r&&(this.regexIndex+=r.position+1,this.regexIndex===this.count&&this.considerAll()),r}}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=r(e.classNameAliases||{}),function n(s,i){if(s.isCompiled)return s;[R,T,U,F].forEach(e=>e(s,i)),e.compilerExtensions.forEach(e=>e(s,i)),s.__beforeBegin=null,[N,M,I].forEach(e=>e(s,i)),s.isCompiled=!0;let o=null;return"object"==typeof s.keywords&&s.keywords.$pattern&&(s.keywords=Object.assign({},s.keywords),o=s.keywords.$pattern,delete s.keywords.$pattern),o=o||/\w+/,s.keywords&&(s.keywords=function e(t,r,n="keyword"){let a=Object.create(null);return"string"==typeof t?s(n,t.split(" ")):Array.isArray(t)?s(n,t):Object.keys(t).forEach(function(n){Object.assign(a,e(t[n],r,n))}),a;function s(e,t){r&&(t=t.map(e=>e.toLowerCase())),t.forEach(function(t){var r,n,s;let i=t.split("|");a[i[0]]=[e,(r=i[0],(n=i[1])?Number(n):+(s=r,!z.includes(s.toLowerCase())))]})}}(s.keywords,e.case_insensitive)),s.keywordPatternRe=t(o,!0),i&&(s.begin||(s.begin=/\B|\b/),s.beginRe=t(s.begin),s.end||s.endsWithParent||(s.end=/\B|\b/),s.end&&(s.endRe=t(s.end)),s.terminatorEnd=c(s.end)||"",s.endsWithParent&&i.terminatorEnd&&(s.terminatorEnd+=(s.end?"|":"")+i.terminatorEnd)),s.illegal&&(s.illegalRe=t(s.illegal)),s.contains||(s.contains=[]),s.contains=[].concat(...s.contains.map(function(e){var t;return(t="self"===e?s:e).variants&&!t.cachedVariants&&(t.cachedVariants=t.variants.map(function(e){return r(t,{variants:null},e)})),t.cachedVariants?t.cachedVariants:!function e(t){return!!t&&(t.endsWithParent||e(t.starts))}(t)?Object.isFrozen(t)?r(t):t:r(t,{starts:t.starts?r(t.starts):null})})),s.contains.forEach(function(e){n(e,s)}),s.starts&&n(s.starts,i),s.matcher=function(e){let t=new a;return e.contains.forEach(e=>t.addRule(e.begin,{rule:e,type:"begin"})),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t}(s),s}(e)}(C),x="",D=l||A,L={},q=new v.__emitter(v);!function(){let e=[];for(let t=D;t!==C;t=t.parent)t.scope&&e.unshift(t.scope);e.forEach(e=>q.openNode(e))}();let B="",P=0,$=0,j=0,H=!1;try{if(C.__emitTokens)C.__emitTokens(s,q);else{for(D.matcher.considerAll();;){j++,H?H=!1:D.matcher.considerAll(),D.matcher.lastIndex=$;let e=D.matcher.exec(s);if(!e)break;let t=s.substring($,e.index),r=y(t,e);$=e.index+r}y(s.substring($))}return q.finalize(),x=q.toHTML(),{language:n,value:x,relevance:P,illegal:!1,_emitter:q,_top:D}}catch(e){if(e.message&&e.message.includes("Illegal"))return{language:n,value:t(s),illegal:!0,relevance:0,_illegalBy:{message:e.message,index:$,context:s.slice($-100,$+100),mode:e.mode,resultSoFar:x},_emitter:q};if(o)return{language:n,value:t(s),illegal:!1,relevance:0,errorRaised:e,_emitter:q,_top:D};throw e}}function E(e,r){r=r||v.languages||Object.keys(a);let n=function(e){let r={value:t(e),illegal:!1,relevance:0,_top:_,_emitter:new v.__emitter(v)};return r._emitter.addText(e),r}(e),s=r.filter(S).filter(q).map(t=>w(t,e,!1));s.unshift(n);let[i,o]=s.sort((e,t)=>{if(e.relevance!==t.relevance)return t.relevance-e.relevance;if(e.language&&t.language){if(S(e.language).supersetOf===t.language)return 1;if(S(t.language).supersetOf===e.language)return -1}return 0});return i.secondBest=o,i}function C(e){let t=null,r=function(e){let t=e.className+" ";t+=e.parentNode?e.parentNode.className:"";let r=v.languageDetectRe.exec(t);if(r){let t=S(r[1]);return t||(B(m.replace("{}",r[1])),B("Falling back to no-highlight mode for this block.",e)),t?r[1]:"no-highlight"}return t.split(/\s+/).find(e=>k(e)||S(e))}(e);if(k(r))return;if($("before:highlightElement",{el:e,language:r}),e.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",e);return}if(e.children.length>0&&(v.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(e)),v.throwUnescapedHTML))throw new H("One of your code blocks includes unescaped HTML.",e.innerHTML);let n=e.textContent,a=r?y(n,{language:r,ignoreIllegals:!0}):E(n);e.innerHTML=a.value,e.dataset.highlighted="yes",function(e,t,r){let n=t&&s[t]||r;e.classList.add("hljs"),e.classList.add(`language-${n}`)}(e,r,a.language),e.result={language:a.language,re:a.relevance,relevance:a.relevance},a.secondBest&&(e.secondBest={language:a.secondBest.language,relevance:a.secondBest.relevance}),$("after:highlightElement",{el:e,result:a,text:n})}let A=!1;function x(){if("loading"===document.readyState){A=!0;return}document.querySelectorAll(v.cssSelector).forEach(C)}function S(e){return a[e=(e||"").toLowerCase()]||a[s[e]]}function L(e,{languageName:t}){"string"==typeof e&&(e=[e]),e.forEach(e=>{s[e.toLowerCase()]=t})}function q(e){let t=S(e);return t&&!t.disableAutodetect}function $(e,t){i.forEach(function(r){r[e]&&r[e](t)})}for(let e in"u">typeof window&&window.addEventListener&&window.addEventListener("DOMContentLoaded",function(){A&&x()},!1),Object.assign(n,{highlight:y,highlightAuto:E,highlightAll:x,highlightElement:C,highlightBlock:function(e){return P("10.7.0","highlightBlock will be removed entirely in v12.0"),P("10.7.0","Please use highlightElement now."),C(e)},configure:function(e){v=r(v,e)},initHighlighting:()=>{x(),P("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){x(),P("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(e,t){let r=null;try{r=t(n)}catch(t){if(O("Language definition for '{}' could not be registered.".replace("{}",e)),o)O(t);else throw t;r=_}r.name||(r.name=e),a[e]=r,r.rawDefinition=t.bind(null,n),r.aliases&&L(r.aliases,{languageName:e})},unregisterLanguage:function(e){for(let t of(delete a[e],Object.keys(s)))s[t]===e&&delete s[t]},listLanguages:function(){return Object.keys(a)},getLanguage:S,registerAliases:L,autoDetection:q,inherit:r,addPlugin:function(e){var t;(t=e)["before:highlightBlock"]&&!t["before:highlightElement"]&&(t["before:highlightElement"]=e=>{t["before:highlightBlock"](Object.assign({block:e.el},e))}),t["after:highlightBlock"]&&!t["after:highlightElement"]&&(t["after:highlightElement"]=e=>{t["after:highlightBlock"](Object.assign({block:e.el},e))}),i.push(e)},removePlugin:function(e){let t=i.indexOf(e);-1!==t&&i.splice(t,1)}}),n.debugMode=function(){o=!1},n.safeMode=function(){o=!0},n.versionString="11.9.0",n.regex={concat:d,lookahead:u,either:f,optional:p,anyNumberOfTimes:h},D)"object"==typeof D[e]&&function e(t){return t instanceof Map?t.clear=t.delete=t.set=function(){throw Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=function(){throw Error("set is read-only")}),Object.freeze(t),Object.getOwnPropertyNames(t).forEach(r=>{let n=t[r],a=typeof n;"object"!==a&&"function"!==a||Object.isFrozen(n)||e(n)}),t}(D[e]);return Object.assign(n,D),n},G=V({});return G.newInstance=()=>V({}),tm=G,G.HighlightJS=G,G.default=G,tm}()),ei="[A-Za-z$_][0-9A-Za-z$_]*",eo=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends"],el=["true","false","null","undefined","NaN","Infinity"],ec=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],eu=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],eh=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],ep=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],ed=[].concat(eh,ec,eu);function ef(e){var t;let r=e.regex,n=(e,{after:t})=>{let r="</"+e[0].slice(1);return -1!==e.input.indexOf(r,t)},a=/<[A-Za-z0-9\\._:-]+/,s=/\/[A-Za-z0-9\\._:-]+>|\/>/,i={$pattern:ei,keyword:eo,literal:el,built_in:ed,"variable.language":ep},o="[0-9](_?[0-9])*",l=`\\.(${o})`,c="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",u={className:"number",variants:[{begin:`(\\b(${c})((${l})|\\.)?|(${l}))[eE][+-]?(${o})\\b`},{begin:`\\b(${c})\\b((${l})\\b|\\.)?|(${l})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},h={className:"subst",begin:"\\$\\{",end:"\\}",keywords:i,contains:[]},p={begin:"html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,h],subLanguage:"xml"}},d={begin:"css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,h],subLanguage:"css"}},f={begin:"gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,h],subLanguage:"graphql"}},g={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,h]},m={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:ei+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},b=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,p,d,f,g,{match:/\$\d+/},u];h.contains=b.concat({begin:/\{/,end:/\}/,keywords:i,contains:["self"].concat(b)});let _=[].concat(m,h.contains),v=_.concat([{begin:/\(/,end:/\)/,keywords:i,contains:["self"].concat(_)}]),k={className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:i,contains:v},y={variants:[{match:[/class/,/\s+/,ei,/\s+/,/extends/,/\s+/,r.concat(ei,"(",r.concat(/\./,ei),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,ei],scope:{1:"keyword",3:"title.class"}}]},w={relevance:0,match:r.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...ec,...eu]}},E={match:r.concat(/\b/,(t=[...eh,"super","import"],r.concat("(?!",t.join("|"),")")),ei,r.lookahead(/\(/)),className:"title.function",relevance:0},C={begin:r.concat(/\./,r.lookahead(r.concat(ei,/(?![0-9A-Za-z$_(])/))),end:ei,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},A="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",x={match:[/const|var|let/,/\s+/,ei,/\s*/,/=\s*/,/(async\s*)?/,r.lookahead(A)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[k]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:i,exports:{PARAMS_CONTAINS:v,CLASS_REFERENCE:w},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,p,d,f,g,m,{match:/\$\d+/},u,w,{className:"attr",begin:ei+r.lookahead(":"),relevance:0},x,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[m,e.REGEXP_MODE,{className:"function",begin:A,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:i,contains:v}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:"<>",end:"</>"},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:a,"on:begin":(e,t)=>{let r;let a=e[0].length+e.index,s=e.input[a];if("<"===s||","===s){t.ignoreMatch();return}">"===s&&(n(e,{after:a})||t.ignoreMatch());let i=e.input.substring(a);if((r=i.match(/^\s*=/))||(r=i.match(/^\s+extends\s+/))&&0===r.index){t.ignoreMatch();return}},end:s}],subLanguage:"xml",contains:[{begin:a,end:s,skip:!0,contains:["self"]}]}]},{variants:[{match:[/function/,/\s+/,ei,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[k],illegal:/%/},{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[k,e.inherit(e.TITLE_MODE,{begin:ei,className:"title.function"})]},{match:/\.\.\./,relevance:0},C,{match:"\\$"+ei,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[k]},E,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},y,{match:[/get|set/,/\s+/,ei,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},k]},{match:/\$[(.]/}]}}function eg(e){let t=e.regex,r=t.concat(/[\p{L}_]/u,t.optional(/[\p{L}0-9_.-]*:/u),/[\p{L}0-9_.-]*/u),n={className:"symbol",begin:/&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/},a={begin:/\s/,contains:[{className:"keyword",begin:/#?[a-z_][a-z1-9_-]+/,illegal:/\n/}]},s=e.inherit(a,{begin:/\(/,end:/\)/}),i=e.inherit(e.APOS_STRING_MODE,{className:"string"}),o=e.inherit(e.QUOTE_STRING_MODE,{className:"string"}),l={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:/[\p{L}0-9._:-]+/u,relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[n]},{begin:/'/,end:/'/,contains:[n]},{begin:/[^\s"'=<>`]+/}]}]}]};return{name:"HTML, XML",aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,unicodeRegex:!0,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,relevance:10,contains:[a,o,i,s,{begin:/\[/,end:/\]/,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,contains:[a,s,o,i]}]}]},e.COMMENT(/<!--/,/-->/,{relevance:10}),{begin:/<!\[CDATA\[/,end:/\]\]>/,relevance:10},n,{className:"meta",end:/\?>/,variants:[{begin:/<\?xml/,relevance:10,contains:[o]},{begin:/<\?[a-z][a-z0-9]+/}]},{className:"tag",begin:/<style(?=\s|>)/,end:/>/,keywords:{name:"style"},contains:[l],starts:{end:/<\/style>/,returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:/<script(?=\s|>)/,end:/>/,keywords:{name:"script"},contains:[l],starts:{end:/<\/script>/,returnEnd:!0,subLanguage:["javascript","handlebars","xml"]}},{className:"tag",begin:/<>|<\/>/},{className:"tag",begin:t.concat(/</,t.lookahead(t.concat(r,t.either(/\/>/,/>/,/\s/)))),end:/\/?>/,contains:[{className:"name",begin:r,relevance:0,starts:l}]},{className:"tag",begin:t.concat(/<\//,t.lookahead(t.concat(r,/>/))),contains:[{className:"name",begin:r,relevance:0},{begin:/>/,relevance:0,endsParent:!0}]}]}}let em=(e,t)=>{let r=e.__vccOpts||e;for(let[e,n]of t)r[e]=n;return r},eb={class:"chat-button"},e_=em({},[["render",function(e,t){return(0,z.uX)(),(0,z.CE)("button",eb,[(0,z.RG)(e.$slots,"default")])}]]),ev={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},ek={name:"mdi-close",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",ev,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"},null,-1)]))}};function ey(){return(0,z.WQ)($)}function ew(){return{options:(0,z.WQ)(j)}}function eE(){let{options:e}=ew(),t=(null==e?void 0:e.defaultLanguage)??"en";return{t:function(r){var n,a;let s=null==(a=null==(n=null==e?void 0:e.i18n)?void 0:n[t])?void 0:a[r];return(0,F.i9)(s)?s.value:s??r},te:function(r){var n,a;return!!(null!=(a=null==(n=null==e?void 0:e.i18n)?void 0:n[t])&&a[r])}}}let eC={class:"chat-get-started"},eA=(0,z.pM)({__name:"GetStarted",setup(e){let{t:t}=eE();return(e,r)=>((0,z.uX)(),(0,z.CE)("div",eC,[(0,z.bF)(e_,{onClick:r[0]||(r[0]=t=>e.$emit("click:button"))},{default:(0,z.k6)(()=>[(0,z.eW)((0,q.v_)((0,F.R1)(t)("getStarted")),1)]),_:1})]))}}),ex={class:"chat-powered-by"},eS=em({},[["render",function(e,t){return(0,z.uX)(),(0,z.CE)("div",ex,t[0]||(t[0]=[(0,z.eW)(" Powered by "),(0,z.Lk)("a",{href:"https://n8n.io?utm_source=n8n-external&utm_medium=widget-powered-by"},"n8n",-1)]))}]]),eD={class:"chat-get-started-footer"},eL={key:0},eR=(0,z.pM)({__name:"GetStartedFooter",setup(e){let{t:t,te:r}=eE();return(e,n)=>((0,z.uX)(),(0,z.CE)("div",eD,[(0,F.R1)(r)("footer")?((0,z.uX)(),(0,z.CE)("div",eL,(0,q.v_)((0,F.R1)(t)("footer")),1)):(0,z.Q3)("",!0),(0,z.bF)(eS)]))}});"u">typeof WorkerGlobalScope&&WorkerGlobalScope;let eN=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),eM="u">typeof window&&"u">typeof document?window.document:void 0,eT={multiple:!0,accept:"*",reset:!1,directory:!1},eI={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},eF={name:"mdi-paperclip",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",eI,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M16.5 6v11.5a4 4 0 0 1-4 4a4 4 0 0 1-4-4V5A2.5 2.5 0 0 1 11 2.5A2.5 2.5 0 0 1 13.5 5v10.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1V6H10v9.5a2.5 2.5 0 0 0 2.5 2.5a2.5 2.5 0 0 0 2.5-2.5V5a4 4 0 0 0-4-4a4 4 0 0 0-4 4v12.5a5.5 5.5 0 0 0 5.5 5.5a5.5 5.5 0 0 0 5.5-5.5V6z"},null,-1)]))}},ez={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},eq={name:"mdi-send",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",ez,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"m2 21l21-9L2 3v7l15 2l-15 2z"},null,-1)]))}},eO={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},eB={name:"mdi-closeThick",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",eO,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M20 6.91L17.09 4L12 9.09L6.91 4L4 6.91L9.09 12L4 17.09L6.91 20L12 14.91L17.09 20L20 17.09L14.91 12z"},null,-1)]))}},eP={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},e$={name:"mdi-fileImage",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",eP,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M13 9h5.5L13 3.5zM6 2h8l6 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4c0-1.11.89-2 2-2m0 18h12v-8l-4 4l-2-2zM8 9a2 2 0 0 0-2 2a2 2 0 0 0 2 2a2 2 0 0 0 2-2a2 2 0 0 0-2-2"},null,-1)]))}},ej={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},eU={name:"mdi-fileMusic",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",ej,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8zm-1 11h-2v5a2 2 0 0 1-2 2a2 2 0 0 1-2-2a2 2 0 0 1 2-2c.4 0 .7.1 1 .3V11h3zm0-4V3.5L18.5 9z"},null,-1)]))}},eH={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},eZ={name:"mdi-fileText",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",eH,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M13 9h5.5L13 3.5zM6 2h8l6 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4c0-1.11.89-2 2-2m9 16v-2H6v2zm3-4v-2H6v2z"},null,-1)]))}},eV={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},eG={name:"mdi-fileVideo",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",eV,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M13 9h5.5L13 3.5zM6 2h8l6 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4c0-1.11.89-2 2-2m11 17v-6l-3 2.2V13H7v6h7v-2.2z"},null,-1)]))}},eW={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},eX={name:"mdi-openInNew",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",eW,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M14 3v2h3.59l-9.83 9.83l1.41 1.41L19 6.41V10h2V3m-2 16H5V5h7V3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7h-2z"},null,-1)]))}},eK={class:"chat-file-name"},eQ=em((0,z.pM)({__name:"ChatFile",props:{file:{},isRemovable:{type:Boolean},isPreviewable:{type:Boolean}},emits:["remove"],setup(e,{emit:t}){let r={document:eZ,audio:eU,image:e$,video:eG},n=(0,z.EW)(()=>{var t;return r[null==(t=e.file)?void 0:t.type.split("/")[0]]||eZ});function a(){e.isPreviewable&&window.open(URL.createObjectURL(e.file))}function s(){t("remove",e.file)}return(e,t)=>((0,z.uX)(),(0,z.CE)("div",{class:"chat-file",onClick:a},[(0,z.bF)((0,F.R1)(n)),(0,z.Lk)("p",eK,(0,q.v_)(e.file.name),1),e.isRemovable?((0,z.uX)(),(0,z.CE)("span",{key:0,class:"chat-file-delete",onClick:(0,O.D$)(s,["stop"])},[(0,z.bF)((0,F.R1)(eB))])):e.isPreviewable?((0,z.uX)(),(0,z.Wv)((0,F.R1)(eX),{key:1,class:"chat-file-preview"})):(0,z.Q3)("",!0)]))}}),[["__scopeId","data-v-e0d57af7"]]),eJ={class:"chat-inputs"},eY={key:0,class:"chat-input-left-panel"},e0=["disabled","placeholder"],e1={class:"chat-inputs-controls"},e2=["disabled"],e3=["disabled"],e5={key:0,class:"chat-files"},e4=em((0,z.pM)({__name:"Input",props:{placeholder:{default:"inputPlaceholder"}},emits:["arrowKeyDown"],setup(e,{emit:t}){let{t:r}=eE(),{options:n}=ew(),a=ey(),{waitingForResponse:s}=a,i=(0,F.KR)(null),o=(0,F.KR)(null),l=(0,F.KR)(""),c=(0,F.KR)(!1),u=(0,F.KR)(null),h=(0,z.EW)(()=>{var e;return""===l.value||(0,F.R1)(s)||(null==(e=n.disabled)?void 0:e.value)===!0}),p=(0,z.EW)(()=>{var e;return(null==(e=n.disabled)?void 0:e.value)===!0}),d=(0,z.EW)(()=>{var e;return f.value&&(0,F.R1)(s)&&!(null!=(e=n.disabled)&&e.value)}),f=(0,z.EW)(()=>!0===(0,F.R1)(n.allowFileUploads)),g=(0,z.EW)(()=>(0,F.R1)(n.allowedFilesMimeTypes)),m=(0,z.EW)(()=>({"--controls-count":f.value?2:1})),{open:b,reset:_,onChange:v}=function(e={}){let t;let{document:r=eM}=e,n=(0,F.KR)(null),{on:a,trigger:s}=function(){let e=new Set,t=t=>{e.delete(t)};return{on:r=>{e.add(r);let n=()=>t(r);return(0,F.o5)()&&(0,F.jr)(n),{off:n}},off:t,trigger:(...t)=>Promise.all(Array.from(e).map(e=>e(...t)))}}();r&&((t=r.createElement("input")).type="file",t.onchange=e=>{n.value=e.target.files,s(n.value)});let i=()=>{n.value=null,t&&t.value&&(t.value="",s(null))};return{files:(0,F.tB)(n),open:r=>{if(!t)return;let n={...eT,...e,...r};t.multiple=n.multiple,t.accept=n.accept,t.webkitdirectory=n.directory,eN(n,"capture")&&(t.capture=n.capture),n.reset&&i(),t.click()},reset:i,onChange:a}}({multiple:!0,reset:!1});function k(){o.value&&o.value.blur()}function y(){o.value&&o.value.focus()}function w(e){l.value=e,y()}async function E(e){if(e.preventDefault(),h.value)return;let t=l.value;l.value="",c.value=!0,await a.sendMessage(t,Array.from(i.value??[])),c.value=!1,_(),i.value=null}async function C(e){e.shiftKey||(await E(e),D())}function A(e){if(!i.value)return;let t=new DataTransfer;for(let r=0;r<i.value.length;r++){let n=i.value[r];e.name!==n.name&&t.items.add(n)}_(),i.value=t.files}function x(e){("ArrowUp"===e.key||"ArrowDown"===e.key)&&(e.preventDefault(),t("arrowKeyDown",{key:e.key,currentInputValue:l.value}))}function S(){d.value||b({accept:(0,F.R1)(g)})}function D(){let e=o.value;if(!e)return;e.style.height="var(--chat--textarea--height)";let t=Math.min(e.scrollHeight,480);e.style.height=`${t}px`}return v(e=>{if(!e)return;let t=new DataTransfer;if(i.value)for(let e=0;e<i.value.length;e++)t.items.add(i.value[e]);for(let r=0;r<e.length;r++)t.items.add(e[r]);i.value=t.files}),(0,z.sV)(()=>{er.on("focusInput",y),er.on("blurInput",k),er.on("setInputValue",w),o.value&&(u.value=new ResizeObserver(e=>{for(let t of e)t.target===o.value&&D()}),u.value.observe(o.value))}),(0,z.hi)(()=>{er.off("focusInput",y),er.off("blurInput",k),er.off("setInputValue",w),u.value&&(u.value.disconnect(),u.value=null)}),(t,n)=>{var a;return(0,z.uX)(),(0,z.CE)("div",{class:"chat-input",style:(0,q.Tr)(m.value),onKeydown:(0,O.D$)(x,["stop"])},[(0,z.Lk)("div",eJ,[t.$slots.leftPanel?((0,z.uX)(),(0,z.CE)("div",eY,[(0,z.RG)(t.$slots,"leftPanel",{},void 0,!0)])):(0,z.Q3)("",!0),(0,z.bo)((0,z.Lk)("textarea",{ref_key:"chatTextArea",ref:o,"onUpdate:modelValue":n[0]||(n[0]=e=>l.value=e),"data-test-id":"chat-input",disabled:p.value,placeholder:(0,F.R1)(r)(e.placeholder),onKeydown:(0,O.jR)(C,["enter"]),onInput:D,onMousedown:D,onFocus:D},null,40,e0),[[O.Jo,l.value]]),(0,z.Lk)("div",e1,[f.value?((0,z.uX)(),(0,z.CE)("button",{key:0,disabled:d.value,class:"chat-input-file-button","data-test-id":"chat-attach-file-button",onClick:S},[(0,z.bF)((0,F.R1)(eF),{height:"24",width:"24"})],8,e2)):(0,z.Q3)("",!0),(0,z.Lk)("button",{disabled:h.value,class:"chat-input-send-button",onClick:E},[(0,z.bF)((0,F.R1)(eq),{height:"24",width:"24"})],8,e3)])]),null!=(a=i.value)&&a.length&&!c.value?((0,z.uX)(),(0,z.CE)("div",e5,[((0,z.uX)(!0),(0,z.CE)(z.FK,null,(0,z.pI)(i.value,e=>((0,z.uX)(),(0,z.Wv)(eQ,{key:e.name,file:e,"is-removable":!0,"is-previewable":!0,onRemove:A},null,8,["file"]))),128))])):(0,z.Q3)("",!0)],36)}}}),[["__scopeId","data-v-31e29ba2"]]),e6={class:"chat-layout"},e9={key:0,class:"chat-header"},e8={key:2,class:"chat-footer"},e7=(0,z.pM)({__name:"Layout",setup(e){let t=(0,F.KR)(null);function r(){let e=t.value;e&&(e.scrollTop=e.scrollHeight)}return(0,z.sV)(()=>{er.on("scrollToBottom",r),window.addEventListener("resize",r)}),(0,z.xo)(()=>{er.off("scrollToBottom",r),window.removeEventListener("resize",r)}),(e,r)=>((0,z.uX)(),(0,z.CE)("main",e6,[e.$slots.header?((0,z.uX)(),(0,z.CE)("div",e9,[(0,z.RG)(e.$slots,"header")])):(0,z.Q3)("",!0),e.$slots.default?((0,z.uX)(),(0,z.CE)("div",{key:1,ref_key:"chatBodyRef",ref:t,class:"chat-body"},[(0,z.RG)(e.$slots,"default")],512)):(0,z.Q3)("",!0),e.$slots.footer?((0,z.uX)(),(0,z.CE)("div",e8,[(0,z.RG)(e.$slots,"footer")])):(0,z.Q3)("",!0)]))}});function te(e){let t=e.regex,r={};Object.assign(r,{className:"variable",variants:[{begin:t.concat(/\$[\w\d#@][\w\d_]*/,"(?![\\w\\d])(?![$])")},{begin:/\$\{/,end:/\}/,contains:["self",{begin:/:-/,contains:[r]}]}]});let n={className:"subst",begin:/\$\(/,end:/\)/,contains:[e.BACKSLASH_ESCAPE]},a={begin:/<<-?\s*(?=\w+)/,starts:{contains:[e.END_SAME_AS_BEGIN({begin:/(\w+)/,end:/(\w+)/,className:"string"})]}},s={className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,r,n]};n.contains.push(s);let i={begin:/\$?\(\(/,end:/\)\)/,contains:[{begin:/\d+#[0-9a-f]+/,className:"number"},e.NUMBER_MODE,r]},o=e.SHEBANG({binary:"(fish|bash|zsh|sh|csh|ksh|tcsh|dash|scsh)",relevance:10}),l={className:"function",begin:/\w[\w\d_]*\s*\(\s*\)\s*\{/,returnBegin:!0,contains:[e.inherit(e.TITLE_MODE,{begin:/\w[\w\d_]*/})],relevance:0};return{name:"Bash",aliases:["sh"],keywords:{$pattern:/\b[a-z][a-z0-9._-]+\b/,keyword:["if","then","else","elif","fi","for","while","until","in","do","done","case","esac","function","select"],literal:["true","false"],built_in:["break","cd","continue","eval","exec","exit","export","getopts","hash","pwd","readonly","return","shift","test","times","trap","umask","unset","alias","bind","builtin","caller","command","declare","echo","enable","help","let","local","logout","mapfile","printf","read","readarray","source","type","typeset","ulimit","unalias","set","shopt","autoload","bg","bindkey","bye","cap","chdir","clone","comparguments","compcall","compctl","compdescribe","compfiles","compgroups","compquote","comptags","comptry","compvalues","dirs","disable","disown","echotc","echoti","emulate","fc","fg","float","functions","getcap","getln","history","integer","jobs","kill","limit","log","noglob","popd","print","pushd","pushln","rehash","sched","setcap","setopt","stat","suspend","ttyctl","unfunction","unhash","unlimit","unsetopt","vared","wait","whence","where","which","zcompile","zformat","zftp","zle","zmodload","zparseopts","zprof","zpty","zregexparse","zsocket","zstyle","ztcp","chcon","chgrp","chown","chmod","cp","dd","df","dir","dircolors","ln","ls","mkdir","mkfifo","mknod","mktemp","mv","realpath","rm","rmdir","shred","sync","touch","truncate","vdir","b2sum","base32","base64","cat","cksum","comm","csplit","cut","expand","fmt","fold","head","join","md5sum","nl","numfmt","od","paste","ptx","pr","sha1sum","sha224sum","sha256sum","sha384sum","sha512sum","shuf","sort","split","sum","tac","tail","tr","tsort","unexpand","uniq","wc","arch","basename","chroot","date","dirname","du","echo","env","expr","factor","groups","hostid","id","link","logname","nice","nohup","nproc","pathchk","pinky","printenv","printf","pwd","readlink","runcon","seq","sleep","stat","stdbuf","stty","tee","test","timeout","tty","uname","unlink","uptime","users","who","whoami","yes"]},contains:[o,e.SHEBANG(),l,i,e.HASH_COMMENT_MODE,a,{match:/(\/[a-z._-]+)+/},s,{match:/\\"/},{className:"string",begin:/'/,end:/'/},{match:/\\'/},r]}}function tt(e){let t=e.regex,r=RegExp("[\\p{XID_Start}_]\\p{XID_Continue}*","u"),n=["and","as","assert","async","await","break","case","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","in","is","lambda","match","nonlocal|10","not","or","pass","raise","return","try","while","with","yield"],a={$pattern:/[A-Za-z]\w+|__\w+__/,keyword:n,built_in:["__import__","abs","all","any","ascii","bin","bool","breakpoint","bytearray","bytes","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","exec","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","print","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip"],literal:["__debug__","Ellipsis","False","None","NotImplemented","True"],type:["Any","Callable","Coroutine","Dict","List","Literal","Generic","Optional","Sequence","Set","Tuple","Type","Union"]},s={className:"meta",begin:/^(>>>|\.\.\.) /},i={className:"subst",begin:/\{/,end:/\}/,keywords:a,illegal:/#/},o={begin:/\{\{/,relevance:0},l={className:"string",contains:[e.BACKSLASH_ESCAPE],variants:[{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,s],relevance:10},{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,s],relevance:10},{begin:/([fF][rR]|[rR][fF]|[fF])'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,s,o,i]},{begin:/([fF][rR]|[rR][fF]|[fF])"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,s,o,i]},{begin:/([uU]|[rR])'/,end:/'/,relevance:10},{begin:/([uU]|[rR])"/,end:/"/,relevance:10},{begin:/([bB]|[bB][rR]|[rR][bB])'/,end:/'/},{begin:/([bB]|[bB][rR]|[rR][bB])"/,end:/"/},{begin:/([fF][rR]|[rR][fF]|[fF])'/,end:/'/,contains:[e.BACKSLASH_ESCAPE,o,i]},{begin:/([fF][rR]|[rR][fF]|[fF])"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,o,i]},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},c="[0-9](_?[0-9])*",u=`(\\b(${c}))?\\.(${c})|\\b(${c})\\.`,h=`\\b|${n.join("|")}`,p={className:"number",relevance:0,variants:[{begin:`(\\b(${c})|(${u}))[eE][+-]?(${c})[jJ]?(?=${h})`},{begin:`(${u})[jJ]?`},{begin:`\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${h})`},{begin:`\\b0[bB](_?[01])+[lL]?(?=${h})`},{begin:`\\b0[oO](_?[0-7])+[lL]?(?=${h})`},{begin:`\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${h})`},{begin:`\\b(${c})[jJ](?=${h})`}]},d={className:"comment",begin:t.lookahead(/# type:/),end:/$/,keywords:a,contains:[{begin:/# type:/},{begin:/#/,end:/\b\B/,endsWithParent:!0}]},f={className:"params",variants:[{className:"",begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:a,contains:["self",s,p,l,e.HASH_COMMENT_MODE]}]};return i.contains=[l,p,s],{name:"Python",aliases:["py","gyp","ipython"],unicodeRegex:!0,keywords:a,illegal:/(<\/|\?)|=>/,contains:[s,p,{begin:/\bself\b/},{beginKeywords:"if",relevance:0},l,d,e.HASH_COMMENT_MODE,{match:[/\bdef/,/\s+/,r],scope:{1:"keyword",3:"title.function"},contains:[f]},{variants:[{match:[/\bclass/,/\s+/,r,/\s*/,/\(\s*/,r,/\s*\)/]},{match:[/\bclass/,/\s+/,r]}],scope:{1:"keyword",3:"title.class",6:"title.class.inherited"}},{className:"meta",begin:/^[\t ]*@/,end:/(?=#)|$/,contains:[p,f,l]}]}}let tr="[A-Za-z$_][0-9A-Za-z$_]*",tn=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends"],ta=["true","false","null","undefined","NaN","Infinity"],ts=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],ti=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],to=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],tl=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],tc=[].concat(to,ts,ti);function tu(e){let t=function(e){var t;let r=e.regex,n=(e,{after:t})=>{let r="</"+e[0].slice(1);return -1!==e.input.indexOf(r,t)},a=/<[A-Za-z0-9\\._:-]+/,s=/\/[A-Za-z0-9\\._:-]+>|\/>/,i={$pattern:tr,keyword:tn,literal:ta,built_in:tc,"variable.language":tl},o="[0-9](_?[0-9])*",l=`\\.(${o})`,c="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",u={className:"number",variants:[{begin:`(\\b(${c})((${l})|\\.)?|(${l}))[eE][+-]?(${o})\\b`},{begin:`\\b(${c})\\b((${l})\\b|\\.)?|(${l})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},h={className:"subst",begin:"\\$\\{",end:"\\}",keywords:i,contains:[]},p={begin:"html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,h],subLanguage:"xml"}},d={begin:"css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,h],subLanguage:"css"}},f={begin:"gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,h],subLanguage:"graphql"}},g={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,h]},m={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:tr+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},b=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,p,d,f,g,{match:/\$\d+/},u];h.contains=b.concat({begin:/\{/,end:/\}/,keywords:i,contains:["self"].concat(b)});let _=[].concat(m,h.contains),v=_.concat([{begin:/\(/,end:/\)/,keywords:i,contains:["self"].concat(_)}]),k={className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:i,contains:v},y={variants:[{match:[/class/,/\s+/,tr,/\s+/,/extends/,/\s+/,r.concat(tr,"(",r.concat(/\./,tr),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,tr],scope:{1:"keyword",3:"title.class"}}]},w={relevance:0,match:r.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...ts,...ti]}},E={match:r.concat(/\b/,(t=[...to,"super","import"],r.concat("(?!",t.join("|"),")")),tr,r.lookahead(/\(/)),className:"title.function",relevance:0},C={begin:r.concat(/\./,r.lookahead(r.concat(tr,/(?![0-9A-Za-z$_(])/))),end:tr,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},A="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",x={match:[/const|var|let/,/\s+/,tr,/\s*/,/=\s*/,/(async\s*)?/,r.lookahead(A)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[k]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:i,exports:{PARAMS_CONTAINS:v,CLASS_REFERENCE:w},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,p,d,f,g,m,{match:/\$\d+/},u,w,{className:"attr",begin:tr+r.lookahead(":"),relevance:0},x,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[m,e.REGEXP_MODE,{className:"function",begin:A,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:i,contains:v}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:"<>",end:"</>"},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:a,"on:begin":(e,t)=>{let r;let a=e[0].length+e.index,s=e.input[a];if("<"===s||","===s){t.ignoreMatch();return}">"===s&&(n(e,{after:a})||t.ignoreMatch());let i=e.input.substring(a);if((r=i.match(/^\s*=/))||(r=i.match(/^\s+extends\s+/))&&0===r.index){t.ignoreMatch();return}},end:s}],subLanguage:"xml",contains:[{begin:a,end:s,skip:!0,contains:["self"]}]}]},{variants:[{match:[/function/,/\s+/,tr,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[k],illegal:/%/},{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[k,e.inherit(e.TITLE_MODE,{begin:tr,className:"title.function"})]},{match:/\.\.\./,relevance:0},C,{match:"\\$"+tr,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[k]},E,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},y,{match:[/get|set/,/\s+/,tr,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},k]},{match:/\$[(.]/}]}}(e),r=["any","void","number","boolean","string","object","never","symbol","bigint","unknown"],n={beginKeywords:"namespace",end:/\{/,excludeEnd:!0,contains:[t.exports.CLASS_REFERENCE]},a={beginKeywords:"interface",end:/\{/,excludeEnd:!0,keywords:{keyword:"interface extends",built_in:r},contains:[t.exports.CLASS_REFERENCE]},s={$pattern:tr,keyword:tn.concat(["type","namespace","interface","public","private","protected","implements","declare","abstract","readonly","enum","override"]),literal:ta,built_in:tc.concat(r),"variable.language":tl},i={className:"meta",begin:"@"+tr},o=(e,t,r)=>{let n=e.contains.findIndex(e=>e.label===t);if(-1===n)throw Error("can not find mode to replace");e.contains.splice(n,1,r)};return Object.assign(t.keywords,s),t.exports.PARAMS_CONTAINS.push(i),t.contains=t.contains.concat([i,n,a]),o(t,"shebang",e.SHEBANG()),o(t,"use_strict",{className:"meta",relevance:10,begin:/^\s*['"]use strict['"]/}),t.contains.find(e=>"func.def"===e.label).relevance=0,Object.assign(t,{name:"TypeScript",aliases:["ts","tsx","mts","cts"]}),t}let th=ea(function(){if(tv)return t_;function e(e,t){Object.freeze(t=t?Array.isArray(t)?t:[t]:[]);var r=e.renderer.rules.link_open||this.defaultRender;e.renderer.rules.link_open=function(e,n,a,s,i){var o=function(e,t){var r,n,a=e.attrs[e.attrIndex("href")][1];for(r=0;r<t.length;++r){if("function"==typeof(n=t[r]).matcher){if(n.matcher(a,n))return n;continue}return n}}(e[n],t),l=o&&o.attrs;return l&&function(e,t,r){Object.keys(r).forEach(function(n){var a,s=r[n];"className"===n&&(n="class"),(a=t[e].attrIndex(n))<0?t[e].attrPush([n,s]):t[e].attrs[a][1]=s})}(n,e,l),r(e,n,a,s,i)}}return tv=1,e.defaultRender=function(e,t,r,n,a){return a.renderToken(e,t,r)},t_=e}());var tp={};let td={Aacute:"\xc1",aacute:"\xe1",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"\xc2",acirc:"\xe2",acute:"\xb4",Acy:"А",acy:"а",AElig:"\xc6",aelig:"\xe6",af:"⁡",Afr:"\uD835\uDD04",afr:"\uD835\uDD1E",Agrave:"\xc0",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",amp:"&",AMP:"&",andand:"⩕",And:"⩓",and:"∧",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angmsd:"∡",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"\uD835\uDD38",aopf:"\uD835\uDD52",apacir:"⩯",ap:"≈",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"\xc5",aring:"\xe5",Ascr:"\uD835\uDC9C",ascr:"\uD835\uDCB6",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"\xc3",atilde:"\xe3",Auml:"\xc4",auml:"\xe4",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",barwed:"⌅",Barwed:"⌆",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",Because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"\uD835\uDD05",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"\uD835\uDD39",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxdl:"┐",boxdL:"╕",boxDl:"╖",boxDL:"╗",boxdr:"┌",boxdR:"╒",boxDr:"╓",boxDR:"╔",boxh:"─",boxH:"═",boxhd:"┬",boxHd:"╤",boxhD:"╥",boxHD:"╦",boxhu:"┴",boxHu:"╧",boxhU:"╨",boxHU:"╩",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxul:"┘",boxuL:"╛",boxUl:"╜",boxUL:"╝",boxur:"└",boxuR:"╘",boxUr:"╙",boxUR:"╚",boxv:"│",boxV:"║",boxvh:"┼",boxvH:"╪",boxVh:"╫",boxVH:"╬",boxvl:"┤",boxvL:"╡",boxVl:"╢",boxVL:"╣",boxvr:"├",boxvR:"╞",boxVr:"╟",boxVR:"╠",bprime:"‵",breve:"˘",Breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",Bscr:"ℬ",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsolb:"⧅",bsol:"\\",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",capand:"⩄",capbrcup:"⩉",capcap:"⩋",cap:"∩",Cap:"⋒",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"\xc7",ccedil:"\xe7",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"\xb8",Cedilla:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",CenterDot:"\xb7",cfr:"\uD835\uDD20",Cfr:"ℭ",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"\xae",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cir:"○",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",colon:":",Colon:"∷",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",conint:"∮",Conint:"∯",ContourIntegral:"∮",copf:"\uD835\uDD54",Copf:"ℂ",coprod:"∐",Coproduct:"∐",copy:"\xa9",COPY:"\xa9",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",cross:"✗",Cross:"⨯",Cscr:"\uD835\uDC9E",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cupbrcap:"⩈",cupcap:"⩆",CupCap:"≍",cup:"∪",Cup:"⋓",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dagger:"†",Dagger:"‡",daleth:"ℸ",darr:"↓",Darr:"↡",dArr:"⇓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",ddagger:"‡",ddarr:"⇊",DD:"ⅅ",dd:"ⅆ",DDotrahd:"⤑",ddotseq:"⩷",deg:"\xb0",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"\uD835\uDD07",dfr:"\uD835\uDD21",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",diamond:"⋄",Diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"\uD835\uDD3B",dopf:"\uD835\uDD55",Dot:"\xa8",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrowBar:"⤓",downarrow:"↓",DownArrow:"↓",Downarrow:"⇓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVectorBar:"⥖",DownLeftVector:"↽",DownRightTeeVector:"⥟",DownRightVectorBar:"⥗",DownRightVector:"⇁",DownTeeArrow:"↧",DownTee:"⊤",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"\uD835\uDC9F",dscr:"\uD835\uDCB9",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"\xc9",eacute:"\xe9",easter:"⩮",Ecaron:"Ě",ecaron:"ě",Ecirc:"\xca",ecirc:"\xea",ecir:"≖",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",edot:"ė",eDot:"≑",ee:"ⅇ",efDot:"≒",Efr:"\uD835\uDD08",efr:"\uD835\uDD22",eg:"⪚",Egrave:"\xc8",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp13:" ",emsp14:" ",emsp:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"\uD835\uDD3C",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",escr:"ℯ",Escr:"ℰ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"\xd0",eth:"\xf0",Euml:"\xcb",euml:"\xeb",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",exponentiale:"ⅇ",ExponentialE:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"\uD835\uDD09",ffr:"\uD835\uDD23",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"\uD835\uDD3D",fopf:"\uD835\uDD57",forall:"∀",ForAll:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",Fscr:"ℱ",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",ge:"≥",gE:"≧",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",gescc:"⪩",ges:"⩾",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"\uD835\uDD0A",gfr:"\uD835\uDD24",gg:"≫",Gg:"⋙",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gla:"⪥",gl:"≷",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gnE:"≩",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"\uD835\uDD3E",gopf:"\uD835\uDD58",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gtcc:"⪧",gtcir:"⩺",gt:">",GT:">",Gt:"≫",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"\xbd",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",harrcir:"⥈",harr:"↔",hArr:"⇔",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",Hfr:"ℌ",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",Hopf:"ℍ",horbar:"―",HorizontalLine:"─",hscr:"\uD835\uDCBD",Hscr:"ℋ",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"\xcd",iacute:"\xed",ic:"⁣",Icirc:"\xce",icirc:"\xee",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",Ifr:"ℑ",Igrave:"\xcc",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",Im:"ℑ",imof:"⊷",imped:"Ƶ",Implies:"⇒",incare:"℅",in:"∈",infin:"∞",infintie:"⧝",inodot:"ı",intcal:"⊺",int:"∫",Int:"∬",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"\uD835\uDD40",iopf:"\uD835\uDD5A",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",Iscr:"ℐ",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"\xcf",iuml:"\xef",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"\uD835\uDD0D",jfr:"\uD835\uDD27",jmath:"ȷ",Jopf:"\uD835\uDD41",jopf:"\uD835\uDD5B",Jscr:"\uD835\uDCA5",jscr:"\uD835\uDCBF",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"\uD835\uDD0E",kfr:"\uD835\uDD28",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"\uD835\uDD42",kopf:"\uD835\uDD5C",Kscr:"\uD835\uDCA6",kscr:"\uD835\uDCC0",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",lang:"⟨",Lang:"⟪",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"\xab",larrb:"⇤",larrbfs:"⤟",larr:"←",Larr:"↞",lArr:"⇐",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",latail:"⤙",lAtail:"⤛",lat:"⪫",late:"⪭",lates:"⪭︀",lbarr:"⤌",lBarr:"⤎",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",lE:"≦",LeftAngleBracket:"⟨",LeftArrowBar:"⇤",leftarrow:"←",LeftArrow:"←",Leftarrow:"⇐",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVectorBar:"⥙",LeftDownVector:"⇃",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTeeArrow:"↤",LeftTee:"⊣",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangleBar:"⧏",LeftTriangle:"⊲",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVectorBar:"⥘",LeftUpVector:"↿",LeftVectorBar:"⥒",LeftVector:"↼",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",lescc:"⪨",les:"⩽",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"\uD835\uDD0F",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",llarr:"⇇",ll:"≪",Ll:"⋘",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoustache:"⎰",lmoust:"⎰",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lnE:"≨",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftrightarrow:"⟷",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longmapsto:"⟼",longrightarrow:"⟶",LongRightArrow:"⟶",Longrightarrow:"⟹",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"\uD835\uDD43",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",Lscr:"ℒ",lsh:"↰",Lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",ltcc:"⪦",ltcir:"⩹",lt:"<",LT:"<",Lt:"≪",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",midast:"*",midcir:"⫰",mid:"∣",middot:"\xb7",minusb:"⊟",minus:"−",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"\uD835\uDD44",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",Mscr:"ℳ",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natural:"♮",naturals:"ℕ",natur:"♮",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",nearhk:"⤤",nearr:"↗",neArr:"⇗",nearrow:"↗",ne:"≠",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:`
`,nexist:"∄",nexists:"∄",Nfr:"\uD835\uDD11",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nharr:"↮",nhArr:"⇎",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlarr:"↚",nlArr:"⇍",nldr:"‥",nlE:"≦̸",nle:"≰",nleftarrow:"↚",nLeftarrow:"⇍",nleftrightarrow:"↮",nLeftrightarrow:"⇎",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:"\xa0",nopf:"\uD835\uDD5F",Nopf:"ℕ",Not:"⫬",not:"\xac",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangleBar:"⧏̸",NotLeftTriangle:"⋪",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangleBar:"⧐̸",NotRightTriangle:"⋫",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",nparallel:"∦",npar:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",nprec:"⊀",npreceq:"⪯̸",npre:"⪯̸",nrarrc:"⤳̸",nrarr:"↛",nrArr:"⇏",nrarrw:"↝̸",nrightarrow:"↛",nRightarrow:"⇏",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"\uD835\uDCA9",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"\xd1",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nvdash:"⊬",nvDash:"⊭",nVdash:"⊮",nVDash:"⊯",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwarr:"↖",nwArr:"⇖",nwarrow:"↖",nwnear:"⤧",Oacute:"\xd3",oacute:"\xf3",oast:"⊛",Ocirc:"\xd4",ocirc:"\xf4",ocir:"⊚",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"\uD835\uDD12",ofr:"\uD835\uDD2C",ogon:"˛",Ograve:"\xd2",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"\uD835\uDD46",oopf:"\uD835\uDD60",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",orarr:"↻",Or:"⩔",or:"∨",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"\uD835\uDCAA",oscr:"ℴ",Oslash:"\xd8",oslash:"\xf8",osol:"⊘",Otilde:"\xd5",otilde:"\xf5",otimesas:"⨶",Otimes:"⨷",otimes:"⊗",Ouml:"\xd6",ouml:"\xf6",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",para:"\xb6",parallel:"∥",par:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"\uD835\uDD13",pfr:"\uD835\uDD2D",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plus:"+",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"\xb1",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",Poincareplane:"ℌ",pointint:"⨕",popf:"\uD835\uDD61",Popf:"ℙ",pound:"\xa3",prap:"⪷",Pr:"⪻",pr:"≺",prcue:"≼",precapprox:"⪷",prec:"≺",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",pre:"⪯",prE:"⪳",precsim:"≾",prime:"′",Prime:"″",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportional:"∝",Proportion:"∷",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"\uD835\uDCAB",pscr:"\uD835\uDCC5",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"\uD835\uDD14",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",Qopf:"ℚ",qprime:"⁗",Qscr:"\uD835\uDCAC",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',QUOT:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",Rang:"⟫",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarr:"→",Rarr:"↠",rArr:"⇒",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",ratail:"⤚",rAtail:"⤜",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rBarr:"⤏",RBarr:"⤐",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",Re:"ℜ",rect:"▭",reg:"\xae",REG:"\xae",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",Rfr:"ℜ",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrowBar:"⇥",rightarrow:"→",RightArrow:"→",Rightarrow:"⇒",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVectorBar:"⥕",RightDownVector:"⇂",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTeeArrow:"↦",RightTee:"⊢",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangleBar:"⧐",RightTriangle:"⊳",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVectorBar:"⥔",RightUpVector:"↾",RightVectorBar:"⥓",RightVector:"⇀",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoustache:"⎱",rmoust:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",Ropf:"ℝ",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",rscr:"\uD835\uDCC7",Rscr:"ℛ",rsh:"↱",Rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",scap:"⪸",Scaron:"Š",scaron:"š",Sc:"⪼",sc:"≻",sccue:"≽",sce:"⪰",scE:"⪴",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdotb:"⊡",sdot:"⋅",sdote:"⩦",searhk:"⤥",searr:"↘",seArr:"⇘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"\uD835\uDD16",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"\xad",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",solbar:"⌿",solb:"⧄",sol:"/",Sopf:"\uD835\uDD4A",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",square:"□",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squ:"□",squf:"▪",srarr:"→",Sscr:"\uD835\uDCAE",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",Sub:"⋐",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",Subset:"⋐",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succapprox:"⪸",succ:"≻",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",sum:"∑",Sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",Sup:"⋑",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",Supset:"⋑",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swarr:"↙",swArr:"⇙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",Tab:"	",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"\uD835\uDD17",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",Therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",ThinSpace:" ",thinsp:" ",thkap:"≈",thksim:"∼",THORN:"\xde",thorn:"\xfe",tilde:"˜",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",timesbar:"⨱",timesb:"⊠",times:"\xd7",timesd:"⨰",tint:"∭",toea:"⤨",topbot:"⌶",topcir:"⫱",top:"⊤",Topf:"\uD835\uDD4B",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",TRADE:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"\uD835\uDCAF",tscr:"\uD835\uDCC9",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"\xda",uacute:"\xfa",uarr:"↑",Uarr:"↟",uArr:"⇑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"\xdb",ucirc:"\xfb",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"\uD835\uDD18",ufr:"\uD835\uDD32",Ugrave:"\xd9",ugrave:"\xf9",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"\xa8",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"\uD835\uDD4C",uopf:"\uD835\uDD66",UpArrowBar:"⤒",uparrow:"↑",UpArrow:"↑",Uparrow:"⇑",UpArrowDownArrow:"⇅",updownarrow:"↕",UpDownArrow:"↕",Updownarrow:"⇕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",upsi:"υ",Upsi:"ϒ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTeeArrow:"↥",UpTee:"⊥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"\uD835\uDCB0",uscr:"\uD835\uDCCA",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"\xdc",uuml:"\xfc",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",vArr:"⇕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vBar:"⫨",Vbar:"⫫",vBarv:"⫩",Vcy:"В",vcy:"в",vdash:"⊢",vDash:"⊨",Vdash:"⊩",VDash:"⊫",Vdashl:"⫦",veebar:"⊻",vee:"∨",Vee:"⋁",veeeq:"≚",vellip:"⋮",verbar:"|",Verbar:"‖",vert:"|",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"\uD835\uDD4D",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",Vscr:"\uD835\uDCB1",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",Wedge:"⋀",wedgeq:"≙",weierp:"℘",Wfr:"\uD835\uDD1A",wfr:"\uD835\uDD34",Wopf:"\uD835\uDD4E",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",Wscr:"\uD835\uDCB2",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"\uD835\uDD1B",xfr:"\uD835\uDD35",xharr:"⟷",xhArr:"⟺",Xi:"Ξ",xi:"ξ",xlarr:"⟵",xlArr:"⟸",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"\uD835\uDD4F",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrarr:"⟶",xrArr:"⟹",Xscr:"\uD835\uDCB3",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"\xdd",yacute:"\xfd",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"\xa5",Yfr:"\uD835\uDD1C",yfr:"\uD835\uDD36",YIcy:"Ї",yicy:"ї",Yopf:"\uD835\uDD50",yopf:"\uD835\uDD6A",Yscr:"\uD835\uDCB4",yscr:"\uD835\uDCCE",YUcy:"Ю",yucy:"ю",yuml:"\xff",Yuml:"Ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",zfr:"\uD835\uDD37",Zfr:"ℨ",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",Zopf:"ℤ",Zscr:"\uD835\uDCB5",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"};function tf(){return ty||(ty=1,tk=td),tk}function tg(){return tE||(tE=1,tw=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4E\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDF55-\uDF59]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDF3C-\uDF3E]|\uD806[\uDC3B\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/),tw}var tm,tb,t_,tv,tk,ty,tw,tE,tC,tA,tx={};function tS(){return tI||(tI=1,tx.encode=function(){if(tA)return tC;tA=1;var e={};function t(r,n,a){var s,i,o,l,c,u="";for("string"!=typeof n&&(a=n,n=t.defaultChars),typeof a>"u"&&(a=!0),c=function(t){var r,n,a=e[t];if(a)return a;for(a=e[t]=[],r=0;r<128;r++)n=String.fromCharCode(r),/^[0-9a-z]$/i.test(n)?a.push(n):a.push("%"+("0"+r.toString(16).toUpperCase()).slice(-2));for(r=0;r<t.length;r++)a[t.charCodeAt(r)]=t[r];return a}(n),s=0,i=r.length;s<i;s++){if(o=r.charCodeAt(s),a&&37===o&&s+2<i&&/^[0-9a-f]{2}$/i.test(r.slice(s+1,s+3))){u+=r.slice(s,s+3),s+=2;continue}if(o<128){u+=c[o];continue}if(o>=55296&&o<=57343){if(o>=55296&&o<=56319&&s+1<i&&(l=r.charCodeAt(s+1))>=56320&&l<=57343){u+=encodeURIComponent(r[s]+r[s+1]),s++;continue}u+="%EF%BF%BD";continue}u+=encodeURIComponent(r[s])}return u}return t.defaultChars=";/?:@&=+$,-_.!~*'()#",t.componentChars="-_.!~*'()",tC=t}(),tx.decode=function(){if(tL)return tD;tL=1;var e={};function t(r,n){var a;return"string"!=typeof n&&(n=t.defaultChars),a=function(t){var r,n,a=e[t];if(a)return a;for(a=e[t]=[],r=0;r<128;r++)n=String.fromCharCode(r),a.push(n);for(r=0;r<t.length;r++)a[n=t.charCodeAt(r)]="%"+("0"+n.toString(16).toUpperCase()).slice(-2);return a}(n),r.replace(/(%[a-f0-9]{2})+/gi,function(e){var t,r,n,s,i,o,l,c="";for(t=0,r=e.length;t<r;t+=3){if((n=parseInt(e.slice(t+1,t+3),16))<128){c+=a[n];continue}if((224&n)==192&&t+3<r&&(192&(s=parseInt(e.slice(t+4,t+6),16)))==128){(l=n<<6&1984|63&s)<128?c+="��":c+=String.fromCharCode(l),t+=3;continue}if((240&n)==224&&t+6<r&&(s=parseInt(e.slice(t+4,t+6),16),i=parseInt(e.slice(t+7,t+9),16),(192&s)==128&&(192&i)==128)){(l=n<<12&61440|s<<6&4032|63&i)<2048||l>=55296&&l<=57343?c+="���":c+=String.fromCharCode(l),t+=6;continue}if((248&n)==240&&t+9<r&&(s=parseInt(e.slice(t+4,t+6),16),i=parseInt(e.slice(t+7,t+9),16),o=parseInt(e.slice(t+10,t+12),16),(192&s)==128&&(192&i)==128&&(192&o)==128)){(l=n<<18&1835008|s<<12&258048|i<<6&4032|63&o)<65536||l>1114111?c+="����":(l-=65536,c+=String.fromCharCode(55296+(l>>10),56320+(1023&l))),t+=9;continue}c+="�"}return c})}return t.defaultChars=";/?:@&=+$,#",t.componentChars="",tD=t}(),tN||(tN=1,tR=function(e){var t="";return t+=e.protocol||"",t+=e.slashes?"//":"",t+=e.auth?e.auth+"@":"",e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t+=e.port?":"+e.port:"",t+=e.pathname||"",t+=e.search||"",t+=e.hash||""}),tx.format=tR,tx.parse=function(){if(tT)return tM;function e(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}tT=1;var t=/^([a-z0-9.+-]+:)/i,r=/:[0-9]*$/,n=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,a=["%","/","?",";","#"].concat(["'"].concat(["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r",`
`,"	"]))),s=["/","?","#"],i=/^[+a-z0-9A-Z_-]{0,63}$/,o=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,l={javascript:!0,"javascript:":!0},c={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};return e.prototype.parse=function(e,r){var u,h,p,d,f,g=e;if(g=g.trim(),!r&&1===e.split("#").length){var m=n.exec(g);if(m)return this.pathname=m[1],m[2]&&(this.search=m[2]),this}var b=t.exec(g);if(b&&(p=(b=b[0]).toLowerCase(),this.protocol=b,g=g.substr(b.length)),(r||b||g.match(/^\/\/[^@\/]+@[^@\/]+/))&&(f="//"===g.substr(0,2))&&!(b&&l[b])&&(g=g.substr(2),this.slashes=!0),!l[b]&&(f||b&&!c[b])){var _,v,k=-1;for(u=0;u<s.length;u++)-1!==(d=g.indexOf(s[u]))&&(-1===k||d<k)&&(k=d);for(-1!==(v=-1===k?g.lastIndexOf("@"):g.lastIndexOf("@",k))&&(_=g.slice(0,v),g=g.slice(v+1),this.auth=_),k=-1,u=0;u<a.length;u++)-1!==(d=g.indexOf(a[u]))&&(-1===k||d<k)&&(k=d);-1===k&&(k=g.length),":"===g[k-1]&&k--;var y=g.slice(0,k);g=g.slice(k),this.parseHost(y),this.hostname=this.hostname||"";var w="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!w){var E=this.hostname.split(/\./);for(u=0,h=E.length;u<h;u++){var C=E[u];if(C&&!C.match(i)){for(var A="",x=0,S=C.length;x<S;x++)C.charCodeAt(x)>127?A+="x":A+=C[x];if(!A.match(i)){var D=E.slice(0,u),L=E.slice(u+1),R=C.match(o);R&&(D.push(R[1]),L.unshift(R[2])),L.length&&(g=L.join(".")+g),this.hostname=D.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),w&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}var N=g.indexOf("#");-1!==N&&(this.hash=g.substr(N),g=g.slice(0,N));var M=g.indexOf("?");return -1!==M&&(this.search=g.substr(M),g=g.slice(0,M)),g&&(this.pathname=g),c[p]&&this.hostname&&!this.pathname&&(this.pathname=""),this},e.prototype.parseHost=function(e){var t=r.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)},tM=function(t,r){if(t&&t instanceof e)return t;var n=new e;return n.parse(t,r),n}}()),tx}var tD,tL,tR,tN,tM,tT,tI,tF,tz,tq={};function tO(){return tz||(tz=1,tF=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/),tF}function tB(){return tU||(tU=1,tj=/[\0-\x1F\x7F-\x9F]/),tj}function tP(){return tG||(tG=1,tV=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/),tV}function t$(){return tX||(tX=1,function(e){var t=Object.prototype.hasOwnProperty;function r(e,r){return t.call(e,r)}function n(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||(65535&e)==65535||(65535&e)==65534||e>=0&&e<=8||11===e||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function a(e){return e>65535?String.fromCharCode(55296+((e-=65536)>>10),56320+(1023&e)):String.fromCharCode(e)}var s=/\\([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g,i=RegExp(s.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),o=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i,l=tf(),c=/[&<>"]/,u=/[&<>"]/g,h={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function p(e){return h[e]}var d=/[.?*+^$[\]\\(){}|-]/g,f=tg();e.lib={},e.lib.mdurl=tS(),e.lib.ucmicro=(tW||(tW=1,tq.Any=tO(),tq.Cc=tB(),tZ||(tZ=1,tH=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/),tq.Cf=tH,tq.P=tg(),tq.Z=tP()),tq),e.assign=function(e){var t=Array.prototype.slice.call(arguments,1);return t.forEach(function(t){if(t){if("object"!=typeof t)throw TypeError(t+"must be object");Object.keys(t).forEach(function(r){e[r]=t[r]})}}),e},e.isString=function(e){return"[object String]"===Object.prototype.toString.call(e)},e.has=r,e.unescapeMd=function(e){return 0>e.indexOf("\\")?e:e.replace(s,"$1")},e.unescapeAll=function(e){return 0>e.indexOf("\\")&&0>e.indexOf("&")?e:e.replace(i,function(e,t,s){var i;return t||(r(l,s)?l[s]:35===s.charCodeAt(0)&&o.test(s)&&n(i="x"===s[1].toLowerCase()?parseInt(s.slice(2),16):parseInt(s.slice(1),10))?a(i):e)})},e.isValidEntityCode=n,e.fromCodePoint=a,e.escapeHtml=function(e){return c.test(e)?e.replace(u,p):e},e.arrayReplaceAt=function(e,t,r){return[].concat(e.slice(0,t),r,e.slice(t+1))},e.isSpace=function(e){switch(e){case 9:case 32:return!0}return!1},e.isWhiteSpace=function(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1},e.isMdAsciiPunct=function(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}},e.isPunctChar=function(e){return f.test(e)},e.escapeRE=function(e){return e.replace(d,"\\$&")},e.normalizeReference=function(e){return(e=e.trim().replace(/\s+/g," ")).toLowerCase().toUpperCase()}}(tp)),tp}var tj,tU,tH,tZ,tV,tG,tW,tX,tK,tQ,tJ={};function tY(){if(t7)return t8;function e(){this.__rules__=[],this.__cache__=null}return t7=1,e.prototype.__find__=function(e){for(var t=0;t<this.__rules__.length;t++)if(this.__rules__[t].name===e)return t;return -1},e.prototype.__compile__=function(){var e=this,t=[""];e.__rules__.forEach(function(e){e.enabled&&e.alt.forEach(function(e){0>t.indexOf(e)&&t.push(e)})}),e.__cache__={},t.forEach(function(t){e.__cache__[t]=[],e.__rules__.forEach(function(r){r.enabled&&(t&&0>r.alt.indexOf(t)||e.__cache__[t].push(r.fn))})})},e.prototype.at=function(e,t,r){var n=this.__find__(e);if(-1===n)throw Error("Parser rule not found: "+e);this.__rules__[n].fn=t,this.__rules__[n].alt=(r||{}).alt||[],this.__cache__=null},e.prototype.before=function(e,t,r,n){var a=this.__find__(e);if(-1===a)throw Error("Parser rule not found: "+e);this.__rules__.splice(a,0,{name:t,enabled:!0,fn:r,alt:(n||{}).alt||[]}),this.__cache__=null},e.prototype.after=function(e,t,r,n){var a=this.__find__(e);if(-1===a)throw Error("Parser rule not found: "+e);this.__rules__.splice(a+1,0,{name:t,enabled:!0,fn:r,alt:(n||{}).alt||[]}),this.__cache__=null},e.prototype.push=function(e,t,r){this.__rules__.push({name:e,enabled:!0,fn:t,alt:(r||{}).alt||[]}),this.__cache__=null},e.prototype.enable=function(e,t){Array.isArray(e)||(e=[e]);var r=[];return e.forEach(function(e){var n=this.__find__(e);if(n<0){if(t)return;throw Error("Rules manager: invalid rule name "+e)}this.__rules__[n].enabled=!0,r.push(e)},this),this.__cache__=null,r},e.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach(function(e){e.enabled=!1}),this.enable(e,t)},e.prototype.disable=function(e,t){Array.isArray(e)||(e=[e]);var r=[];return e.forEach(function(e){var n=this.__find__(e);if(n<0){if(t)return;throw Error("Rules manager: invalid rule name "+e)}this.__rules__[n].enabled=!1,r.push(e)},this),this.__cache__=null,r},e.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},t8=e}function t0(){if(rg)return rf;function e(e,t,r){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=r,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}return rg=1,e.prototype.attrIndex=function(e){var t,r,n;if(!this.attrs)return -1;for(t=this.attrs,r=0,n=t.length;r<n;r++)if(t[r][0]===e)return r;return -1},e.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},e.prototype.attrSet=function(e,t){var r=this.attrIndex(e),n=[e,t];r<0?this.attrPush(n):this.attrs[r]=n},e.prototype.attrGet=function(e){var t=this.attrIndex(e),r=null;return t>=0&&(r=this.attrs[t][1]),r},e.prototype.attrJoin=function(e,t){var r=this.attrIndex(e);r<0?this.attrPush([e,t]):this.attrs[r][1]=this.attrs[r][1]+" "+t},rf=e}var t1,t2,t3,t5,t4,t6,t9,t8,t7,re,rt,rr,rn,ra,rs,ri,ro,rl,rc,ru,rh,rp,rd,rf,rg,rm,rb,r_,rv,rk,ry,rw,rE,rC,rA,rx,rS,rD,rL,rR,rN,rM,rT,rI,rF,rz,rq={};function rO(){if(rz)return rq;rz=1;var e="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",t="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",r=RegExp("^(?:"+e+"|"+t+"|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),n=RegExp("^(?:"+e+"|"+t+")");return rq.HTML_TAG_RE=r,rq.HTML_OPEN_CLOSE_TAG_RE=n,rq}var rB,rP,r$,rj,rU,rH,rZ,rV,rG,rW,rX,rK,rQ,rJ,rY,r0,r1,r2,r3,r5,r4,r6,r9,r8={};function r7(){if(r9)return r8;function e(e,t){var r,n,a,s,i,o=[],l=t.length;for(r=0;r<l;r++)126===(a=t[r]).marker&&-1!==a.end&&(s=t[a.end],(i=e.tokens[a.token]).type="s_open",i.tag="s",i.nesting=1,i.markup="~~",i.content="",(i=e.tokens[s.token]).type="s_close",i.tag="s",i.nesting=-1,i.markup="~~",i.content="","text"===e.tokens[s.token-1].type&&"~"===e.tokens[s.token-1].content&&o.push(s.token-1));for(;o.length;){for(n=(r=o.pop())+1;n<e.tokens.length&&"s_close"===e.tokens[n].type;)n++;r!==--n&&(i=e.tokens[n],e.tokens[n]=e.tokens[r],e.tokens[r]=i)}}return r9=1,r8.tokenize=function(e,t){var r,n,a,s,i,o=e.pos,l=e.src.charCodeAt(o);if(t||126!==l||(s=(n=e.scanDelims(e.pos,!0)).length,i=String.fromCharCode(l),s<2))return!1;for(s%2&&(e.push("text","",0).content=i,s--),r=0;r<s;r+=2)e.push("text","",0).content=i+i,e.delimiters.push({marker:l,length:0,token:e.tokens.length-1,end:-1,open:n.can_open,close:n.can_close});return e.pos+=n.length,!0},r8.postProcess=function(t){var r,n=t.tokens_meta,a=t.tokens_meta.length;for(e(t,t.delimiters),r=0;r<a;r++)n[r]&&n[r].delimiters&&e(t,n[r].delimiters)},r8}var ne,nt={};function nr(){if(ne)return nt;function e(e,t){var r,n,a,s,i,o;for(r=t.length-1;r>=0;r--)(95===(n=t[r]).marker||42===n.marker)&&-1!==n.end&&(a=t[n.end],o=r>0&&t[r-1].end===n.end+1&&t[r-1].marker===n.marker&&t[r-1].token===n.token-1&&t[n.end+1].token===a.token+1,i=String.fromCharCode(n.marker),(s=e.tokens[n.token]).type=o?"strong_open":"em_open",s.tag=o?"strong":"em",s.nesting=1,s.markup=o?i+i:i,s.content="",(s=e.tokens[a.token]).type=o?"strong_close":"em_close",s.tag=o?"strong":"em",s.nesting=-1,s.markup=o?i+i:i,s.content="",o&&(e.tokens[t[r-1].token].content="",e.tokens[t[n.end+1].token].content="",r--))}return ne=1,nt.tokenize=function(e,t){var r,n,a=e.pos,s=e.src.charCodeAt(a);if(t||95!==s&&42!==s)return!1;for(n=e.scanDelims(e.pos,42===s),r=0;r<n.length;r++)e.push("text","",0).content=String.fromCharCode(s),e.delimiters.push({marker:s,length:n.length,token:e.tokens.length-1,end:-1,open:n.can_open,close:n.can_close});return e.pos+=n.length,!0},nt.postProcess=function(t){var r,n=t.tokens_meta,a=t.tokens_meta.length;for(e(t,t.delimiters),r=0;r<a;r++)n[r]&&n[r].delimiters&&e(t,n[r].delimiters)},nt}let nn=/^xn--/,na=/[^\0-\x7F]/,ns=/[\x2E\u3002\uFF0E\uFF61]/g,ni={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},no=Math.floor,nl=String.fromCharCode;function nc(e){throw RangeError(ni[e])}function nu(e,t){let r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+(function(e,t){let r=[],n=e.length;for(;n--;)r[n]=t(e[n]);return r})((e=e.replace(ns,".")).split("."),t).join(".")}function nh(e){let t=[],r=0,n=e.length;for(;r<n;){let a=e.charCodeAt(r++);if(a>=55296&&a<=56319&&r<n){let n=e.charCodeAt(r++);(64512&n)==56320?t.push(((1023&a)<<10)+(1023&n)+65536):(t.push(a),r--)}else t.push(a)}return t}let np=e=>String.fromCodePoint(...e),nd=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},nf=function(e,t,r){let n=0;for(e=r?no(e/700):e>>1,e+=no(e/t);e>455;n+=36)e=no(e/35);return no(n+36*e/(e+38))},ng=function(e){let t=[],r=e.length,n=0,a=128,s=72,i=e.lastIndexOf("-");i<0&&(i=0);for(let r=0;r<i;++r)e.charCodeAt(r)>=128&&nc("not-basic"),t.push(e.charCodeAt(r));for(let l=i>0?i+1:0;l<r;){let i=n;for(let t=1,a=36;;a+=36){var o;l>=r&&nc("invalid-input");let i=(o=e.charCodeAt(l++))>=48&&o<58?26+(o-48):o>=65&&o<91?o-65:o>=97&&o<123?o-97:36;i>=36&&nc("invalid-input"),i>no((0x7fffffff-n)/t)&&nc("overflow"),n+=i*t;let c=a<=s?1:a>=s+26?26:a-s;if(i<c)break;let u=36-c;t>no(0x7fffffff/u)&&nc("overflow"),t*=u}let c=t.length+1;s=nf(n-i,c,0==i),no(n/c)>0x7fffffff-a&&nc("overflow"),a+=no(n/c),n%=c,t.splice(n++,0,a)}return String.fromCodePoint(...t)},nm=function(e){let t=[],r=(e=nh(e)).length,n=128,a=0,s=72;for(let r of e)r<128&&t.push(nl(r));let i=t.length,o=i;for(i&&t.push("-");o<r;){let r=0x7fffffff;for(let t of e)t>=n&&t<r&&(r=t);let l=o+1;for(let c of(r-n>no((0x7fffffff-a)/l)&&nc("overflow"),a+=(r-n)*l,n=r,e))if(c<n&&++a>0x7fffffff&&nc("overflow"),c===n){let e=a;for(let r=36;;r+=36){let n=r<=s?1:r>=s+26?26:r-s;if(e<n)break;let a=e-n,i=36-n;t.push(nl(nd(n+a%i,0))),e=no(a/i)}t.push(nl(nd(e,0))),s=nf(a,l,o===i),a=0,++o}++a,++n}return t.join("")},nb=function(e){return nu(e,function(e){return nn.test(e)?ng(e.slice(4).toLowerCase()):e})},n_=function(e){return nu(e,function(e){return na.test(e)?"xn--"+nm(e):e})},nv=function(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}),r}(Object.freeze(Object.defineProperty({__proto__:null,decode:ng,default:{version:"2.3.1",ucs2:{decode:nh,encode:np},decode:ng,encode:nm,toASCII:n_,toUnicode:nb},encode:nm,toASCII:n_,toUnicode:nb,ucs2decode:nh,ucs2encode:np},Symbol.toStringTag,{value:"Module"}))),nk=ea((I||(I=1,T=function(){if(M)return N;M=1;var e=t$(),t=(t4||(t4=1,tQ||(tQ=1,tK=function(e,t,r){var n,a,s,i,o=-1,l=e.posMax,c=e.pos;for(e.pos=t+1,n=1;e.pos<l;){if(93===(s=e.src.charCodeAt(e.pos))&&0==--n){a=!0;break}if(i=e.pos,e.md.inline.skipToken(e),91===s){if(i===e.pos-1)n++;else if(r)return e.pos=c,-1}}return a&&(o=e.pos),e.pos=c,o}),tJ.parseLinkLabel=tK,tJ.parseLinkDestination=function(){if(t2)return t1;t2=1;var e=t$().unescapeAll;return t1=function(t,r,n){var a,s,i=r,o={ok:!1,pos:0,lines:0,str:""};if(60===t.charCodeAt(i)){for(i++;i<n&&10!==(a=t.charCodeAt(i))&&60!==a;){if(62===a)return o.pos=i+1,o.str=e(t.slice(r+1,i)),o.ok=!0,o;if(92===a&&i+1<n){i+=2;continue}i++}return o}for(s=0;i<n&&!(32===(a=t.charCodeAt(i))||a<32||127===a);){if(92===a&&i+1<n){if(32===t.charCodeAt(i+1))break;i+=2;continue}if(40===a&&++s>32)return o;if(41===a){if(0===s)break;s--}i++}return r===i||0!==s||(o.str=e(t.slice(r,i)),o.pos=i,o.ok=!0),o}}(),tJ.parseLinkTitle=function(){if(t5)return t3;t5=1;var e=t$().unescapeAll;return t3=function(t,r,n){var a,s,i=0,o=r,l={ok:!1,pos:0,lines:0,str:""};if(o>=n||34!==(s=t.charCodeAt(o))&&39!==s&&40!==s)return l;for(o++,40===s&&(s=41);o<n;){if((a=t.charCodeAt(o))===s)return l.pos=o+1,l.lines=i,l.str=e(t.slice(r+1,o)),l.ok=!0,l;if(40===a&&41===s)break;10===a?i++:92===a&&o+1<n&&(o++,10===t.charCodeAt(o)&&i++),o++}return l}}()),tJ),r=function(){if(t9)return t6;t9=1;var e=t$().assign,t=t$().unescapeAll,r=t$().escapeHtml,n={};function a(){this.rules=e({},n)}return n.code_inline=function(e,t,n,a,s){var i=e[t];return"<code"+s.renderAttrs(i)+">"+r(i.content)+"</code>"},n.code_block=function(e,t,n,a,s){var i=e[t];return"<pre"+s.renderAttrs(i)+"><code>"+r(e[t].content)+`</code></pre>
`},n.fence=function(e,n,a,s,i){var o,l,c,u,h=e[n],p=h.info?t(h.info).trim():"",d="",f="";return p&&(d=(c=p.split(/(\s+)/g))[0],f=c.slice(2).join("")),0===(o=a.highlight&&a.highlight(h.content,d,f)||r(h.content)).indexOf("<pre")?o+`
`:p?(l=h.attrIndex("class"),u=h.attrs?h.attrs.slice():[],l<0?u.push(["class",a.langPrefix+d]):(u[l]=u[l].slice(),u[l][1]+=" "+a.langPrefix+d),"<pre><code"+i.renderAttrs({attrs:u})+">"+o+`</code></pre>
`):"<pre><code"+i.renderAttrs(h)+">"+o+`</code></pre>
`},n.image=function(e,t,r,n,a){var s=e[t];return s.attrs[s.attrIndex("alt")][1]=a.renderInlineAsText(s.children,r,n),a.renderToken(e,t,r)},n.hardbreak=function(e,t,r){return r.xhtmlOut?`<br />
`:`<br>
`},n.softbreak=function(e,t,r){return r.breaks?r.xhtmlOut?`<br />
`:`<br>
`:`
`},n.text=function(e,t){return r(e[t].content)},n.html_block=function(e,t){return e[t].content},n.html_inline=function(e,t){return e[t].content},a.prototype.renderAttrs=function(e){var t,n,a;if(!e.attrs)return"";for(a="",t=0,n=e.attrs.length;t<n;t++)a+=" "+r(e.attrs[t][0])+'="'+r(e.attrs[t][1])+'"';return a},a.prototype.renderToken=function(e,t,r){var n,a="",s=!1,i=e[t];return i.hidden?"":(i.block&&-1!==i.nesting&&t&&e[t-1].hidden&&(a+=`
`),a+=(-1===i.nesting?"</":"<")+i.tag,a+=this.renderAttrs(i),0===i.nesting&&r.xhtmlOut&&(a+=" /"),i.block&&(s=!0,1===i.nesting&&t+1<e.length&&("inline"===(n=e[t+1]).type||n.hidden||-1===n.nesting&&n.tag===i.tag)&&(s=!1)),a+=s?`>
`:">")},a.prototype.renderInline=function(e,t,r){for(var n,a="",s=this.rules,i=0,o=e.length;i<o;i++)"u">typeof s[n=e[i].type]?a+=s[n](e,i,t,r,this):a+=this.renderToken(e,i,t);return a},a.prototype.renderInlineAsText=function(e,t,r){for(var n="",a=0,s=e.length;a<s;a++)"text"===e[a].type?n+=e[a].content:"image"===e[a].type?n+=this.renderInlineAsText(e[a].children,t,r):"softbreak"===e[a].type&&(n+=`
`);return n},a.prototype.render=function(e,t,r){var n,a,s,i="",o=this.rules;for(n=0,a=e.length;n<a;n++)"inline"===(s=e[n].type)?i+=this.renderInline(e[n].children,t,r):"u">typeof o[s]?i+=o[s](e,n,t,r,this):i+=this.renderToken(e,n,t,r);return i},t6=a}(),T=function(){if(rv)return r_;rv=1;var e=tY(),t=[["normalize",function(){if(rt)return re;rt=1;var e=/\r\n?|\n/g,t=/\0/g;return re=function(r){var n;n=(n=r.src.replace(e,`
`)).replace(t,"�"),r.src=n}}()],["block",(rn||(rn=1,rr=function(e){var t;e.inlineMode?((t=new e.Token("inline","",0)).content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}),rr)],["inline",(rs||(rs=1,ra=function(e){var t,r,n,a=e.tokens;for(r=0,n=a.length;r<n;r++)"inline"===(t=a[r]).type&&e.md.inline.parse(t.content,e.md,e.env,t.children)}),ra)],["linkify",function(){if(ro)return ri;ro=1;var e=t$().arrayReplaceAt;return ri=function(t){var r,n,a,s,i,o,l,c,u,h,p,d,f,g,m,b,_,v,k,y=t.tokens;if(t.md.options.linkify){for(s=0,i=y.length;s<i;s++)if(!("inline"!==y[s].type||!t.md.linkify.pretest(y[s].content)))for(o=y[s].children,m=0,a=o.length-1;a>=0;a--){if("link_close"===(c=o[a]).type){for(a--;o[a].level!==c.level&&"link_open"!==o[a].type;)a--;continue}if("html_inline"===c.type&&(r=c.content,/^<a[>\s]/i.test(r)&&m>0&&m--,n=c.content,/^<\/a\s*>/i.test(n)&&m++),!(m>0)&&"text"===c.type&&t.md.linkify.test(c.content)){for(p=c.content,k=t.md.linkify.match(p),u=[],g=c.level,f=0,k.length>0&&0===k[0].index&&a>0&&"text_special"===o[a-1].type&&(k=k.slice(1)),h=0;h<k.length;h++)b=k[h].url,_=t.md.normalizeLink(b),t.md.validateLink(_)&&(v=k[h].text,v=k[h].schema?"mailto:"!==k[h].schema||/^mailto:/i.test(v)?t.md.normalizeLinkText(v):t.md.normalizeLinkText("mailto:"+v).replace(/^mailto:/,""):t.md.normalizeLinkText("http://"+v).replace(/^http:\/\//,""),(d=k[h].index)>f&&((l=new t.Token("text","",0)).content=p.slice(f,d),l.level=g,u.push(l)),(l=new t.Token("link_open","a",1)).attrs=[["href",_]],l.level=g++,l.markup="linkify",l.info="auto",u.push(l),(l=new t.Token("text","",0)).content=v,l.level=g,u.push(l),(l=new t.Token("link_close","a",-1)).level=--g,l.markup="linkify",l.info="auto",u.push(l),f=k[h].lastIndex);f<p.length&&((l=new t.Token("text","",0)).content=p.slice(f),l.level=g,u.push(l)),y[s].children=o=e(o,a,u)}}}}}()],["replacements",function(){if(rc)return rl;rc=1;var e=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,t=/\((c|tm|r)\)/i,r=/\((c|tm|r)\)/ig,n={c:"\xa9",r:"\xae",tm:"™"};function a(e,t){return n[t.toLowerCase()]}return rl=function(n){var s;if(n.md.options.typographer)for(s=n.tokens.length-1;s>=0;s--)"inline"===n.tokens[s].type&&(t.test(n.tokens[s].content)&&function(e){var t,n,s=0;for(t=e.length-1;t>=0;t--)"text"!==(n=e[t]).type||s||(n.content=n.content.replace(r,a)),"link_open"===n.type&&"auto"===n.info&&s--,"link_close"===n.type&&"auto"===n.info&&s++}(n.tokens[s].children),e.test(n.tokens[s].content)&&function(t){var r,n,a=0;for(r=t.length-1;r>=0;r--)"text"===(n=t[r]).type&&!a&&e.test(n.content)&&(n.content=n.content.replace(/\+-/g,"\xb1").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/mg,"$1—").replace(/(^|\s)--(?=\s|$)/mg,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/mg,"$1–")),"link_open"===n.type&&"auto"===n.info&&a--,"link_close"===n.type&&"auto"===n.info&&a++}(n.tokens[s].children))}}()],["smartquotes",function(){if(rh)return ru;rh=1;var e=t$().isWhiteSpace,t=t$().isPunctChar,r=t$().isMdAsciiPunct,n=/['"]/,a=/['"]/g;function s(e,t,r){return e.slice(0,t)+r+e.slice(t+1)}return ru=function(i){var o;if(i.md.options.typographer)for(o=i.tokens.length-1;o>=0;o--)"inline"===i.tokens[o].type&&n.test(i.tokens[o].content)&&function(n,i){var o,l,c,u,h,p,d,f,g,m,b,_,v,k,y,w,E,C,A,x,S;for(A=[],o=0;o<n.length;o++){for(l=n[o],d=n[o].level,E=A.length-1;E>=0&&!(A[E].level<=d);E--);if(A.length=E+1,"text"===l.type){c=l.content,h=0,p=c.length;e:for(;h<p&&(a.lastIndex=h,u=a.exec(c));){if(y=w=!0,h=u.index+1,C="'"===u[0],g=32,u.index-1>=0)g=c.charCodeAt(u.index-1);else for(E=o-1;E>=0&&"softbreak"!==n[E].type&&"hardbreak"!==n[E].type;E--)if(n[E].content){g=n[E].content.charCodeAt(n[E].content.length-1);break}if(m=32,h<p)m=c.charCodeAt(h);else for(E=o+1;E<n.length&&"softbreak"!==n[E].type&&"hardbreak"!==n[E].type;E++)if(n[E].content){m=n[E].content.charCodeAt(0);break}if(b=r(g)||t(String.fromCharCode(g)),_=r(m)||t(String.fromCharCode(m)),v=e(g),(k=e(m))?y=!1:_&&(v||b||(y=!1)),v?w=!1:b&&(k||_||(w=!1)),34===m&&'"'===u[0]&&g>=48&&g<=57&&(w=y=!1),y&&w&&(y=b,w=_),!y&&!w){C&&(l.content=s(l.content,u.index,"’"));continue}if(w){for(E=A.length-1;E>=0&&(f=A[E],!(A[E].level<d));E--)if(f.single===C&&A[E].level===d){f=A[E],C?(x=i.md.options.quotes[2],S=i.md.options.quotes[3]):(x=i.md.options.quotes[0],S=i.md.options.quotes[1]),l.content=s(l.content,u.index,S),n[f.token].content=s(n[f.token].content,f.pos,x),h+=S.length-1,f.token===o&&(h+=x.length-1),p=(c=l.content).length,A.length=E;continue e}}y?A.push({token:o,pos:u.index,single:C,level:d}):w&&C&&(l.content=s(l.content,u.index,"’"))}}}}(i.tokens[o].children,i)}}()],["text_join",(rd||(rd=1,rp=function(e){var t,r,n,a,s,i,o=e.tokens;for(t=0,r=o.length;t<r;t++)if("inline"===o[t].type){for(s=(n=o[t].children).length,a=0;a<s;a++)"text_special"===n[a].type&&(n[a].type="text");for(a=i=0;a<s;a++)"text"===n[a].type&&a+1<s&&"text"===n[a+1].type?n[a+1].content=n[a].content+n[a+1].content:(a!==i&&(n[i]=n[a]),i++);a!==i&&(n.length=i)}}),rp)]];function r(){this.ruler=new e;for(var r=0;r<t.length;r++)this.ruler.push(t[r][0],t[r][1])}return r.prototype.process=function(e){var t,r,n;for(n=this.ruler.getRules(""),t=0,r=n.length;t<r;t++)n[t](e)},r.prototype.State=function(){if(rb)return rm;rb=1;var e=t0();function t(e,t,r){this.src=e,this.env=r,this.tokens=[],this.inlineMode=!1,this.md=t}return t.prototype.Token=e,rm=t}(),r_=r}(),I=function(){if(rK)return rX;rK=1;var e=tY(),t=[["table",function(){if(ry)return rk;ry=1;var e=t$().isSpace;function t(e,t){var r=e.bMarks[t]+e.tShift[t],n=e.eMarks[t];return e.src.slice(r,n)}function r(e){var t,r=[],n=0,a=e.length,s=!1,i=0,o="";for(t=e.charCodeAt(n);n<a;)124===t&&(s?(o+=e.substring(i,n-1),i=n):(r.push(o+e.substring(i,n)),o="",i=n+1)),s=92===t,n++,t=e.charCodeAt(n);return r.push(o+e.substring(i)),r}return rk=function(n,a,s,i){var o,l,c,u,h,p,d,f,g,m,b,_,v,k,y,w,E,C;if(a+2>s||(p=a+1,n.sCount[p]<n.blkIndent)||n.sCount[p]-n.blkIndent>=4||(c=n.bMarks[p]+n.tShift[p])>=n.eMarks[p]||124!==(E=n.src.charCodeAt(c++))&&45!==E&&58!==E||c>=n.eMarks[p]||124!==(C=n.src.charCodeAt(c++))&&45!==C&&58!==C&&!e(C)||45===E&&e(C))return!1;for(;c<n.eMarks[p];){if(124!==(o=n.src.charCodeAt(c))&&45!==o&&58!==o&&!e(o))return!1;c++}for(d=(l=t(n,a+1)).split("|"),m=[],u=0;u<d.length;u++){if(!(b=d[u].trim())){if(0===u||u===d.length-1)continue;return!1}if(!/^:?-+:?$/.test(b))return!1;58===b.charCodeAt(b.length-1)?m.push(58===b.charCodeAt(0)?"center":"right"):58===b.charCodeAt(0)?m.push("left"):m.push("")}if(-1===(l=t(n,a).trim()).indexOf("|")||n.sCount[a]-n.blkIndent>=4||((d=r(l)).length&&""===d[0]&&d.shift(),d.length&&""===d[d.length-1]&&d.pop(),0===(f=d.length)||f!==m.length))return!1;if(i)return!0;for(k=n.parentType,n.parentType="table",w=n.md.block.ruler.getRules("blockquote"),(g=n.push("table_open","table",1)).map=_=[a,0],(g=n.push("thead_open","thead",1)).map=[a,a+1],(g=n.push("tr_open","tr",1)).map=[a,a+1],u=0;u<d.length;u++)g=n.push("th_open","th",1),m[u]&&(g.attrs=[["style","text-align:"+m[u]]]),(g=n.push("inline","",0)).content=d[u].trim(),g.children=[],g=n.push("th_close","th",-1);for(g=n.push("tr_close","tr",-1),g=n.push("thead_close","thead",-1),p=a+2;p<s&&!(n.sCount[p]<n.blkIndent);p++){for(y=!1,u=0,h=w.length;u<h;u++)if(w[u](n,p,s,!0)){y=!0;break}if(y||!(l=t(n,p).trim())||n.sCount[p]-n.blkIndent>=4)break;for((d=r(l)).length&&""===d[0]&&d.shift(),d.length&&""===d[d.length-1]&&d.pop(),p===a+2&&((g=n.push("tbody_open","tbody",1)).map=v=[a+2,0]),(g=n.push("tr_open","tr",1)).map=[p,p+1],u=0;u<f;u++)g=n.push("td_open","td",1),m[u]&&(g.attrs=[["style","text-align:"+m[u]]]),(g=n.push("inline","",0)).content=d[u]?d[u].trim():"",g.children=[],g=n.push("td_close","td",-1);g=n.push("tr_close","tr",-1)}return v&&(g=n.push("tbody_close","tbody",-1),v[1]=p),g=n.push("table_close","table",-1),_[1]=p,n.parentType=k,n.line=p,!0}}(),["paragraph","reference"]],["code",(rE||(rE=1,rw=function(e,t,r){var n,a,s;if(e.sCount[t]-e.blkIndent<4)return!1;for(a=n=t+1;n<r;){if(e.isEmpty(n)){n++;continue}if(e.sCount[n]-e.blkIndent>=4){a=++n;continue}break}return e.line=a,(s=e.push("code_block","code",0)).content=e.getLines(t,a,4+e.blkIndent,!1)+`
`,s.map=[t,e.line],!0}),rw)],["fence",(rA||(rA=1,rC=function(e,t,r,n){var a,s,i,o,l,c,u,h=!1,p=e.bMarks[t]+e.tShift[t],d=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4||p+3>d||126!==(a=e.src.charCodeAt(p))&&96!==a||(l=p,(s=(p=e.skipChars(p,a))-l)<3)||(u=e.src.slice(l,p),i=e.src.slice(p,d),96===a&&i.indexOf(String.fromCharCode(a))>=0))return!1;if(n)return!0;for(o=t;!(++o>=r||(p=l=e.bMarks[o]+e.tShift[o])<(d=e.eMarks[o])&&e.sCount[o]<e.blkIndent);)if(e.src.charCodeAt(p)===a&&!(e.sCount[o]-e.blkIndent>=4)&&!((p=e.skipChars(p,a))-l<s)&&!((p=e.skipSpaces(p))<d)){h=!0;break}return s=e.sCount[t],e.line=o+ +!!h,(c=e.push("fence","code",0)).info=i,c.content=e.getLines(t+1,o,s,!0),c.markup=u,c.map=[t,e.line],!0}),rC),["paragraph","reference","blockquote","list"]],["blockquote",function(){if(rS)return rx;rS=1;var e=t$().isSpace;return rx=function(t,r,n,a){var s,i,o,l,c,u,h,p,d,f,g,m,b,_,v,k,y,w,E,C,A=t.lineMax,x=t.bMarks[r]+t.tShift[r],S=t.eMarks[r];if(t.sCount[r]-t.blkIndent>=4||62!==t.src.charCodeAt(x))return!1;if(a)return!0;for(f=[],g=[],_=[],v=[],w=t.md.block.ruler.getRules("blockquote"),b=t.parentType,t.parentType="blockquote",p=r;p<n&&(C=t.sCount[p]<t.blkIndent,!((x=t.bMarks[p]+t.tShift[p])>=(S=t.eMarks[p])));p++){if(62===t.src.charCodeAt(x++)&&!C){for(l=t.sCount[p]+1,32===t.src.charCodeAt(x)?(x++,l++,s=!1,k=!0):9===t.src.charCodeAt(x)?(k=!0,(t.bsCount[p]+l)%4==3?(x++,l++,s=!1):s=!0):k=!1,d=l,f.push(t.bMarks[p]),t.bMarks[p]=x;x<S&&e(i=t.src.charCodeAt(x));)9===i?d+=4-(d+t.bsCount[p]+ +!!s)%4:d++,x++;u=x>=S,g.push(t.bsCount[p]),t.bsCount[p]=t.sCount[p]+1+ +!!k,_.push(t.sCount[p]),t.sCount[p]=d-l,v.push(t.tShift[p]),t.tShift[p]=x-t.bMarks[p];continue}if(u)break;for(y=!1,o=0,c=w.length;o<c;o++)if(w[o](t,p,n,!0)){y=!0;break}if(y){t.lineMax=p,0!==t.blkIndent&&(f.push(t.bMarks[p]),g.push(t.bsCount[p]),v.push(t.tShift[p]),_.push(t.sCount[p]),t.sCount[p]-=t.blkIndent);break}f.push(t.bMarks[p]),g.push(t.bsCount[p]),v.push(t.tShift[p]),_.push(t.sCount[p]),t.sCount[p]=-1}for(m=t.blkIndent,t.blkIndent=0,(E=t.push("blockquote_open","blockquote",1)).markup=">",E.map=h=[r,0],t.md.block.tokenize(t,r,p),(E=t.push("blockquote_close","blockquote",-1)).markup=">",t.lineMax=A,t.parentType=b,h[1]=t.line,o=0;o<v.length;o++)t.bMarks[o+r]=f[o],t.tShift[o+r]=v[o],t.sCount[o+r]=_[o],t.bsCount[o+r]=g[o];return t.blkIndent=m,!0}}(),["paragraph","reference","blockquote","list"]],["hr",function(){if(rL)return rD;rL=1;var e=t$().isSpace;return rD=function(t,r,n,a){var s,i,o,l,c=t.bMarks[r]+t.tShift[r],u=t.eMarks[r];if(t.sCount[r]-t.blkIndent>=4||42!==(s=t.src.charCodeAt(c++))&&45!==s&&95!==s)return!1;for(i=1;c<u;){if((o=t.src.charCodeAt(c++))!==s&&!e(o))return!1;o===s&&i++}return!(i<3)&&(a||(t.line=r+1,(l=t.push("hr","hr",0)).map=[r,t.line],l.markup=Array(i+1).join(String.fromCharCode(s))),!0)}}(),["paragraph","reference","blockquote","list"]],["list",function(){if(rN)return rR;rN=1;var e=t$().isSpace;function t(t,r){var n,a,s;return a=t.bMarks[r]+t.tShift[r],s=t.eMarks[r],42!==(n=t.src.charCodeAt(a++))&&45!==n&&43!==n||a<s&&!e(t.src.charCodeAt(a))?-1:a}function r(t,r){var n,a=t.bMarks[r]+t.tShift[r],s=a,i=t.eMarks[r];if(s+1>=i||(n=t.src.charCodeAt(s++))<48||n>57)return -1;for(;;){if(s>=i)return -1;if((n=t.src.charCodeAt(s++))>=48&&n<=57){if(s-a>=10)return -1;continue}if(41===n||46===n)break;return -1}return s<i&&!e(n=t.src.charCodeAt(s))?-1:s}return rR=function(e,n,a,s){var i,o,l,c,u,h,p,d,f,g,m,b,_,v,k,y,w,E,C,A,x,S,D,L,R,N,M,T=n,I=!1,F=!0;if(e.sCount[T]-e.blkIndent>=4||e.listIndent>=0&&e.sCount[T]-e.listIndent>=4&&e.sCount[T]<e.blkIndent)return!1;if(s&&"paragraph"===e.parentType&&e.sCount[T]>=e.blkIndent&&(I=!0),(S=r(e,T))>=0){if(p=!0,L=e.bMarks[T]+e.tShift[T],_=Number(e.src.slice(L,S-1)),I&&1!==_)return!1}else{if(!((S=t(e,T))>=0))return!1;p=!1}if(I&&e.skipSpaces(S)>=e.eMarks[T])return!1;if(s)return!0;for(b=e.src.charCodeAt(S-1),m=e.tokens.length,p?(M=e.push("ordered_list_open","ol",1),1!==_&&(M.attrs=[["start",_]])):M=e.push("bullet_list_open","ul",1),M.map=g=[T,0],M.markup=String.fromCharCode(b),D=!1,N=e.md.block.ruler.getRules("list"),w=e.parentType,e.parentType="list";T<a;){for(x=S,v=e.eMarks[T],h=k=e.sCount[T]+S-(e.bMarks[T]+e.tShift[T]);x<v;){if(9===(i=e.src.charCodeAt(x)))k+=4-(k+e.bsCount[T])%4;else if(32===i)k++;else break;x++}if((u=(o=x)>=v?1:k-h)>4&&(u=1),c=h+u,(M=e.push("list_item_open","li",1)).markup=String.fromCharCode(b),M.map=d=[T,0],p&&(M.info=e.src.slice(L,S-1)),A=e.tight,C=e.tShift[T],E=e.sCount[T],y=e.listIndent,e.listIndent=e.blkIndent,e.blkIndent=c,e.tight=!0,e.tShift[T]=o-e.bMarks[T],e.sCount[T]=k,o>=v&&e.isEmpty(T+1)?e.line=Math.min(e.line+2,a):e.md.block.tokenize(e,T,a,!0),(!e.tight||D)&&(F=!1),D=e.line-T>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=y,e.tShift[T]=C,e.sCount[T]=E,e.tight=A,(M=e.push("list_item_close","li",-1)).markup=String.fromCharCode(b),T=e.line,d[1]=T,T>=a||e.sCount[T]<e.blkIndent||e.sCount[T]-e.blkIndent>=4)break;for(R=!1,l=0,f=N.length;l<f;l++)if(N[l](e,T,a,!0)){R=!0;break}if(R)break;if(p){if((S=r(e,T))<0)break;L=e.bMarks[T]+e.tShift[T]}else if((S=t(e,T))<0)break;if(b!==e.src.charCodeAt(S-1))break}return(M=p?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1)).markup=String.fromCharCode(b),g[1]=T,e.line=T,e.parentType=w,F&&function(e,t){var r,n,a=e.level+2;for(r=t+2,n=e.tokens.length-2;r<n;r++)e.tokens[r].level===a&&"paragraph_open"===e.tokens[r].type&&(e.tokens[r+2].hidden=!0,e.tokens[r].hidden=!0,r+=2)}(e,m),!0}}(),["paragraph","reference","blockquote"]],["reference",function(){if(rT)return rM;rT=1;var e=t$().normalizeReference,t=t$().isSpace;return rM=function(r,n,a,s){var i,o,l,c,u,h,p,d,f,g,m,b,_,v,k,y,w=0,E=r.bMarks[n]+r.tShift[n],C=r.eMarks[n],A=n+1;if(r.sCount[n]-r.blkIndent>=4||91!==r.src.charCodeAt(E))return!1;for(;++E<C;)if(93===r.src.charCodeAt(E)&&92!==r.src.charCodeAt(E-1)){if(E+1===C||58!==r.src.charCodeAt(E+1))return!1;break}for(c=r.lineMax,k=r.md.block.ruler.getRules("reference"),g=r.parentType,r.parentType="reference";A<c&&!r.isEmpty(A);A++)if(!(r.sCount[A]-r.blkIndent>3)&&!(r.sCount[A]<0)){for(v=!1,h=0,p=k.length;h<p;h++)if(k[h](r,A,c,!0)){v=!0;break}if(v)break}for(C=(_=r.getLines(n,A,r.blkIndent,!1).trim()).length,E=1;E<C;E++){if(91===(i=_.charCodeAt(E)))return!1;if(93===i){f=E;break}10===i?w++:92===i&&++E<C&&10===_.charCodeAt(E)&&w++}if(f<0||58!==_.charCodeAt(f+1))return!1;for(E=f+2;E<C;E++)if(10===(i=_.charCodeAt(E)))w++;else if(!t(i))break;if(!(m=r.md.helpers.parseLinkDestination(_,E,C)).ok||(u=r.md.normalizeLink(m.str),!r.md.validateLink(u)))return!1;for(E=m.pos,w+=m.lines,o=E,l=w,b=E;E<C;E++)if(10===(i=_.charCodeAt(E)))w++;else if(!t(i))break;for(m=r.md.helpers.parseLinkTitle(_,E,C),E<C&&b!==E&&m.ok?(y=m.str,E=m.pos,w+=m.lines):(y="",E=o,w=l);E<C&&t(i=_.charCodeAt(E));)E++;if(E<C&&10!==_.charCodeAt(E)&&y)for(y="",E=o,w=l;E<C&&t(i=_.charCodeAt(E));)E++;return(!(E<C)||10===_.charCodeAt(E))&&!!(d=e(_.slice(1,f)))&&(s||(typeof r.env.references>"u"&&(r.env.references={}),typeof r.env.references[d]>"u"&&(r.env.references[d]={title:y,href:u}),r.parentType=g,r.line=n+w+1),!0)}}()],["html_block",function(){if(rP)return rB;rP=1;var e=(rF||(rF=1,rI=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","section","source","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]),rI),t=rO().HTML_OPEN_CLOSE_TAG_RE,r=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[RegExp("^</?("+e.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[RegExp(t.source+"\\s*$"),/^$/,!1]];return rB=function(e,t,n,a){var s,i,o,l,c=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4||!e.md.options.html||60!==e.src.charCodeAt(c))return!1;for(l=e.src.slice(c,u),s=0;s<r.length&&!r[s][0].test(l);s++);if(s===r.length)return!1;if(a)return r[s][2];if(i=t+1,!r[s][1].test(l)){for(;i<n&&!(e.sCount[i]<e.blkIndent);i++)if(c=e.bMarks[i]+e.tShift[i],u=e.eMarks[i],l=e.src.slice(c,u),r[s][1].test(l)){0!==l.length&&i++;break}}return e.line=i,(o=e.push("html_block","",0)).map=[t,i],o.content=e.getLines(t,i,e.blkIndent,!0),!0}}(),["paragraph","reference","blockquote"]],["heading",function(){if(rj)return r$;rj=1;var e=t$().isSpace;return r$=function(t,r,n,a){var s,i,o,l,c=t.bMarks[r]+t.tShift[r],u=t.eMarks[r];if(t.sCount[r]-t.blkIndent>=4||35!==(s=t.src.charCodeAt(c))||c>=u)return!1;for(i=1,s=t.src.charCodeAt(++c);35===s&&c<u&&i<=6;)i++,s=t.src.charCodeAt(++c);return!(i>6)&&(!(c<u)||!!e(s))&&(a||(u=t.skipSpacesBack(u,c),(o=t.skipCharsBack(u,35,c))>c&&e(t.src.charCodeAt(o-1))&&(u=o),t.line=r+1,(l=t.push("heading_open","h"+String(i),1)).markup="########".slice(0,i),l.map=[r,t.line],(l=t.push("inline","",0)).content=t.src.slice(c,u).trim(),l.map=[r,t.line],l.children=[],(l=t.push("heading_close","h"+String(i),-1)).markup="########".slice(0,i)),!0)}}(),["paragraph","reference","blockquote"]],["lheading",(rH||(rH=1,rU=function(e,t,r){var n,a,s,i,o,l,c,u,h,p,d=t+1,f=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;for(p=e.parentType,e.parentType="paragraph";d<r&&!e.isEmpty(d);d++)if(!(e.sCount[d]-e.blkIndent>3)){if(e.sCount[d]>=e.blkIndent&&(l=e.bMarks[d]+e.tShift[d])<(c=e.eMarks[d])&&(45===(h=e.src.charCodeAt(l))||61===h)&&(l=e.skipChars(l,h),(l=e.skipSpaces(l))>=c)){u=61===h?1:2;break}if(!(e.sCount[d]<0)){for(a=!1,s=0,i=f.length;s<i;s++)if(f[s](e,d,r,!0)){a=!0;break}if(a)break}}return!!u&&(n=e.getLines(t,d,e.blkIndent,!1).trim(),e.line=d+1,(o=e.push("heading_open","h"+String(u),1)).markup=String.fromCharCode(h),o.map=[t,e.line],(o=e.push("inline","",0)).content=n,o.map=[t,e.line-1],o.children=[],(o=e.push("heading_close","h"+String(u),-1)).markup=String.fromCharCode(h),e.parentType=p,!0)}),rU)],["paragraph",(rV||(rV=1,rZ=function(e,t,r){var n,a,s,i,o,l,c=t+1,u=e.md.block.ruler.getRules("paragraph");for(l=e.parentType,e.parentType="paragraph";c<r&&!e.isEmpty(c);c++)if(!(e.sCount[c]-e.blkIndent>3)&&!(e.sCount[c]<0)){for(a=!1,s=0,i=u.length;s<i;s++)if(u[s](e,c,r,!0)){a=!0;break}if(a)break}return n=e.getLines(t,c,e.blkIndent,!1).trim(),e.line=c,(o=e.push("paragraph_open","p",1)).map=[t,e.line],(o=e.push("inline","",0)).content=n,o.map=[t,e.line],o.children=[],o=e.push("paragraph_close","p",-1),e.parentType=l,!0}),rZ)]];function r(){this.ruler=new e;for(var r=0;r<t.length;r++)this.ruler.push(t[r][0],t[r][1],{alt:(t[r][2]||[]).slice()})}return r.prototype.tokenize=function(e,t,r){for(var n,a,s,i=this.ruler.getRules(""),o=i.length,l=t,c=!1,u=e.md.options.maxNesting;l<r&&(e.line=l=e.skipEmptyLines(l),!(l>=r||e.sCount[l]<e.blkIndent));){if(e.level>=u){e.line=r;break}for(s=e.line,a=0;a<o;a++)if(n=i[a](e,l,r,!1)){if(s>=e.line)throw Error("block rule didn't increment state.line");break}if(!n)throw Error("none of the block rules matched");e.tight=!c,e.isEmpty(e.line-1)&&(c=!0),(l=e.line)<r&&e.isEmpty(l)&&(c=!0,e.line=++l)}},r.prototype.parse=function(e,t,r,n){var a;e&&(a=new this.State(e,t,r,n),this.tokenize(a,a.line,a.lineMax))},r.prototype.State=function(){if(rW)return rG;rW=1;var e=t0(),t=t$().isSpace;function r(e,r,n,a){var s,i,o,l,c,u,h,p;for(this.src=e,this.md=r,this.env=n,this.tokens=a,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0,this.result="",i=this.src,p=!1,o=l=u=h=0,c=i.length;l<c;l++){if(s=i.charCodeAt(l),!p){if(t(s)){u++,9===s?h+=4-h%4:h++;continue}p=!0}(10===s||l===c-1)&&(10!==s&&l++,this.bMarks.push(o),this.eMarks.push(l),this.tShift.push(u),this.sCount.push(h),this.bsCount.push(0),p=!1,u=0,h=0,o=l+1)}this.bMarks.push(i.length),this.eMarks.push(i.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}return r.prototype.push=function(t,r,n){var a=new e(t,r,n);return a.block=!0,n<0&&this.level--,a.level=this.level,n>0&&this.level++,this.tokens.push(a),a},r.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},r.prototype.skipEmptyLines=function(e){for(var t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},r.prototype.skipSpaces=function(e){for(var r=this.src.length;e<r&&t(this.src.charCodeAt(e));e++);return e},r.prototype.skipSpacesBack=function(e,r){if(e<=r)return e;for(;e>r;)if(!t(this.src.charCodeAt(--e)))return e+1;return e},r.prototype.skipChars=function(e,t){for(var r=this.src.length;e<r&&this.src.charCodeAt(e)===t;e++);return e},r.prototype.skipCharsBack=function(e,t,r){if(e<=r)return e;for(;e>r;)if(t!==this.src.charCodeAt(--e))return e+1;return e},r.prototype.getLines=function(e,r,n,a){var s,i,o,l,c,u,h,p=e;if(e>=r)return"";for(u=Array(r-e),s=0;p<r;p++,s++){for(i=0,h=l=this.bMarks[p],c=p+1<r||a?this.eMarks[p]+1:this.eMarks[p];l<c&&i<n;){if(t(o=this.src.charCodeAt(l)))9===o?i+=4-(i+this.bsCount[p])%4:i++;else if(l-h<this.tShift[p])i++;else break;l++}i>n?u[s]=Array(i-n+1).join(" ")+this.src.slice(l,c):u[s]=this.src.slice(l,c)}return u.join("")},r.prototype.Token=e,rG=r}(),rX=r}(),F=function(){if(k)return v;k=1;var e=tY(),t=[["text",rJ?rQ:(rJ=1,rQ=function(e,t){for(var r=e.pos;r<e.posMax&&!function(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}(e.src.charCodeAt(r));)r++;return r!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,r)),e.pos=r,!0)})],["linkify",function(){if(r0)return rY;r0=1;var e=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;return rY=function(t,r){var n,a,s,i,o,l,c;return!!(t.md.options.linkify&&!(t.linkLevel>0)&&!((n=t.pos)+3>t.posMax)&&58===t.src.charCodeAt(n)&&47===t.src.charCodeAt(n+1)&&47===t.src.charCodeAt(n+2)&&(a=t.pending.match(e))&&(s=a[1],i=t.md.linkify.matchAtStart(t.src.slice(n-s.length)))&&!((o=i.url).length<=s.length)&&(o=o.replace(/\*+$/,""),l=t.md.normalizeLink(o),t.md.validateLink(l)))&&(r||(t.pending=t.pending.slice(0,-s.length),(c=t.push("link_open","a",1)).attrs=[["href",l]],c.markup="linkify",c.info="auto",(c=t.push("text","",0)).content=t.md.normalizeLinkText(o),(c=t.push("link_close","a",-1)).markup="linkify",c.info="auto"),t.pos+=o.length-s.length,!0)}}()],["newline",function(){if(r2)return r1;r2=1;var e=t$().isSpace;return r1=function(t,r){var n,a,s,i=t.pos;if(10!==t.src.charCodeAt(i))return!1;if(n=t.pending.length-1,a=t.posMax,!r){if(n>=0&&32===t.pending.charCodeAt(n)){if(n>=1&&32===t.pending.charCodeAt(n-1)){for(s=n-1;s>=1&&32===t.pending.charCodeAt(s-1);)s--;t.pending=t.pending.slice(0,s),t.push("hardbreak","br",0)}else t.pending=t.pending.slice(0,-1),t.push("softbreak","br",0)}else t.push("softbreak","br",0)}for(i++;i<a&&e(t.src.charCodeAt(i));)i++;return t.pos=i,!0}}()],["escape",function(){if(r5)return r3;r5=1;for(var e=t$().isSpace,t=[],r=0;r<256;r++)t.push(0);return"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(e){t[e.charCodeAt(0)]=1}),r3=function(r,n){var a,s,i,o,l,c=r.pos,u=r.posMax;if(92!==r.src.charCodeAt(c)||++c>=u)return!1;if(10===(a=r.src.charCodeAt(c))){for(n||r.push("hardbreak","br",0),c++;c<u&&e(a=r.src.charCodeAt(c));)c++;return r.pos=c,!0}return o=r.src[c],a>=55296&&a<=56319&&c+1<u&&(s=r.src.charCodeAt(c+1))>=56320&&s<=57343&&(o+=r.src[c+1],c++),i="\\"+o,n||(l=r.push("text_special","",0),a<256&&0!==t[a]?l.content=o:l.content=i,l.markup=i,l.info="escape"),r.pos=c+1,!0}}()],["backticks",(r6||(r6=1,r4=function(e,t){var r,n,a,s,i,o,l,c,u=e.pos;if(96!==e.src.charCodeAt(u))return!1;for(r=u,u++,n=e.posMax;u<n&&96===e.src.charCodeAt(u);)u++;if(l=(a=e.src.slice(r,u)).length,e.backticksScanned&&(e.backticks[l]||0)<=r)return t||(e.pending+=a),e.pos+=l,!0;for(o=u;-1!==(i=e.src.indexOf("`",o));){for(o=i+1;o<n&&96===e.src.charCodeAt(o);)o++;if((c=o-i)===l)return t||((s=e.push("code_inline","code",0)).markup=a,s.content=e.src.slice(u,i).replace(/\n/g," ").replace(/^ (.+) $/,"$1")),e.pos=o,!0;e.backticks[c]=i}return e.backticksScanned=!0,t||(e.pending+=a),e.pos+=l,!0}),r4)],["strikethrough",r7().tokenize],["emphasis",nr().tokenize],["link",function(){if(a)return n;a=1;var e=t$().normalizeReference,t=t$().isSpace;return n=function(r,n){var a,s,i,o,l,c,u,h,p,d="",f="",g=r.pos,m=r.posMax,b=r.pos,_=!0;if(91!==r.src.charCodeAt(r.pos)||(l=r.pos+1,(o=r.md.helpers.parseLinkLabel(r,r.pos,!0))<0))return!1;if((c=o+1)<m&&40===r.src.charCodeAt(c)){for(_=!1,c++;c<m&&!(!t(s=r.src.charCodeAt(c))&&10!==s);c++);if(c>=m)return!1;if(b=c,(u=r.md.helpers.parseLinkDestination(r.src,c,r.posMax)).ok){for(d=r.md.normalizeLink(u.str),r.md.validateLink(d)?c=u.pos:d="",b=c;c<m&&!(!t(s=r.src.charCodeAt(c))&&10!==s);c++);if(u=r.md.helpers.parseLinkTitle(r.src,c,r.posMax),c<m&&b!==c&&u.ok)for(f=u.str,c=u.pos;c<m&&!(!t(s=r.src.charCodeAt(c))&&10!==s);c++);}(c>=m||41!==r.src.charCodeAt(c))&&(_=!0),c++}if(_){if(typeof r.env.references>"u")return!1;if(c<m&&91===r.src.charCodeAt(c)?(b=c+1,(c=r.md.helpers.parseLinkLabel(r,c))>=0?i=r.src.slice(b,c++):c=o+1):c=o+1,i||(i=r.src.slice(l,o)),!(h=r.env.references[e(i)]))return r.pos=g,!1;d=h.href,f=h.title}return n||(r.pos=l,r.posMax=o,r.push("link_open","a",1).attrs=a=[["href",d]],f&&a.push(["title",f]),r.linkLevel++,r.md.inline.tokenize(r),r.linkLevel--,r.push("link_close","a",-1)),r.pos=c,r.posMax=m,!0}}()],["image",function(){if(i)return s;i=1;var e=t$().normalizeReference,t=t$().isSpace;return s=function(r,n){var a,s,i,o,l,c,u,h,p,d,f,g,m,b="",_=r.pos,v=r.posMax;if(33!==r.src.charCodeAt(r.pos)||91!==r.src.charCodeAt(r.pos+1)||(c=r.pos+2,(l=r.md.helpers.parseLinkLabel(r,r.pos+1,!1))<0))return!1;if((u=l+1)<v&&40===r.src.charCodeAt(u)){for(u++;u<v&&!(!t(s=r.src.charCodeAt(u))&&10!==s);u++);if(u>=v)return!1;for(m=u,(p=r.md.helpers.parseLinkDestination(r.src,u,r.posMax)).ok&&(b=r.md.normalizeLink(p.str),r.md.validateLink(b)?u=p.pos:b=""),m=u;u<v&&!(!t(s=r.src.charCodeAt(u))&&10!==s);u++);if(p=r.md.helpers.parseLinkTitle(r.src,u,r.posMax),u<v&&m!==u&&p.ok)for(d=p.str,u=p.pos;u<v&&!(!t(s=r.src.charCodeAt(u))&&10!==s);u++);else d="";if(u>=v||41!==r.src.charCodeAt(u))return r.pos=_,!1;u++}else{if(typeof r.env.references>"u")return!1;if(u<v&&91===r.src.charCodeAt(u)?(m=u+1,(u=r.md.helpers.parseLinkLabel(r,u))>=0?o=r.src.slice(m,u++):u=l+1):u=l+1,o||(o=r.src.slice(c,l)),!(h=r.env.references[e(o)]))return r.pos=_,!1;b=h.href,d=h.title}return n||(i=r.src.slice(c,l),r.md.inline.parse(i,r.md,r.env,g=[]),(f=r.push("image","img",0)).attrs=a=[["src",b],["alt",""]],f.children=g,f.content=i,d&&a.push(["title",d])),r.pos=u,r.posMax=v,!0}}()],["autolink",function(){if(l)return o;l=1;var e=/^([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,t=/^([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)$/;return o=function(r,n){var a,s,i,o,l,c,u=r.pos;if(60!==r.src.charCodeAt(u))return!1;for(l=r.pos,c=r.posMax;;){if(++u>=c||60===(o=r.src.charCodeAt(u)))return!1;if(62===o)break}return a=r.src.slice(l+1,u),t.test(a)?(s=r.md.normalizeLink(a),!!r.md.validateLink(s)&&(n||((i=r.push("link_open","a",1)).attrs=[["href",s]],i.markup="autolink",i.info="auto",(i=r.push("text","",0)).content=r.md.normalizeLinkText(a),(i=r.push("link_close","a",-1)).markup="autolink",i.info="auto"),r.pos+=a.length+2,!0)):!!e.test(a)&&(s=r.md.normalizeLink("mailto:"+a),!!r.md.validateLink(s)&&(n||((i=r.push("link_open","a",1)).attrs=[["href",s]],i.markup="autolink",i.info="auto",(i=r.push("text","",0)).content=r.md.normalizeLinkText(a),(i=r.push("link_close","a",-1)).markup="autolink",i.info="auto"),r.pos+=a.length+2,!0))}}()],["html_inline",function(){if(u)return c;u=1;var e=rO().HTML_TAG_RE;return c=function(t,r){var n,a,s,i,o,l,c,u=t.pos;return!!(t.md.options.html&&(l=t.posMax,60===t.src.charCodeAt(u)&&!(u+2>=l))&&(33===(i=t.src.charCodeAt(u+1))||63===i||47===i||(n=32|i)>=97&&n<=122))&&!!(o=t.src.slice(u).match(e))&&(r||((c=t.push("html_inline","",0)).content=o[0],a=c.content,/^<a[>\s]/i.test(a)&&t.linkLevel++,s=c.content,/^<\/a\s*>/i.test(s)&&t.linkLevel--),t.pos+=o[0].length,!0)}}()],["entity",function(){if(p)return h;p=1;var e=tf(),t=t$().has,r=t$().isValidEntityCode,n=t$().fromCodePoint,a=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,s=/^&([a-z][a-z0-9]{1,31});/i;return h=function(i,o){var l,c,u,h=i.pos,p=i.posMax;if(38!==i.src.charCodeAt(h)||h+1>=p)return!1;if(35===i.src.charCodeAt(h+1)){if(c=i.src.slice(h).match(a))return o||(l="x"===c[1][0].toLowerCase()?parseInt(c[1].slice(1),16):parseInt(c[1],10),(u=i.push("text_special","",0)).content=r(l)?n(l):n(65533),u.markup=c[0],u.info="entity"),i.pos+=c[0].length,!0}else if((c=i.src.slice(h).match(s))&&t(e,c[1]))return o||((u=i.push("text_special","",0)).content=e[c[1]],u.markup=c[0],u.info="entity"),i.pos+=c[0].length,!0;return!1}}()]],r=[["balance_pairs",function(){if(f)return d;function e(e){var t,r,n,a,s,i,o,l,c={},u=e.length;if(u){var h=0,p=-2,d=[];for(t=0;t<u;t++)if(n=e[t],d.push(0),(e[h].marker!==n.marker||p!==n.token-1)&&(h=t),p=n.token,n.length=n.length||0,n.close){for(c.hasOwnProperty(n.marker)||(c[n.marker]=[-1,-1,-1,-1,-1,-1]),s=c[n.marker][3*!!n.open+n.length%3],i=r=h-d[h]-1;r>s;r-=d[r]+1)if((a=e[r]).marker===n.marker&&a.open&&a.end<0&&(o=!1,(a.close||n.open)&&(a.length+n.length)%3==0&&(a.length%3!=0||n.length%3!=0)&&(o=!0),!o)){l=r>0&&!e[r-1].open?d[r-1]+1:0,d[t]=t-r+l,d[r]=l,n.open=!1,a.end=t,a.close=!1,i=-1,p=-2;break}-1!==i&&(c[n.marker][3*!!n.open+(n.length||0)%3]=i)}}}return f=1,d=function(t){var r,n=t.tokens_meta,a=t.tokens_meta.length;for(e(t.delimiters),r=0;r<a;r++)n[r]&&n[r].delimiters&&e(n[r].delimiters)}}()],["strikethrough",r7().postProcess],["emphasis",nr().postProcess],["fragments_join",(m||(m=1,g=function(e){var t,r,n=0,a=e.tokens,s=e.tokens.length;for(t=r=0;t<s;t++)a[t].nesting<0&&n--,a[t].level=n,a[t].nesting>0&&n++,"text"===a[t].type&&t+1<s&&"text"===a[t+1].type?a[t+1].content=a[t].content+a[t+1].content:(t!==r&&(a[r]=a[t]),r++);t!==r&&(a.length=r)}),g)]];function y(){var n;for(this.ruler=new e,n=0;n<t.length;n++)this.ruler.push(t[n][0],t[n][1]);for(this.ruler2=new e,n=0;n<r.length;n++)this.ruler2.push(r[n][0],r[n][1])}return y.prototype.skipToken=function(e){var t,r,n=e.pos,a=this.ruler.getRules(""),s=a.length,i=e.md.options.maxNesting,o=e.cache;if("u">typeof o[n]){e.pos=o[n];return}if(e.level<i){for(r=0;r<s;r++)if(e.level++,t=a[r](e,!0),e.level--,t){if(n>=e.pos)throw Error("inline rule didn't increment state.pos");break}}else e.pos=e.posMax;t||e.pos++,o[n]=e.pos},y.prototype.tokenize=function(e){for(var t,r,n,a=this.ruler.getRules(""),s=a.length,i=e.posMax,o=e.md.options.maxNesting;e.pos<i;){if(n=e.pos,e.level<o){for(r=0;r<s;r++)if(t=a[r](e,!1)){if(n>=e.pos)throw Error("inline rule didn't increment state.pos");break}}if(t){if(e.pos>=i)break;continue}e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},y.prototype.parse=function(e,t,r,n){var a,s,i,o=new this.State(e,t,r,n);for(this.tokenize(o),i=(s=this.ruler2.getRules("")).length,a=0;a<i;a++)s[a](o)},y.prototype.State=function(){if(_)return b;_=1;var e=t0(),t=t$().isWhiteSpace,r=t$().isPunctChar,n=t$().isMdAsciiPunct;function a(e,t,r,n){this.src=e,this.env=r,this.md=t,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}return a.prototype.pushPending=function(){var t=new e("text","",0);return t.content=this.pending,t.level=this.pendingLevel,this.tokens.push(t),this.pending="",t},a.prototype.push=function(t,r,n){this.pending&&this.pushPending();var a=new e(t,r,n),s=null;return n<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),a.level=this.level,n>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],s={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(a),this.tokens_meta.push(s),a},a.prototype.scanDelims=function(e,a){var s,i,o,l,c,u,h,p,d,f=e,g=!0,m=!0,b=this.posMax,_=this.src.charCodeAt(e);for(s=e>0?this.src.charCodeAt(e-1):32;f<b&&this.src.charCodeAt(f)===_;)f++;return o=f-e,i=f<b?this.src.charCodeAt(f):32,h=n(s)||r(String.fromCharCode(s)),d=n(i)||r(String.fromCharCode(i)),u=t(s),(p=t(i))?g=!1:d&&(u||h||(g=!1)),u?m=!1:h&&(p||d||(m=!1)),a?(l=g,c=m):(l=g&&(!m||h),c=m&&(!g||d)),{can_open:l,can_close:c,length:o}},a.prototype.Token=e,b=a}(),v=y}(),z=function(){if(C)return E;function e(e){var t=Array.prototype.slice.call(arguments,1);return t.forEach(function(t){t&&Object.keys(t).forEach(function(r){e[r]=t[r]})}),e}function t(e){return Object.prototype.toString.call(e)}function r(e){return"[object Function]"===t(e)}function n(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}C=1;var a={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1},s={"http:":{validate:function(e,t,r){var n=e.slice(t);return r.re.http||(r.re.http=RegExp("^\\/\\/"+r.re.src_auth+r.re.src_host_port_strict+r.re.src_path,"i")),r.re.http.test(n)?n.match(r.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,r){var n=e.slice(t);return r.re.no_http||(r.re.no_http=RegExp("^"+r.re.src_auth+"(?:localhost|(?:(?:"+r.re.src_domain+")\\.)+"+r.re.src_domain_root+")"+r.re.src_port+r.re.src_host_terminator+r.re.src_path,"i")),r.re.no_http.test(n)?t>=3&&":"===e[t-3]||t>=3&&"/"===e[t-3]?0:n.match(r.re.no_http)[0].length:0}},"mailto:":{validate:function(e,t,r){var n=e.slice(t);return r.re.mailto||(r.re.mailto=RegExp("^"+r.re.src_email_name+"@"+r.re.src_host_strict,"i")),r.re.mailto.test(n)?n.match(r.re.mailto)[0].length:0}}},i="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function o(){return function(e,t){t.normalize(e)}}function l(e){var a=e.re=(w||(w=1,y=function(e){var t={};e=e||{},t.src_Any=tO().source,t.src_Cc=tB().source,t.src_Z=tP().source,t.src_P=tg().source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");var r="[><｜]";return t.src_pseudo_letter="(?:(?!"+r+"|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|"+r+"|"+t.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|"+r+"|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+"|$)|;(?!"+t.src_ZCc+"|$)|\\!+(?!"+t.src_ZCc+"|[!]|$)|\\?(?!"+t.src_ZCc+"|[?]|$))+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy="(^|"+r+'|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}),y)(e.__opts__),s=e.__tlds__.slice();function i(e){return e.replace("%TLDS%",a.src_tlds)}e.onCompile(),e.__tlds_replaced__||s.push("a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]"),s.push(a.src_xn),a.src_tlds=s.join("|"),a.email_fuzzy=RegExp(i(a.tpl_email_fuzzy),"i"),a.link_fuzzy=RegExp(i(a.tpl_link_fuzzy),"i"),a.link_no_ip_fuzzy=RegExp(i(a.tpl_link_no_ip_fuzzy),"i"),a.host_fuzzy_test=RegExp(i(a.tpl_host_fuzzy_test),"i");var l=[];function c(e,t){throw Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}e.__compiled__={},Object.keys(e.__schemas__).forEach(function(n){var a=e.__schemas__[n];if(null!==a){var s,i={validate:null,link:null};if(e.__compiled__[n]=i,"[object Object]"===t(a)){"[object RegExp]"===t(a.validate)?(s=a.validate,i.validate=function(e,t){var r=e.slice(t);return s.test(r)?r.match(s)[0].length:0}):r(a.validate)?i.validate=a.validate:c(n,a),r(a.normalize)?i.normalize=a.normalize:a.normalize?c(n,a):i.normalize=o();return}if("[object String]"===t(a)){l.push(n);return}c(n,a)}}),l.forEach(function(t){e.__compiled__[e.__schemas__[t]]&&(e.__compiled__[t].validate=e.__compiled__[e.__schemas__[t]].validate,e.__compiled__[t].normalize=e.__compiled__[e.__schemas__[t]].normalize)}),e.__compiled__[""]={validate:null,normalize:o()};var u=Object.keys(e.__compiled__).filter(function(t){return t.length>0&&e.__compiled__[t]}).map(n).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+a.src_ZPCc+"))("+u+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+a.src_ZPCc+"))("+u+")","ig"),e.re.schema_at_start=RegExp("^"+e.re.schema_search.source,"i"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),e.__index__=-1,e.__text_cache__=""}function c(e,t){var r=e.__index__,n=e.__last_index__,a=e.__text_cache__.slice(r,n);this.schema=e.__schema__.toLowerCase(),this.index=r+t,this.lastIndex=n+t,this.raw=a,this.text=a,this.url=a}function u(e,t){var r=new c(e,t);return e.__compiled__[r.schema].normalize(r,e),r}function h(t,r){if(!(this instanceof h))return new h(t,r);r||Object.keys(t||{}).reduce(function(e,t){return e||a.hasOwnProperty(t)},!1)&&(r=t,t={}),this.__opts__=e({},a,r),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=e({},s,t),this.__compiled__={},this.__tlds__=i,this.__tlds_replaced__=!1,this.re={},l(this)}return h.prototype.add=function(e,t){return this.__schemas__[e]=t,l(this),this},h.prototype.set=function(t){return this.__opts__=e(this.__opts__,t),this},h.prototype.test=function(e){var t,r,n,a,s,i,o,l;if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;if(this.re.schema_test.test(e)){for((o=this.re.schema_search).lastIndex=0;null!==(t=o.exec(e));)if(a=this.testSchemaAt(e,t[2],o.lastIndex)){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+a;break}}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(l=e.search(this.re.host_fuzzy_test))>=0&&(this.__index__<0||l<this.__index__)&&null!==(r=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(s=r.index+r[1].length,(this.__index__<0||s<this.__index__)&&(this.__schema__="",this.__index__=s,this.__last_index__=r.index+r[0].length)),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&e.indexOf("@")>=0&&null!==(n=e.match(this.re.email_fuzzy))&&(s=n.index+n[1].length,i=n.index+n[0].length,(this.__index__<0||s<this.__index__||s===this.__index__&&i>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=s,this.__last_index__=i)),this.__index__>=0},h.prototype.pretest=function(e){return this.re.pretest.test(e)},h.prototype.testSchemaAt=function(e,t,r){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,r,this):0},h.prototype.match=function(e){var t=0,r=[];this.__index__>=0&&this.__text_cache__===e&&(r.push(u(this,t)),t=this.__last_index__);for(var n=t?e.slice(t):e;this.test(n);)r.push(u(this,t)),n=n.slice(this.__last_index__),t+=this.__last_index__;return r.length?r:null},h.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;var t=this.re.schema_at_start.exec(e);if(!t)return null;var r=this.testSchemaAt(e,t[2],t[0].length);return r?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+r,u(this,0)):null},h.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?this.__tlds__=this.__tlds__.concat(e).sort().filter(function(e,t,r){return e!==r[t-1]}).reverse():(this.__tlds__=e.slice(),this.__tlds_replaced__=!0),l(this),this},h.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},h.prototype.onCompile=function(){},E=h}(),q=tS(),O={default:(x||(x=1,A={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}}),A),zero:(D||(D=1,S={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}}),S),commonmark:(R||(R=1,L={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}}),L)},B=/^(vbscript|javascript|file|data):/,P=/^data:image\/(gif|png|jpeg|webp);/;function $(e){var t=e.trim().toLowerCase();return!B.test(t)||!!P.test(t)}var j=["http:","https:","mailto:"];function U(e){var t=q.parse(e,!0);if(t.hostname&&(!t.protocol||j.indexOf(t.protocol)>=0))try{t.hostname=nv.toASCII(t.hostname)}catch{}return q.encode(q.format(t))}function H(e){var t=q.parse(e,!0);if(t.hostname&&(!t.protocol||j.indexOf(t.protocol)>=0))try{t.hostname=nv.toUnicode(t.hostname)}catch{}return q.decode(q.format(t),q.decode.defaultChars+"%")}function Z(n,a){if(!(this instanceof Z))return new Z(n,a);a||e.isString(n)||(a=n||{},n="default"),this.inline=new F,this.block=new I,this.core=new T,this.renderer=new r,this.linkify=new z,this.validateLink=$,this.normalizeLink=U,this.normalizeLinkText=H,this.utils=e,this.helpers=e.assign({},t),this.options={},this.configure(n),a&&this.set(a)}return Z.prototype.set=function(t){return e.assign(this.options,t),this},Z.prototype.configure=function(t){var r,n=this;if(e.isString(t)&&!(t=O[r=t]))throw Error('Wrong `markdown-it` preset "'+r+'", check name');if(!t)throw Error("Wrong `markdown-it` preset, can't be empty");return t.options&&n.set(t.options),t.components&&Object.keys(t.components).forEach(function(e){t.components[e].rules&&n[e].ruler.enableOnly(t.components[e].rules),t.components[e].rules2&&n[e].ruler2.enableOnly(t.components[e].rules2)}),this},Z.prototype.enable=function(e,t){var r=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach(function(t){r=r.concat(this[t].ruler.enable(e,!0))},this),r=r.concat(this.inline.ruler2.enable(e,!0));var n=e.filter(function(e){return 0>r.indexOf(e)});if(n.length&&!t)throw Error("MarkdownIt. Failed to enable unknown rule(s): "+n);return this},Z.prototype.disable=function(e,t){var r=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach(function(t){r=r.concat(this[t].ruler.disable(e,!0))},this),r=r.concat(this.inline.ruler2.disable(e,!0));var n=e.filter(function(e){return 0>r.indexOf(e)});if(n.length&&!t)throw Error("MarkdownIt. Failed to disable unknown rule(s): "+n);return this},Z.prototype.use=function(e){var t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},Z.prototype.parse=function(e,t){if("string"!=typeof e)throw Error("Input data should be a String");var r=new this.core.State(e,this,t);return this.core.process(r),r.tokens},Z.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},Z.prototype.parseInline=function(e,t){var r=new this.core.State(e,this,t);return r.inlineMode=!0,this.core.process(r),r.tokens},Z.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)},N=Z}()),T)),ny=(0,z.pM)({name:"VueMarkdown",props:{source:{type:String,required:!0},options:{type:Object,required:!1},plugins:{type:Array,required:!1}},setup(e){let t=(0,F.KR)(new nk(e.options??{}));for(let r of e.plugins??[])t.value.use(r);let r=(0,z.EW)(()=>t.value.render(e.source));return()=>(0,z.h)("div",{innerHTML:r.value})}}),nw={key:0,class:"chat-message-actions"},nE={key:2,class:"chat-message-files"},nC=(0,z.pM)({__name:"Message",props:{message:{}},setup(e,{expose:t}){es.registerLanguage("javascript",ef),es.registerLanguage("typescript",tu),es.registerLanguage("python",tt),es.registerLanguage("xml",eg),es.registerLanguage("bash",te);let{message:r}=(0,F.QW)(e),{options:n}=ew(),a=(0,F.KR)(null),s=(0,F.KR)({}),i=(0,z.EW)(()=>r.value.text||"&lt;Empty response&gt;"),o=(0,z.EW)(()=>({"chat-message-from-user":"user"===r.value.sender,"chat-message-from-bot":"bot"===r.value.sender,"chat-message-transparent":!0===r.value.transparent})),l=e=>{e.use(th,{attrs:{target:"_blank",rel:"noopener"}})},c={highlight(e,t){if(t&&es.getLanguage(t))try{return es.highlight(e,{language:t}).value}catch{}return""}},u={...(null==n?void 0:n.messageComponents)??{}};t({scrollToView:()=>{var e;null!=(e=a.value)&&e.scrollIntoView&&a.value.scrollIntoView({block:"start"})}});let h=async e=>await new Promise((t,r)=>{let n=new FileReader;n.onload=()=>t(n.result),n.onerror=r,n.readAsDataURL(e)});return(0,z.sV)(async()=>{if(r.value.files)for(let e of r.value.files)try{let t=await h(e);s.value[e.name]=t}catch(e){console.error("Error reading file:",e)}}),(e,t)=>((0,z.uX)(),(0,z.CE)("div",{ref_key:"messageContainer",ref:a,class:(0,q.C4)(["chat-message",o.value])},[e.$slots.beforeMessage?((0,z.uX)(),(0,z.CE)("div",nw,[(0,z.RG)(e.$slots,"beforeMessage",(0,q._B)((0,z.Ng)({message:(0,F.R1)(r)})))])):(0,z.Q3)("",!0),(0,z.RG)(e.$slots,"default",{},()=>["component"===(0,F.R1)(r).type&&u[(0,F.R1)(r).key]?((0,z.uX)(),(0,z.Wv)((0,z.$y)(u[(0,F.R1)(r).key]),(0,q._B)((0,z.v6)({key:0},(0,F.R1)(r).arguments)),null,16)):((0,z.uX)(),(0,z.Wv)((0,F.R1)(ny),{key:1,class:"chat-message-markdown",source:i.value,options:c,plugins:[l]},null,8,["source","plugins"])),((0,F.R1)(r).files??[]).length>0?((0,z.uX)(),(0,z.CE)("div",nE,[((0,z.uX)(!0),(0,z.CE)(z.FK,null,(0,z.pI)((0,F.R1)(r).files??[],e=>((0,z.uX)(),(0,z.CE)("div",{key:e.name,class:"chat-message-file"},[(0,z.bF)(eQ,{file:e,"is-removable":!1,"is-previewable":!0},null,8,["file"])]))),128))])):(0,z.Q3)("",!0)])],2))}}),nA=(0,z.pM)({__name:"MessageTyping",props:{animation:{default:"bouncing"}},setup(e){let t={id:"typing",text:"",sender:"bot"},r=(0,F.KR)(),n=(0,z.EW)(()=>({"chat-message-typing":!0,[`chat-message-typing-animation-${e.animation}`]:!0}));return(0,z.sV)(()=>{var e;null==(e=r.value)||e.scrollToView()}),(e,a)=>((0,z.uX)(),(0,z.Wv)((0,F.R1)(nC),{ref_key:"messageContainer",ref:r,class:(0,q.C4)(n.value),message:t,"data-test-id":"chat-message-typing"},{default:(0,z.k6)(()=>a[0]||(a[0]=[(0,z.Lk)("div",{class:"chat-message-typing-body"},[(0,z.Lk)("span",{class:"chat-message-typing-circle"}),(0,z.Lk)("span",{class:"chat-message-typing-circle"}),(0,z.Lk)("span",{class:"chat-message-typing-circle"})],-1)])),_:1},8,["class"]))}}),nx={key:0,class:"empty-container"},nS={class:"empty","data-test-id":"chat-messages-empty"},nD={key:1,class:"chat-messages-list"},nL=(0,z.pM)({__name:"MessagesList",props:{messages:{},emptyText:{}},setup(e){let t=ey(),r=(0,F.KR)([]),{initialMessages:n,waitingForResponse:a}=t;return(0,z.wB)(()=>r.value.length,()=>{let e=r.value[r.value.length-1];e&&e.scrollToView()}),(e,t)=>{let s=(0,z.g2)("N8nIcon"),i=(0,z.g2)("N8nText");return e.emptyText&&0===(0,F.R1)(n).length&&0===e.messages.length?((0,z.uX)(),(0,z.CE)("div",nx,[(0,z.Lk)("div",nS,[(0,z.bF)(s,{icon:"comment",size:"large",class:"emptyIcon"}),(0,z.bF)(i,{tag:"p",size:"medium",color:"text-base"},{default:(0,z.k6)(()=>[(0,z.eW)((0,q.v_)(e.emptyText),1)]),_:1})])])):((0,z.uX)(),(0,z.CE)("div",nD,[((0,z.uX)(!0),(0,z.CE)(z.FK,null,(0,z.pI)((0,F.R1)(n),e=>((0,z.uX)(),(0,z.Wv)(nC,{key:e.id,message:e},null,8,["message"]))),128)),((0,z.uX)(!0),(0,z.CE)(z.FK,null,(0,z.pI)(e.messages,t=>((0,z.uX)(),(0,z.Wv)(nC,{key:t.id,ref_for:!0,ref_key:"messageComponents",ref:r,message:t},{beforeMessage:(0,z.k6)(({message:t})=>[(0,z.RG)(e.$slots,"beforeMessage",(0,z.v6)({ref_for:!0},{message:t}))]),_:2},1032,["message"]))),128)),(0,F.R1)(a)?((0,z.uX)(),(0,z.Wv)(nA,{key:0})):(0,z.Q3)("",!0)]))}}}),nR={class:"chat-heading"},nN=["title"],nM={key:0},nT=(0,z.pM)({__name:"Chat",setup(e){let{t:t}=eE(),r=ey(),{messages:n,currentSessionId:a}=r,{options:s}=ew(),i=(0,z.EW)(()=>"window"===s.mode&&s.showWindowCloseButton);async function o(){r.startNewSession&&(r.startNewSession(),(0,z.dY)(()=>{er.emit("scrollToBottom")}))}async function l(){r.loadPreviousSession&&(await r.loadPreviousSession(),(0,z.dY)(()=>{er.emit("scrollToBottom")}))}function c(){er.emit("close")}return(0,z.sV)(async()=>{await l(),s.showWelcomeScreen||a.value||await o()}),(e,r)=>((0,z.uX)(),(0,z.Wv)(e7,{class:"chat-wrapper"},{header:(0,z.k6)(()=>[(0,z.Lk)("div",nR,[(0,z.Lk)("h1",null,(0,q.v_)((0,F.R1)(t)("title")),1),i.value?((0,z.uX)(),(0,z.CE)("button",{key:0,class:"chat-close-button",title:(0,F.R1)(t)("closeButtonTooltip"),onClick:c},[(0,z.bF)((0,F.R1)(ek),{height:"18",width:"18"})],8,nN)):(0,z.Q3)("",!0)]),(0,F.R1)(t)("subtitle")?((0,z.uX)(),(0,z.CE)("p",nM,(0,q.v_)((0,F.R1)(t)("subtitle")),1)):(0,z.Q3)("",!0)]),footer:(0,z.k6)(()=>[(0,F.R1)(a)?((0,z.uX)(),(0,z.Wv)(e4,{key:0})):((0,z.uX)(),(0,z.Wv)(eR,{key:1}))]),default:(0,z.k6)(()=>[!(0,F.R1)(a)&&(0,F.R1)(s).showWelcomeScreen?((0,z.uX)(),(0,z.Wv)(eA,{key:0,"onClick:button":o})):((0,z.uX)(),(0,z.Wv)(nL,{key:1,messages:(0,F.R1)(n)},null,8,["messages"]))]),_:1}))}}),nI={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},nF={name:"mdi-chat",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",nI,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M12 3c5.5 0 10 3.58 10 8s-4.5 8-10 8c-1.24 0-2.43-.18-3.53-.5C5.55 21 2 21 2 21c2.33-2.33 2.7-3.9 2.75-4.5C3.05 15.07 2 13.13 2 11c0-4.42 4.5-8 10-8"},null,-1)]))}},nz={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"},nq={name:"mdi-chevron-down",render:function(e,t){return(0,z.uX)(),(0,z.CE)("svg",nz,t[0]||(t[0]=[(0,z.Lk)("path",{fill:"currentColor",d:"M7.41 8.58L12 13.17l4.59-4.59L18 10l-6 6l-6-6z"},null,-1)]))}},nO={class:"chat-window-wrapper"},nB={class:"chat-window"},nP=(0,z.pM)({__name:"ChatWindow",setup(e){let t=(0,F.KR)(!1);function r(){t.value=!t.value,t.value&&(0,z.dY)(()=>{er.emit("scrollToBottom")})}return(e,n)=>((0,z.uX)(),(0,z.CE)("div",nO,[(0,z.bF)(O.eB,{name:"chat-window-transition"},{default:(0,z.k6)(()=>[(0,z.bo)((0,z.Lk)("div",nB,[(0,z.bF)(nT)],512),[[O.aG,t.value]])]),_:1}),(0,z.Lk)("div",{class:"chat-window-toggle",onClick:r},[(0,z.bF)(O.eB,{name:"chat-window-toggle-transition",mode:"out-in"},{default:(0,z.k6)(()=>[t.value?((0,z.uX)(),(0,z.Wv)((0,F.R1)(nq),{key:1,height:"32",width:"32"})):((0,z.uX)(),(0,z.Wv)((0,F.R1)(nF),{key:0,height:"32",width:"32"}))]),_:1})])]))}}),n$=(0,z.pM)({__name:"App",props:{},setup(e){let{options:t}=ew(),r=(0,z.EW)(()=>"fullscreen"===t.mode);return(0,z.sV)(()=>{es.registerLanguage("xml",eg),es.registerLanguage("javascript",ef)}),(e,t)=>r.value?((0,z.uX)(),(0,z.Wv)((0,F.R1)(nT),{key:0,class:"n8n-chat"})):((0,z.uX)(),(0,z.Wv)((0,F.R1)(nP),{key:1,class:"n8n-chat"}))}});function nj(e){var t,r;let n={...B,...e,webhookConfig:{...B.webhookConfig,...null==e?void 0:e.webhookConfig},i18n:{...B.i18n,...null==e?void 0:e.i18n,en:{...null==(t=B.i18n)?void 0:t.en,...null==(r=null==e?void 0:e.i18n)?void 0:r.en}},theme:{...B.theme,...null==e?void 0:e.theme}},a=n.target??"#n8n-chat";"string"==typeof a&&function(e){if(!document.querySelector(e)){let t=document.createElement("div");e.startsWith("#")&&(t.id=e.replace("#","")),e.startsWith(".")&&t.classList.add(e.replace(".","")),document.body.appendChild(t)}}(a);let s=(0,O.Ef)(n$);return s.use(en,n),s.mount(a),s}}}]);