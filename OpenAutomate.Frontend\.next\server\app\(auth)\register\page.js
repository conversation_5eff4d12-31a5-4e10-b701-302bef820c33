(()=>{var e={};e.id=2678,e.ids=[2678],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5843:(e,t,r)=>{Promise.resolve().then(r.bind(r,98324)),Promise.resolve().then(r.bind(r,10590))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31567:(e,t,r)=>{"use strict";r.d(t,{g:()=>s});var n=r(51787);let s={inviteUser:async(e,t)=>n.F.post(`${e}/api/organization-unit-invitation`,{email:t}),acceptInvitation:async(e,t)=>{try{let r=await n.F.post(`${e}/api/organization-unit-invitation/accept`,{token:t});if(!r)throw Error("Empty response received");if(void 0===r.success)return{success:!0};return r}catch(e){return function(e){if(e?.status===409)return{success:!0};if(e?.response?.data?.message)throw Error(e.response.data.message);if(e?.message)throw Error(e.message);throw Error("Failed to accept invitation.")}(e)}},checkInvitation:async(e,t)=>n.F.get(`${e}/api/organization-unit-invitation/check?email=${encodeURIComponent(t)}`),checkInvitationToken:async(e,t)=>{try{return await n.F.get(`${e}/api/organization-unit-invitation/check-token?token=${encodeURIComponent(t)}`)}catch(e){if(console.error("Error checking invitation token:",e),e?.status===404)return{status:"Invalid",recipientEmail:"",expiresAt:"",organizationUnitId:""};throw e}},listInvitations:async(e,t)=>{let r=`/${e}/odata/OrganizationUnitInvitations`;if(t){let e=new URLSearchParams;Object.entries(t).forEach(([t,r])=>{void 0!==r&&e.append(t,r)}),r+=`?${e.toString()}`}return function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:"object"==typeof e&&null!==e&&"invitations"in e?{value:e.invitations,"@odata.count":e.count}:{value:[]}}(await n.F.get(r))}}},31568:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(43210),s=r(86522);function a(){let e=(0,n.useContext)(s.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},33873:e=>{"use strict";e.exports=require("path")},42795:(e,t,r)=>{Promise.resolve().then(r.bind(r,67958)),Promise.resolve().then(r.bind(r,64147))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67958:(e,t,r)=>{"use strict";r.d(t,{RegisterClient:()=>P});var n=r(60687),s=r(43210),a=r.n(s),i=r(85814),o=r.n(i),l=r(16189),c=r(27605),d=r(63442),u=r(45880),p=r(11365),m=r(29523),h=r(71669),x=r(89667),f=r(31568),j=r(91821),v=r(31567),g=r(59321);let w=u.Ik({firstName:u.Yj().min(2,"First name must be at least 2 characters"),lastName:u.Yj().min(2,"Last name must be at least 2 characters"),email:u.Yj().email("Please enter a valid email"),password:u.Yj().min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),confirmPassword:u.Yj().min(8,"Password must be at least 8 characters")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function b(){let e=(0,l.useRouter)(),{register:t,error:r}=(0,f.A)(),[a,i]=s.useState(!1),[o,u]=s.useState(null),b=(0,l.useSearchParams)().get("returnUrl"),P=b?.includes("/invitation/accept"),y=()=>{if(!b)return{token:null,tenant:null};try{let e=new URL(b,window.location.origin),t=e.searchParams.get("token"),r=e.pathname.split("/")[1];return{token:t,tenant:r}}catch(e){return console.error("Error parsing return URL:",e),{token:null,tenant:null}}},C=async()=>{try{let{token:t,tenant:r}=y();if(t&&r)return await v.g.acceptInvitation(r,t),e.push(`/${r}/tenant-selector`),!0}catch(e){console.error("Error accepting invitation:",e)}return!1},N=(0,c.mN)({resolver:(0,d.u)(w),defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:""}});async function A(r){i(!0),u(null);try{if(await t({email:r.email,password:r.password,confirmPassword:r.confirmPassword,firstName:r.firstName,lastName:r.lastName}),P&&await C())return;let n=new URLSearchParams({email:r.email});b&&n.append("returnUrl",b);let s=`/verification-pending?${n.toString()}`;e.push(s)}catch(e){u((0,g.PE)(e))}finally{i(!1)}}return(0,n.jsx)("div",{className:"grid gap-6",children:(0,n.jsx)(h.lV,{...N,children:(0,n.jsxs)("form",{onSubmit:N.handleSubmit(A),className:"space-y-4",children:[(o||r)&&(0,n.jsx)(j.Fc,{variant:"destructive",className:"mb-4",children:(0,n.jsx)(j.TN,{children:o??r})}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsx)(h.zB,{control:N.control,name:"firstName",render:({field:e})=>(0,n.jsxs)(h.eI,{children:[(0,n.jsx)(h.lR,{children:"First Name"}),(0,n.jsx)(h.MJ,{children:(0,n.jsx)(x.p,{placeholder:"John",...e,disabled:a})}),(0,n.jsx)(h.C5,{})]})}),(0,n.jsx)(h.zB,{control:N.control,name:"lastName",render:({field:e})=>(0,n.jsxs)(h.eI,{children:[(0,n.jsx)(h.lR,{children:"Last Name"}),(0,n.jsx)(h.MJ,{children:(0,n.jsx)(x.p,{placeholder:"Doe",...e,disabled:a})}),(0,n.jsx)(h.C5,{})]})})]}),(0,n.jsx)(h.zB,{control:N.control,name:"email",render:({field:e})=>(0,n.jsxs)(h.eI,{children:[(0,n.jsx)(h.lR,{children:"Email"}),(0,n.jsx)(h.MJ,{children:(0,n.jsx)(x.p,{type:"email",placeholder:"<EMAIL>",...e,disabled:a})}),(0,n.jsx)(h.C5,{})]})}),(0,n.jsx)(h.zB,{control:N.control,name:"password",render:({field:e})=>(0,n.jsxs)(h.eI,{children:[(0,n.jsx)(h.lR,{children:"Password"}),(0,n.jsx)(h.MJ,{children:(0,n.jsx)(x.p,{type:"password",placeholder:"••••••••",...e,disabled:a})}),(0,n.jsx)(h.Rr,{className:"text-xs",children:"Use 8+ characters with a mix of uppercase, lowercase, numbers & symbols."}),(0,n.jsx)(h.C5,{})]})}),(0,n.jsx)(h.zB,{control:N.control,name:"confirmPassword",render:({field:e})=>(0,n.jsxs)(h.eI,{children:[(0,n.jsx)(h.lR,{children:"Confirm Password"}),(0,n.jsx)(h.MJ,{children:(0,n.jsx)(x.p,{type:"password",placeholder:"••••••••",...e,disabled:a})}),(0,n.jsx)(h.C5,{})]})}),(0,n.jsxs)(m.$,{type:"submit",className:"w-full transition-all duration-300 hover:translate-y-[-2px]",disabled:a,children:[a&&(0,n.jsx)(p.F.Spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Account"]})]})})})}function P(){let[e,t]=a().useState(""),r=e?`/login?returnUrl=${encodeURIComponent(e)}`:"/login";return(0,n.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,n.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,n.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:"Create an account"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enter your details to create your account"})]}),(0,n.jsx)(b,{}),(0,n.jsxs)("p",{className:"px-8 text-center text-sm text-muted-foreground",children:["Already have an account?"," ",(0,n.jsx)(o(),{href:r,className:"underline underline-offset-4 hover:text-primary font-medium transition-all duration-300 hover:underline-offset-8",children:"Sign in"})]})]})}},73363:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>l});var n=r(65239),s=r(48088),a=r(31369),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let l={children:["",{children:["(auth)",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99308)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\register\\page.tsx"]}]},{}]},{forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\register\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(auth)/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},98324:(e,t,r)=>{"use strict";r.d(t,{RegisterClient:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call RegisterClient() from the server but RegisterClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(auth)\\register\\client.tsx","RegisterClient")},99308:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>o});var n=r(37413),s=r(10590),a=r(98324),i=r(61120);let o={title:"Register | OpenAutomate",description:"Create a new account on OpenAutomate"};function l(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.Header,{}),(0,n.jsx)("div",{className:"container flex-1 flex items-center justify-center py-12",children:(0,n.jsx)(i.Suspense,{children:(0,n.jsx)(a.RegisterClient,{})})})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7966,5584,5156,4654,5880,7943,5684,6763,8826],()=>r(73363));module.exports=n})();