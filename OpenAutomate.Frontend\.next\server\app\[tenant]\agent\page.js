(()=>{var e={};e.id=8993,e.ids=[8993],e.modules={1303:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12277:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l,metadata:()=>r});var s=a(37413),n=a(71065);let r={title:"Agent",description:"Agent management page"};function l(){return(0,s.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,s.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,s.jsx)(n.default,{})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27590:(e,t,a)=>{"use strict";function s(e,t={}){let{dateStyle:a="medium",timeStyle:n="short",fallback:r="N/A",customFormat:l,locale:i="en-US"}=t;if(!e)return r;try{let t;if("string"==typeof e){let a=e;a.endsWith("Z")||a.includes("+")||a.includes("-",10)||(a=a.replace(/(\.\d+)?$/,"$1Z"),console.debug(`Corrected UTC date format: ${e} -> ${a}`)),t=new Date(a)}else t=e;if(isNaN(t.getTime()))return console.warn(`Invalid date provided to formatUtcToLocal: ${e}`),r;if(l)return function(e,t){let a=e.getFullYear(),s=e.getMonth()+1,n=e.getDate(),r=e.getHours(),l=e.getMinutes(),i={yyyy:a.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][s-1],dd:n.toString().padStart(2,"0"),h:(r%12||12).toString(),mm:l.toString().padStart(2,"0"),a:r>=12?"PM":"AM"},o=t;return Object.entries(i).forEach(([e,t])=>{o=o.replace(RegExp(e,"g"),t)}),o}(t,l);return new Intl.DateTimeFormat(i,{dateStyle:a,timeStyle:n}).format(t)}catch(t){return console.error(`Error formatting date ${e}:`,t),r}}a.d(t,{Ej:()=>s})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37946:(e,t,a)=>{Promise.resolve().then(a.bind(a,71065))},42300:(e,t,a)=>{"use strict";a.d(t,{z:()=>r});var s=a(16189),n=a(43210);function r(){let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),a=(0,n.useCallback)(e=>{let a=new URLSearchParams(t.toString());return Object.entries(e).forEach(([e,t])=>{null===t?a.delete(e):a.set(e,t)}),a.toString()},[t]),r=(0,n.useCallback)((t,s)=>{let n=a(s);e.push(`${t}?${n}`,{scroll:!1})},[a,e]);return{createQueryString:a,updateUrl:r}}},53984:(e,t,a)=>{"use strict";a.d(t,{i:()=>o});var s=a(60687),n=a(4654),r=a(56476),l=a(29523),i=a(21342);function o({table:e}){return(0,s.jsxs)(i.rI,{children:[(0,s.jsx)(n.ty,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,s.jsx)(r.A,{}),"View"]})}),(0,s.jsxs)(i.SQ,{align:"end",className:"w-[150px]",children:[(0,s.jsx)(i.lp,{children:"Toggle columns"}),(0,s.jsx)(i.mB,{}),e.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,s.jsx)(i.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},57175:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},61170:(e,t,a)=>{"use strict";a.d(t,{b:()=>d});var s=a(43210);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}a(51215);var r=a(60687),l=Symbol("radix.slottable");function i(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:a,...r}=e;if(s.isValidElement(a)){var l;let e,i;let o=(l=a,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let a={...t};for(let s in t){let n=e[s],r=t[s];/^on[A-Z]/.test(s)?n&&r?a[s]=(...e)=>{r(...e),n(...e)}:n&&(a[s]=n):"style"===s?a[s]={...n,...r}:"className"===s&&(a[s]=[n,r].filter(Boolean).join(" "))}return{...e,...a}}(r,a.props);return a.type!==s.Fragment&&(c.ref=t?function(...e){return t=>{let a=!1,s=e.map(e=>{let s=n(e,t);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let t=0;t<s.length;t++){let a=s[t];"function"==typeof a?a():n(e[t],null)}}}}(t,o):o),s.cloneElement(a,c)}return s.Children.count(a)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=s.forwardRef((e,a)=>{let{children:n,...l}=e,o=s.Children.toArray(n),c=o.find(i);if(c){let e=c.props.children,n=o.map(t=>t!==c?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,r.jsx)(t,{...l,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,r.jsx)(t,{...l,ref:a,children:n})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),l=s.forwardRef((e,s)=>{let{asChild:n,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,r.jsx)(n?a:t,{...l,ref:s})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),c=s.forwardRef((e,t)=>(0,r.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var d=c},61401:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>r.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=a(65239),n=a(48088),r=a(31369),l=a(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let o={children:["",{children:["[tenant]",{children:["agent",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,12277)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\agent\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\agent\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[tenant]/agent/page",pathname:"/[tenant]/agent",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>g,Es:()=>x,HM:()=>u,L3:()=>h,c7:()=>p,lG:()=>o,rr:()=>f,zM:()=>c});var s=a(60687),n=a(43210),r=a(88562),l=a(11860),i=a(36966);let o=r.bL,c=r.l9,d=r.ZL,u=r.bm,m=n.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",e),...t}));m.displayName=r.hJ.displayName;let g=n.forwardRef(({className:e,children:t,...a},n)=>(0,s.jsxs)(d,{children:[(0,s.jsx)(m,{}),(0,s.jsxs)(r.UC,{ref:n,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",e),...a,children:[t,(0,s.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));g.displayName=r.UC.displayName;let p=({className:e,...t})=>(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let x=({className:e,...t})=>(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});x.displayName="DialogFooter";let h=n.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=r.hE.displayName;let f=n.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=r.VY.displayName},71065:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var s=a(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call agentSchema() from the server but agentSchema is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\agent\\agent.tsx","agentSchema");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\agent\\\\agent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\agent\\agent.tsx","default")},72226:(e,t,a)=>{Promise.resolve().then(a.bind(a,85790))},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var s=a(60687);a(43210);var n=a(61170),r=a(36966);function l({className:e,...t}){return(0,s.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80462:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},85790:(e,t,a)=>{"use strict";a.d(t,{default:()=>B});var s=a(60687),n=a(31158),r=a(1303),l=a(29523),i=a(56896),o=a(34208),c=a(43210),d=a(93661),u=a(57175),m=a(96362),g=a(63503),p=a(39582),x=a(21342),h=a(63143),f=a(93613);let j=(0,a(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var y=a(11860),b=a(13964),v=a(96474),N=a(89667),w=a(80013);function C({isOpen:e,onClose:t,mode:a,agent:n,onSuccess:i}){let[o,d]=(0,c.useState)(n?.name||""),[u,m]=(0,c.useState)(n?.machineName||""),[x,C]=(0,c.useState)(!1),[S,k]=(0,c.useState)(null),[A,P]=(0,c.useState)(null),[z,M]=(0,c.useState)(!1),[R,$]=(0,c.useState)(""),F="edit"===a,I=()=>F?!!(o!==n?.name&&o.trim()||u!==n?.machineName&&u.trim())||(k("Please change at least one field to update."),!1):!!(o.trim()&&u.trim())||(k("Please fill in all required fields"),!1),O=e=>{let t="Failed to create agent. Please try again.";return"object"==typeof e&&null!==e&&("message"in e&&(t=String(e.message)),"status"in e&&(t+=` (Status: ${e.status})`),"details"in e&&(t+=` - ${e.details}`),console.error("Error details:",JSON.stringify(e,null,2))),t},E=async()=>{if(I()){C(!0),k(null);try{let e;if(F){let t=await (0,p.Ri)(n.id);if("Disconnected"!==t.status){$('You can only edit an agent when its status is "Disconnected".'),M(!0),C(!1);return}e=await (0,p.Qk)(n.id,{name:o!==n.name?o:void 0,machineName:u!==n.machineName?u:void 0})}else e=await (0,p.xR)({name:o,machineName:u});P(e),i&&i(e)}catch(e){k(O(e))}finally{C(!1)}}},D=e=>{navigator.clipboard.writeText(e),console.log("Copied to clipboard")},V=()=>{d(""),m(""),k(null),P(null)},_=()=>{V(),t()};return(0,s.jsx)(g.lG,{open:e,onOpenChange:_,children:(0,s.jsxs)(g.Cf,{className:"sm:max-w-[500px] p-0 max-h-[85vh] flex flex-col",onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsxs)(g.c7,{className:"flex items-center gap-2 p-6 pb-2 border-b",children:[F?(0,s.jsx)(h.A,{className:"w-5 h-5 text-primary"}):(0,s.jsx)(r.A,{className:"w-5 h-5 text-primary"}),(0,s.jsx)(g.L3,{className:"text-xl font-bold",children:F?"Edit Agent":"Create a new Agent"})]}),A?(0,s.jsxs)("div",{className:"space-y-4 px-6 py-4",children:[(0,s.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-md text-sm",children:[(0,s.jsx)("p",{className:"font-medium text-green-800 dark:text-green-300 mb-2",children:"Agent created successfully!"}),(0,s.jsx)("p",{className:"text-green-700 dark:text-green-400 mb-4",children:"Please copy the machine key below. It will only be shown once."})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("label",{htmlFor:"machine-key",className:"block text-sm font-medium",children:"Machine Key"}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(N.p,{id:"machine-key",value:A.machineKey,readOnly:!0,className:"flex-1 bg-muted font-mono text-xs"}),(0,s.jsx)(l.$,{variant:"ghost",size:"icon",onClick:()=>D(A.machineKey),className:"ml-2",children:(0,s.jsx)(j,{className:"h-4 w-4"})})]})]})]}):(0,s.jsxs)("form",{className:"space-y-4 px-6 py-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(w.J,{htmlFor:"name",className:"flex items-center gap-1 mb-2",children:["Name",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(N.p,{id:"name",value:o,onChange:e=>d(e.target.value),disabled:x,className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20 mb-2",autoComplete:"off",spellCheck:"false",onFocus:e=>{setTimeout(()=>{let t=e.target.value.length;e.target.setSelectionRange(t,t)},0)}}),S&&S.includes("Name")&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mt-1",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),S]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(w.J,{htmlFor:"machine-name",className:"flex items-center gap-1 mb-2",children:["Machine name",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(N.p,{id:"machine-name",value:u,onChange:e=>m(e.target.value),disabled:x,className:"bg-white text-black dark:text-white border rounded-xl shadow focus:border-primary focus:ring-2 focus:ring-primary/20 mt-2",autoComplete:"off",spellCheck:"false"}),S&&S.includes("Machine")&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mt-1",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),S]})]}),S&&!S.includes("Name")&&!S.includes("Machine")&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-red-500 text-sm mt-1",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),S]})]}),(0,s.jsxs)(g.Es,{className:"p-6 pt-4 border-t bg-background z-10 flex justify-end gap-2",children:[(0,s.jsxs)(l.$,{variant:"outline",onClick:_,disabled:x,className:"flex items-center gap-1",children:[(0,s.jsx)(y.A,{className:"w-4 h-4"})," ",A?"Close":"Cancel"]}),!A&&(0,s.jsxs)(l.$,{onClick:E,disabled:x,className:"flex items-center gap-1",children:[F?(0,s.jsx)(b.A,{className:"w-4 h-4"}):(0,s.jsx)(v.A,{className:"w-4 h-4"}),F?"Save Changes":"Add Agent"]})]}),(0,s.jsx)(g.lG,{open:z,onOpenChange:M,children:(0,s.jsxs)(g.Cf,{children:[(0,s.jsx)(g.c7,{children:(0,s.jsx)(g.L3,{children:"Error"})}),(0,s.jsx)("div",{children:R}),(0,s.jsx)(g.Es,{children:(0,s.jsx)(l.$,{onClick:()=>M(!1),children:"OK"})})]})})]})})}function S({row:e,onRefresh:t}){let[a,n]=(0,c.useState)(!1),[r,i]=(0,c.useState)(!1),[o,h]=(0,c.useState)(""),[f,j]=(0,c.useState)(!1),[y,b]=(0,c.useState)(!1),[v,N]=(0,c.useState)(null),w=async t=>{if(t&&t.stopPropagation(),"Disconnected"!==e.original.status){h('You can only edit an agent when its status is "Disconnected".'),i(!0);return}try{let t=await (0,p.Ri)(e.original.id);N(t),b(!0)}catch{h("Failed to fetch agent details."),i(!0)}},S=async()=>{j(!0);try{"Disconnected"===e.original.status?(await (0,p.kz)(e.original.id),n(!1),t&&t()):(n(!1),h('You can only delete an agent when its status is "Disconnected".'),i(!0))}catch(e){n(!1),e instanceof Error?h(e.message):h("Failed to delete agent."),i(!0)}finally{j(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(x.rI,{children:[(0,s.jsx)(x.ty,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,s.jsxs)(x.SQ,{align:"start",className:"w-[160px]",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)(x._2,{onClick:w,children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Edit"})]}),(0,s.jsx)(x.mB,{}),(0,s.jsxs)(x._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e&&e.stopPropagation(),n(!0)},children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Delete"})]})]})]}),(0,s.jsx)(C,{isOpen:y,onClose:()=>{b(!1),N(null)},mode:"edit",agent:v,onSuccess:()=>{b(!1),N(null),t&&t()}}),(0,s.jsx)(g.lG,{open:a,onOpenChange:n,children:(0,s.jsxs)(g.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(g.c7,{children:(0,s.jsx)(g.L3,{children:"Confirm Delete"})}),(0,s.jsxs)("div",{children:["Are you sure you want to delete agent ",(0,s.jsx)("b",{children:e.original.name}),"?"]}),(0,s.jsxs)(g.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:()=>n(!1),disabled:f,children:"Cancel"}),(0,s.jsx)(l.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:S,disabled:f,children:"Delete"})]})]})}),(0,s.jsx)(g.lG,{open:r,onOpenChange:i,children:(0,s.jsxs)(g.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(g.c7,{children:(0,s.jsx)(g.L3,{children:"Error"})}),(0,s.jsx)("div",{children:o}),(0,s.jsx)(g.Es,{children:(0,s.jsx)(l.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:()=>i(!1),children:"OK"})})]})})]})}var k=a(27590);let A=e=>[{id:"select",header:({table:e})=>(0,s.jsx)(i.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>(0,s.jsx)(i.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1},{id:"actions",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Actions"}),cell:({row:t})=>(0,s.jsx)(S,{row:t,onRefresh:e})},{accessorKey:"name",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Name"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex space-x-2",children:(0,s.jsx)("span",{className:"max-w-[500px] truncate font-medium",children:e.getValue("name")})})},{accessorKey:"machineName",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Machine Name"}),cell:({row:e})=>(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:e.getValue("machineName")})})},{accessorKey:"status",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Status"}),cell:({row:e})=>{let t=String(e.getValue("status")),a="";switch(t){case"Connected":case"Available":a="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";break;case"Busy":a="bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400";break;case"Disconnected":case"Offline":a="bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";break;default:a="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400"}return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${a}`,children:t})})}},{accessorKey:"lastConnected",header:({column:e})=>(0,s.jsx)(o.w,{column:e,title:"Last Connected"}),cell:({row:e})=>{let t=e.getValue("lastConnected"),a="string"==typeof t?t:t?String(t):null,n=(0,k.Ej)(a,{fallback:"Never"});return(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{children:n})})}}];var P=a(50723),z=a(45880),M=a(16189),R=a(99270),$=a(41862),F=a(80462),I=a(53984),O=a(15079),E=a(96834);function D({table:e,statuses:t,onSearch:a,onStatusChange:n,searchValue:r="",isFiltering:i=!1,isPending:o=!1}){let d=e.getState().columnFilters.length>0,u=e.getState().columnFilters.length,m=(0,c.useRef)(null),g=(0,c.useRef)(null),p=t=>{m.current&&(g.current=m.current.selectionStart),a?a(t):e.getColumn("name")?.setFilterValue(t)};return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,s.jsx)(R.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(N.p,{ref:m,placeholder:"Search by Name or Machine Name...",value:r,onChange:e=>p(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:i,onFocus:()=>{m.current&&(g.current=m.current.selectionStart)}}),i&&(0,s.jsx)($.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!i&&""!==r&&(0,s.jsx)(y.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>p("")})]}),e.getColumn("status")&&(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)(O.l6,{onValueChange:t=>{n?n(t):e.getColumn("status")?.setFilterValue("all"===t?"":t)},value:e.getColumn("status")?.getFilterValue()||"all",disabled:i||o,children:[(0,s.jsx)(O.bq,{className:"h-10 sm:w-[180px]",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(F.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)(O.yv,{placeholder:"Filter status"}),e.getColumn("status")?.getFilterValue()&&(0,s.jsx)(E.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,s.jsxs)(O.gC,{children:[(0,s.jsx)(O.eb,{value:"all",children:"All Statuses"}),t.map(e=>(0,s.jsx)(O.eb,{value:e.value,children:e.label},e.value))]})]})}),u>0&&(0,s.jsxs)(E.E,{variant:"secondary",className:"rounded-sm px-1",children:[u," active ",1===u?"filter":"filters"]}),d&&(0,s.jsxs)(l.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),a&&a("")},className:"h-8 px-2 lg:px-3",disabled:i,children:["Reset",(0,s.jsx)(y.A,{className:"ml-2 h-4 w-4"})]})]}),(0,s.jsx)(I.i,{table:e})]})}var V=a(56090),_=a(93772),G=a(42300),L=a(14583),q=a(63676),T=a(31207),K=a(70891),J=a(20140);function B(){let e=(0,M.useRouter)(),t=(0,M.usePathname)(),a=(0,M.useSearchParams)(),{updateUrl:i}=(0,G.z)(),{toast:o}=(0,J.d)(),[d,u]=(0,c.useState)(!1),[m,g]=(0,c.useState)("create"),[x,h]=(0,c.useState)({}),[f,j]=(0,c.useState)({}),[y,b]=(0,c.useState)(0);(0,c.useRef)(0);let[v,N]=(0,c.useState)(!1),[w,S]=(0,c.useState)(!1),[k,z]=(0,c.useState)(!1),R=(0,c.useRef)(null);(0,c.useRef)(!0);let[$,F]=(0,c.useState)(()=>{let e=[],t=a.get("name");t&&e.push({id:"name",value:t});let s=a.get("status");return s&&e.push({id:"status",value:s}),e}),[I,O]=(0,c.useState)(()=>{let e=a.get("sort"),t=a.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[E,B]=(0,c.useState)(()=>{let e=a.get("page"),t=a.get("size");return{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:t?parseInt(t):10}}),[H,U]=(0,c.useState)(a.get("name")??""),W=t.split("/")[1],Y=(0,c.useMemo)(()=>{let e={$top:E.pageSize,$skip:E.pageIndex*E.pageSize,$count:!0};if(I.length>0&&(e.$orderby=I.map(e=>`${e.id} ${e.desc?"desc":"asc"}`).join(",")),$.length>0){let t=$.filter(e=>"string"!=typeof e.value||""!==e.value).map(e=>{let t=e.id,a=e.value;return"string"==typeof a?"name"===t&&a?`(contains(tolower(name), '${a.toLowerCase()}') or contains(tolower(machineName), '${a.toLowerCase()}'))`:`contains(tolower(${t}), '${a.toLowerCase()}')`:Array.isArray(a)?a.map(e=>`${t} eq '${e}'`).join(" or "):""}).filter(Boolean);t.length>0&&(e.$filter=t.join(" and "))}return e},[E,I,$]),{data:Q,error:Z,isLoading:X,mutate:ee}=(0,T.Ay)(K.DC.agentsWithOData(Y),()=>(0,p.dT)(Y)),et=(0,c.useMemo)(()=>Q?.value?Q.value.map(e=>({...e,botAgentId:e.id})):[],[Q]),ea=(0,c.useCallback)(async()=>{N(!1),S(!1),await ee()},[ee]),es=(0,q.d)(W),en=e=>e+1,er=(e,t)=>Math.max(1,Math.ceil(e/t)),el=(0,c.useMemo)(()=>{let e=er(y,E.pageSize),t=et.length===E.pageSize&&y<=E.pageSize*(E.pageIndex+1),a=en(E.pageIndex);return t?Math.max(a,e,E.pageIndex+2):Math.max(a,e)},[E.pageSize,E.pageIndex,et.length,y]),ei=(0,c.useMemo)(()=>et.map(e=>{let t=es[e.botAgentId];return t&&console.debug("Merging real-time status for",e.botAgentId,t.status),t?{...e,status:t.status}:e}),[et,es]),eo=(0,c.useMemo)(()=>!k&&et.length===E.pageSize,[k,et.length,E.pageSize]),ec=(0,V.N4)({data:ei,columns:A(ea),state:{sorting:I,columnVisibility:f,rowSelection:x,columnFilters:$,pagination:E},enableRowSelection:!0,onRowSelectionChange:h,onSortingChange:e=>{let a="function"==typeof e?e(I):e;O(a),a.length>0?i(t,{sort:a[0].id,order:a[0].desc?"desc":"asc",page:"1"}):i(t,{sort:null,order:null,page:"1"})},onColumnFiltersChange:F,onColumnVisibilityChange:j,onPaginationChange:e=>{let a="function"==typeof e?e(E):e;B(a),i(t,{page:(a.pageIndex+1).toString(),size:a.pageSize.toString()})},getCoreRowModel:(0,_.HT)(),getFilteredRowModel:(0,_.hM)(),getPaginationRowModel:(0,_.kW)(),getSortedRowModel:(0,_.h5)(),getFacetedRowModel:(0,_.kQ)(),getFacetedUniqueValues:(0,_.oS)(),manualPagination:!0,pageCount:el,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.botAgentId}),ed=(0,c.useCallback)(e=>{U(e),N(!0),R.current&&clearTimeout(R.current),R.current=setTimeout(()=>{let a=ec.getColumn("name");a&&(a.setFilterValue(e),i(t,{name:e||null,page:"1"})),N(!1)},500)},[ec,i,t]),eu=(0,c.useCallback)(e=>{let a=ec.getColumn("status");if(a){let s="all"===e?"":e;a.setFilterValue(s),i(t,{status:s||null,page:"1"})}},[ec,i,t]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Agents"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[y>0&&(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,s.jsxs)("span",{children:["Total: ",y," agent",1!==y?"s":""]})}),(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>{window.location.href="https://openautomate-agent.s3.ap-southeast-1.amazonaws.com/OpenAutomate.BotAgent.Installer.msi"},className:"flex items-center justify-center",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Download Agent"]}),(0,s.jsxs)(l.$,{onClick:()=>{g("create"),u(!0)},className:"flex items-center justify-center",children:[(0,s.jsx)(r.A,{className:"mr-2 h-4 w-4"}),"Create"]})]})]}),Z&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,s.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load agents. Please try again."}),(0,s.jsx)(l.$,{variant:"outline",className:"mt-2",onClick:()=>ee(),children:"Retry"})]}),(0,s.jsx)(D,{table:ec,statuses:[{value:"Available",label:"Available"},{value:"Busy",label:"Busy"},{value:"Disconnected",label:"Disconnected"}],onSearch:ed,onStatusChange:eu,searchValue:H,isFiltering:X,isPending:v}),(0,s.jsx)(P.b,{data:ei,columns:A(ea),table:ec,onRowClick:a=>{let s=t.startsWith("/admin")?`/admin/agent/${a.id}`:`/${W}/agent/${a.id}`;e.push(s)},isLoading:X,totalCount:y}),(0,s.jsx)(L.d,{currentPage:E.pageIndex+1,pageSize:E.pageSize,totalCount:y,totalPages:el,isLoading:X,isChangingPageSize:w,isUnknownTotalCount:eo,onPageChange:e=>{B({...E,pageIndex:e-1}),i(t,{page:e.toString()})},onPageSizeChange:e=>{S(!0);let a=Math.floor(E.pageIndex*E.pageSize/e);B({pageSize:e,pageIndex:a}),i(t,{size:e.toString(),page:(a+1).toString()})}})]}),(0,s.jsx)(C,{isOpen:d,onClose:()=>u(!1),mode:m,onSuccess:()=>ee()})]})}z.z.object({id:z.z.string(),botAgentId:z.z.string(),name:z.z.string(),machineName:z.z.string(),status:z.z.string(),lastConnected:z.z.string()})},93661:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,7966,5584,5156,4654,6467,5880,1694,6945,8759,1637,6763,519,4881,3210],()=>a(61401));module.exports=s})();