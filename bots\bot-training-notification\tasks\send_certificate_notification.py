"""
Certificate Notification Module

Orchestrates the certificate notification process using organized service modules.
This is the main controller that coordinates data processing, certificate generation,
and email notifications using Outlook.

Usage:
    from tasks.send_certificate_notification import CertificateNotificationBot

    bot = CertificateNotificationBot(outlook_account="<EMAIL>")
    bot.process_certificate_notifications("path/to/excel/file.xlsx")
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# Import our organized services
from .data_service import DataService
from .certificate_service import CertificateService
from .email_service import EmailService


class CertificateNotificationBot:
    """
    Main orchestrator for certificate notification process.
    
    This bot coordinates:
    1. Data processing (Excel reading and filtering)
    2. Certificate generation (PDF creation from templates)
    3. Email notifications (Outlook-based email sending)
    4. Process reporting and error handling
    """
    
    def __init__(self, outlook_account: str = None, template_path: str = None, 
                 output_dir: str = None, logger: Optional[logging.Logger] = None):
        """
        Initialize the certificate notification bot.
        
        Args:
            outlook_account: Email account to use for sending (from OpenAutomate assets)
            template_path: Path to certificate template (optional)
            output_dir: Directory for certificate output (optional)
            logger: Optional logger instance
        """
        self.logger = logger or logging.getLogger(__name__)
        self.outlook_account = outlook_account
        
        # Initialize services
        self.data_service = DataService(logger=self.logger)
        self.certificate_service = CertificateService(
            template_path=template_path,
            output_dir=output_dir,
            logger=self.logger
        )
        self.email_service = EmailService(
            outlook_account=outlook_account,
            logger=self.logger
        )
        
        self.logger.info("Certificate Notification Bot initialized with organized services")
    
    def process_certificate_notifications(self, excel_file_path: str) -> Dict[str, Any]:
        """
        Main method to process certificate notifications.
        
        Args:
            excel_file_path: Path to Excel training data file
            
        Returns:
            Dictionary with processing results
        """
        results = {
            'success': False,
            'message': '',
            'total_eligible': 0,
            'certificates_generated': 0,
            'emails_sent': 0,
            'errors': [],
            'data_summary': {},
            'processing_details': []
        }
        
        try:
            self.logger.info("Starting certificate notification process")
            
            # Step 1: Read and analyze Excel data
            self.logger.info("Step 1: Reading Excel data...")
            df = self.data_service.read_training_data(excel_file_path)
            
            # Get data summary for reporting
            results['data_summary'] = self.data_service.get_data_summary(df)
            
            # Step 2: Find eligible students
            self.logger.info("Step 2: Finding eligible students...")
            eligible_students = self.data_service.find_eligible_students(df)
            results['total_eligible'] = len(eligible_students)
            
            if len(eligible_students) == 0:
                results['success'] = True
                results['message'] = "No students eligible for certificate notification today"
                return results
            
            # Step 3: Process each eligible student
            self.logger.info(f"Step 3: Processing {len(eligible_students)} eligible students...")
            
            certificates_data = []
            
            for idx, student_row in eligible_students.iterrows():
                student_result = self._process_single_student(student_row)
                results['processing_details'].append(student_result)
                
                if student_result['certificate_generated']:
                    results['certificates_generated'] += 1
                    certificates_data.append({
                        'student_data': student_result['student_data'],
                        'certificate_path': student_result['certificate_path']
                    })
                
                if student_result['email_sent']:
                    results['emails_sent'] += 1
                
                if student_result.get('error'):
                    results['errors'].append(student_result['error'])
            
            # Set final results
            results['success'] = results['certificates_generated'] > 0
            results['message'] = self._create_summary_message(results)
            
            self.logger.info(f"Certificate notification process completed: {results['message']}")
            return results
            
        except Exception as e:
            error_msg = f"Certificate notification process failed: {str(e)}"
            self.logger.error(error_msg)
            results['success'] = False
            results['message'] = error_msg
            results['errors'].append(error_msg)
            return results
    
    def _process_single_student(self, student_row) -> Dict[str, Any]:
        """
        Process certificate notification for a single student.
        
        Args:
            student_row: Pandas Series with student data
            
        Returns:
            Dictionary with processing results for this student
        """
        result = {
            'student_data': {},
            'certificate_generated': False,
            'certificate_path': None,
            'email_sent': False,
            'success': False,
            'error': None
        }
        
        try:
            # Prepare student data
            student_data = self.data_service.prepare_student_data(student_row)
            result['student_data'] = student_data
            
            # Validate student data
            if not self.data_service.validate_student_data(student_data):
                result['error'] = f"Invalid student data for {student_data.get('TEN_UNG_VIEN', 'Unknown')}"
                return result
            
            # Generate certificate
            certificate_path = self.certificate_service.generate_certificate(student_data)
            result['certificate_path'] = certificate_path
            result['certificate_generated'] = certificate_path is not None
            
            if certificate_path:
                self.logger.info(f"Certificate generated for {student_data['TEN_UNG_VIEN']}")
                
                # Send email notification
                email_sent = self.email_service.send_certificate_notification(
                    student_data, 
                    certificate_path
                )
                result['email_sent'] = email_sent
                
                if email_sent:
                    self.logger.info(f"Email sent to {student_data['TEN_UNG_VIEN']}")
                    result['success'] = True
                else:
                    result['error'] = f"Failed to send email to {student_data['TEN_UNG_VIEN']}"
            else:
                result['error'] = f"Failed to generate certificate for {student_data['TEN_UNG_VIEN']}"
            
            return result
            
        except Exception as e:
            student_name = result['student_data'].get('TEN_UNG_VIEN', 'Unknown')
            error_msg = f"Error processing {student_name}: {str(e)}"
            self.logger.error(error_msg)
            result['error'] = error_msg
            return result
    
    def _create_summary_message(self, results: Dict[str, Any]) -> str:
        """
        Create a summary message for the processing results.
        
        Args:
            results: Processing results dictionary
            
        Returns:
            Summary message string
        """
        total = results['total_eligible']
        certs = results['certificates_generated']
        emails = results['emails_sent']
        errors = len(results['errors'])
        
        message = f"Processed {total} eligible students: {certs} certificates generated, {emails} emails sent"
        
        if errors > 0:
            message += f", {errors} errors encountered"
            
        return message
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        Get status information about all services.
        
        Returns:
            Dictionary with service status information
        """
        status = {
            'data_service': {'initialized': self.data_service is not None},
            'certificate_service': {
                'initialized': self.certificate_service is not None,
                'template_info': None
            },
            'email_service': {
                'initialized': self.email_service is not None,
                'outlook_account': self.outlook_account,
                'connection_test': None
            }
        }
        
        try:
            # Test certificate service
            if self.certificate_service:
                status['certificate_service']['template_info'] = self.certificate_service.get_template_info()
            
            # Test email service
            if self.email_service:
                status['email_service']['connection_test'] = self.email_service.test_connection()
                
        except Exception as e:
            self.logger.error(f"Error getting service status: {str(e)}")
        
        return status
    
    def test_services(self) -> Dict[str, Any]:
        """
        Test all services and return results.
        
        Returns:
            Dictionary with test results
        """
        test_results = {
            'overall_status': 'success',
            'tests': {
                'data_service': {'status': 'unknown', 'message': ''},
                'certificate_service': {'status': 'unknown', 'message': ''},
                'email_service': {'status': 'unknown', 'message': ''}
            }
        }
        
        try:
            # Test data service
            if self.data_service:
                test_results['tests']['data_service'] = {
                    'status': 'success',
                    'message': 'Data service initialized and ready'
                }
            else:
                test_results['tests']['data_service'] = {
                    'status': 'failed',
                    'message': 'Data service not initialized'
                }
            
            # Test certificate service
            try:
                template_info = self.certificate_service.get_template_info()
                if template_info.get('exists'):
                    test_results['tests']['certificate_service'] = {
                        'status': 'success',
                        'message': f"Template found: {template_info['filename']}"
                    }
                else:
                    test_results['tests']['certificate_service'] = {
                        'status': 'failed',
                        'message': f"Template issue: {template_info.get('error', 'Unknown')}"
                    }
            except Exception as e:
                test_results['tests']['certificate_service'] = {
                    'status': 'failed',
                    'message': f"Certificate service error: {str(e)}"
                }
            
            # Test email service
            try:
                if self.email_service.test_connection():
                    test_results['tests']['email_service'] = {
                        'status': 'success',
                        'message': f"Outlook connection successful (Account: {self.outlook_account or 'Default'})"
                    }
                else:
                    test_results['tests']['email_service'] = {
                        'status': 'failed',
                        'message': 'Outlook connection failed'
                    }
            except Exception as e:
                test_results['tests']['email_service'] = {
                    'status': 'failed',
                    'message': f"Email service error: {str(e)}"
                }
            
            # Determine overall status
            failed_tests = [test for test in test_results['tests'].values() if test['status'] == 'failed']
            if failed_tests:
                test_results['overall_status'] = 'failed'
            
            return test_results
            
        except Exception as e:
            self.logger.error(f"Error testing services: {str(e)}")
            test_results['overall_status'] = 'error'
            test_results['error'] = str(e)
            return test_results


def example_usage():
    """
    Example of how to use the reorganized CertificateNotificationBot.
    """
    # Excel file path
    excel_file_path = r"C:\Users\<USER>\OneDrive - VFC Corp\DuLieuDaoTao\DU_LIEU_DAO_TAO_HOC_VIEN.xlsx"
    
    # Outlook account (from OpenAutomate assets)
    outlook_account = "<EMAIL>"  # This will come from assets
    
    try:
        # Initialize bot with organized services
        bot = CertificateNotificationBot(outlook_account=outlook_account)
        
        # Test services before processing
        print("Testing services...")
        test_results = bot.test_services()
        
        if test_results['overall_status'] == 'success':
            print("✅ All services ready")
            
            # Process certificate notifications
            results = bot.process_certificate_notifications(excel_file_path)
            
            # Print results
            if results['success']:
                print(f"✅ SUCCESS: {results['message']}")
                print(f"📊 Data Summary:")
                summary = results['data_summary']
                print(f"   - Total records: {summary.get('total_records', 0)}")
                print(f"   - Students passed: {summary.get('students_passed', 0)}")
                print(f"   - Certificates due today: {summary.get('certificates_due_today', 0)}")
                print(f"📜 Results:")
                print(f"   - Eligible students: {results['total_eligible']}")
                print(f"   - Certificates generated: {results['certificates_generated']}")
                print(f"   - Emails sent: {results['emails_sent']}")
            else:
                print(f"❌ FAILED: {results['message']}")
                if results['errors']:
                    print("Errors:")
                    for error in results['errors']:
                        print(f"  - {error}")
        else:
            print("❌ Service tests failed:")
            for service, test in test_results['tests'].items():
                if test['status'] == 'failed':
                    print(f"  - {service}: {test['message']}")
                    
    except FileNotFoundError:
        print("❌ Excel file not found. Please check the file path.")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    # Run example when script is executed directly
    example_usage()
