(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6602],{30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d,r:()=>l});var n=a(95155),s=a(12115),i=a(66634),r=a(74466),o=a(36928);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:a,variant:s,size:r,asChild:d=!1,...c}=e,u=d?i.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,o.cn)(l({variant:s,size:r,className:a})),ref:t,...c})});d.displayName="Button"},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>h,Es:()=>f,HM:()=>u,L3:()=>p,c7:()=>m,lG:()=>l,rr:()=>x,zM:()=>d});var n=a(95155),s=a(12115),i=a(59096),r=a(54416),o=a(36928);let l=i.bL,d=i.l9,c=i.ZL,u=i.bm,g=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)(i.hJ,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ",a),...s})});g.displayName=i.hJ.displayName;let h=s.forwardRef((e,t)=>{let{className:a,children:s,...l}=e;return(0,n.jsxs)(c,{children:[(0,n.jsx)(g,{}),(0,n.jsxs)(i.UC,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full dark:bg-black",a),...l,children:[s,(0,n.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(r.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});h.displayName=i.UC.displayName;let m=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};m.displayName="DialogHeader";let f=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};f.displayName="DialogFooter";let p=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)(i.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...s})});p.displayName=i.hE.displayName;let x=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)(i.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",a),...s})});x.displayName=i.VY.displayName},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var n=a(95155);a(12115);var s=a(36928);function i(e){let{className:t,type:a,...i}=e;return(0,n.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},70112:(e,t,a)=>{Promise.resolve().then(a.bind(a,79782))},70449:(e,t,a)=>{"use strict";a.d(t,{DC:()=>o,EJ:()=>r,IS:()=>l,bb:()=>i});var n=a(7283),s=a(15874);function i(){return{fetcher:e=>(0,n.fetchApi)(e),onError:function(e){(0,s.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let r={fetcher:e=>(0,n.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},o={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},l=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},74126:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},79782:(e,t,a)=>{"use strict";a.d(t,{default:()=>f});var n=a(95155),s=a(12115),i=a(62523),r=a(30285),o=a(35695),l=a(96365),d=a(88262),c=a(54165),u=a(89917),g=a(74126),h=a(34953),m=a(70449);function f(){let e=(0,o.useParams)().tenant,[t,a]=(0,s.useState)(null),[f,p]=(0,s.useState)(null),[x,v]=(0,s.useState)(!1),[b,y]=(0,s.useState)(!1),[j,w]=(0,s.useState)(""),[N,k]=(0,s.useState)(""),[C,D]=(0,s.useState)(!1),[A,z]=(0,s.useState)(!1),[F,S]=(0,s.useState)(!1),{toast:E}=(0,d.d)(),[O,I]=(0,s.useState)(null),[R,U]=(0,s.useState)(!1),B=(0,s.useRef)(!1);(0,s.useEffect)(()=>{if(!e){a(null);return}l.K.getBySlug(e).then(e=>{a(e.id)}).catch(()=>{a(null)})},[e]),(0,s.useEffect)(()=>{t&&l.K.getById(t).then(e=>{p(e),w(e.name),k(e.description)}).catch(()=>{p(null)})},[t]);let $=async()=>{if(!j.trim()){E({title:"Error",description:"Organization unit name cannot be empty",variant:"destructive"});return}if(t){if(f&&j.trim()!==f.name){D(!0);return}y(!0);try{let e=await l.K.update(t,{name:j,description:N});p(e),v(!1),E({title:"Success",description:"Organization unit information updated successfully"})}catch(e){E({title:"Error",description:"Update failed",variant:"destructive"})}finally{y(!1)}}},P=async()=>{if(!t){E({title:"Error",description:"Organization unit ID is missing. Please try again.",variant:"destructive"});return}z(!0);try{let e=await l.K.update(t,{name:j,description:N});p(e),v(!1),D(!1),E({title:"Success",description:"Organization unit information updated successfully"}),window.location.href="/tenant-selector"}catch(e){E({title:"Error",description:"Update failed",variant:"destructive"})}finally{z(!1)}},T=async()=>{if(t){S(!1);try{await l.K.requestDeletion(t),L(),E({title:"Deletion Requested",description:"Organization unit deletion has been initiated."})}catch(t){let e="Failed to request deletion.";t instanceof Error&&(e=t.message),E({title:"Error",description:e,variant:"destructive"})}}},M=async()=>{if(t)try{await l.K.cancelDeletion(t),L(),E({title:"Deletion Cancelled",description:"Organization unit deletion has been cancelled."})}catch(t){let e="Failed to cancel deletion.";t instanceof Error&&(e=t.message),E({title:"Error",description:e,variant:"destructive"})}finally{U(!1)}},_=async()=>{var e,a,n;if(!t)throw Error("Missing ID");let s=await l.K.getDeletionStatus(t),i=null!==(a=null!==(e=s.isPendingDeletion)&&void 0!==e?e:s.isDeletionPending)&&void 0!==a&&a,r=null;return"number"==typeof s.remainingSeconds?r=s.remainingSeconds:"number"==typeof s.hoursUntilDeletion&&(r=3600*s.hoursUntilDeletion),{isPendingDeletion:i,remainingSeconds:r,scheduledDeletionAt:s.scheduledDeletionAt,canCancel:null!==(n=s.canCancel)&&void 0!==n&&n}},{data:K,mutate:L}=(0,h.Ay)(t?m.DC.organizationUnitDeletionStatus(t):null,_,{refreshInterval:6e4,refreshWhenHidden:!0});(0,s.useEffect)(()=>{if(!(null==K?void 0:K.isPendingDeletion)){I(null),B.current=!1;return}!B.current&&"number"==typeof K.remainingSeconds&&K.remainingSeconds>=0&&(I(K.remainingSeconds),B.current=!0);let e=setInterval(()=>{I(e=>null===e||e<=0?0:e-1)},1e3);return()=>clearInterval(e)},[null==K?void 0:K.isPendingDeletion,null==K?void 0:K.remainingSeconds]);let q=!!(null==K?void 0:K.isPendingDeletion);return(0,n.jsx)("div",{className:"flex justify-center pt-8",children:(0,n.jsxs)("div",{className:"w-full max-w-2xl",children:[(0,n.jsxs)("div",{className:"bg-background rounded-2xl shadow border border-border px-8 py-7",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-lg font-bold",children:"Organization Unit Information"}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Details of your organization unit"})]}),!x&&!q&&(0,n.jsxs)("div",{className:"flex gap-2 ml-auto",children:[(0,n.jsxs)(r.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 border-gray-300 hover:border-[#FF6A1A] hover:bg-[#FFF3EC] rounded-lg font-medium",onClick:()=>{f&&(w(f.name),k(f.description),v(!0))},children:[(0,n.jsx)(u.A,{className:"h-4 w-4"}),"Edit"]}),(0,n.jsxs)(r.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 border-red-300 hover:border-red-500 hover:bg-red-50 rounded-lg font-medium text-red-600 hover:text-red-600",onClick:()=>{S(!0)},children:[(0,n.jsx)(g.A,{className:"h-4 w-4 text-red-600"}),"Delete"]})]})]}),q&&(0,n.jsxs)("div",{className:"flex items-center justify-between dark:bg-orange-950/50 bg-orange-50 border border-orange-300 dark:border-orange-800/50 rounded-lg px-4 py-3 my-4",children:[(0,n.jsx)("div",{className:"text-orange-700 dark:text-orange-400 font-semibold",children:"number"==typeof O&&O>0?"This organization unit will be deleted in ".concat((e=>{if(e<=0)return"Deleting...";let t=Math.floor(e/86400),a=Math.floor(e%86400/3600),n=Math.floor(e%3600/60),s=e%60,i=[];return t>0&&i.push("".concat(t," day").concat(t>1?"s":"")),a>0&&i.push("".concat(a," hour").concat(a>1?"s":"")),n>0&&i.push("".concat(n," minute").concat(n>1?"s":"")),s>0&&0===i.length&&i.push("".concat(s," second").concat(s>1?"s":"")),i.join(", ")})(O)):"Deleting organization unit..."}),(null==K?void 0:K.canCancel)&&(0,n.jsx)(r.$,{variant:"outline",className:"ml-4 border-orange-600 text-orange-700 hover:bg-orange-100",onClick:()=>U(!0),children:"Cancel Deletion"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5 mt-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"ou-name",className:"block text-xs font-semibold text-muted-foreground mb-1",children:"Name"}),x?(0,n.jsx)(i.p,{id:"ou-name",value:j,onChange:e=>w(e.target.value),className:"rounded-lg border-input bg-background focus:border-[#FF6A1A] focus:ring-[#FF6A1A]/30",placeholder:"Organization unit name"}):(0,n.jsx)("div",{className:"rounded-lg bg-muted px-3 py-2 text-base border border-border",children:null==f?void 0:f.name})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"ou-description",className:"block text-xs font-semibold text-muted-foreground mb-1",children:"Description"}),x?(0,n.jsx)(i.p,{id:"ou-description",value:N,onChange:e=>k(e.target.value),className:"rounded-lg border-input bg-background focus:border-[#FF6A1A] focus:ring-[#FF6A1A]/30",placeholder:"Organization unit description"}):(0,n.jsx)("div",{className:"rounded-lg bg-muted px-3 py-2 text-base border border-border",children:(null==f?void 0:f.description)||(0,n.jsx)("span",{className:"italic text-muted-foreground",children:"No description"})})]})]}),x&&(0,n.jsxs)("div",{className:"flex justify-end gap-2 mt-8",children:[(0,n.jsx)(r.$,{variant:"outline",onClick:()=>{v(!1)},disabled:b,className:"rounded-lg",children:"Cancel"}),(0,n.jsx)(r.$,{onClick:$,disabled:b,className:"rounded-lg bg-[#FF6A1A] text-white hover:bg-orange-500",children:b?"Saving...":"Save Changes"})]})]}),(0,n.jsx)(c.lG,{open:C,onOpenChange:D,children:(0,n.jsxs)(c.Cf,{children:[(0,n.jsx)(c.c7,{children:(0,n.jsx)(c.L3,{children:"Warning"})}),(0,n.jsx)("div",{children:"If you change the name, the tenant will also change, which will result in a changed URL and the Bot agent will be disconnected. Do you still want to proceed?"}),(0,n.jsxs)(c.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,n.jsx)(r.$,{variant:"outline",onClick:()=>D(!1),disabled:A,children:"Cancel"}),(0,n.jsx)(r.$,{onClick:P,disabled:A,className:"bg-[#FF6A1A] text-white hover:bg-orange-500",children:"Accept"})]})]})}),(0,n.jsx)(c.lG,{open:F,onOpenChange:S,children:(0,n.jsxs)(c.Cf,{children:[(0,n.jsx)(c.c7,{children:(0,n.jsx)(c.L3,{children:"Confirm Deletion"})}),(0,n.jsx)("div",{children:"Are you sure you want to delete this organization unit? It will be deleted in 7 days."}),(0,n.jsxs)(c.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,n.jsx)(r.$,{variant:"outline",onClick:()=>S(!1),children:"Cancel"}),(0,n.jsx)(r.$,{onClick:T,className:"bg-red-600 text-white hover:bg-red-700",children:"Delete"})]})]})}),(0,n.jsx)(c.lG,{open:R,onOpenChange:U,children:(0,n.jsxs)(c.Cf,{children:[(0,n.jsx)(c.c7,{children:(0,n.jsx)(c.L3,{children:"Cancel Deletion"})}),(0,n.jsx)("div",{children:"Are you sure you want to cancel the deletion of this organization unit?"}),(0,n.jsxs)(c.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,n.jsx)(r.$,{variant:"outline",onClick:()=>U(!1),children:"No"}),(0,n.jsx)(r.$,{onClick:M,className:"bg-[#FF6A1A] text-white hover:bg-orange-500",children:"Cancel Deletion"})]})]})})]})})}},88262:(e,t,a)=>{"use strict";a.d(t,{$:()=>s,d:()=>i});var n=a(12115);let s=(0,n.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function i(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}},89917:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},96365:(e,t,a)=>{"use strict";a.d(t,{K:()=>s});var n=a(7283);let s={getMyOrganizationUnits:async()=>await n.F.get("/api/ou/my-ous"),getBySlug:async e=>await n.F.get("/api/ou/slug/".concat(e)),getById:async e=>await n.F.get("/api/ou/".concat(e)),create:async e=>await n.F.post("/api/ou/create",e),update:async(e,t)=>await n.F.put("/api/ou/".concat(e),t),requestDeletion:async e=>await n.F.post("/api/ou/".concat(e,"/request-deletion"),{}),cancelDeletion:async e=>await n.F.post("/api/ou/".concat(e,"/cancel-deletion"),{}),getDeletionStatus:async e=>await n.F.get("/api/ou/".concat(e,"/deletion-status"))}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,2178,4727,8441,1684,7358],()=>t(70112)),_N_E=e.O()}]);