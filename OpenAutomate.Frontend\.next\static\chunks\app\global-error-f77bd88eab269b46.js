(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4219],{67570:(e,s,n)=>{Promise.resolve().then(n.bind(n,98385))},98385:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>a});var r=n(95155);function a(e){let{error:s,reset:n}=e;return(0,r.jsx)("html",{children:(0,r.jsx)("body",{className:"min-h-screen flex items-center justify-center bg-orange-50 text-orange-800 font-sans p-4",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white shadow-md rounded-xl p-6 border border-orange-200",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-orange-600 mb-2",children:"Something went wrong!"}),(0,r.jsx)("p",{className:"mb-4",children:s.message}),(0,r.jsx)("div",{className:"flex ",children:(0,r.jsx)("button",{onClick:n,className:"bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded w-full",children:"Try again"})})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(67570)),_N_E=e.O()}]);