"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8916],{30285:(e,t,a)=>{a.d(t,{$:()=>l,r:()=>d});var s=a(95155),n=a(12115),r=a(66634),o=a(74466),i=a(36928);let d=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef((e,t)=>{let{className:a,variant:n,size:o,asChild:l=!1,...c}=e,u=l?r.DX:"button";return(0,s.jsx)(u,{"data-slot":"button",className:(0,i.cn)(d({variant:n,size:o,className:a})),ref:t,...c})});l.displayName="Button"},38382:(e,t,a)=>{a.d(t,{CG:()=>d,Fm:()=>f,Qs:()=>m,cj:()=>i,h:()=>u,qp:()=>p});var s=a(95155);a(12115);var n=a(59096),r=a(54416),o=a(36928);function i(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"sheet",...t})}function d(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"sheet-trigger",...t})}function l(e){let{...t}=e;return(0,s.jsx)(n.ZL,{"data-slot":"sheet-portal",...t})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(n.hJ,{"data-slot":"sheet-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,side:i="right",...d}=e;return(0,s.jsxs)(l,{children:[(0,s.jsx)(c,{}),(0,s.jsxs)(n.UC,{"data-slot":"sheet-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===i&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===i&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===i&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===i&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...d,children:[a,(0,s.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(r.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function f(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,o.cn)("flex flex-col gap-1.5 p-4",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(n.hE,{"data-slot":"sheet-title",className:(0,o.cn)("text-foreground font-semibold",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(n.VY,{"data-slot":"sheet-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}},44838:(e,t,a)=>{a.d(t,{I:()=>c,SQ:()=>l,_2:()=>u,hO:()=>f,lp:()=>p,mB:()=>m,rI:()=>i,ty:()=>d});var s=a(95155);a(12115);var n=a(18289),r=a(5196),o=a(36928);function i(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function d(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function l(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function c(e){let{...t}=e;return(0,s.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...t})}function u(e){let{className:t,inset:a,variant:r="default",...i}=e;return(0,s.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}function f(e){let{className:t,children:a,checked:i,...d}=e;return(0,s.jsxs)(n.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:i,...d,children:[(0,s.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(r.A,{className:"size-4"})})}),a]})}function p(e){let{className:t,inset:a,...r}=e;return(0,s.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},54629:(e,t,a)=>{a.d(t,{F:()=>J});var s=a(54785),n=a(54416),r=a(51154),o=a(42355),i=a(13052),d=a(74126),l=a(381),c=a(81586),u=a(44020),f=a(84616),p=a(94788),m=a(71007),g=a(92138),x=a(62530),h=a(30130),v=a(93509),b=a(42148),j=a(59099),w=a(18175),y=a(5196),A=a(99890),k=a(57434),N=a(27213),z=a(85690),_=a(82178),C=a(57340),F=a(50741),L=a(17051),S=a(34835),U=a(53904),D=a(45347),E=a(52631),I=a(28883);let J={logo:s.A,close:n.A,Spinner:r.A,chevronLeft:o.A,chevronRight:i.A,trash:d.A,settings:l.A,billing:c.A,ellipsis:u.A,add:f.A,warning:p.A,user:m.A,arrowRight:g.A,help:p.A,pizza:x.A,sun:h.A,moon:v.A,laptop:b.A,gitHub:j.A,twitter:w.A,check:y.A,file:A.A,fileText:k.A,image:N.A,play:z.A,pause:_.A,home:C.A,chart:F.A,cog:L.A,logout:S.A,refresh:U.A,about:D.A,guide:E.A,contact:I.A}},62523:(e,t,a)=>{a.d(t,{p:()=>r});var s=a(95155);a(12115);var n=a(36928);function r(e){let{className:t,type:a,...r}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},71185:(e,t,a)=>{a.d(t,{U:()=>d});var s=a(95155);a(12115);var n=a(51362),r=a(30285),o=a(54629),i=a(44838);function d(){let{setTheme:e}=(0,n.D)();return(0,s.jsxs)(i.rI,{children:[(0,s.jsx)(i.ty,{asChild:!0,children:(0,s.jsxs)(r.$,{variant:"ghost",size:"sm",className:"h-8 w-8 px-0",children:[(0,s.jsx)(o.F.sun,{className:"rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(o.F.moon,{className:"absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsxs)(i.SQ,{align:"end",children:[(0,s.jsxs)(i._2,{onClick:()=>e("light"),children:[(0,s.jsx)(o.F.sun,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Light"})]}),(0,s.jsxs)(i._2,{onClick:()=>e("dark"),children:[(0,s.jsx)(o.F.moon,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Dark"})]}),(0,s.jsxs)(i._2,{onClick:()=>e("system"),children:[(0,s.jsx)(o.F.laptop,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"System"})]})]})]})}}}]);