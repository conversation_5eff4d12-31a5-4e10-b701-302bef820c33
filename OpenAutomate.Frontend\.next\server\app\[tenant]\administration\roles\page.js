(()=>{var e={};e.id=5644,e.ids=[5644],e.modules={1303:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},2526:(e,t,s)=>{Promise.resolve().then(s.bind(s,95229))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7254:(e,t,s)=>{Promise.resolve().then(s.bind(s,67664))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19830:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>a});var r=s(37413),n=s(67664);let a={title:"Automation",description:"Agent management page"};function i(){return(0,r.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4",children:[(0,r.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"}),(0,r.jsx)(n.default,{})]})}},27551:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=s(65239),n=s(48088),a=s(31369),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let o={children:["",{children:["[tenant]",{children:["administration",{children:["roles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,19830)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\roles\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,64656)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\[tenant]\\administration\\roles\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[tenant]/administration/roles/page",pathname:"/[tenant]/administration/roles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},27590:(e,t,s)=>{"use strict";function r(e,t={}){let{dateStyle:s="medium",timeStyle:n="short",fallback:a="N/A",customFormat:i,locale:l="en-US"}=t;if(!e)return a;try{let t;if("string"==typeof e){let s=e;s.endsWith("Z")||s.includes("+")||s.includes("-",10)||(s=s.replace(/(\.\d+)?$/,"$1Z"),console.debug(`Corrected UTC date format: ${e} -> ${s}`)),t=new Date(s)}else t=e;if(isNaN(t.getTime()))return console.warn(`Invalid date provided to formatUtcToLocal: ${e}`),a;if(i)return function(e,t){let s=e.getFullYear(),r=e.getMonth()+1,n=e.getDate(),a=e.getHours(),i=e.getMinutes(),l={yyyy:s.toString(),MMM:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][r-1],dd:n.toString().padStart(2,"0"),h:(a%12||12).toString(),mm:i.toString().padStart(2,"0"),a:a>=12?"PM":"AM"},o=t;return Object.entries(l).forEach(([e,t])=>{o=o.replace(RegExp(e,"g"),t)}),o}(t,i);return new Intl.DateTimeFormat(l,{dateStyle:s,timeStyle:n}).format(t)}catch(t){return console.error(`Error formatting date ${e}:`,t),a}}s.d(t,{Ej:()=>r})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42300:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(16189),n=s(43210);function a(){let e=(0,r.useRouter)(),t=(0,r.useSearchParams)(),s=(0,n.useCallback)(e=>{let s=new URLSearchParams(t.toString());return Object.entries(e).forEach(([e,t])=>{null===t?s.delete(e):s.set(e,t)}),s.toString()},[t]),a=(0,n.useCallback)((t,r)=>{let n=s(r);e.push(`${t}?${n}`,{scroll:!1})},[s,e]);return{createQueryString:s,updateUrl:a}}},53984:(e,t,s)=>{"use strict";s.d(t,{i:()=>o});var r=s(60687),n=s(4654),a=s(56476),i=s(29523),l=s(21342);function o({table:e}){return(0,r.jsxs)(l.rI,{children:[(0,r.jsx)(n.ty,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,r.jsx)(a.A,{}),"View"]})}),(0,r.jsxs)(l.SQ,{align:"end",className:"w-[150px]",children:[(0,r.jsx)(l.lp,{children:"Toggle columns"}),(0,r.jsx)(l.mB,{}),e.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,r.jsx)(l.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},57175:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67664:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call rolesSchema() from the server but rolesSchema is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\administration\\roles\\roles.tsx","rolesSchema");let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\administration\\\\roles\\\\roles.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\administration\\roles\\roles.tsx","default")},93661:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},95229:(e,t,s)=>{"use strict";s.d(t,{default:()=>$});var r=s(60687),n=s(1303),a=s(29523),i=s(96834),l=s(56896),o=s(34208),d=s(43210),c=s(93661),u=s(57175),m=s(96362),p=s(63503),h=s(6091),x=s(21342),g=s(80391),f=s(20140);function j({row:e,onRefresh:t}){let[s,n]=(0,d.useState)(!1),[i,l]=(0,d.useState)(!1),[o,j]=(0,d.useState)(""),[v,S]=(0,d.useState)(!1),[y,C]=(0,d.useState)(!1),{toast:b}=(0,f.d)(),w=async()=>{S(!0);try{await h.eV.deleteRole(e.original.id),n(!1),b({title:"Success",description:"Role deleted successfully."}),t&&t()}catch(e){n(!1),e instanceof Error?j(e.message):j("Failed to delete role."),l(!0)}finally{S(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(x.rI,{children:[(0,r.jsx)(x.ty,{asChild:!0,children:(0,r.jsxs)(a.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,r.jsxs)(x.SQ,{align:"start",className:"w-[160px]",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)(x._2,{onClick:t=>{if(t&&t.stopPropagation(),e.original.isSystemAuthority){j("System roles cannot be edited."),l(!0);return}C(!0)},children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,r.jsx)("span",{children:"Edit"})]}),(0,r.jsx)(x.mB,{}),(0,r.jsxs)(x._2,{className:"text-destructive focus:text-destructive",onClick:t=>{if(t&&t.stopPropagation(),e.original.isSystemAuthority){j("System roles cannot be deleted."),l(!0);return}n(!0)},children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,r.jsx)("span",{children:"Delete"})]})]})]}),(0,r.jsx)(g.I,{isOpen:y,onClose:e=>{C(!1),e&&t&&t()},editingRole:e.original},e.original.id),(0,r.jsx)(p.lG,{open:s,onOpenChange:n,children:(0,r.jsxs)(p.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(p.c7,{children:(0,r.jsx)(p.L3,{children:"Confirm Delete"})}),(0,r.jsxs)("div",{children:["Are you sure you want to delete role ",(0,r.jsx)("b",{children:e.original.name}),"?"]}),(0,r.jsxs)(p.Es,{children:[(0,r.jsx)(a.$,{variant:"outline",onClick:()=>n(!1),disabled:v,children:"Cancel"}),(0,r.jsx)(a.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:w,disabled:v,children:v?"Deleting...":"Delete"})]})]})}),(0,r.jsx)(p.lG,{open:i,onOpenChange:l,children:(0,r.jsxs)(p.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(p.c7,{children:(0,r.jsx)(p.L3,{children:"Error"})}),(0,r.jsx)("div",{children:o}),(0,r.jsx)(p.Es,{children:(0,r.jsx)(a.$,{onClick:()=>l(!1),children:"OK"})})]})})]})}var v=s(27590);let S=e=>[{id:"select",header:({table:e})=>(0,r.jsx)(l.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:e})=>(0,r.jsx)(l.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1},{id:"actions",header:({column:e})=>(0,r.jsx)(o.w,{column:e,title:"Actions"}),cell:({row:t})=>(0,r.jsx)(j,{row:t,onRefresh:e})},{accessorKey:"name",header:({column:e})=>(0,r.jsx)(o.w,{column:e,title:"Name"}),cell:({row:e})=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"font-medium",children:e.getValue("name")}),e.original.isSystemAuthority&&(0,r.jsx)(i.E,{variant:"secondary",className:"text-xs",children:"System"})]})},{accessorKey:"description",header:({column:e})=>(0,r.jsx)(o.w,{column:e,title:"Description"}),cell:({row:e})=>(0,r.jsx)("div",{className:"max-w-[300px]",children:(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:e.getValue("description")||"No description"})})},{accessorKey:"permissions",header:({column:e})=>(0,r.jsx)(o.w,{column:e,title:"Permissions"}),cell:({row:e})=>{let t=e.original.permissions||[];return(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.length>0?t.slice(0,3).map((e,t)=>(0,r.jsx)(i.E,{variant:"outline",className:"text-xs",children:e.resourceName},t)):(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"No permissions"}),t.length>3&&(0,r.jsxs)(i.E,{variant:"outline",className:"text-xs",children:["+",t.length-3," more"]})]})},enableSorting:!1},{accessorKey:"createdAt",header:({column:e})=>(0,r.jsx)(o.w,{column:e,title:"Created"}),cell:({row:e})=>{let t=e.getValue("createdAt"),s=(0,v.Ej)(t,{dateStyle:"medium",timeStyle:void 0,fallback:"Invalid date"});return(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:s})}}];var y=s(50723),C=s(45880),b=s(16189),w=s(99270),A=s(41862),N=s(11860),k=s(89667),P=s(53984);function R({table:e,onSearch:t,searchValue:s="",isFiltering:n=!1,isPending:l=!1}){let o=e.getState().columnFilters.length>0,c=e.getState().columnFilters.length,u=(0,d.useRef)(null),m=(0,d.useRef)(null),p=s=>{u.current&&(m.current=u.current.selectionStart),t?t(s):e.getColumn("name")?.setFilterValue(s)};return(0,r.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,r.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,r.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,r.jsx)(w.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(k.p,{ref:u,placeholder:"Search roles...",value:s,onChange:e=>p(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:n,onFocus:()=>{u.current&&(m.current=u.current.selectionStart)}}),n&&(0,r.jsx)(A.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!n&&""!==s&&(0,r.jsx)(N.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>p("")})]}),c>0&&(0,r.jsxs)(i.E,{variant:"secondary",className:"rounded-sm px-1",children:[c," active ",1===c?"filter":"filters"]}),o&&(0,r.jsxs)(a.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),t&&t("")},className:"h-8 px-2 lg:px-3",disabled:n,children:["Reset",(0,r.jsx)(N.A,{className:"ml-2 h-4 w-4"})]})]}),(0,r.jsx)(P.i,{table:e})]})}var z=s(56090),F=s(93772),M=s(42300),I=s(14583),O=s(31207),E=s(70891);function $(){let e=(0,b.useRouter)(),t=(0,b.usePathname)(),s=(0,b.useSearchParams)(),{updateUrl:i}=(0,M.z)(),{toast:l}=(0,f.d)(),[o,c]=(0,d.useState)(!1),[u,m]=(0,d.useState)(null),[p,x]=(0,d.useState)({}),[j,v]=(0,d.useState)({}),[C,w]=(0,d.useState)(0),[A,N]=(0,d.useState)(!1),[k,P]=(0,d.useState)(!1),$=(0,d.useRef)(null);(0,d.useRef)(!0);let[_,G]=(0,d.useState)(()=>{let e=[],t=s.get("name");return t&&e.push({id:"name",value:t}),e}),[V,D]=(0,d.useState)(()=>{let e=s.get("sort"),t=s.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[T,L]=(0,d.useState)(()=>{let e=s.get("page"),t=s.get("size");return{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:t?parseInt(t):10}}),[q,K]=(0,d.useState)(s.get("name")??""),{data:U,error:H,isLoading:J,mutate:Q}=(0,O.Ay)(E.DC.roles(),h.eV.getAllRoles),B=(0,d.useMemo)(()=>{if(!U)return[];let e=U.map(e=>({id:e.id,name:e.name,description:e.description,isSystemAuthority:e.isSystemAuthority,createdAt:e.createdAt,updatedAt:e.updatedAt,permissions:e.permissions?.map(e=>({resourceName:e.resourceName,permission:e.permission}))}));if(_.length>0){let t=_.find(e=>"name"===e.id);if(t&&t.value){let s=t.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(s)||e.description.toLowerCase().includes(s))}}if(V.length>0){let t=V[0];e.sort((e,s)=>{let r=e[t.id],n=s[t.id];return void 0===r&&void 0===n?0:void 0===r?t.desc?1:-1:void 0===n?t.desc?-1:1:r<n?t.desc?1:-1:r>n?t.desc?-1:1:0})}return e},[U,_,V]),W=(0,d.useCallback)(async()=>{N(!1),P(!1),await Q()},[Q]),Z=(0,d.useMemo)(()=>Math.max(1,Math.ceil(C/T.pageSize)),[C,T.pageSize]),X=(0,z.N4)({data:B,columns:S(W),state:{sorting:V,columnVisibility:j,rowSelection:p,columnFilters:_,pagination:T},enableRowSelection:!0,onRowSelectionChange:x,onSortingChange:e=>{let s="function"==typeof e?e(V):e;D(s),s.length>0?i(t,{sort:s[0].id,order:s[0].desc?"desc":"asc",page:"1"}):i(t,{sort:null,order:null,page:"1"})},onColumnFiltersChange:G,onColumnVisibilityChange:v,onPaginationChange:e=>{let s="function"==typeof e?e(T):e;L(s),i(t,{page:(s.pageIndex+1).toString(),size:s.pageSize.toString()})},getCoreRowModel:(0,F.HT)(),getFilteredRowModel:(0,F.hM)(),getPaginationRowModel:(0,F.kW)(),getSortedRowModel:(0,F.h5)(),getFacetedRowModel:(0,F.kQ)(),getFacetedUniqueValues:(0,F.oS)(),manualPagination:!0,pageCount:Z,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.id}),Y=(0,d.useCallback)(e=>{K(e),N(!0),$.current&&clearTimeout($.current),$.current=setTimeout(()=>{let s=X.getColumn("name");s&&(s.setFilterValue(e),i(t,{name:e||null,page:"1"})),N(!1)},500)},[X,i,t]),ee=()=>Q();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Roles"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage user roles and permissions within your organization."})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[C>0&&(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("span",{children:["Total: ",C," role",1!==C?"s":""]})}),(0,r.jsxs)(a.$,{onClick:()=>{m(null),c(!0)},className:"flex items-center justify-center",children:[(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Create Role"]})]})]}),H&&(0,r.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,r.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load roles. Please try again."}),(0,r.jsx)(a.$,{variant:"outline",className:"mt-2",onClick:()=>Q(),children:"Retry"})]}),(0,r.jsx)(R,{table:X,onSearch:Y,searchValue:q,isFiltering:J,isPending:A}),(0,r.jsx)(y.b,{data:B,columns:S(W),table:X,onRowClick:t=>{e.push(`roles/${t.id}`)},isLoading:J,totalCount:C}),(0,r.jsx)(I.d,{currentPage:T.pageIndex+1,pageSize:T.pageSize,totalCount:C,totalPages:Z,isLoading:J,isChangingPageSize:k,isUnknownTotalCount:!1,onPageChange:e=>{L({...T,pageIndex:e-1}),i(t,{page:e.toString()})},onPageSizeChange:e=>{P(!0);let s=Math.floor(T.pageIndex*T.pageSize/e);L({pageSize:e,pageIndex:s}),i(t,{size:e.toString(),page:(s+1).toString()})}})]}),(0,r.jsx)(g.I,{isOpen:o,onClose:e=>{c(!1),m(null),e&&ee()},editingRole:u},u?.id??"new")]})}C.z.object({id:C.z.string(),name:C.z.string(),description:C.z.string(),isSystemAuthority:C.z.boolean(),createdAt:C.z.string(),updatedAt:C.z.string().optional()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7966,5584,5156,4654,6467,5880,1694,6945,8759,6763,519,4881,7288],()=>s(27551));module.exports=r})();