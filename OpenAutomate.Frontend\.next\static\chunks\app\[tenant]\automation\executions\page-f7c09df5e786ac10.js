(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6415],{14186:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},14839:(e,t,a)=>{Promise.resolve().then(a.bind(a,28892))},17759:(e,t,a)=>{"use strict";a.d(t,{C5:()=>j,MJ:()=>p,Rr:()=>v,eI:()=>x,lR:()=>h,lV:()=>c,zB:()=>u});var l=a(95155),n=a(12115),s=a(66634),r=a(62177),i=a(36928),o=a(85057);let c=r.Op,d=n.createContext({}),u=e=>{let{...t}=e;return(0,l.jsx)(d.Provider,{value:{name:t.name},children:(0,l.jsx)(r.xI,{...t})})},g=()=>{let e=n.useContext(d),t=n.useContext(m),{getFieldState:a}=(0,r.xW)(),l=(0,r.lN)({name:e.name}),s=a(e.name,l);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...s}},m=n.createContext({});function x(e){let{className:t,...a}=e,s=n.useId();return(0,l.jsx)(m.Provider,{value:{id:s},children:(0,l.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",t),...a})})}function h(e){let{className:t,...a}=e,{error:n,formItemId:s}=g();return(0,l.jsx)(o.J,{"data-slot":"form-label","data-error":!!n,className:(0,i.cn)("data-[error=true]:text-destructive",t),htmlFor:s,...a})}function p(e){let{...t}=e,{error:a,formItemId:n,formDescriptionId:r,formMessageId:i}=g();return(0,l.jsx)(s.DX,{"data-slot":"form-control",id:n,"aria-describedby":a?"".concat(r," ").concat(i):"".concat(r),"aria-invalid":!!a,...t})}function v(e){let{className:t,...a}=e,{formDescriptionId:n}=g();return(0,l.jsx)("p",{"data-slot":"form-description",id:n,className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}function j(e){var t;let{className:a,...n}=e,{error:s,formMessageId:r}=g(),o=s?String(null!==(t=null==s?void 0:s.message)&&void 0!==t?t:""):n.children;return o?(0,l.jsx)("p",{"data-slot":"form-message",id:r,className:(0,i.cn)("text-destructive text-sm",a),...n,children:o}):null}},28892:(e,t,a)=>{"use strict";a.d(t,{default:()=>ef});var l=a(95155),n=a(49103),s=a(30285),r=a(12115),i=a(47262),o=a(87570),c=a(26126),d=a(14186),u=a(51154),g=a(40646),m=a(54861),x=a(85690);function h(e){let{status:t,className:a}=e,n=(e=>{switch(e.toLowerCase()){case"pending":return{variant:"secondary",icon:d.A,label:"Pending",className:"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"};case"running":return{variant:"default",icon:u.A,label:"Running",className:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"};case"completed":return{variant:"default",icon:g.A,label:"Completed",className:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"};case"failed":return{variant:"destructive",icon:m.A,label:"Failed",className:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"};case"cancelled":return{variant:"outline",icon:m.A,label:"Cancelled",className:"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"};default:return{variant:"outline",icon:x.A,label:e,className:"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"}}})(t),s=n.icon;return(0,l.jsxs)(c.E,{variant:n.variant,className:"".concat(n.className," ").concat(a||""),children:[(0,l.jsx)(s,{className:"mr-1 h-3 w-3 ".concat("running"===t.toLowerCase()?"animate-spin":"")}),n.label]})}var p=a(5623),v=a(91788),j=a(57434),f=a(74126),b=a(54416),y=a(44838),N=a(54165),w=a(88262),C=a(49171),S=a(12187);function k(e){let{execution:t,onDeleted:a}=e,[n,i]=(0,r.useState)(!1),[o,c]=(0,r.useState)(!1),[d,g]=(0,r.useState)(!1),{toast:m}=(0,w.d)(),x=async()=>{c(!0);try{console.log("Delete execution:",t.id),m({title:"Delete Simulation",description:"Delete execution API not yet implemented."}),i(!1),a&&a()}catch(e){console.error("Delete failed:",e),m({title:"Delete Failed",description:"Failed to delete execution. Please try again.",variant:"destructive"})}finally{c(!1)}},h=async()=>{if(!t.hasLogs){m({title:"No Logs Available",description:"This execution does not have logs available for download.",variant:"destructive"});return}g(!0);try{let e="execution_".concat(t.id.substring(0,8),"_logs.log");await (0,C.iX)(t.id,e),m({title:"Download Started",description:"Execution logs download has started."})}catch(e){console.error("Failed to download logs:",e),m((0,S.m4)(e))}finally{g(!1)}},k=e=>{e.stopPropagation()};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(y.rI,{children:[(0,l.jsx)(y.ty,{asChild:!0,onClick:k,children:(0,l.jsx)(s.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted","aria-label":"Open menu",onClick:k,children:(0,l.jsx)(p.A,{className:"h-4 w-4"})})}),(0,l.jsxs)(y.SQ,{align:"end",className:"w-[180px]",onClick:k,onPointerDown:k,onMouseDown:k,children:[(0,l.jsxs)(y._2,{onClick:e=>{e.stopPropagation(),h()},disabled:!t.hasLogs||d,children:[d?(0,l.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin","aria-hidden":"true"}):t.hasLogs?(0,l.jsx)(v.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}):(0,l.jsx)(j.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,l.jsx)("span",{children:d?"Downloading...":t.hasLogs?"View Logs":"No Logs"})]}),(0,l.jsx)(y.mB,{}),(0,l.jsxs)(y._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e.stopPropagation(),i(!0)},children:[(0,l.jsx)(f.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,l.jsx)("span",{children:"Delete"})]})]})]}),(0,l.jsx)(N.lG,{open:n,onOpenChange:i,children:(0,l.jsxs)(N.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,l.jsxs)(N.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,l.jsx)(b.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,l.jsx)(N.c7,{children:(0,l.jsx)(N.L3,{children:"Confirm Delete"})}),(0,l.jsxs)("div",{children:["Are you sure you want to delete this execution? ",(0,l.jsx)("br",{}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Execution ID: ",(0,l.jsxs)("b",{children:[t.id.substring(0,8),"..."]})]})]}),(0,l.jsxs)(N.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,l.jsx)(s.$,{variant:"outline",onClick:e=>{e.stopPropagation(),i(!1)},disabled:o,children:"Cancel"}),(0,l.jsxs)(s.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:e=>{e.stopPropagation(),x()},disabled:o,children:[o&&(0,l.jsx)(u.A,{className:"animate-spin w-4 h-4 mr-2"}),o?"Deleting...":"Delete"]})]})]})})]})}let A=function(){let{onDeleted:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return[{id:"select",header:e=>{let{table:t}=e;return(0,l.jsx)(i.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e,a=e=>{e.stopPropagation()};return(0,l.jsx)("span",{onClick:a,onMouseDown:a,onPointerDown:a,children:(0,l.jsx)(i.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]",onClick:a})})},enableSorting:!1,enableHiding:!1},{id:"actions",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Actions"})},cell:t=>{let{row:a}=t;return(0,l.jsx)(k,{execution:a.original,onDeleted:e})},enableSorting:!1,enableHiding:!1},{accessorKey:"packageName",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Package Name"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{className:"font-medium",children:t.getValue("packageName")||"N/A"})})}},{accessorKey:"Version",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Package Version"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("Version")||"N/A"})})}},{accessorKey:"agent",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Agent"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("agent")})})}},{accessorKey:"state",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"State"})},cell:e=>{let{row:t}=e,a=String(t.getValue("state"));return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)(h,{status:a})})}},{accessorKey:"startTime",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Start Time"})},cell:e=>{let{row:t}=e,a=t.getValue("startTime");return console.log("StartTime column value:",{value:a,type:typeof a,rowId:t.original.id}),(0,l.jsx)("span",{children:"string"==typeof a?a:""})}},{accessorKey:"endTime",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"End Time"})},cell:e=>{let{row:t}=e,a=t.getValue("endTime");return console.log("EndTime column value:",{value:a,type:typeof a,rowId:t.original.id}),(0,l.jsx)("span",{children:"string"==typeof a?a:""})}},{accessorKey:"source",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Source"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("source")})})}},{accessorKey:"command",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Command"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("command")})})}},{accessorKey:"schedules",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Schedules"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("schedules")})})}},{accessorKey:"taskId",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Task Id"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("taskId")})})}}]};function F(e){let{execution:t,onDeleted:a}=e,[n,i]=(0,r.useState)(!1),[o,c]=(0,r.useState)(!1),[d,g]=(0,r.useState)(!1),{toast:m}=(0,w.d)(),x=async()=>{c(!0);try{console.log("Delete execution:",t.id),m({title:"Delete Simulation",description:"Delete execution API not yet implemented."}),i(!1),a&&a()}catch(e){console.error("Delete failed:",e),m({title:"Delete Failed",description:"Failed to delete execution. Please try again.",variant:"destructive"})}finally{c(!1)}},h=async()=>{if(!t.hasLogs){m({title:"No Logs Available",description:"This execution does not have logs available for download.",variant:"destructive"});return}g(!0);try{let e="execution_".concat(t.id.substring(0,8),"_logs.log");await (0,C.iX)(t.id,e),m({title:"Download Started",description:"Execution logs download has started."})}catch(e){console.error("Failed to download logs:",e),m((0,S.m4)(e))}finally{g(!1)}},k=e=>{e.stopPropagation()};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(y.rI,{children:[(0,l.jsx)(y.ty,{asChild:!0,onClick:k,children:(0,l.jsx)(s.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted","aria-label":"Open menu",onClick:k,children:(0,l.jsx)(p.A,{className:"h-4 w-4"})})}),(0,l.jsxs)(y.SQ,{align:"end",className:"w-[180px]",onClick:k,onPointerDown:k,onMouseDown:k,children:[(0,l.jsxs)(y._2,{onClick:e=>{e.stopPropagation(),h()},disabled:!t.hasLogs||d,children:[d?(0,l.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin","aria-hidden":"true"}):t.hasLogs?(0,l.jsx)(v.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}):(0,l.jsx)(j.A,{className:"mr-2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),(0,l.jsx)("span",{children:d?"Downloading...":t.hasLogs?"Download Logs":"No Logs"})]}),(0,l.jsx)(y.mB,{}),(0,l.jsxs)(y._2,{className:"text-destructive focus:text-destructive",onClick:e=>{e.stopPropagation(),i(!0)},children:[(0,l.jsx)(f.A,{className:"mr-2 h-4 w-4 text-destructive","aria-hidden":"true"}),(0,l.jsx)("span",{children:"Delete"})]})]})]}),(0,l.jsx)(N.lG,{open:n,onOpenChange:i,children:(0,l.jsxs)(N.Cf,{onInteractOutside:e=>e.preventDefault(),children:[(0,l.jsxs)(N.HM,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,l.jsx)(b.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Close"})]}),(0,l.jsx)(N.c7,{children:(0,l.jsx)(N.L3,{children:"Confirm Delete"})}),(0,l.jsxs)("div",{children:["Are you sure you want to delete this execution? ",(0,l.jsx)("br",{}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Execution ID: ",(0,l.jsxs)("b",{children:[t.id.substring(0,8),"..."]})]})]}),(0,l.jsxs)(N.Es,{className:"flex justify-end gap-2 pt-4",children:[(0,l.jsx)(s.$,{variant:"outline",onClick:e=>{e.stopPropagation(),i(!1)},disabled:o,children:"Cancel"}),(0,l.jsxs)(s.$,{variant:"destructive",className:"text-white dark:text-neutral-900",onClick:e=>{e.stopPropagation(),x()},disabled:o,children:[o&&(0,l.jsx)(u.A,{className:"animate-spin w-4 h-4 mr-2"}),o?"Deleting...":"Delete"]})]})]})})]})}A();var I=a(15426);let V=function(){let{onDeleted:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return[{id:"select",header:e=>{let{table:t}=e;return(0,l.jsx)(i.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e,a=e=>{e.stopPropagation()};return(0,l.jsx)("span",{onClick:a,onMouseDown:a,onPointerDown:a,children:(0,l.jsx)(i.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]",onClick:a})})},enableSorting:!1,enableHiding:!1},{id:"actions",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Actions"})},cell:t=>{let{row:a}=t;return(0,l.jsx)(F,{execution:a.original,onDeleted:e})},enableSorting:!1,enableHiding:!1},{accessorKey:"packageName",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Package Name"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{className:"font-medium",children:t.getValue("packageName")||"N/A"})})}},{accessorKey:"Version",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Package Version"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("Version")||"N/A"})})}},{accessorKey:"agent",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Agent"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("agent")})})}},{accessorKey:"state",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"State"})},cell:e=>{let{row:t}=e,a=String(t.getValue("state"));return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)(h,{status:a})})}},{accessorKey:"startTime",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Start Time"})},cell:e=>{let{row:t}=e,a=t.getValue("startTime"),n=(0,I.Ej)(a,{fallback:""});return(0,l.jsx)("span",{children:n})}},{accessorKey:"endTime",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"End Time"})},cell:e=>{let{row:t}=e,a=t.getValue("endTime"),n=(0,I.Ej)(a,{fallback:""});return(0,l.jsx)("span",{children:n})}},{accessorKey:"source",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Source"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("source")})})}},{accessorKey:"command",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Command"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("command")})})}},{accessorKey:"schedules",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Schedules"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("schedules")})})}},{accessorKey:"taskId",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Task Id"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("taskId")})})}}]};V();let E=[{id:"select",header:e=>{let{table:t}=e;return(0,l.jsx)(i.S,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all",className:"translate-y-[2px]"})},cell:e=>{let{row:t}=e;return(0,l.jsx)(i.S,{checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row",className:"translate-y-[2px]"})},enableSorting:!1,enableHiding:!1},{accessorKey:"packageName",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Package name"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("packageName")})})}},{accessorKey:"agent",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Agent"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("agent")})})}},{accessorKey:"nextRunTime",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Next Run Time"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("nextRunTime")})})}},{accessorKey:"schedule",header:e=>{let{column:t}=e;return(0,l.jsx)(o.w,{column:t,title:"Schedule"})},cell:e=>{let{row:t}=e;return(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{children:t.getValue("schedule")})})}}];var R=a(54333),z=a(62177),P=a(90221),D=a(55594),T=a(17759),M=a(59409),L=a(34953),$=a(70449),q=a(32771),O=a(86490);let U=D.Ik({packageId:D.Yj().min(1,"Package is required"),version:D.Yj().min(1,"Version is required"),botAgentId:D.Yj().min(1,"Agent is required")});function K(e){let{isOpen:t,onClose:a,onSuccess:n}=e,{toast:i}=(0,w.d)(),{data:o,error:c}=(0,L.Ay)(t?$.DC.packages():null,q.s9),{data:d,error:g}=(0,L.Ay)(t?$.DC.agents():null,O.NA),[m,h]=(0,r.useState)(!1),p=(0,r.useMemo)(()=>{var e;return null!==(e=null==o?void 0:o.filter(e=>e.isActive))&&void 0!==e?e:[]},[o]),v=(0,r.useMemo)(()=>{var e;return null!==(e=null==d?void 0:d.filter(e=>"Disconnected"!==e.status))&&void 0!==e?e:[]},[d]),j=!o||!d,f=(0,z.mN)({resolver:(0,P.u)(U),defaultValues:{packageId:"",version:"",botAgentId:""}}),b=f.watch("packageId"),y=null==o?void 0:o.find(e=>e.id===b),S=(null==y?void 0:y.versions)||[];(0,r.useEffect)(()=>{c&&console.error("Error loading packages:",c),g&&console.error("Error loading agents:",g)},[c,g]),(0,r.useEffect)(()=>{b&&f.setValue("version","")},[b,f]);let k=e=>{let t=null==d?void 0:d.find(t=>t.id===e);return t?"Disconnected"===t.status?(i({variant:"destructive",title:"Agent Disconnected",description:"Selected agent is disconnected and cannot execute packages"}),null):("Busy"===t.status&&i({title:"Agent Busy",description:"Selected agent is currently busy. The execution will be queued."}),t):(i({variant:"destructive",title:"Agent Not Found",description:"Selected agent not found"}),null)},A=(e,t)=>{let a=null==o?void 0:o.find(t=>t.id===e),l=null==a?void 0:a.versions.find(e=>e.versionNumber===t);return a&&l?l.isActive?{selectedPackage:a,selectedVersion:l}:(i({variant:"destructive",title:"Version Inactive",description:"Selected package version is not active"}),null):(i({variant:"destructive",title:"Invalid Selection",description:"Invalid package or version selected"}),null)},F=e=>{console.error("Error triggering execution:",e)},V=async e=>{h(!0);try{let t=k(e.botAgentId);if(!t)return;let l=A(e.packageId,e.version);if(!l)return;let{selectedPackage:s}=l,r={botAgentId:e.botAgentId,packageId:e.packageId,packageName:s.name,version:e.version},o=await (0,C.RT)(r);i({title:"Execution Started",description:"Execution started successfully (ID: ".concat(o.id.substring(0,8),"...)")}),f.reset(),a(),null==n||n({id:o.id,packageName:s.name,botAgentName:t.name})}catch(e){F(e)}finally{h(!1)}},E=()=>{f.reset(),a()};return(0,l.jsx)(N.lG,{open:t,onOpenChange:E,children:(0,l.jsxs)(N.Cf,{className:"sm:max-w-[500px]",children:[(0,l.jsxs)(N.c7,{children:[(0,l.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"h-5 w-5"}),"Create New Execution"]}),(0,l.jsx)(N.rr,{children:"Select a package, version, and agent to execute immediately."})]}),j?(0,l.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,l.jsx)(u.A,{className:"h-6 w-6 animate-spin"}),(0,l.jsx)("span",{className:"ml-2",children:"Loading packages and agents..."})]}):(0,l.jsx)(T.lV,{...f,children:(0,l.jsxs)("form",{onSubmit:f.handleSubmit(V),className:"space-y-4",children:[(0,l.jsx)(T.zB,{control:f.control,name:"packageId",render:e=>{let{field:t}=e;return(0,l.jsxs)(T.eI,{children:[(0,l.jsx)(T.lR,{children:"Package"}),(0,l.jsxs)(M.l6,{onValueChange:t.onChange,value:t.value,children:[(0,l.jsx)(T.MJ,{children:(0,l.jsx)(M.bq,{children:(0,l.jsx)(M.yv,{placeholder:"Select a package"})})}),(0,l.jsx)(M.gC,{children:p.map(e=>(0,l.jsx)(M.eb,{value:e.id,children:(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)("span",{className:"font-medium",children:e.name}),e.description&&(0,l.jsx)("span",{className:"text-sm text-muted-foreground",children:e.description})]})},e.id))})]}),(0,l.jsx)(T.C5,{})]})}}),(0,l.jsx)(T.zB,{control:f.control,name:"version",render:e=>{let{field:t}=e;return(0,l.jsxs)(T.eI,{children:[(0,l.jsx)(T.lR,{children:"Version"}),(0,l.jsxs)(M.l6,{onValueChange:t.onChange,value:t.value,disabled:!b,children:[(0,l.jsx)(T.MJ,{children:(0,l.jsx)(M.bq,{children:(0,l.jsx)(M.yv,{placeholder:"Select a version"})})}),(0,l.jsx)(M.gC,{children:S.map(e=>(0,l.jsx)(M.eb,{value:e.versionNumber,children:(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsxs)("span",{className:"font-medium",children:["v",e.versionNumber]}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Uploaded ",(0,I.Ej)(e.uploadedAt,{dateStyle:"medium",timeStyle:void 0,fallback:"Unknown date"})]})]})},e.id))})]}),(0,l.jsx)(T.C5,{})]})}}),(0,l.jsx)(T.zB,{control:f.control,name:"botAgentId",render:e=>{let{field:t}=e;return(0,l.jsxs)(T.eI,{children:[(0,l.jsx)(T.lR,{children:"Agent"}),(0,l.jsxs)(M.l6,{onValueChange:t.onChange,value:t.value,children:[(0,l.jsx)(T.MJ,{children:(0,l.jsx)(M.bq,{children:(0,l.jsx)(M.yv,{placeholder:"Select an agent"})})}),(0,l.jsx)(M.gC,{children:v.map(e=>(0,l.jsx)(M.eb,{value:e.id,children:(0,l.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)("span",{className:"font-medium",children:e.name}),(0,l.jsx)("span",{className:"text-sm text-muted-foreground",children:e.machineName})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:"h-2 w-2 rounded-full ".concat("Available"===e.status?"bg-green-500":"Busy"===e.status?"bg-yellow-500":"bg-red-500")}),(0,l.jsx)("span",{className:"text-xs font-medium ".concat("Available"===e.status?"text-green-600":"Busy"===e.status?"text-yellow-600":"text-red-600"),children:e.status})]})]})},e.id))})]}),(0,l.jsx)(T.C5,{})]})}}),(0,l.jsxs)(N.Es,{children:[(0,l.jsx)(s.$,{type:"button",variant:"outline",onClick:E,children:"Cancel"}),(0,l.jsx)(s.$,{type:"submit",disabled:m,children:m?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Starting..."]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Start Execution"]})})]})]})})]})})}var _=a(35695),B=a(47924),H=a(66932),W=a(62523),G=a(11832),J=a(14636),X=a(85511),Q=a(36928),Y=a(69074),Z=a(67426);function ee(e){var t,a,n,i,o,d;let{table:g,statuses:m,onSearch:x,onStatusChange:h,searchValue:p="",isFiltering:v=!1,isPending:j=!1,searchPlaceholder:f="Search by ID or Agent..."}=e,y=g.getState().columnFilters.length>0,[N,w]=r.useState(void 0),C=g.getState().columnFilters.length,S=(0,r.useRef)(null),k=(0,r.useRef)(null);(0,r.useEffect)(()=>{document.activeElement!==S.current&&null!==k.current&&S.current&&(S.current.focus(),null!==k.current&&S.current.setSelectionRange(k.current,k.current))},[j,v]);let A=e=>{if(S.current&&(k.current=S.current.selectionStart),x)x(e);else{var t;null===(t=g.getColumn("name"))||void 0===t||t.setFilterValue(e)}};return(0,l.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,l.jsx)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md space-y-2",children:(0,l.jsx)("div",{className:"relative",children:(0,l.jsxs)(J.AM,{children:[(0,l.jsx)(J.Wv,{asChild:!0,children:(0,l.jsxs)(s.$,{variant:"outline",className:(0,Q.cn)("w-full justify-start text-left font-normal",!N&&"text-muted-foreground"),disabled:v,children:[(0,l.jsx)(Y.A,{className:"mr-2 h-4 w-4"}),N?(0,Z.GP)(N,"PPP"):"Select date"]})}),(0,l.jsx)(J.hl,{className:"w-auto p-0",align:"start",children:(0,l.jsx)(X.V,{mode:"single",selected:N,onSelect:e=>{var t;w(e);let a=e?(0,Z.GP)(e,"yyyy-MM-dd"):"";null===(t=g.getColumn("createdAt"))||void 0===t||t.setFilterValue(a)},initialFocus:!0})})]})})}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(W.p,{ref:S,placeholder:f,value:p,onChange:e=>A(e.target.value),className:"h-10 pl-8 pr-8 w-full",disabled:v,onFocus:()=>{S.current&&(k.current=S.current.selectionStart)}}),(0,l.jsx)(B.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground pointer-events-none"}),v&&(0,l.jsx)(u.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!v&&""!==p&&(0,l.jsx)(b.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>A("")})]}),g.getColumn("agent")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("agent"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("agent"))||void 0===a||a.setFilterValue(e)}},value:(null===(t=g.getColumn("agent"))||void 0===t?void 0:t.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter agent"}),(null===(a=g.getColumn("agent"))||void 0===a?void 0:a.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All Agents"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),g.getColumn("packageName")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("packageName"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("packageName"))||void 0===a||a.setFilterValue(e)}},value:(null===(n=g.getColumn("packageName"))||void 0===n?void 0:n.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter package"}),(null===(i=g.getColumn("packageName"))||void 0===i?void 0:i.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All Packages"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),g.getColumn("state")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("state"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("state"))||void 0===a||a.setFilterValue(e)}},value:(null===(o=g.getColumn("state"))||void 0===o?void 0:o.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter state"}),(null===(d=g.getColumn("state"))||void 0===d?void 0:d.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All States"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),C>0&&(0,l.jsxs)(c.E,{variant:"secondary",className:"rounded-sm px-1",children:[C," active ",1===C?"filter":"filters"]}),y&&(0,l.jsxs)(s.$,{variant:"ghost",onClick:()=>{g.resetColumnFilters(),x&&x("")},className:"h-8 px-2 lg:px-3",disabled:v,children:["Reset",(0,l.jsx)(b.A,{className:"ml-2 h-4 w-4"})]})]}),(0,l.jsx)(G.i,{table:g})]})}function et(e){var t,a,n,i,o,d;let{table:g,statuses:m,onSearch:x,onStatusChange:h,searchValue:p="",isFiltering:v=!1,isPending:j=!1,searchPlaceholder:f="Search by ID or Agent..."}=e,y=g.getState().columnFilters.length>0,N=g.getState().columnFilters.length,w=(0,r.useRef)(null),C=(0,r.useRef)(null);(0,r.useEffect)(()=>{document.activeElement!==w.current&&null!==C.current&&w.current&&(w.current.focus(),null!==C.current&&w.current.setSelectionRange(C.current,C.current))},[j,v]);let S=e=>{if(w.current&&(C.current=w.current.selectionStart),x)x(e);else{var t;null===(t=g.getColumn("name"))||void 0===t||t.setFilterValue(e)}};return(0,l.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,l.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,l.jsx)(B.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,l.jsx)(W.p,{ref:w,placeholder:f,value:p,onChange:e=>S(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:v,onFocus:()=>{w.current&&(C.current=w.current.selectionStart)}}),v&&(0,l.jsx)(u.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!v&&""!==p&&(0,l.jsx)(b.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>S("")})]}),g.getColumn("agent")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("agent"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("agent"))||void 0===a||a.setFilterValue(e)}},value:(null===(t=g.getColumn("agent"))||void 0===t?void 0:t.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter agent"}),(null===(a=g.getColumn("agent"))||void 0===a?void 0:a.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All Agents"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),g.getColumn("packageName")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("packageName"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("packageName"))||void 0===a||a.setFilterValue(e)}},value:(null===(n=g.getColumn("packageName"))||void 0===n?void 0:n.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter package"}),(null===(i=g.getColumn("packageName"))||void 0===i?void 0:i.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All Packages"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),g.getColumn("state")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("state"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("state"))||void 0===a||a.setFilterValue(e)}},value:(null===(o=g.getColumn("state"))||void 0===o?void 0:o.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter state"}),(null===(d=g.getColumn("state"))||void 0===d?void 0:d.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All States"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),N>0&&(0,l.jsxs)(c.E,{variant:"secondary",className:"rounded-sm px-1",children:[N," active ",1===N?"filter":"filters"]}),y&&(0,l.jsxs)(s.$,{variant:"ghost",onClick:()=>{g.resetColumnFilters(),x&&x("")},className:"h-8 px-2 lg:px-3",disabled:v,children:["Reset",(0,l.jsx)(b.A,{className:"ml-2 h-4 w-4"})]})]}),(0,l.jsx)(G.i,{table:g})]})}function ea(e){var t,a,n,i,o,d;let{table:g,statuses:m,onSearch:x,onStatusChange:h,searchValue:p="",isFiltering:v=!1,isPending:j=!1,searchPlaceholder:f="Search by ID or Agent..."}=e,y=g.getState().columnFilters.length>0,N=g.getState().columnFilters.length,w=(0,r.useRef)(null),C=(0,r.useRef)(null);(0,r.useEffect)(()=>{document.activeElement!==w.current&&null!==C.current&&w.current&&(w.current.focus(),null!==C.current&&w.current.setSelectionRange(C.current,C.current))},[j,v]);let S=e=>{if(w.current&&(C.current=w.current.selectionStart),x)x(e);else{var t;null===(t=g.getColumn("name"))||void 0===t||t.setFilterValue(e)}};return(0,l.jsxs)("div",{className:"flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,l.jsxs)("div",{className:"relative w-full md:w-auto md:flex-1 max-w-md",children:[(0,l.jsx)(B.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,l.jsx)(W.p,{ref:w,placeholder:f,value:p,onChange:e=>S(e.target.value),className:"h-10 pl-8 w-full pr-8",disabled:v,onFocus:()=>{w.current&&(C.current=w.current.selectionStart)}}),v&&(0,l.jsx)(u.A,{className:"absolute right-2 top-2.5 h-4 w-4 animate-spin text-primary"}),!v&&""!==p&&(0,l.jsx)(b.A,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground",onClick:()=>S("")})]}),g.getColumn("agent")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("agent"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("agent"))||void 0===a||a.setFilterValue(e)}},value:(null===(t=g.getColumn("agent"))||void 0===t?void 0:t.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter agent"}),(null===(a=g.getColumn("agent"))||void 0===a?void 0:a.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All Agents"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),g.getColumn("packageName")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("packageName"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("packageName"))||void 0===a||a.setFilterValue(e)}},value:(null===(n=g.getColumn("packageName"))||void 0===n?void 0:n.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter package"}),(null===(i=g.getColumn("packageName"))||void 0===i?void 0:i.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All Packages"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),g.getColumn("state")&&(0,l.jsx)("div",{className:"flex items-center space-x-1",children:(0,l.jsxs)(M.l6,{onValueChange:e=>{if(h)h(e);else{var t,a;"all"===e?null===(t=g.getColumn("state"))||void 0===t||t.setFilterValue(""):null===(a=g.getColumn("state"))||void 0===a||a.setFilterValue(e)}},value:(null===(o=g.getColumn("state"))||void 0===o?void 0:o.getFilterValue())||"all",disabled:v||j,children:[(0,l.jsx)(M.bq,{className:"h-10 sm:w-[180px]",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)(M.yv,{placeholder:"Filter state"}),(null===(d=g.getColumn("state"))||void 0===d?void 0:d.getFilterValue())&&(0,l.jsx)(c.E,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:"1"})]})}),(0,l.jsxs)(M.gC,{children:[(0,l.jsx)(M.eb,{value:"all",children:"All States"}),m.map(e=>(0,l.jsx)(M.eb,{value:e.value,children:e.label},e.value))]})]})}),N>0&&(0,l.jsxs)(c.E,{variant:"secondary",className:"rounded-sm px-1",children:[N," active ",1===N?"filter":"filters"]}),y&&(0,l.jsxs)(s.$,{variant:"ghost",onClick:()=>{g.resetColumnFilters(),x&&x("")},className:"h-8 px-2 lg:px-3",disabled:v,children:["Reset",(0,l.jsx)(b.A,{className:"ml-2 h-4 w-4"})]})]}),(0,l.jsx)(G.i,{table:g})]})}var el=a(62668),en=a(29797),es=a(35984),er=a(67136),ei=a(99667),eo=a(48133);let ec=e=>["Server timeout elapsed","The connection was stopped during negotiation","Failed to start the connection","Error: Error: The connection was stopped","connection disconnected","Unable to connect","disconnected with error","Stopping the connection","WebSocket closed","network error","transport timed out","Transport disconnected","Response status code does not indicate success: 401","Unauthorized","Cannot start a HubConnection"].some(t=>e.includes(t)),ed=e=>{console.debug("[SignalR Execution] Attempting to reconnect after delay..."),e.start().catch(e=>{console.debug("[SignalR Execution] Reconnection attempt also failed:",e instanceof Error?e.message:"Unknown error")})},eu=async e=>{try{await e.start(),console.debug("[SignalR Execution] Connected successfully")}catch(t){t instanceof Error&&t.message&&ec(t.message)?(console.debug("[SignalR Execution] Expected connection issue (suppressed):",t.message),setTimeout(()=>ed(e),3e3)):console.error("[SignalR Execution] Connection Error:",t)}},eg=async()=>{try{let e=await fetch("/api/connection-info");if(!e.ok)throw Error("Discovery failed: ".concat(e.status));return(await e.json()).apiUrl}catch(e){return console.error("[SignalR Execution] Failed to discover API URL:",e),null}},em=async e=>{let t=await eg();if(!t)return console.error("[SignalR Execution] Cannot create connection: Failed to discover backend API URL"),null;let a="".concat(t.replace(/\/$/,""),"/").concat(e,"/hubs/botagent"),l=(0,eo.c4)();if(!l)return console.warn("[SignalR Execution] No auth token available for SignalR connection"),null;console.debug("[SignalR Execution] Creating direct connection to backend hub:",a);let n={accessTokenFactory:()=>l,transport:es.w.WebSockets|es.w.LongPolling,headers:{Authorization:"Bearer ".concat(l)}};return new er.$().withUrl(a,n).configureLogging(ei.$.Warning).withAutomaticReconnect({nextRetryDelayInMilliseconds:e=>Math.min(1e3*Math.pow(2,e.previousRetryCount),6e4)}).withServerTimeout(12e4).withKeepAliveInterval(3e4).build()},ex=e=>{var t,a,l,n,s,r,i,o,c,d,u;return{botAgentId:null!==(a=null!==(t=e.botAgentId)&&void 0!==t?t:e.BotAgentId)&&void 0!==a?a:"",botAgentName:null!==(n=null!==(l=e.botAgentName)&&void 0!==l?l:e.BotAgentName)&&void 0!==n?n:"",status:null!==(r=null!==(s=e.status)&&void 0!==s?s:e.Status)&&void 0!==r?r:"",executionId:null!==(o=null!==(i=e.executionId)&&void 0!==i?i:e.ExecutionId)&&void 0!==o?o:"",message:null!==(c=e.message)&&void 0!==c?c:e.Message,timestamp:null!==(u=null!==(d=e.timestamp)&&void 0!==d?d:e.Timestamp)&&void 0!==u?u:new Date().toISOString()}},eh=e=>{e&&(e.message&&ec(e.message)?console.debug("[SignalR Execution] Connection issue - will automatically reconnect if possible"):console.error("[SignalR Execution] Connection closed with error:",e))},ep=(e,t,a)=>{e.onclose(eh),e.on("ExecutionStatusUpdate",e=>{let l=ex(e);console.debug("[SignalR Execution] ExecutionStatusUpdate received:",l),t(e=>({...e,[l.executionId]:l})),a&&a(l)}),e.onreconnecting(e=>{console.debug("[SignalR Execution] Attempting to reconnect...",null==e?void 0:e.message)}),e.onreconnected(e=>{console.debug("[SignalR Execution] Reconnected successfully with ID:",e)})};var ev=a(36268),ej=a(11032);function ef(){var e,t;let a=(0,_.useRouter)(),i=(0,_.usePathname)(),o=(0,_.useSearchParams)(),{updateUrl:c}=(0,el.z)(),{toast:d}=(0,w.d)(),[u,g]=(0,r.useState)(!1),[m,x]=(0,r.useState)("inprogress"),[h,p]=(0,r.useState)({}),[v,j]=(0,r.useState)({}),[f,b]=(0,r.useState)(0),y=(0,r.useRef)(0),[N,S]=(0,r.useState)(!1),[k,F]=(0,r.useState)(!1),[z,P]=(0,r.useState)(!1),D=(0,r.useRef)(null),T=(0,r.useRef)(!0),[M,q]=(0,r.useState)(()=>{let e=[],t=o.get("search");t&&("inprogress"===m||"historical"===m?e.push({id:"agent",value:t}):e.push({id:"packageName",value:t}));let a=o.get("status");return a&&e.push({id:"state",value:a}),e}),[O,U]=(0,r.useState)(()=>{let e=o.get("sort"),t=o.get("order");return e&&("asc"===t||"desc"===t)?[{id:e,desc:"desc"===t}]:[]}),[B,H]=(0,r.useState)(()=>{let e=o.get("page"),t=o.get("size"),a=t?Math.max(1,parseInt(t)):10;return console.log("Initializing pagination from URL: page=".concat(e,", size=").concat(t,", pageSize=").concat(a)),{pageIndex:e?Math.max(0,parseInt(e)-1):0,pageSize:a}}),[W,G]=(0,r.useState)(null!==(t=o.get("search"))&&void 0!==t?t:""),J=i.split("/")[1],X=function(e,t){let[a,l]=(0,r.useState)({}),n=(0,r.useRef)(null);return(0,r.useEffect)(()=>{if(e)return(async()=>{let t=await em(e);if(!t){console.error("[SignalR Execution] Failed to create connection");return}ep(t,l,void 0),await eu(t),n.current=t})(),()=>{n.current&&n.current.stop().catch(e=>{console.debug("[SignalR Execution] Error during connection stop:",null==e?void 0:e.message)})}},[e,void 0]),a}(J);(0,r.useEffect)(()=>{console.debug("[Executions] Component mounted - SignalR error handling delegated to connection hooks")},[]);let Q=(0,r.useCallback)(()=>{let e={$top:B.pageSize,$skip:B.pageIndex*B.pageSize,$count:!0};if(O.length>0&&(e.$orderby=O.map(e=>"".concat(e.id," ").concat(e.desc?"desc":"asc")).join(",")),M.length>0){let t=M.filter(e=>"string"!=typeof e.value||""!==e.value).map(e=>{let t=e.id,a=e.value;return"string"==typeof a?"agent"===t&&a||"packageName"===t&&a?"contains(tolower(botAgentName), '".concat(a.toLowerCase(),"')"):"state"===t&&a?"state eq '".concat(a,"'"):"contains(tolower(".concat(t,"), '").concat(a.toLowerCase(),"')"):Array.isArray(a)?a.map(e=>"".concat(t," eq '").concat(e,"'")).join(" or "):""}).filter(Boolean);t.length>0&&(e.$filter=t.join(" and "))}let t="";switch(m){case"inprogress":t="status eq 'Running' or status eq 'Pending'";break;case"sheduled":t="status eq 'Scheduled'";break;case"historical":t="status eq 'Completed' or status eq 'Failed' or status eq 'Cancelled'"}return t&&(e.$filter=e.$filter?"(".concat(e.$filter,") and (").concat(t,")"):t),e},[B,O,M,m])(),Y=(0,r.useMemo)(()=>$.DC.executionsWithOData(Q),[Q]),{data:Z,error:es,isLoading:er,mutate:ei}=(0,L.Ay)(Y,()=>(0,C.bF)(Q),{dedupingInterval:0,revalidateOnFocus:!1,revalidateIfStale:!1,keepPreviousData:!1}),{data:eo}=(0,L.Ay)((null==Z?void 0:null===(e=Z.value)||void 0===e?void 0:e.length)===0||es?$.DC.executions():null,C.vH),ec=er||es&&!eo,ed=(0,r.useCallback)(e=>{let t=X[e.id],a=(null==t?void 0:t.status)||e.status,l=e=>(0,I.Ej)(e,{fallback:""}),n=l(e.startTime),s=l(e.endTime);return console.log("Execution transformation:",{id:e.id,startTime:e.startTime,endTime:e.endTime,formattedStartTime:n,formattedEndTime:s}),{id:e.id,Version:e.packageVersion||"",Agent:e.botAgentName||"",State:a,"Start Time":n,"End Time":s,Source:"Manual",Command:"execute",Schedules:"Once","Task Id":e.id,"Created Date":(0,I.Ej)(e.startTime,{dateStyle:"medium",timeStyle:void 0,fallback:""}),"Created By":"Current User",name:e.packageName||"",type:"execution",value:e.packageVersion||"",createdBy:"Current User",label:e.botAgentName||"",status:a,agent:e.botAgentName||"",state:a,startTime:n,endTime:s,source:"Manual",command:"execute",schedules:"Once",taskId:e.id,createdDate:(0,I.Ej)(e.startTime,{dateStyle:"medium",timeStyle:void 0,fallback:""}),packageName:e.packageName||"",hasLogs:e.hasLogs||!1}},[X]),eg=(0,r.useMemo)(()=>V({onDeleted:()=>ei()}),[ei]),ex=(0,r.useMemo)(()=>A({onDeleted:()=>ei()}),[ei]),eh=(0,r.useMemo)(()=>{if(eo&&(!(null==Z?void 0:Z.value)||0===Z.value.length)){var e;console.log("Using fallback data with pagination:",B);let t=eo.map(e=>ed(e));t=t.filter(e=>"inprogress"===m?"Running"===e.state||"Pending"===e.state:"sheduled"===m?"Scheduled"===e.state:"historical"!==m||"Completed"===e.state||"Failed"===e.state||"Cancelled"===e.state),W&&(t=t.filter(e=>e.agent&&e.agent.toLowerCase().includes(W.toLowerCase())));let a=null===(e=M.find(e=>"state"===e.id))||void 0===e?void 0:e.value;a&&(t=t.filter(e=>e.state===a));let l=t.length,n=B.pageIndex*B.pageSize,s=n+B.pageSize;console.log("Slicing fallback data from ".concat(n," to ").concat(s," out of ").concat(t.length," items"));let r=t.slice(n,s);return console.log("Returning ".concat(r.length," items from fallback data")),f!==l&&setTimeout(()=>{b(l),y.current=l},0),r}return(null==Z?void 0:Z.value)?(console.log("Returning ".concat(Z.value.length," items from OData response")),Z.value.map(e=>ed(e))):(console.log("No data available from OData or fallback"),[])},[Z,eo,ed,m,W,M,B,f]);(0,r.useEffect)(()=>{if(eo&&(!(null==Z?void 0:Z.value)||0===Z.value.length)){var e;let t=eo.map(e=>ed(e));t=t.filter(e=>"inprogress"===m?"Running"===e.state||"Pending"===e.state:"sheduled"===m?"Scheduled"===e.state:"historical"!==m||"Completed"===e.state||"Failed"===e.state||"Cancelled"===e.state),W&&(t=t.filter(e=>e.agent&&e.agent.toLowerCase().includes(W.toLowerCase())));let a=null===(e=M.find(e=>"state"===e.id))||void 0===e?void 0:e.value;a&&(t=t.filter(e=>e.state===a)),f!==t.length&&(console.log("Updating total count from fallback data:",t.length),b(t.length),y.current=t.length)}},[eo,Z,ed,m,W,M,f]);let ef=(0,r.useMemo)(()=>eh,[eh]);(0,r.useEffect)(()=>{if(Z){if(console.log("OData response received:",Z),"number"==typeof Z["@odata.count"]){b(Z["@odata.count"]),y.current=Z["@odata.count"],P(!0);return}if(Array.isArray(Z.value)){let e=B.pageIndex*B.pageSize+Z.value.length;e>y.current&&(b(e),y.current=e),Z.value.length===B.pageSize&&0===B.pageIndex&&(b(e+1),y.current=e+1),P(!1)}}else if(eo&&eo.length>0){console.log("Using fallback data count:",eo.length);let e=eo.filter(e=>"inprogress"===m?"Running"===e.status||"Pending"===e.status:"sheduled"===m?"Scheduled"===e.status:"historical"!==m||"Completed"===e.status||"Failed"===e.status||"Cancelled"===e.status).length;console.log("Filtered fallback count:",e),b(e),y.current=e,P(!0)}},[Z,eo,m,B.pageIndex,B.pageSize]),(0,r.useEffect)(()=>{if((null==Z?void 0:Z.value)&&0===Z.value.length&&y.current>0&&B.pageIndex>0){let e=Math.max(1,Math.ceil(y.current/B.pageSize));B.pageIndex>=e&&(H(e=>({...e,pageIndex:0})),c(i,{page:"1"}))}},[Z,B.pageIndex,B.pageSize,y,c,i]),(0,r.useEffect)(()=>{if(T.current){T.current=!1;let e=o.get("page"),t=o.get("size");e&&t||c(i,{page:null!=e?e:"1",size:null!=t?t:"10"})}},[o,c,i]),(0,r.useEffect)(()=>{let e=o.get("page"),t=o.get("size");if(e&&t){let a=Math.max(0,parseInt(e)-1),l=parseInt(t);(a!==B.pageIndex||l!==B.pageSize)&&(console.log("URL changed: page=".concat(e,", size=").concat(t,". Updating pagination state.")),H({pageIndex:a,pageSize:l}))}},[o,B.pageIndex,B.pageSize]);let eb=e=>e+1,ey=(e,t)=>Math.max(1,Math.ceil(e/t)),eN=(0,r.useMemo)(()=>{let e=ey(f,B.pageSize),t=ef.length===B.pageSize&&f<=B.pageSize*(B.pageIndex+1),a=eb(B.pageIndex);return t?Math.max(a,e,B.pageIndex+2):Math.max(a,e)},[B.pageSize,B.pageIndex,ef.length,f]),ew=(0,r.useMemo)(()=>!z&&ef.length===B.pageSize,[z,ef.length,B.pageSize]),eC=(0,ev.N4)({data:ef,columns:"inprogress"===m?eg:"sheduled"===m?E:ex,state:{sorting:O,columnVisibility:v,rowSelection:h,columnFilters:M,pagination:B},enableRowSelection:!0,onRowSelectionChange:p,onSortingChange:e=>{let t="function"==typeof e?e(O):e;U(t),t.length>0?c(i,{sort:t[0].id,order:t[0].desc?"desc":"asc",page:"1"}):c(i,{sort:null,order:null,page:"1"}),ei()},onColumnFiltersChange:q,onColumnVisibilityChange:j,onPaginationChange:e=>{console.log("Pagination change triggered");let t="function"==typeof e?e(B):e;console.log("Current pagination:",B,"New pagination:",t),H(t),c(i,{page:(t.pageIndex+1).toString(),size:t.pageSize.toString()}),console.log("Forcing data reload for pagination change"),ei()},getCoreRowModel:(0,ej.HT)(),getFilteredRowModel:(0,ej.hM)(),getPaginationRowModel:(0,ej.kW)(),getSortedRowModel:(0,ej.h5)(),getFacetedRowModel:(0,ej.kQ)(),getFacetedUniqueValues:(0,ej.oS)(),manualPagination:!0,pageCount:eN,manualSorting:!0,manualFiltering:!0,getRowId:e=>e.id}),eS=(0,r.useCallback)(e=>{G(e),S(!0),D.current&&clearTimeout(D.current),D.current=setTimeout(()=>{let t="sheduled"===m?"packageName":"agent",a=eC.getColumn(t);a&&(a.setFilterValue(e),c(i,{search:e||null,page:"1"}),ei()),S(!1)},500)},[eC,c,i,m,ei]),ek=(0,r.useCallback)(e=>{let t=eC.getColumn("state");if(t){let a="all"===e?"":e;t.setFilterValue(a),c(i,{status:a||null,page:"1"})}},[eC,c,i]);(0,r.useEffect)(()=>()=>{D.current&&clearTimeout(D.current)},[]),(0,r.useEffect)(()=>{es&&(console.error("Failed to load executions:",es),eo||d({title:"Error",description:"Failed to load executions. Please try again.",variant:"destructive"}))},[es,eo,d]),(0,r.useEffect)(()=>{let e=["Completed","Failed","Cancelled"];if(Object.values(X).some(t=>e.includes(t.status))){let e=setTimeout(()=>{ei()},1e3);return()=>clearTimeout(e)}},[X,ei]);let eA=(0,r.useCallback)(e=>{e&&ei(t=>{if(!t)return t;if("value"in t&&Array.isArray(t.value)){let a={id:e.id,packageName:e.packageName,botAgentName:e.botAgentName,status:"Pending",startTime:new Date().toISOString(),endTime:void 0,packageVersion:void 0,errorMessage:void 0,logOutput:void 0,botAgentId:"",packageId:""};return{...t,value:[a,...t.value],"@odata.count":(t["@odata.count"]||0)+1}}return t},!1),setTimeout(()=>{ei()},2e3)},[ei]),eF=e=>{let t=i.startsWith("/admin")?"/admin/executions/".concat(e.id):"/".concat(J,"/executions/").concat(e.id);a.push(t)},eI=e=>{x(e),H(e=>({...e,pageIndex:0})),c(i,{page:"1"}),ei()};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"hidden h-full flex-1 flex-col space-y-8 md:flex",children:[(0,l.jsx)("div",{className:"mb-4 border-b w-full",children:(0,l.jsxs)("nav",{className:"flex space-x-8","aria-label":"Tabs",children:[(0,l.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"inprogress"===m,type:"button",onClick:()=>eI("inprogress"),children:"In Progress"}),(0,l.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"sheduled"===m,type:"button",onClick:()=>eI("sheduled"),children:"Scheduled"}),(0,l.jsx)("button",{className:"px-3 py-2 font-medium text-sm border-b-2 border-transparent hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary","data-active":"historical"===m,type:"button",onClick:()=>eI("historical"),children:"Historical"})]})}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Executions"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[f>0&&(0,l.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,l.jsxs)("span",{children:["Total: ",f," execution",1!==f?"s":""]})}),"inprogress"===m&&(0,l.jsxs)(s.$,{onClick:()=>{g(!0)},className:"flex items-center justify-center",children:[(0,l.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Create Execution"]})]})]}),es&&!eo&&(0,l.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,l.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load executions. Please try again."}),(0,l.jsx)(s.$,{variant:"outline",className:"mt-2",onClick:()=>ei(),children:"Retry"})]}),"inprogress"===m&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(et,{table:eC,statuses:[{value:"Running",label:"Running"},{value:"Pending",label:"Pending"}],onSearch:eS,onStatusChange:ek,searchValue:W,isFiltering:ec,isPending:N}),(0,l.jsx)(R.b,{data:ef,columns:eg,onRowClick:eF,table:eC,isLoading:ec,totalCount:f})]}),"sheduled"===m&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(ea,{table:eC,statuses:[{value:"Scheduled",label:"Scheduled"}],onSearch:eS,onStatusChange:ek,searchValue:W,isFiltering:ec,isPending:N}),(0,l.jsx)(R.b,{data:ef,columns:E,onRowClick:eF,table:eC,isLoading:ec,totalCount:f})]}),"historical"===m&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(ee,{table:eC,statuses:[{value:"Completed",label:"Completed"},{value:"Failed",label:"Failed"},{value:"Cancelled",label:"Cancelled"}],onSearch:eS,onStatusChange:ek,searchValue:W,isFiltering:ec,isPending:N}),(0,l.jsx)(R.b,{data:ef,columns:ex,onRowClick:eF,table:eC,isLoading:ec,totalCount:f})]}),(0,l.jsx)(en.d,{currentPage:B.pageIndex+1,pageSize:B.pageSize,totalCount:f,totalPages:eN,isLoading:ec,isChangingPageSize:k,isUnknownTotalCount:ew,onPageChange:e=>{console.log("Page change requested to page ".concat(e)),H(t=>({...t,pageIndex:e-1})),c(i,{page:e.toString()}),console.log("Reloading data after page change"),setTimeout(()=>{ei()},0)},onPageSizeChange:e=>{console.log("Page size change requested to ".concat(e)),F(!0);let t=Math.floor(B.pageIndex*B.pageSize/e);H({pageSize:e,pageIndex:t}),c(i,{size:e.toString(),page:(t+1).toString()}),console.log("Reloading data after page size change"),setTimeout(()=>{ei()},0)}})]}),(0,l.jsx)(K,{isOpen:u,onClose:()=>g(!1),onSuccess:eA})]})}D.z.object({id:D.z.string(),name:D.z.string(),type:D.z.string(),value:D.z.string(),createdBy:D.z.string(),label:D.z.string(),status:D.z.string(),Version:D.z.string().optional(),Agent:D.z.string().optional(),State:D.z.string().optional(),"Start Time":D.z.string().optional(),"End Time":D.z.string().optional(),Source:D.z.string().optional(),Command:D.z.string().optional(),Schedules:D.z.string().optional(),"Task Id":D.z.string().optional(),"Created Date":D.z.string().optional(),"Created By":D.z.string().optional(),agent:D.z.string().optional(),state:D.z.string().optional(),startTime:D.z.string().optional(),endTime:D.z.string().optional(),source:D.z.string().optional(),command:D.z.string().optional(),schedules:D.z.string().optional(),taskId:D.z.string().optional(),createdDate:D.z.string().optional(),packageName:D.z.string().optional(),hasLogs:D.z.boolean().optional()})},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},49171:(e,t,a)=>{"use strict";a.d(t,{RT:()=>s,bF:()=>i,iX:()=>c,vH:()=>r});var l=a(7283);let n=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},s=async e=>{let t=n();return await l.F.post("".concat(t,"/api/executions/trigger"),e)},r=async()=>{let e=n();try{return await l.F.get("".concat(e,"/api/executions"))}catch(e){return console.error("Error fetching all executions:",e),[]}},i=async e=>{let t=n(),a={...e};(void 0===a.$top||a.$top<=0)&&(a.$top=10);let s=new Date().getTime(),r=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[a,l]=e;null!=l&&"$count"!==a&&t.append(a,String(l))}),t.toString()}(a),i="".concat(t,"/odata/Executions");r?i+="?".concat(r,"&_t=").concat(s):i+="?_t=".concat(s),console.log("Fetching executions with endpoint: ".concat(i)),console.log("Page: ".concat(a.$skip?a.$skip/a.$top+1:1,", Size: ").concat(a.$top));try{let e=await l.F.get(i),t=function(e){return e&&"object"==typeof e?{value:Array.isArray(e.value)?e.value:[],"@odata.count":"number"==typeof e["@odata.count"]?e["@odata.count"]:void 0,"@odata.nextLink":"string"==typeof e["@odata.nextLink"]?e["@odata.nextLink"]:void 0}:{value:[]}}(e);return a.$top&&t.value.length>a.$top&&(console.warn("OData returned ".concat(t.value.length," items but only ").concat(a.$top," were requested. Trimming results.")),t.value=t.value.slice(0,a.$top)),console.log("Received ".concat(t.value.length," executions from OData")),t}catch(e){return console.error("Error fetching executions with OData:",e),{value:[]}}},o=async e=>{let t=n();return await l.F.get("".concat(t,"/api/executions/").concat(e,"/logs/download"))},c=async(e,t)=>{try{let{downloadUrl:a}=await o(e),l=t||"execution_".concat(e,"_logs.log");try{let e=await fetch(a,{method:"GET",headers:{Accept:"application/octet-stream"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.blob(),n=window.URL.createObjectURL(t),s=document.createElement("a");s.href=n,s.download=l,s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(n)}catch(t){console.warn("Fetch method failed, falling back to direct link:",t);let e=document.createElement("a");e.href=a,e.download=l,e.target="_blank",e.rel="noopener noreferrer",e.style.display="none",document.body.appendChild(e),e.click(),document.body.removeChild(e)}}catch(e){throw console.error("Error downloading execution logs:",e),e}}},54861:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},66932:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},69074:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var l=a(95155);a(12115);var n=a(24265),s=a(36928);function r(e){let{className:t,...a}=e;return(0,l.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85690:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,4953,6341,2178,8852,5699,5594,8523,9483,3085,1941,106,3701,7426,7295,4727,5224,3003,8441,1684,7358],()=>t(14839)),_N_E=e.O()}]);