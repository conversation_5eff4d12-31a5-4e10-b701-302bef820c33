(()=>{var e={};e.id=481,e.ids=[481],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12098:(e,r,t)=>{"use strict";t.d(r,{$:()=>a});let a={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63553:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var a={};t.r(a),t.d(a,{GET:()=>u});var s=t(96559),i=t(48088),o=t(37719),n=t(32190),p=t(12098);async function u(e){try{let r=e.nextUrl.searchParams.get("token");if(!r)return n.NextResponse.redirect(`${new URL(p.$.paths.auth.emailVerified,e.nextUrl.origin)}?success=false&reason=missing-token`);let t=`${p.$.api.baseUrl}/api/email/verify?token=${r}`,a=(await fetch(t,{method:"GET",headers:{"Content-Type":"application/json"},redirect:"manual"})).headers.get("location");if(a)return n.NextResponse.redirect(a);return n.NextResponse.redirect(`${new URL(p.$.paths.auth.emailVerified,e.nextUrl.origin)}?success=true`)}catch(r){return console.error("Error during email verification:",r),n.NextResponse.redirect(`${new URL(p.$.paths.auth.emailVerified,e.nextUrl.origin)}?success=false&reason=server-error`)}}let l=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/email/verify/route",pathname:"/email/verify",filename:"route",bundlePath:"app/email/verify/route"},resolvedPagePath:"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\email\\verify\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:f}=l;function h(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580],()=>t(63553));module.exports=a})();