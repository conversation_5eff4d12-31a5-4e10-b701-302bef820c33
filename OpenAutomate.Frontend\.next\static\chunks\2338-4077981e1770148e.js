"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2338],{12187:(e,t,r)=>{function a(e){if(!e)return"An unexpected error occurred";if("string"==typeof e)return e;if("object"==typeof e&&null!==e&&"status"in e&&"message"in e&&"number"==typeof e.status&&"string"==typeof e.message)return 0===e.status?e.message||"Network error. Please check your connection.":function(e){var t;if(e.details){let t=function(e){try{var t,r;let a=JSON.parse(e);return null!==(r=null!==(t=a.error)&&void 0!==t?t:a.message)&&void 0!==r?r:null}catch(e){return null}}(e.details);return t||e.details}return null!==(t=e.message)&&void 0!==t?t:"An error occurred"}(e);if(e instanceof Error)return e.message.includes("Failed to fetch")||e.message.includes("NetworkError")?"Network error. Please check your connection.":e.message;let t=function(e){if(null!==e&&"object"==typeof e&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response){let t=e.response.data;if("object"==typeof t&&null!==t){if("message"in t&&"string"==typeof t.message)return t.message;if("error"in t&&"string"==typeof t.error)return t.error}if("string"==typeof t)return t}return null}(e);if(null!==t)return t;if("object"==typeof e&&null!==e){let t=void 0!==e.error&&null!==e.error&&"string"==typeof e.error?e.error:void 0!==e.message&&null!==e.message&&"string"==typeof e.message?e.message:null;if(null!==t)return t}return"An unexpected error occurred"}function s(e){return{title:"Error",description:a(e),variant:function(e){if("object"==typeof e&&null!==e&&"status"in e)return e.status<400?"default":"destructive";if("string"==typeof e){let t=e.toLowerCase();if(t.includes("warning")||t.includes("info"))return"default"}return"destructive"}(e)}}r.d(t,{PE:()=>a,m4:()=>s})},32771:(e,t,r)=>{r.d(t,{AW:()=>c,Cb:()=>d,QQ:()=>l,ae:()=>n,jm:()=>u,oy:()=>i,s9:()=>o});var a=r(7283);let s=()=>{{let e=window.location.pathname.split("/");if(e.length>1&&e[1])return e[1]}return"default"},n=async e=>{let t=s();return await a.F.get("".concat(t,"/api/packages/").concat(e))},o=async()=>{let e=s();return await a.F.get("".concat(e,"/api/packages"))},i=async e=>{let t=s(),r=new FormData;return r.append("file",e.file),e.name&&r.append("name",e.name),e.description&&r.append("description",e.description),e.version&&r.append("version",e.version),await a.F.post("".concat(t,"/api/packages/upload"),r)},l=async(e,t)=>{let r=s();return await a.F.get("".concat(r,"/api/packages/").concat(e,"/versions/").concat(t,"/download"))},u=async e=>{let t=s();await a.F.delete("".concat(t,"/api/packages/").concat(e))},c=async(e,t)=>{let r=s();await a.F.delete("".concat(r,"/api/packages/").concat(e,"/versions/").concat(t))},d=async e=>{let t=s(),r=function(e){let t=new URLSearchParams;return t.append("$count","true"),e&&Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&"$count"!==r&&t.append(r,String(a))}),t.toString()}(e),n="".concat(t,"/odata/AutomationPackages");r&&(n+="?".concat(r)),console.log("OData query endpoint:",n);try{let e=await a.F.get(n);return console.log("Raw OData response:",e),function(e){if("object"==typeof e&&null!==e&&"value"in e&&Array.isArray(e.value)&&Array.isArray(e.value))return console.log("Received ".concat(e.value.length," packages from OData. Total count: ").concat(e["@odata.count"])),{value:e.value,"@odata.count":void 0!==e["@odata.count"]?e["@odata.count"]:e.value.length};if(Array.isArray(e))return console.log("Converting array response to OData format"),{value:e,"@odata.count":e.length};if("object"==typeof e&&null!==e){let t=Object.keys(e).filter(t=>Array.isArray(e[t]));if(t.length>0){let r=t[0];console.log('Found array property "'.concat(r,'" in response'));let a=e[r],s=e["@odata.count"];return{value:a,"@odata.count":"number"==typeof s?s:a.length}}}return console.warn("Could not parse OData response, returning empty result"),{value:[]}}(e)}catch(e){return console.error("Error fetching packages with OData:",e),{value:[]}}}},55365:(e,t,r)=>{r.d(t,{Fc:()=>l,TN:()=>c,XL:()=>u});var a=r(95155),s=r(12115),n=r(74466),o=r(36928);let i=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-600/50 text-yellow-600 dark:border-yellow-600/30 [&>svg]:text-yellow-600",success:"border-green-600/50 text-green-600 dark:border-green-600/30 [&>svg]:text-green-600"}},defaultVariants:{variant:"default"}}),l=s.forwardRef((e,t)=>{let{className:r,variant:s,...n}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,o.cn)(i({variant:s}),r),...n})});l.displayName="Alert";let u=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return s||console.warn("AlertTitle must have content for accessibility"),(0,a.jsx)("h5",{ref:t,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",r),...n,children:s})});u.displayName="AlertTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",r),...s})});c.displayName="AlertDescription"},70449:(e,t,r)=>{r.d(t,{DC:()=>i,EJ:()=>o,IS:()=>l,bb:()=>n});var a=r(7283),s=r(15874);function n(){return{fetcher:e=>(0,a.fetchApi)(e),onError:function(e){(0,s.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let o={fetcher:e=>(0,a.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!((null==e?void 0:e.status)>=400)||!((null==e?void 0:e.status)<500),dedupingInterval:2e3,focusThrottleInterval:5e3},i={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},l=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},88262:(e,t,r)=>{r.d(t,{$:()=>s,d:()=>n});var a=r(12115);let s=(0,a.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function n(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}}}]);