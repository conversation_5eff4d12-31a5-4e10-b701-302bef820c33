[{"Key": "OA-793", "Summary": "(Test) AutomationPackageServiceTests", "Issue Type": "Subtask", "Description": "", "Status": "To Do"}, {"Key": "OA-792", "Summary": "Edit Schedule", "Issue Type": "Story", "Description": "", "Status": "To Do"}, {"Key": "OA-791", "Summary": "Delete Schedule", "Issue Type": "Story", "Description": "", "Status": "To Do"}, {"Key": "OA-790", "Summary": "[BUG] Fix DateTime conversion error in schedule", "Issue Type": "Bug", "Description": "", "Status": "In Progress"}, {"Key": "OA-789", "Summary": "(Upskilling) Quartz.NET", "Issue Type": "Task", "Description": "", "Status": "In Progress"}, {"Key": "OA-788", "Summary": "Unit test - OrganizationUnitUserService", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-787", "Summary": "Unit test - OrganizationUnitUserController", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-786", "Summary": "Unit test - EmailTestControllerTest", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-785", "Summary": "Unit test - CustomControllerBaseTest", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-784", "Summary": "Unit test - AuthorControllerTest", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-783", "Summary": "Test - OrganizationUnitUser", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-781", "Summary": "Test - Unit test Custom + Email", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-780", "Summary": "(Test) - Unit test Authorization", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-779", "Summary": "(Bug) Deploy TypeScript lint issue", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Frontend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Status": "In Progress"}, {"Key": "OA-778", "Summary": "(FE) Create Schedule Integration", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-777", "Summary": "(Docs) Unification 15-Week plan, jira, weekly report", "Issue Type": "Task", "Description": "", "Status": "Review"}, {"Key": "OA-776", "Summary": "(Docs) Process Design Document for Bot demo", "Issue Type": "Task", "Description": "", "Status": "To Do"}, {"Key": "OA-775", "Summary": "(Docs) Business Workflow", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-774", "Summary": "(Test) AutomationPackageControllerTests", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'codeBlock', 'content': [{'type': 'text', 'text': '#### AutomationPackageController Tests\\n**File**: `OpenAutomate.API.Tests/ControllerTests/AutomationPackageControllerTests.cs`\\n**Priority**: 🔴 HIGH (Core automation functionality)\\n\\n**Test Cases**:\\n```csharp\\n// Package CRUD\\n- CreatePackage_WithValidData_ReturnsCreated\\n- CreatePackage_WithInvalidData_ReturnsBadRequest\\n- CreatePackage_WithoutAuth_ReturnsUnauthorized\\n- GetPackage_WithValidId_ReturnsPackage\\n- GetPackage_WithInvalidId_ReturnsNotFound\\n- GetPackage_CrossTenant_ReturnsNotFound\\n- GetAllPackages_FiltersByTenant_ReturnsCorrectPackages\\n- UpdatePackage_WithValidData_ReturnsUpdated\\n- UpdatePackage_CrossTenant_ReturnsNotFound\\n- DeletePackage_WithValidId_ReturnsNoContent\\n- DeletePackage_WithActiveExecutions_ReturnsBadRequest\\n\\n// Version Management\\n- UploadVersion_WithValidFile_ReturnsCreated\\n- UploadVersion_WithInvalidFile_ReturnsBadRequest\\n- UploadVersion_WithDuplicateVersion_ReturnsConflict\\n- DownloadVersion_WithValidVersion_ReturnsFile\\n- DownloadVersion_WithInvalidVersion_ReturnsNotFound\\n- DownloadVersion_CrossTenant_ReturnsNotFound\\n- DeleteVersion_WithValidVersion_ReturnsNoContent\\n- DeleteVersion_WithActiveExecutions_ReturnsBadRequest\\n\\n// Error Handling\\n- CreatePackage_WhenServiceThrows_ReturnsInternalServerError\\n- UploadVersion_WhenStorageUnavailable_ReturnsServiceUnavailable\\n```'}]}]}", "Status": "To Do"}, {"Key": "OA-773", "Summary": "(Test) Unit Test - AutomationPackage", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaGroup', 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'e0ba5c70-ef34-43aa-8251-1a281a1deac8', 'collection': ''}}]}, {'type': 'paragraph'}]}", "Status": "In Progress"}, {"Key": "OA-772", "Summary": "(Test) CronExpressionServiceTests", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'codeBlock', 'content': [{'type': 'text', 'text': '**File**: `OpenAutomate.Core.Tests/ServiceTests/CronExpressionServiceTests.cs`\\n**Priority**: 🔴 HIGH (Critical for scheduling validation)\\n\\n**Test Cases**:\\n```csharp\\n// Cron Validation\\n- ValidateCronExpression_WithValidExpression_ReturnsTrue\\n- ValidateCronExpression_WithInvalidExpression_ReturnsFalse\\n- ValidateCronExpression_WithNullExpression_ReturnsFalse\\n- ValidateCronExpression_WithEmptyExpression_ReturnsFalse\\n\\n// Next Execution Calculation\\n- GetNextExecutionTime_WithValidCron_ReturnsCorrectTime\\n- GetNextExecutionTime_WithInvalidCron_ThrowsException\\n- GetNextExecutionTimes_WithValidCron_ReturnsMultipleTimes\\n- GetNextExecutionTimes_WithCount_ReturnsCorrectCount\\n\\n// Cron Description\\n- GetCronDescription_WithValidExpression_ReturnsDescription\\n- GetCronDescription_WithInvalidExpression_ThrowsException\\n```'}]}]}", "Status": "Review"}, {"Key": "OA-771", "Summary": "(Test) ScheduleServiceTests", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'codeBlock', 'content': [{'type': 'text', 'text': '**File**: `OpenAutomate.Core.Tests/ServiceTests/ScheduleServiceTests.cs`\\n**Priority**: 🔴 HIGH (Quartz.NET integration critical for capstone)\\n\\n**Test Cases**:\\n```csharp\\n// Schedule CRUD\\n- CreateScheduleAsync_WithValidData_ReturnsSchedule\\n- CreateScheduleAsync_WithInvalidCron_ThrowsException\\n- GetScheduleByIdAsync_WithValidId_ReturnsSchedule\\n- GetScheduleByIdAsync_CrossTenant_ReturnsNull\\n- UpdateScheduleAsync_WithValidData_UpdatesSuccessfully\\n- UpdateScheduleAsync_CrossTenant_ThrowsException\\n- DeleteScheduleAsync_WithValidId_DeletesSuccessfully\\n- DeleteScheduleAsync_CrossTenant_ThrowsException\\n\\n// Schedule Management\\n- GetSchedulesAsync_FiltersByTenant_ReturnsCorrectSchedules\\n- EnableScheduleAsync_WithValidId_EnablesSuccessfully\\n- DisableScheduleAsync_WithValidId_DisablesSuccessfully\\n- GetNextExecutionTimeAsync_WithValidCron_ReturnsCorrectTime\\n- GetNextExecutionTimeAsync_WithInvalidCron_ThrowsException\\n\\n// Quartz Integration\\n- ScheduleJobAsync_WithValidSchedule_CreatesQuartzJob\\n- UnscheduleJobAsync_WithValidSchedule_RemovesQuartzJob\\n- UpdateQuartzJobAsync_WithModifiedSchedule_UpdatesJob\\n```'}]}]}", "Status": "Review"}, {"Key": "OA-770", "Summary": "(Bug) Edit Calendar Component Shadcn", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Adjust the text color to ensure proper visibility and contrast in dark mode.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Replace the Vietnamese word '}, {'type': 'text', 'text': '\"Tháng\"', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' with its English equivalent (e.g., \"Month\") throughout the relevant components or UI sections.'}]}]}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '67240c73-b3cf-4b1a-af75-7ae8cfd08e8c', 'alt': 'image-20250619-182652.png', 'collection': '', 'height': 350, 'width': 259}}]}]}", "Status": "Done"}, {"Key": "OA-769", "Summary": "(FE) Modify theme color dark mode", "Issue Type": "Task", "Description": "", "Status": "To Do"}, {"Key": "OA-768", "Summary": "(Test) Unit test SchedulesController", "Issue Type": "Subtask", "Description": "", "Status": "Review"}, {"Key": "OA-766", "Summary": "(Python) Bot template work with execution id", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-765", "Summary": "(Test) Unit Test - Scheduling", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaGroup', 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'e7760093-b59a-48fa-a5cb-65784a7be8dc', 'collection': ''}}]}, {'type': 'paragraph'}]}", "Status": "Review"}, {"Key": "OA-764", "Summary": "Create a basic schedule", "Issue Type": "Story", "Description": "", "Status": "Review"}, {"Key": "OA-763", "Summary": "(FE) (Bug) User management API returns 403 for OWNER role", "Issue Type": "Subtask", "Description": "", "Status": "In Progress"}, {"Key": "OA-762", "Summary": "View Logs for each execution completed", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-761", "Summary": "(Bug) User management API returns 403 for OWNER role", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Status": "Done"}, {"Key": "OA-760", "Summary": "(Bug) Roles page layout inconsistent", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Chỉnh layout cho thống nhất vs các tab khác nhé'}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '54e25560-b267-4c3b-a5d4-1ab8aacbc815', 'alt': 'image-20250614-121132.png', 'collection': '', 'height': 517, 'width': 1527}}]}]}", "Status": "Done"}, {"Key": "OA-759", "Summary": "(BE) Bug Form Login issue", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-758", "Summary": "(FE) Bug Form Login issue", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-756", "Summary": "Execution Status Update", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-755", "Summary": "Resend email verification", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'User Story'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Title:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' Resend Email Verification'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': \" registered user who hasn't verified their email address  \"}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'I want', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' to be able to request a new verification email  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'So that', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': \" I can complete my account verification if I didn't receive the original email or if it expired\"}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Description'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'This feature allows authenticated users to resend their email verification if they haven\\'t received the original verification email or if the verification token has expired. The user can trigger this action through a \"Resend Email Verification\" button/link in the application.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a user is logged in and their email is not yet verified  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they click \"Resend Email Verification\"  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a new verification email should be sent to their registered email address'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a user is logged in and their email is already verified  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they attempt to resend verification  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they should receive a message \"Email is already verified\"'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' an unauthenticated user  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they attempt to access the resend verification endpoint  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they should receive an \"Unauthorized\" response'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Implementation'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The backend implementation is already complete in EmailVerificationController.cs:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Endpoint:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'POST /api/email/resend', 'marks': [{'type': 'code'}]}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Authentication:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' Required (Bearer token)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Response Codes:', 'marks': [{'type': 'strong'}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '200 OK: Verification email sent successfully'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '400 Bad Request: Email already verified or failed to send'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '401 Unauthorized: User not authenticated'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '404 Not Found: User not found'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '500 Internal Server Error: Server error'}]}]}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Security & Validation'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Validates user authentication token from JWT claims'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Checks if user exists in the system'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Prevents sending verification emails to already verified accounts'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive logging for audit purposes'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Proper error handling for all edge cases'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Frontend Requirements'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add \"Resend Email Verification\" button/link in user dashboard or profile'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Show success/error messages based on API response'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Disable button after successful send to prevent spam'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Display appropriate UI for already verified users'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'API Response Examples'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Success Response (200):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"Verification email sent\"\\n}'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Already Verified (400):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"Email is already verified\"\\n}'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User Not Found (404):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"User not found\"\\n}'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Definition of Done'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Backend API endpoint implemented and tested ✓ (Already complete)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Frontend UI component for resend button implemented'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Unit tests written and passing'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Integration tests covering happy path and error scenarios'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User acceptance testing completed'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Code reviewed and approved'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Documentation updated'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Story Points: 2'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Priority: Medium'}]}]}", "Status": "Done"}, {"Key": "OA-754", "Summary": "(Docs) Draw Usecase Diagram", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-753", "Summary": "(Bug) Form Login issue", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'orderedList', 'attrs': {'order': 1}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Đặt lại vị trí Forgot password xuống dưới password.'}]}]}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': '      UX khó chịu thực sự'}]}, {'type': 'mediaSingle', 'attrs': {'width': 50, 'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '57e539ae-7ad8-4362-9b2d-17932cce96ee', 'alt': 'image-20250613-133550.png', 'collection': '', 'height': 526, 'width': 837}}]}, {'type': 'orderedList', 'attrs': {'order': 2}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Chỉnh lại message, sai mật khẩu thì báo sai mật khẩu (có thể chỉnh cả BE và FE)'}]}]}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'bb5ae47b-328f-4007-9682-da0aa72910c0', 'alt': 'image-20250613-133936.png', 'collection': '', 'height': 873, 'width': 909}}]}]}", "Status": "Done"}, {"Key": "OA-752", "Summary": "(FE)Resend email verification", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Frontend Requirements'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add \"Resend Email Verification\" button/link in user dashboard or profile'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Show success/error messages based on API response'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Disable button after successful send to prevent spam'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Display appropriate UI for already verified users'}]}]}]}]}", "Status": "In Progress"}, {"Key": "OA-751", "Summary": "(Bug) Roles are not isolated", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Status": "Done"}, {"Key": "OA-750", "Summary": "(Bug) Schedule - Calendar UI issue", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Frontend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': '![Image]('}, {'type': 'text', 'text': 'https://github.com/user-attachments/assets/05008f7f-bfdf-424f-833c-dbaae91376dc', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/user-attachments/assets/05008f7f-bfdf-424f-833c-dbaae91376dc'}}]}, {'type': 'text', 'text': ')'}]}]}", "Status": "Done"}, {"Key": "OA-749", "Summary": "BotAgentConnectionController", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Connection management'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Machine key validation'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-748", "Summary": "BotAgentAssetController", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Asset access authorization'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Machine key authentication'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-747", "Summary": "EmailVerificationController", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Email verification flow'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Token validation'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-746", "Summary": "OrganizationUnitInvitationController test", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Invitation CRUD'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Invitation acceptance flow'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-745", "Summary": "(Test) Execution Unit Test - API", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-744", "Summary": "IOrganizationUnitService test", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Multi-tenant isolation tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Invitation management tests'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-743", "Summary": "IExecutionService test", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-742", "Summary": "IAssetService test", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAssetValueForBotAgentAsync (security testing)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Asset encryption/decryption tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Authorization validation tests'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-741", "Summary": "IAutomationPackageService", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-740", "Summary": "Domain Entity tests", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'OrganizationUnitInvitation', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' entity tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'PasswordResetToken', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' entity tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Entity validation tests'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Entity relationship constraint tests'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-739", "Summary": "(Test) Execution Unit Test - Core", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Core project'}]}]}", "Status": "Done"}, {"Key": "OA-738", "Summary": "(BE)Resend email verification", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'User Story'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Title:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' Resend Email Verification'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': \" registered user who hasn't verified their email address  \"}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'I want', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' to be able to request a new verification email  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'So that', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': \" I can complete my account verification if I didn't receive the original email or if it expired\"}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Description'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'This feature allows authenticated users to resend their email verification if they haven\\'t received the original verification email or if the verification token has expired. The user can trigger this action through a \"Resend Email Verification\" button/link in the application.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a user is logged in and their email is not yet verified  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they click \"Resend Email Verification\"  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a new verification email should be sent to their registered email address'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' a user is logged in and their email is already verified  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they attempt to resend verification  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they should receive a message \"Email is already verified\"'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Given', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' an unauthenticated user  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'When', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they attempt to access the resend verification endpoint  '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Then', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' they should receive an \"Unauthorized\" response'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Implementation'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The backend implementation is already complete in EmailVerificationController.cs:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Endpoint:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'POST /api/email/resend', 'marks': [{'type': 'code'}]}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Authentication:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' Required (Bearer token)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Response Codes:', 'marks': [{'type': 'strong'}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '200 OK: Verification email sent successfully'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '400 Bad Request: Email already verified or failed to send'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '401 Unauthorized: User not authenticated'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '404 Not Found: User not found'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '500 Internal Server Error: Server error'}]}]}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Security & Validation'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Validates user authentication token from JWT claims'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Checks if user exists in the system'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Prevents sending verification emails to already verified accounts'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive logging for audit purposes'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Proper error handling for all edge cases'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Frontend Requirements'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add \"Resend Email Verification\" button/link in user dashboard or profile'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Show success/error messages based on API response'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Disable button after successful send to prevent spam'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Display appropriate UI for already verified users'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'API Response Examples'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Success Response (200):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"Verification email sent\"\\n}'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Already Verified (400):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"Email is already verified\"\\n}'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User Not Found (404):', 'marks': [{'type': 'strong'}]}]}, {'type': 'codeBlock', 'attrs': {'language': 'json'}, 'content': [{'type': 'text', 'text': '{\\n  \"message\": \"User not found\"\\n}'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Definition of Done'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Backend API endpoint implemented and tested ✓ (Already complete)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Frontend UI component for resend button implemented'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Unit tests written and passing'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Integration tests covering happy path and error scenarios'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User acceptance testing completed'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Code reviewed and approved'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Documentation updated'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Story Points: 2'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Priority: Medium'}]}]}", "Status": "In Progress"}, {"Key": "OA-735", "Summary": "(Agent) Update Agent Installer to include executor and new UI", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-734", "Summary": "(Epic) DevOps and Infrastructure", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Development operations including CI/CD pipelines, deployment automation, logging enhancement, and infrastructure management. Covers build automation, deployment processes, and monitoring setup.'}]}]}", "Status": "To Do"}, {"Key": "OA-733", "Summary": "(Epic) System Administration", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'System-level administration features for platform management including user management, organization unit oversight, and system monitoring. Covers admin dashboard, user management, and system-wide controls.'}]}]}", "Status": "To Do"}, {"Key": "OA-732", "Summary": "(Epic) User Management and Authentication", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Complete user management system including registration, password reset, profile management, and authentication flows. Covers user CRUD operations, password management, and authentication UI.'}]}]}", "Status": "To Do"}, {"Key": "OA-731", "Summary": "(Epic) Public Website and Guest Experience", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Public-facing website with guest UI, landing pages, and marketing content. Covers splash screens, hero sections, about us, contact pages, and multi-language support.'}]}]}", "Status": "To Do"}, {"Key": "OA-730", "Summary": "(Epic) Testing and Quality Assurance", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive testing infrastructure including unit tests, integration tests, and CI/CD pipeline setup. Covers controller tests, repository tests, infrastructure layer testing, and automated testing workflows.'}]}]}", "Status": "To Do"}, {"Key": "OA-729", "Summary": "(Epic) Automation Execution Engine", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Python-based automation execution system including script execution, template management, and execution tracking. Covers executor application, Python templates, and execution monitoring.'}]}]}", "Status": "To Do"}, {"Key": "OA-728", "Summary": "(Epic) Frontend Core Infrastructure", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Core frontend application infrastructure including UI layout, components, authentication integration, and real-time updates. Covers React application setup, component library, routing, and SignalR client integration.'}]}]}", "Status": "To Do"}, {"Key": "OA-727", "Summary": "(Epic) Asset Management System", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive automation asset management including creation, editing, deletion, retrieval, and agent assignment. Covers asset CRUD operations, encryption, OData APIs, and agent-asset relationships.'}]}]}", "Status": "To Do"}, {"Key": "OA-726", "Summary": "(Epic) Bot Agent Management System", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Complete bot agent lifecycle management including registration, connection, status monitoring, and real-time communication. Covers agent creation, Windows service implementation, WPF UI, SignalR infrastructure, and agent status tracking.'}]}]}", "Status": "To Do"}, {"Key": "OA-725", "Summary": "(Epic) Core Platform Foundation", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Foundational platform components including authentication, multi-tenant architecture, project setup, and core infrastructure. Covers user login, organization unit creation, multi-tenant data architecture, project setup, and base architecture patterns.'}]}]}", "Status": "To Do"}, {"Key": "OA-722", "Summary": "(FE) OU owner - assign role to user in OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-721", "Summary": "(BE) OU owner - assign role to user in OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-720", "Summary": "(Docs) Create \"How to use Agent\" for docsites", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-718", "Summary": "(Docs) Create \"About Agent\" for docsite", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-716", "Summary": "(Agent) Change the UI of Agent App", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-715", "Summary": "Deployment with vercel (publicSite)", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Deployment URL: '}, {'type': 'inlineCard', 'attrs': {'url': 'https://open-automate-public-site.vercel.app/'}}, {'type': 'text', 'text': '  '}]}]}", "Status": "Done"}, {"Key": "OA-714", "Summary": "Executions View History", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-711", "Summary": "Could not view the executions page", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Frontend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': '![Image]('}, {'type': 'text', 'text': 'https://github.com/user-attachments/assets/d96ce5e9-3e59-4bc6-8726-7eb5ababc5dd', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/user-attachments/assets/d96ce5e9-3e59-4bc6-8726-7eb5ababc5dd'}}]}, {'type': 'text', 'text': ')'}]}]}", "Status": "Done"}, {"Key": "OA-698", "Summary": "(FE) Implement loading states and error boundaries", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add comprehensive loading states, error boundaries, and graceful error handling throughout the application.'}]}]}", "Status": "To Do"}, {"Key": "OA-661", "Summary": "(FE) Create platform-wide analytics dashboard", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a system admin, I want platform-wide analytics so I can understand usage patterns and make informed decisions.'}]}]}", "Status": "In Progress"}, {"Key": "OA-650", "Summary": "(FE) Create system admin dashboard", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a system admin, I want a comprehensive dashboard so I can monitor and manage the entire platform effectively.'}]}]}", "Status": "To Do"}, {"Key": "OA-642", "Summary": "(FE) Create personal profile management", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a user, I want to manage my personal profile so I can keep my information up to date and customize my experience.'}]}]}", "Status": "Done"}, {"Key": "OA-608", "Summary": "(BE) Quartz.NET Integration for Scheduling", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add notification actions (mark as read, delete) and bulk operations for managing multiple notifications at once.'}]}]}", "Status": "Review"}, {"Key": "OA-607", "Summary": "(BE) API - Delete Schedule", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement filtering by notification type, date, read status, and search functionality for finding specific notifications.'}]}]}", "Status": "To Do"}, {"Key": "OA-606", "Summary": "(BE) API - Edit Schedule", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create notification list component with pagination, infinite scroll, and efficient rendering for large notification counts.'}]}]}", "Status": "To Do"}, {"Key": "OA-605", "Summary": "(BE) API - Create Schedule", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a user, I want a notification center so I can see all my notifications in one place and manage them easily.'}]}]}", "Status": "Done"}, {"Key": "OA-604", "Summary": "(BE) Schedule Management API - View Schedules", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build API endpoints for retrieving, marking as read, and managing in-app notifications.'}]}]}", "Status": "Review"}, {"Key": "OA-603", "Summary": "(FE) Delete Schedule", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement notification persistence with automatic cleanup policies and read/unread status management.'}]}]}", "Status": "To Do"}, {"Key": "OA-602", "Summary": "(FE) Edit Schedule", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create real-time notification delivery using SignalR with connection management and offline message queuing.'}]}]}", "Status": "To Do"}, {"Key": "OA-601", "Summary": "(FE) Create Schedule", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design data models for in-app notifications including content, metadata, and user interaction tracking.'}]}]}", "Status": "Done"}, {"Key": "OA-572", "Summary": "(FE) Create PDF report export", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build PDF export functionality for formatted reports with charts, tables, and professional layout.'}]}]}", "Status": "To Do"}, {"Key": "OA-523", "Summary": "(FE) Add view logs functionality", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement log export capabilities in various formats (TXT, CSV, JSON) for external analysis and reporting.'}]}]}", "Status": "Done"}, {"Key": "OA-518", "Summary": "(FE) Implement SignalR client connection for Execution Status", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Setup SignalR client connection with automatic reconnection and connection state management.'}]}]}", "Status": "Done"}, {"Key": "OA-516", "Summary": "(Agent) Agent Executor Integration and ExecutionId Flow", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create automated log retention policies and cleanup processes to manage storage and maintain performance.'}]}]}", "Status": "Done"}, {"Key": "OA-515", "Summary": "(BE) Database Schema and S3 Log Storage Service Implementation", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement API endpoints for retrieving logs with search, filtering, and pagination capabilities.'}]}]}", "Status": "Done"}, {"Key": "OA-514", "Summary": "(BE) Logs Upload Execution Controller API Endpoints Implementation", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create structured logging service with different log levels, categorization, and efficient storage mechanisms.'}]}]}", "Status": "Done"}, {"Key": "OA-511", "Summary": "(BE) Implement execution status broadcasting", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create service to broadcast execution status changes, progress updates, and completion events to connected clients.'}]}]}", "Status": "Done"}, {"Key": "OA-509", "Summary": "(BE) Add execution history API endpoints", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement API endpoints to retrieve execution history with filtering, pagination, and detailed execution information.'}]}]}", "Status": "Done"}, {"Key": "OA-508", "Summary": "(Agent) Executor and Service to update status to SignalRHub", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create service to track and record execution events, status changes, and completion results for scheduled jobs.'}]}]}", "Status": "Done"}, {"Key": "OA-507", "Summary": "(BE) Design execution history data model", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create data models for storing schedule execution history including status, timestamps, and execution metadata.'}]}]}", "Status": "Done"}, {"Key": "OA-506", "Summary": "(FE) Create schedule edit modal", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a user, I want to edit existing schedules so I can modify timing or parameters without recreating them.'}]}]}", "Status": "Done"}, {"Key": "OA-504", "Summary": "(FE) Implement schedule filtering and search", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add filtering capabilities by status, schedule type, automation, and search functionality for schedule management.'}]}]}", "Status": "Done"}, {"Key": "OA-503", "Summary": "(FE) Create schedule list view component", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a user, I want to view all my scheduled executions in a list so I can see their status and next run times.'}]}]}", "Status": "Done"}, {"Key": "OA-502", "Summary": "(FE) Add recurring pattern selection UI", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement UI components for selecting recurring patterns (daily, weekly, monthly) with visual preview of next execution times.'}]}]}", "Status": "Done"}, {"Key": "OA-500", "Summary": "(FE) Implement date/time picker components", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create reusable date/time picker components with timezone support and validation for schedule creation.'}]}]}", "Status": "Done"}, {"Key": "OA-498", "Summary": "(BE) Create recurring schedule management API", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement CRUD operations for recurring schedules including update, delete, and pause/resume functionality.'}]}]}", "Status": "Done"}, {"Key": "OA-497", "Summary": "(BE) Add cron expression support", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement cron expression parsing, validation, and scheduling with support for complex time patterns.'}]}]}", "Status": "Done"}, {"Key": "OA-496", "Summary": "(BE) Implement recurring schedule patterns", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create support for daily, weekly, monthly recurring patterns with configurable intervals and end dates.'}]}]}", "Status": "Done"}, {"Key": "OA-495", "Summary": "(BE) Add schedule validation and conflict detection", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement validation logic for schedule parameters and detect scheduling conflicts or resource constraints.'}]}]}", "Status": "Done"}, {"Key": "OA-494", "Summary": "(BE) Implement one-time schedule API", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create API endpoints for scheduling one-time automation executions with specific date/time and execution parameters.'}]}]}", "Status": "Done"}, {"Key": "OA-493", "Summary": "(BE) Create schedule model and DTOs", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design and implement data models for scheduled executions including schedule configuration, execution parameters, and validation rules.'}]}]}", "Status": "Done"}, {"Key": "OA-492", "Summary": "(BE) Implement basic job management service", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create service layer for basic job operations including start, stop, pause, resume scheduler and job lifecycle management.'}]}]}", "Status": "Done"}, {"Key": "OA-491", "Summary": "(BE) Create job store configuration", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configure Quartz job store with database persistence, connection pooling, and clustering support for production environment.'}]}]}", "Status": "Done"}, {"Key": "OA-490", "Summary": "(BE) Setup Quartz.NET infrastructure", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Install and configure Quartz.NET package, setup basic scheduler configuration, and create database schema for job persistence.'}]}]}", "Status": "Done"}, {"Key": "OA-489", "Summary": "(Epic) Platform Polish and Production Readiness", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Final platform polish including performance optimization, security hardening, accessibility, documentation, and testing.'}]}]}", "Status": "To Do"}, {"Key": "OA-488", "Summary": "(Epic) Python Bot Development and Marketplace", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Complete Python bot development ecosystem with templates, documentation, tutorials, and marketplace foundation.'}]}]}", "Status": "To Do"}, {"Key": "OA-487", "Summary": "(Epic) Account Management and System Administration", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Personal account management and comprehensive system administration features for platform management.'}]}]}", "Status": "To Do"}, {"Key": "OA-486", "Summary": "(Epic) Payment and Credit System", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Complete payment system with LemonSqueezy integration, credit management, trial system, and billing dashboard.'}]}]}", "Status": "To Do"}, {"Key": "OA-485", "Summary": "(Epic) Multi-Channel Notification System", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive notification system with email, in-app, and push notifications with user preference management.'}]}]}", "Status": "To Do"}, {"Key": "OA-483", "Summary": "(Epic) OU Analytics and Reporting", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive analytics system with OU-specific metrics, automated reporting, and data export capabilities.'}]}]}", "Status": "To Do"}, {"Key": "OA-482", "Summary": "(Epic) OU-Based Authorization System", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive permission system with fine-grained access control, role management, and hierarchical OU permissions.'}]}]}", "Status": "To Do"}, {"Key": "OA-481", "Summary": "(Epic) Execution Monitoring and Logging", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Comprehensive execution monitoring system with real-time tracking, detailed logging, performance metrics, and user notifications.'}]}]}", "Status": "To Do"}, {"Key": "OA-480", "Summary": "(Epic) Job Scheduling System", "Issue Type": "Epic", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement comprehensive job scheduling system using Quartz.NET with UI for creating, managing, and monitoring scheduled automation executions.'}]}]}", "Status": "To Do"}, {"Key": "OA-335", "Summary": "(FE) List of invitation in OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-334", "Summary": "(BE) API list of invitation in OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-333", "Summary": "(FE) remove user from OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-332", "Summary": "(BE) API remove user from OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-331", "Summary": "Remove init loading effect (publicSite)", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-330", "Summary": "basic SEO public site", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-329", "Summary": "Public Site Animation (FE)", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-328", "Summary": "Pagination & filter for Executions", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-327", "Summary": "Pagination & filter for Packages", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-325", "Summary": "(FE) OU owner - view list of users in OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-324", "Summary": "(BE) API OU owner - view list of users in OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-323", "Summary": "Execution run from Orchestrator (BE API)", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-322", "Summary": "Execution run from Orchestrator (API Integration)", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-321", "Summary": "Execution run from orchestrator", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-319", "Summary": "Draw the Product Overview Diagram", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Report 3 - mục 1. Product Overview'}, {'type': 'hardBreak'}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'b7306687-c495-4766-ba81-5bea3557d4aa', 'alt': 'image-20250530-163218.png', 'collection': '', 'height': 1219, 'width': 2887}}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'dùng '}, {'type': 'text', 'text': 'draw.io', 'marks': [{'type': 'link', 'attrs': {'href': 'http://draw.io'}}]}, {'type': 'text', 'text': ' hoặc phần mềm gì đó cloud để có thể chỉnh sửa chung được.'}]}]}", "Status": "Done"}, {"Key": "OA-318", "Summary": "(Docs) Make docsite be better UI", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'OpenAutomate Documentation – OpenAutomate Documentation', 'marks': [{'type': 'link', 'attrs': {'href': 'https://docs.openautomate.me/'}}]}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'make it be better'}, {'type': 'hardBreak'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'chủ yếu là trang chủ. '}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'sửa bố cục và logo lại cho đẹp'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'để ý  tránh làm mất chức năng search'}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'b67502ed-ad06-45f1-bf33-0b1a94e38302', 'alt': 'image-20250529-153420.png', 'collection': '', 'height': 1504, 'width': 3792}}]}]}", "Status": "Done"}, {"Key": "OA-317", "Summary": "deploy DocsSite to vercel", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-316", "Summary": "OU owner - view list of roles in OU", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-315", "Summary": "OU owner - view list of invitation in OU", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-314", "Summary": "OU owner - view list of users in OU", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-313", "Summary": "OU owner - assign resource to role in OU", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-312", "Summary": "OU owner - create role in OU", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-311", "Summary": "OU owner - assign role to user in OU", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-310", "Summary": "Remove user from OU", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-309", "Summary": "(BE) Implement Payment Integration (LemonSqueezy)", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-308", "Summary": "(BE) Implement System Admin for OU/User Management", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-307", "Summary": "(BE) Implement Personal Account Management", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-305", "Summary": "(BE) Implement Notifications", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-304", "Summary": "(BE) Implement AI Chatbot for Documentation", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-303", "Summary": "(BE) Implement Analytics per OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-302", "Summary": "(BE) Implement OU-based Authorization", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-301", "Summary": "(BE) Implement Execution Logs", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-300", "Summary": "(BE) Implement Execution Monitoring", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-299", "Summary": "(BE) API Agent Edit", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-298", "Summary": "(BE) API for Agent Delete", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-297", "Summary": "bug could not create/get ou list", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Status": "Done"}, {"Key": "OA-296", "Summary": "Refactor code (BE) - reset password", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'bcee701e-9248-417f-b71e-df798bdd2002', 'alt': 'image-20250522-184651.png', 'collection': '', 'height': 529, 'width': 1417}}]}]}", "Status": "Done"}, {"Key": "OA-295", "Summary": "OU Owner - invite user to OU", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-294", "Summary": "Agent View Detail", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-293", "Summary": "Agent <PERSON><PERSON>", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-292", "Summary": "Agent Edit ", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-291", "Summary": "Change Deploy server", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-290", "Summary": "<PERSON><PERSON><PERSON> For Backend", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '25e51c58-2948-4605-8075-d55ac850d791', 'alt': 'image-20250521-162316.png', 'collection': '', 'height': 1373, 'width': 1886}}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Backend nó hay lỗi tenant context, để thuận tiện cho việc debug thì a triển khai cái này.'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'khi code frontend thì ae nhớ note lại cái endpoint nào gây lỗi để tạo issue fix nhé.'}, {'type': 'hardBreak'}, {'type': 'mention', 'attrs': {'id': '712020:97c81d0a-1388-491a-878b-9389a0050aed', 'text': '@Kh<PERSON>h <PERSON>ng', 'accessLevel': '', 'localId': '0281ca5f-ce0d-417d-894a-aeac4ed40ef1'}}, {'type': 'text', 'text': ' '}, {'type': 'mention', 'attrs': {'id': '712020:105a75f3-cc45-4601-baaf-56b6d40c9716', 'text': '@vunlhde160548', 'accessLevel': '', 'localId': '07b943a6-6fee-4117-a476-8e8ae760d1f5'}}, {'type': 'text', 'text': ' '}, {'type': 'mention', 'attrs': {'id': '712020:a5d63575-296a-4a0d-9060-d4b8b9751863', 'text': '@Phan Hoang Nhat (K17 DN)', 'accessLevel': '', 'localId': '7b54039f-4f8a-4714-9cba-c33956046a81'}}, {'type': 'text', 'text': ' '}, {'type': 'mention', 'attrs': {'id': '712020:9bb57e18-5571-4637-987d-93bb3bca0428', 'text': '@Huỳnh Đức Chính', 'accessLevel': '', 'localId': '59ef68f9-7ea2-45fa-a424-f16ec7c24626'}}, {'type': 'text', 'text': ' '}]}]}", "Status": "Done"}, {"Key": "OA-289", "Summary": "Support Multi Language for PublicSite", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-288", "Summary": "Modify Asset UI", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '41672d81-fec0-42e1-8772-b28b5330abc0', 'alt': 'image-20250521-115928.png', 'collection': '', 'height': 916, 'width': 1644}}]}, {'type': 'orderedList', 'attrs': {'order': 1}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Dùng màu đang ko hợp lí, tốt hơn hết là không dùng màu mè gì cả, nhìn sẽ gây hiểu nhầm.'}]}]}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '03c1eead-9004-426c-b74c-e1ef04fe0b45', 'alt': 'image-20250521-121125.png', 'collection': '', 'height': 802, 'width': 1201}}]}, {'type': 'orderedList', 'attrs': {'order': 2}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Agent ko bắt buộc, có quyền rỗng, cho phép user tạo asset trước rồi sau này add Agent sau'}]}]}]}, {'type': 'paragraph', 'content': [{'type': 'mention', 'attrs': {'id': '712020:97c81d0a-1388-491a-878b-9389a0050aed', 'text': '@Khánh Hưng', 'accessLevel': '', 'localId': '056c40a2-3d0a-4467-b8a1-21da01d59db5'}}, {'type': 'text', 'text': ' '}]}]}", "Status": "Done"}, {"Key": "OA-286", "Summary": "System Admin Dashboard page (FE) ", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '<PERSON>à<PERSON> một trang tách biệt hoàn toàn so với phần tenant.'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': '<PERSON><PERSON> các mục để admin quản lý: quản lý user, quản lý organization unit, quản lý Agents,…'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Lên mạng tham khảo các Design và bắt đầu thiết kế nhé.'}]}, {'type': 'paragraph'}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Note: C<PERSON>i này là dành cho System Admin (quản lý tất cả), nó khác với tenant owner.'}]}]}", "Status": "Done"}, {"Key": "OA-284", "Summary": "(FE) UI to reset password", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-283", "Summary": "(BE) API to reset password", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-282", "Summary": "User - Reset password", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-281", "Summary": "UI forgot password", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-278", "Summary": "Setup Docs Site Project/Repo", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-276", "Summary": "sonarqube test coverage not calculated", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'fix the sonarqube pipeline '}]}]}", "Status": "Done"}, {"Key": "OA-274", "Summary": "SignalR client for frontend to update status real-time", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-273", "Summary": "Frontend reverse proxy connection request from agent to backend server", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-271", "Summary": "Asset Frontend Code Enhancement", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'tối ưu code frontend lại cho sạch đẹp'}]}]}", "Status": "Done"}, {"Key": "OA-270", "Summary": "AssetRepository Test Failed", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '53e8f953-61cd-4cf5-8dac-78b0791bfecd', 'alt': 'image-20250518-100420.png', 'collection': '', 'height': 1064, 'width': 1840}}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'fix để chạy đc pass hết'}]}]}", "Status": "Done"}, {"Key": "OA-268", "Summary": "Standadize agent status", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.BotAgent'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Status to be: Available, Busy, Disconnected'}]}]}", "Status": "Done"}, {"Key": "OA-266", "Summary": "Product Introduction (Report 1)", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-265", "Summary": "i18n_Support multiple languages ", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-264", "Summary": "Ui Administration", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-263", "Summary": "Ui Automation", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-262", "Summary": "Bug url agent details and asset details", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Frontend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'the url is wrong tenant'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': '![Image]('}, {'type': 'text', 'text': 'https://github.com/user-attachments/assets/dff2bbfd-91f6-485e-9577-042a892b616c', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/user-attachments/assets/dff2bbfd-91f6-485e-9577-042a892b616c'}}]}, {'type': 'text', 'text': ')'}]}]}", "Status": "Done"}, {"Key": "OA-261", "Summary": "fix domain-test-asset", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-260", "Summary": "API OData for Asset", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-259", "Summary": "Asset for agent to retrieve", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-257", "Summary": "Contact Us Page ", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-256", "Summary": "About Us Page", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-255", "Summary": "Public site Guest UI", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-254", "Summary": "UI Agent", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-253", "Summary": "Set up OData, and API OData for BotAgent", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-251", "Summary": "fix Asset encrypt", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-240", "Summary": "(BE) API for Agent Status Updates", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Repository:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' AssetRepository.cs ('}, {'type': 'text', 'text': 'OpenAutomate.Infrastructure/Repositories/AssetRepository.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' AssetRepositoryTests.cs ('}, {'type': 'text', 'text': 'OpenAutomate.Infrastructure.Tests/Repositories/AssetRepositoryTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddAsset'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAssetById'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'UpdateAsset'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'DeleteAsset'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Any custom queries'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public method is tested for both valid and invalid input.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use in-memory database for integration tests.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use AAA pattern.'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-239", "Summary": "(BE) API for Agent Registration", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Repository:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' UserRepository.cs ('}, {'type': 'text', 'text': 'OpenAutomate.Infrastructure/Repositories/UserRepository.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' UserRepositoryTests.cs ('}, {'type': 'text', 'text': 'OpenAutomate.Infrastructure.Tests/Repositories/UserRepositoryTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddUser'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetUserById'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'UpdateUser'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'DeleteUser'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Any custom queries'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public method has at least one test for success and one for error path.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use in-memory database for integration tests.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use AAA pattern.'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-238", "Summary": "(BE) API for Asset Deletion", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'OpenAutomate.Docs/testing/InfrastructureLayerTestingGuide.md at main · OpenAutomateOrg/OpenAutomate.Docs', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/OpenAutomateOrg/OpenAutomate.Docs/blob/main/testing/InfrastructureLayerTestingGuide.md'}}]}]}]}", "Status": "Done"}, {"Key": "OA-237", "Summary": "(BE) API for Asset Update", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' AdminController.cs ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/AdminController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' AdminControllerTests.cs ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/ControllerTests/AdminControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '(List each public action in AdminController, e.g.)'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAdmins'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddAdmin'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'RemoveAdmin'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'UpdateAdmin'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '(…add all public actions found in the controller)'}]}]}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public action has at least one test for success and one for error path (e.g., unauthorized, validation error, not found, exception).'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All dependencies (e.g., IAdminService, ILogger<AdminController>) are mocked.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Tests follow the Arrange-Act-Assert (AAA) pattern.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test method names follow '}, {'type': 'text', 'text': '{ActionName}_{Condition}_{ExpectedResult}', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' convention.'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-236", "Summary": "(BE) API for Asset Retrieval", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Mục tiêu: '}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'User tạo được 1 Asset Với Name, Key - Value.'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Và gán được Agent vào cho Asset đó.'}]}]}]}, {'type': 'paragraph'}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Business là: Sau này những Agent đ<PERSON> được gán thì mới có quyền get Asset về.'}]}]}", "Status": "Done"}, {"Key": "OA-235", "Summary": "(BE) API for Asset Creation", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-233", "Summary": "(Docs) Draw Screen Flow & Function Description (Software Requirement Specification)", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-232", "Summary": "(BE) OrganizationUnitController", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'OrganizationUnitController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/OrganizationUnitController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'OrganizationUnitControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/OrganizationUnitControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetOrganizationUnits', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddOrganizationUnit', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All public actions tested for both success and failure.'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-231", "Summary": "(Testing) AssetController", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'AssetController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/AssetController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'AssetControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/AssetControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAssets', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AddAsset', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All public actions tested for main scenarios.'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-230", "Summary": "(Testing) BotAgentController", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'BotAgentController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/BotAgentController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'BotAgentControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/BotAgentControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'RegisterAgent', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'GetAgentStatus', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Success and error paths are covered.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All dependencies are mocked.'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-229", "Summary": "(Testing) AuthenController", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'AuthenController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/AuthenController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'AuthenControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/AuthenControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Login', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Logout', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'RefreshToken', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public action is tested for both valid and invalid input.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Mock all dependencies.'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-228", "Summary": "(Testing) UserController", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Controller:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'UserController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API/Controllers/UserController.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Test File:', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'UserControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ' ('}, {'type': 'text', 'text': 'OpenAutomate.API.Tests/Controllers/UserControllerTests.cs', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ')'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Actions to Test:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'UpdateUserInfo', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'ChangePassword', 'marks': [{'type': 'code'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Acceptance Criteria:', 'marks': [{'type': 'strong'}]}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Each public action has at least one test for success and one for error path.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'All dependencies ('}, {'type': 'text', 'text': 'IUserService', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ', '}, {'type': 'text', 'text': 'ILogger<UserController>', 'marks': [{'type': 'code'}]}, {'type': 'text', 'text': ') are mocked.'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use AAA pattern.'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-227", "Summary": "(Testing) Write Unit Tests for Controllers", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-226", "Summary": "(FE) Update Features Section Animation", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-223", "Summary": "(BE) API to Create OU", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-221", "Summary": "(Agent) API Client to Communicate with Service", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-220", "Summary": "(Agent) Create Basic UI for Agent", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-219", "Summary": "(Testing) Unit Test API for Admin to Edit User Info", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-218", "Summary": "(Testing) Unit Test API for Admin View All Users", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-217", "Summary": "(FE) Update Hero Section Animation", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-216", "Summary": "(FE) Create Splash Screen with Animation AnimeJS", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-215", "Summary": "(FE) Update Animation for Guest UI", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-214", "Summary": "(Agent) Implement SignalR Client for Agent Service", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-213", "Summary": "(BE) Implement BotAgent Hub for Backend", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-212", "Summary": "(BE) API for Admin to Delete User", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-211", "Summary": "(BE) API for Admin to Edit User Info", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-210", "Summary": "(BE) API for Admin to View All Users", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-208", "Summary": "(BE) APIs for Admin to Manage Users", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-205", "Summary": "(FE) Update UI", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-204", "Summary": "(BE) Tenant Routing", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-203", "Summary": "(DevOps) CD for Frontend", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-202", "Summary": "(DevOps) CD for Backend", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-201", "Summary": "(DevOps) CD Pipeline", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-199", "Summary": "fix test coverage", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from OpenAutomate.Backend'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Status": "Done"}, {"Key": "OA-196", "Summary": "Core Layer Test - Function in TokenService", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-194", "Summary": "(BE) API for Authentication", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-193", "Summary": "(FE) UI and Integration for Organization Unit General Management", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-192", "Summary": "(BE) API for Organization Unit General Managemenmt", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-191", "Summary": "(Task) Core Layer Test - Domain-Test", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'tạo children subtask tương tự, ghi rõ function nào mình test. '}, {'type': 'mention', 'attrs': {'id': '712020:105a75f3-cc45-4601-baaf-56b6d40c9716', 'text': '@vunlhde160548', 'accessLevel': '', 'localId': 'fbe14687-3a5c-477b-8cdb-fbd5ac66f296'}}, {'type': 'text', 'text': ' '}, {'type': 'mention', 'attrs': {'id': '712020:97c81d0a-1388-491a-878b-9389a0050aed', 'text': '@Khánh Hưng', 'accessLevel': '', 'localId': '279667d0-f43b-4359-9163-c8f28c9bd39d'}}, {'type': 'text', 'text': ' '}]}]}", "Status": "Done"}, {"Key": "OA-189", "Summary": "Unit test Core ServiceTests", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-188", "Summary": "API code returns a list of OUs after login", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-177", "Summary": "update revoke token", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Description:'}]}]}", "Status": "Done"}, {"Key": "OA-176", "Summary": "Map Register API with UI", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-172", "Summary": "Create modal UI", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-171", "Summary": "Setup pipeline SonarQube Scan for frontend develop branch", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-165", "Summary": "Possible null reference return", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Issue from '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'GitHub Issue URL: '}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Possible null reference return. · Issue #66 · OpenAutomateOrg/OpenAutomate.Backend', 'marks': [{'type': 'link', 'attrs': {'href': 'https://github.com/OpenAutomateOrg/OpenAutomate.Backend/issues/66'}}]}]}]}", "Status": "Done"}, {"Key": "OA-161", "Summary": "Non-nullable property must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Check sonar và fix bug'}]}]}", "Status": "Done"}, {"Key": "OA-160", "Summary": "Possible null reference return", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': 'e1b45b36-56a9-4e78-94ce-74c3d526e10c', 'alt': 'image-20250424-111333.png', 'collection': '', 'height': 428, 'width': 837}}]}]}", "Status": "Done"}, {"Key": "OA-159", "Summary": " async method lacks 'await' operators", "Issue Type": "Bug", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '78d1d413-80f5-4b7c-9d42-6d7ede7c1c72', 'alt': 'image-20250424-111215.png', 'collection': '', 'height': 414, 'width': 833}}]}]}", "Status": "Done"}, {"Key": "OA-147", "Summary": "Map Login API with UI", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-146", "Summary": "Create Protected Routes", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement route protection'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add tenant resolution logic'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create tenant context provider'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-145", "Summary": "Implement Auth Context Provider", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create Auth provider component'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement token storage strategy'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add authentication state management'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-144", "Summary": "Create Authentication UI Components", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design and implement login form'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create registration form'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add password reset functionality'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-143", "Summary": "Implement Tenant Management API", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create organization CRUD endpoints'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement tenant slug validation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add tenant administrator role management'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-142", "Summary": "Create Core Domain Entities", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement base entity with tenant ID'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create repository pattern with tenant context'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build unit of work pattern'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-141", "Summary": "Implement EF Core Global Query Filters", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up tenant-based filtering'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create tenant resolution middleware'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement tenant context service'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-140", "Summary": "Send Email Confirm User Registration", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-139", "Summary": "Send Email Welcome When Registration Successfully", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-138", "Summary": "Authority APIs for organization unit", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-137", "Summary": "Create Auth API Endpoints", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement registration endpoint'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement login endpoint'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement token refresh endpoint'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-136", "Summary": "Implement JWT Authentication", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create token generation service'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement refresh token mechanism'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configure HTTP-only cookies'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-135", "Summary": "Design Auth Domain Model", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create user and organization entities'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement role-based authorization model'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design database schema for auth'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-134", "Summary": "Setup pipeline SonarQube Code Scan for backend develop branch", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-133", "Summary": "Setup SonarQube", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-132", "Summary": "Jira + Github Issue Sync", "Issue Type": "Subtask", "Description": "", "Status": "Done"}, {"Key": "OA-131", "Summary": "Set up Email Service", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'AWS SES.'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'all information should be in config/ appsetting for dynamic changes, not hard code.'}]}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Cloud Email Sending Service - Amazon Simple Email Service - AWS', 'marks': [{'type': 'link', 'attrs': {'href': 'https://aws.amazon.com/ses/'}}]}]}]}", "Status": "Done"}, {"Key": "OA-130", "Summary": "(Agent) Initialize Bot Agent Repository", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up .NET Core Windows Service project'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up WPF client application project'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configure build pipeline'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-129", "Summary": "(FE) Initialize Frontend Repository", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up Next.js 14 with TypeScript and App Router'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configure linting and formatting'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Establish component structure'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-128", "Summary": "(BE) Initialize Backend Repository", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create '}, {'type': 'inlineCard', 'attrs': {'url': 'http://ASP.NET'}}, {'type': 'text', 'text': '  Core 8 project structure with clean architecture'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement multi-tenant architecture patterns'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up dependency injection'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-127", "Summary": "(Bug) Use Model Binding Instead of Raw Request Data", "Issue Type": "Bug", "Description": "", "Status": "Done"}, {"Key": "OA-118", "Summary": "(FE) Create Table UI", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Table UI and Detail Page'}]}]}", "Status": "Done"}, {"Key": "OA-117", "Summary": "(FE) Create Tabs UI", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-72", "Summary": "(FE) Create Dashboard UI", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build dashboard layout'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement placeholder widgets'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add basic responsive grid'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-70", "Summary": "(FE) Design Application Layout", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create main application shell'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement responsive navigation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design sidebar and header components'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-69", "Summary": "(BE) Create Agent Management API", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build CRUD operations for agents'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement agent filtering and search'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add agent status updates'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-68", "Summary": "(BE) Implement Agent Registration", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create agent registration endpoint'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add agent authentication'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement tenant association'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-67", "Summary": "(BE) Design Bot Agent Model", "Issue Type": "Subtask", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create bot agent entity'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design agent status tracking system'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Add heartbeat mechanism'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-60", "Summary": "(FE) Authentication Integration and UI", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create authentication UI and context providers for frontend.'}]}]}", "Status": "Done"}, {"Key": "OA-55", "Summary": "(Task) Project Setup and Base Architecture", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Set up project repositories, infrastructure, and establish core architecture patterns.'}]}]}", "Status": "Done"}, {"Key": "OA-53", "Summary": "(Story) Bot Agent Creation & Connection via Orchestrator", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As an organization unit member, I want to create and connect a new bot agent so that I can execute automation tasks on my infrastructure and manage it from the central platform.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': \"I can access a bot agent creation form from the organization's dashboard\"}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can provide a name and optional description for the bot agent'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Upon creation, the system generates a secure machine key for authentication'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I am presented with clear installation instructions and the machine key'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can download the bot agent installer package directly from the UI'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can run the installer on my target machine and provide the machine key during setup'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The bot agent automatically connects to the central platform using the provided key'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': \"I can see the bot agent's connection status in real-time on the dashboard\"}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The bot agent appears in the list of available agents with its status (online/offline)'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can manage which assets the bot agent has access to'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The bot agent maintains a secure connection with the platform through SignalR'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The bot agent regularly sends heartbeat signals to confirm its online status'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Notes:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement in BotAgentController with proper tenant isolation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use RandomNumberGenerator for cryptographically secure machine key generation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Store machine key with proper hashing for verification'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Create AssetBotAgent relationships for asset access control'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use SignalR hub for real-time status communication'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement heartbeat tracking in the database'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-52", "Summary": "(Story) Asset Creation via Orchestrator", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-35", "Summary": "(Task) Bugs & Enhancement in Sprint 1", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-34", "Summary": "(DevOps) CI Pipeline Setup", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-31", "Summary": "(FE) Core UI Layout & Components", "Issue Type": "Task", "Description": "", "Status": "Done"}, {"Key": "OA-30", "Summary": "(Story) Python-based Automation Template", "Issue Type": "Story", "Description": "", "Status": "Done"}, {"Key": "OA-29", "Summary": "(FE) Agent Screen UI", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph'}, {'type': 'paragraph', 'content': [{'type': 'mention', 'attrs': {'id': '712020:105a75f3-cc45-4601-baaf-56b6d40c9716', 'text': '@vunlhde160548', 'accessLevel': '', 'localId': 'bd47ea4b-197e-4ab8-bbe0-6ed999b4a49e'}}, {'type': 'text', 'text': '  xem code backend, tham khảo giao diện Agent  '}, {'type': 'text', 'text': 'http://open-bot.live/', 'marks': [{'type': 'link', 'attrs': {'href': 'http://open-bot.live/'}}]}, {'type': 'text', 'text': ' sau đó tích hợp API vào'}]}, {'type': 'mediaSingle', 'attrs': {'layout': 'align-start'}, 'content': [{'type': 'media', 'attrs': {'type': 'file', 'id': '415871c5-c657-4824-86ef-b00205b13dee', 'alt': 'image-20250512-154923.png', 'collection': '', 'height': 759, 'width': 751}}]}, {'type': 'paragraph'}, {'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Output là:'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'User tạo được 1 Agent.'}, {'type': 'hardBreak'}, {'type': 'text', 'text': 'Cái Agent Key, là lúc tạo xong nó mới popup lên và chỉ hiện 1 lần (như kiểu API key), sau khi đóng popup thì Agent Key ko hiện nữa, có thể hiện thị là: ******00c1'}]}]}", "Status": "Done"}, {"Key": "OA-25", "Summary": "(Story) Asset Retrieve Windows Application", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Integrate real-time updates into the frontend.'}]}]}", "Status": "Done"}, {"Key": "OA-24", "Summary": "(Story) Agent Executor Application", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Develop the Python script execution engine for the bot agent.'}]}]}", "Status": "Done"}, {"Key": "OA-23", "Summary": "(BE) SignalR Infrastructure", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': []}", "Status": "Done"}, {"Key": "OA-22", "Summary": "(Story) Bot Agent WPF UI for Configuration", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As a machine operator or automation administrator', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ', I want to be able to configure and monitor my OpenAutomate Bot Agent through a user-friendly interface, so that I can easily connect my machine to the OpenAutomate platform and manage its connection status without requiring technical expertise.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria'}]}, {'type': 'orderedList', 'attrs': {'order': 1}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to see my machine name automatically detected when I launch the application'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to configure required credentials (Server URL and Agent Key) through clear input fields'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to connect and disconnect from the platform with simple button clicks'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to see a clear visual indication of my current connection status'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want my configuration settings to be saved automatically between application restarts'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to set my logging level preference (Debug, Info, Warning, Error)'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to receive helpful error messages if connection issues occur'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-20", "Summary": "(Story) Agent Status Real-time Updates", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': []}", "Status": "Done"}, {"Key": "OA-19", "Summary": "(Story) Asset Delete", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Design and implement the package management UI in the frontend.'}]}]}", "Status": "Done"}, {"Key": "OA-18", "Summary": "(Story) Bot Agent Windows Service Connection", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As an automation administrator', 'marks': [{'type': 'strong'}]}, {'type': 'text', 'text': ', I want to install and manage a Windows service for the OpenAutomate Bot Agent, so that it runs automatically on system startup, maintains connection to the OpenAutomate platform, and executes automation tasks without requiring manual intervention or an open user interface.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria'}]}, {'type': 'orderedList', 'attrs': {'order': 1}, 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want the Bot Agent to run as a Windows service that starts automatically with the operating system'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want the service to establish connection to the OpenAutomate platform using saved configuration'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want the service to automatically reconnect if the connection is lost'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I want to be able to start, stop, and restart the service through standard Windows service controls'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Notes'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Windows Service implementation using .NET'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Configuration shared with UI application'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Robust error handling and reconnection logic'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Event logging with rotating file storage'}]}]}, {'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'SignalR for real-time communication with platform'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-17", "Summary": "(Story) Asset Edit", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Develop the automation package system for storing and managing automation scripts.'}]}]}", "Status": "Done"}, {"Key": "OA-16", "Summary": "(Story) OU Creation", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'As an authenticated user, I want to create a new organization unit so that I can establish a dedicated tenant space for my team or department with proper isolation of automation assets and processes.'}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Acceptance Criteria:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can access an organization unit creation form from the dashboard after logging in'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can specify a name for the organization unit which will automatically generate a URL slug'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The system validates the name and slug for uniqueness across the platform'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I receive clear error messages if validation fails'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The system automatically creates default authority roles (OWNER, MANAGER, DEVELOPER, USER) for the new organization unit'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I am automatically assigned the OWNER role for the newly created organization unit'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'I can optionally invite other users during the creation process'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Upon successful creation, the system provides a confirmation with the generated tenant URL'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'The new organization unit is immediately accessible via its tenant URL path'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Data created within this organization unit is properly isolated from other tenants'}]}]}]}, {'type': 'heading', 'attrs': {'level': 2}, 'content': [{'type': 'text', 'text': 'Technical Notes:'}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement in OrganizationUnitController with standard authentication check'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Use TenantQueryFilterService to ensure proper data isolation'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Generate slugs using normalized form of the organization name (lowercase, hyphens for spaces)'}]}]}]}, {'type': 'bulletList', 'content': [{'type': 'listItem', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Ensure proper database indexes for OrganizationUnitId fields'}]}]}]}]}", "Status": "Done"}, {"Key": "OA-15", "Summary": "(BE) Bot Agent Model & Connection", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement bot agent registration and management system.'}]}]}", "Status": "Done"}, {"Key": "OA-14", "Summary": "(BE) Multi-Tenant Data Architecture Implementation", "Issue Type": "Task", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Build the core multi-tenant data architecture to support organization isolation.'}]}]}", "Status": "Done"}, {"Key": "OA-13", "Summary": "(Story) User Login to Platform", "Issue Type": "Story", "Description": "{'type': 'doc', 'version': 1, 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': 'Implement secure authentication system supporting JWT tokens and refresh tokens.'}]}]}", "Status": "Done"}]