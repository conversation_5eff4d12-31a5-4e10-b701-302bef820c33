(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6164],{7283:(e,t,r)=>{"use strict";r.d(t,{F:()=>w,fetchApi:()=>p});var n=r(48133),a=r(67938);let i=a.$.api.defaultHeaders,o=!1,s=[],c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;s.forEach(r=>{e?r.reject(e):r.resolve(t)}),s=[]},l=async e=>{let t={message:e.statusText,status:e.status};try{let r=await e.json();r.message?(t.message=r.message,t.details=r.details||r.message):r.error?(t.message=r.error,t.details=r.error):t.details=JSON.stringify(r)}catch(r){t.details=e.statusText}return t},d=()=>{window.dispatchEvent(new Event("auth:token-expired"))},u=e=>{if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw{message:"Network error. Please check your connection.",status:0,details:e.message};if(e instanceof Error)throw{message:e.message||"Network error occurred",status:0,details:e.stack||e.message};throw{message:"An unexpected error occurred",status:0,details:String(e)}},g=async e=>{if(204===e.status)return{};let t=e.headers.get("content-type"),r=e.headers.get("content-length");if(!t||-1===t.indexOf("application/json")||"0"===r)return{};let n=await e.text();return n?JSON.parse(n):{}},h=async()=>{if(o)return new Promise((e,t)=>{s.push({resolve:e,reject:t})});o=!0;try{let e=(await p("api/auth/refresh-token",{method:"POST",credentials:"include"})).token;return(0,n.O5)(e),c(null,e),e}catch(e){throw c(e),e}finally{o=!1}},m=async(e,t,r,n,a)=>{if(e.includes("refresh-token")||e.includes("login"))return d(),null;try{let e=await h();if(!e)return null;let n=x(r,a);n.Authorization="Bearer ".concat(e);let{body:i}=f(a),o=await fetch(t,{...r,body:i,headers:n,credentials:"include"});if(o.ok)return g(o);return null}catch(e){return d(),console.error("Token refresh failed:",e),null}},v=e=>{if(e.startsWith("http"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat(a.$.api.baseUrl,"/").concat(t)},f=e=>e?e instanceof FormData?{body:e,headers:{}}:{body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}:{body:void 0,headers:{}},x=(e,t)=>{let r={...t instanceof FormData?{Accept:i.Accept}:{...i},...e.headers};if(!r.Authorization){let e=(0,n.c4)();e&&(r.Authorization="Bearer ".concat(e))}return r};async function p(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,n=v(e),{body:a,headers:i}=f(r),o={...x(t,r),...i};try{let i=await fetch(n,{...t,body:a,headers:o,credentials:"include"});if(i.ok)return g(i);if(401===i.status){let a=await m(e,n,t,o,r);if(a)return a}throw await l(i)}catch(e){if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;throw u(e)}}let w={get:(e,t)=>p(e,{...t,method:"GET"}),post:(e,t,r)=>{let{body:n,headers:a}=f(t);return p(e,{...r,method:"POST",body:n,headers:{...a,...null==r?void 0:r.headers}},t)},put:(e,t,r)=>{let{body:n,headers:a}=f(t);return p(e,{...r,method:"PUT",body:n,headers:{...a,...null==r?void 0:r.headers}},t)},patch:(e,t,r)=>{let{body:n,headers:a}=f(t);return p(e,{...r,method:"PATCH",body:n,headers:{...a,...null==r?void 0:r.headers}},t)},delete:(e,t)=>p(e,{...t,method:"DELETE"})}},22100:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(12115),a=r(67057);function i(){let e=(0,n.useContext)(a.c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},24285:(e,t,r)=>{Promise.resolve().then(r.bind(r,82062))},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},48133:(e,t,r)=>{"use strict";r.d(t,{O5:()=>l,c4:()=>c,gV:()=>u,m_:()=>g,wz:()=>d});var n=r(67938);let a=n.$.auth.tokenStorageKey,i=n.$.auth.userStorageKey,o=null,s=null,c=()=>{if(o)return o;try{let e=localStorage.getItem(a);if(e)return o=e,e}catch(e){console.error("Error accessing localStorage",e)}return null},l=e=>{o=e;try{e?localStorage.setItem(a,e):localStorage.removeItem(a)}catch(e){console.error("Error accessing localStorage",e)}},d=()=>{if(s)return s;try{let e=localStorage.getItem(i);if(e)try{let t=JSON.parse(e);return s=t,t}catch(e){console.error("Error parsing stored user data",e)}}catch(e){console.error("Error accessing localStorage",e)}return null},u=e=>{s=e;try{e?localStorage.setItem(i,JSON.stringify(e)):localStorage.removeItem(i)}catch(e){console.error("Error accessing localStorage",e)}},g=()=>{o=null,s=null;try{localStorage.removeItem(a),localStorage.removeItem(i)}catch(e){console.error("Error accessing localStorage",e)}}},67938:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});let n={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},79285:(e,t,r)=>{"use strict";r.d(t,{g:()=>a});var n=r(7283);let a={inviteUser:async(e,t)=>n.F.post("".concat(e,"/api/organization-unit-invitation"),{email:t}),acceptInvitation:async(e,t)=>{try{let r=await n.F.post("".concat(e,"/api/organization-unit-invitation/accept"),{token:t});if(!r)throw Error("Empty response received");if(void 0===r.success)return{success:!0};return r}catch(e){return function(e){var t,r;if((null==e?void 0:e.status)===409)return{success:!0};if(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.message)throw Error(e.response.data.message);if(null==e?void 0:e.message)throw Error(e.message);throw Error("Failed to accept invitation.")}(e)}},checkInvitation:async(e,t)=>n.F.get("".concat(e,"/api/organization-unit-invitation/check?email=").concat(encodeURIComponent(t))),checkInvitationToken:async(e,t)=>{try{return await n.F.get("".concat(e,"/api/organization-unit-invitation/check-token?token=").concat(encodeURIComponent(t)))}catch(e){if(console.error("Error checking invitation token:",e),(null==e?void 0:e.status)===404)return{status:"Invalid",recipientEmail:"",expiresAt:"",organizationUnitId:""};throw e}},listInvitations:async(e,t)=>{let r="/".concat(e,"/odata/OrganizationUnitInvitations");if(t){let e=new URLSearchParams;Object.entries(t).forEach(t=>{let[r,n]=t;void 0!==n&&e.append(r,n)}),r+="?".concat(e.toString())}return function(e){return"object"==typeof e&&null!==e&&"value"in e?e:Array.isArray(e)?{value:e,"@odata.count":e.length}:"object"==typeof e&&null!==e&&"invitations"in e?{value:e.invitations,"@odata.count":e.count}:{value:[]}}(await n.F.get(r))}}},82062:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(95155),a=r(6874),i=r.n(a),o=r(35695),s=r(12115),c=r(79285),l=r(22100);function d(){let e=(0,o.useRouter)(),t=(0,o.useSearchParams)(),r=(0,o.useParams)(),a=(null==r?void 0:r.tenant)||"",d=t.get("token"),{isAuthenticated:u,user:g,logout:h}=(0,l.A)(),[m,v]=(0,s.useState)("loading"),[f,x]=(0,s.useState)(""),[p,w]=(0,s.useState)(0),[y,k]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{if(!d||!a){v("error"),x("Invalid invitation link.");return}if(!u){v("idle");return}try{let e=await c.g.checkInvitationToken(a,d);if(e.recipientEmail&&k(e.recipientEmail),"Accepted"===e.status){v("already_accepted");return}if("Expired"===e.status||"Invalid"===e.status){v("error"),x("Invitation is ".concat(e.status.toLowerCase(),"."));return}if(u&&(null==g?void 0:g.email)&&e.recipientEmail&&g.email.toLowerCase()!==e.recipientEmail.toLowerCase()){v("wrong_email"),x("You are currently logged in as ".concat(g.email,", but this invitation was sent to ").concat(e.recipientEmail,"."));return}v("idle")}catch(e){console.error("Error checking invitation status:",e),v("error"),x("Failed to verify invitation. Please try again later.")}})()},[d,a,u,g,p]);let b=async()=>{if(function(e){var t;let{token:r,tenant:n,isAuthenticated:a,invitedEmail:i,user:o,setStatus:s,setErrorMsg:c,router:l}=e;return r&&n?a?!i||(null==o?void 0:null===(t=o.email)||void 0===t?void 0:t.toLowerCase())===i.toLowerCase()||(s("wrong_email"),c("You are currently logged in as ".concat(null==o?void 0:o.email,", but this invitation was sent to ").concat(i,".")),!1):(l.replace("/login?returnUrl=/".concat(n,"/invitation/accept?token=").concat(r)),!1):(s("error"),c("Invalid invitation link."),!1)}({token:d,tenant:a,isAuthenticated:u,invitedEmail:y,user:g,setStatus:v,setErrorMsg:x,router:e})){v("accepting");try{let e=await c.g.acceptInvitation(a,d);if(function(e,t,r,n,a){if((null==e?void 0:e.success)===!0){var i;return e.invitedEmail&&(null==t?void 0:null===(i=t.email)||void 0===i?void 0:i.toLowerCase())!==e.invitedEmail.toLowerCase()?(r("wrong_email"),n(e.invitedEmail),a("You are currently logged in as ".concat(null==t?void 0:t.email,", but this invitation was sent to ").concat(e.invitedEmail,"."))):r("success"),!0}return!1}(e,g,v,k,x))return;w(e=>e+1),v("error"),x("Failed to accept invitation. Please try again.")}catch(e){!function(e,t,r,n){if(e instanceof Response){e.json().then(e=>{(null==e?void 0:e.message)&&r(e.message)}).catch(()=>{}),n(e=>e+1),t("error");return}let a=function(e){if("object"==typeof e&&null!==e){var t,r,n,a;if((null===(t=e.message)||void 0===t?void 0:t.includes("not invited"))||(null===(r=e.message)||void 0===r?void 0:r.includes("You are not invited to this OU")))return{status:"wrong_email",msg:"You are not invited to this organization with your current email address."};if(null===(a=e.response)||void 0===a?void 0:null===(n=a.data)||void 0===n?void 0:n.message)return{status:"error",msg:e.response.data.message};if(e.message||e.status&&e.message)return{status:"error",msg:e.message}}return null}(e);if(a){if(t(a.status),r(a.msg),"wrong_email"===a.status)return}else t("error"),r("Failed to accept invitation.");n(e=>e+1)}(e,v,x,w)}}},j=async()=>{await h(),e.replace("/login?returnUrl=/".concat(a,"/invitation/accept?token=").concat(d))},z=()=>{e.push("/tenant-selector")};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,n.jsxs)("div",{className:"w-full max-w-md bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 shadow-xl rounded-2xl p-8 flex flex-col items-center space-y-6 transition-colors",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-orange-600 text-center",children:"Organization Invitation"}),"loading"===m&&(0,n.jsxs)("div",{className:"flex items-center justify-center py-2 text-zinc-500 dark:text-zinc-300",children:[(0,n.jsxs)("svg",{className:"animate-spin h-5 w-5 text-orange-600 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8z"})]}),"Verifying invitation..."]}),"idle"===m&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("p",{className:"text-sm text-zinc-600 dark:text-zinc-300 text-center",children:["You have been invited to join this Organization Unit (OU).",!u&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("br",{}),"Please sign in or register with the invited email address first."]}),u&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("br",{}),"Click ",(0,n.jsx)("b",{children:"Accept"})," to join."]})]}),u&&(0,n.jsx)("button",{className:"w-full px-8 py-2 bg-orange-600 text-white rounded font-semibold hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:b,children:"Accept"})]}),"accepting"===m&&(0,n.jsxs)("div",{className:"flex items-center justify-center py-2 text-zinc-500 dark:text-zinc-300",children:[(0,n.jsxs)("svg",{className:"animate-spin h-5 w-5 text-orange-600 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8z"})]}),"Accepting invitation..."]}),"success"===m&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"text-green-600 dark:text-green-400 text-center font-medium",children:"Successfully joined the organization!"}),(0,n.jsx)("button",{className:"w-full px-8 py-2 bg-orange-600 text-white rounded font-semibold hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:z,children:"Go to My Organizations"})]}),"already_accepted"===m&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"text-green-600 dark:text-green-400 text-center font-medium",children:"You have already joined this organization."}),(0,n.jsx)("button",{className:"w-full px-8 py-2 bg-orange-600 text-white rounded font-semibold hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:z,children:"Go to My Organizations"})]}),"wrong_email"===m&&(0,n.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,n.jsx)("div",{className:"text-red-600 dark:text-red-400 text-center",children:f}),(0,n.jsx)("div",{className:"text-sm text-zinc-600 dark:text-zinc-300 text-center",children:"Please sign in with the email address that was invited or ask for a new invitation to your current email."}),(0,n.jsxs)("div",{className:"flex flex-col w-full space-y-3",children:[(0,n.jsx)("button",{className:"w-full px-8 py-2 bg-orange-600 text-white rounded font-semibold hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:j,children:"Sign in with different account"}),(0,n.jsx)("div",{className:"flex justify-center items-center space-x-2",children:(0,n.jsx)("span",{className:"text-xs text-zinc-500",children:"or"})}),(0,n.jsx)(i(),{href:"/register?returnUrl=/".concat(a,"/invitation/accept?token=").concat(d),className:"w-full px-8 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-800 dark:text-zinc-200 rounded font-semibold hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors text-center",children:"Register new account"})]})]}),"error"===m&&(0,n.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,n.jsx)("div",{className:"text-red-600 dark:text-red-400 text-center",children:f}),(0,n.jsxs)("div",{className:"flex space-x-4",children:[(0,n.jsx)("button",{className:"px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-800 dark:text-zinc-200 rounded font-medium hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors",onClick:()=>{v("loading"),w(e=>e+1)},children:"Retry"}),(0,n.jsx)("button",{className:"px-4 py-2 bg-orange-600 text-white rounded font-medium hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors",onClick:z,children:"Go to Organizations"})]})]}),!u&&"loading"!==m&&(0,n.jsxs)("p",{className:"text-center text-xs text-zinc-400 dark:text-zinc-500",children:["Already have an account?"," ",(0,n.jsx)(i(),{href:"/login?returnUrl=/".concat(a,"/invitation/accept?token=").concat(d),className:"text-orange-600 underline underline-offset-4 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-all duration-300 hover:underline-offset-8",children:"Sign in"})," ","or"," ",(0,n.jsx)(i(),{href:"/register?returnUrl=/".concat(a,"/invitation/accept?token=").concat(d),className:"text-orange-600 underline underline-offset-4 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-all duration-300 hover:underline-offset-8",children:"Register"})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,7057,8441,1684,7358],()=>t(24285)),_N_E=e.O()}]);