(()=>{var e={};e.id=672,e.ids=[672],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18596:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var r=s(60687),a=s(29523),n=s(44493),l=s(43210),i=s(31207),o=s(47565),d=s(50723),c=s(56896),m=s(34208);let u=[{id:"select",header:({table:e})=>(0,r.jsx)(c.S,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all"}),cell:({row:e})=>(0,r.jsx)(c.S,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row"}),enableSorting:!1,enableHiding:!1},{accessorKey:"firstName",header:({column:e})=>(0,r.jsx)(m.w,{column:e,title:"First Name"}),cell:({row:e})=>(0,r.jsx)("div",{className:"font-medium",children:e.getValue("firstName")})},{accessorKey:"lastName",header:({column:e})=>(0,r.jsx)(m.w,{column:e,title:"Last Name"}),cell:({row:e})=>(0,r.jsx)("div",{className:"font-medium",children:e.getValue("lastName")})},{accessorKey:"email",header:({column:e})=>(0,r.jsx)(m.w,{column:e,title:"Email"}),cell:({row:e})=>(0,r.jsx)("div",{className:"text-muted-foreground",children:e.getValue("email")})},{accessorKey:"systemRole",header:({column:e})=>(0,r.jsx)(m.w,{column:e,title:"Role"}),cell:({row:e})=>{let t=e.getValue("systemRole");return(0,r.jsx)("div",{children:"Admin"===t||1===t?"Admin":"User"})}}];var x=s(99270),h=s(41862),g=s(80462),p=s(11860),f=s(89667),j=s(53984),b=s(15079),v=s(96834);function N({table:e,roles:t,onSearch:s,onRoleChange:n,searchValue:i="",isFiltering:o=!1,isPending:d=!1}){let c=(0,l.useRef)(null),m=e.getState().columnFilters.length>0;return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex flex-1 items-center space-x-2",children:[(0,r.jsxs)("div",{className:"relative flex-1 md:max-w-sm",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,r.jsx)(f.p,{ref:c,placeholder:"Search users...",value:i,onChange:e=>{let t=e.target.value;s?.(t)},className:"pl-9 h-9",disabled:o}),o&&(0,r.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsxs)(b.l6,{onValueChange:e=>{"all"===e?n?.(""):n?.(e)},disabled:o,children:[(0,r.jsx)(b.bq,{className:"h-9 w-[120px]",children:(0,r.jsx)(b.yv,{placeholder:"Role"})}),(0,r.jsxs)(b.gC,{children:[(0,r.jsx)(b.eb,{value:"all",children:"All Roles"}),t.map(e=>(0,r.jsx)(b.eb,{value:e.value,children:e.label},e.value))]})]})]}),(m||i)&&(0,r.jsxs)(a.$,{variant:"ghost",onClick:()=>{e.resetColumnFilters(),s?.(""),n?.("")},className:"h-9 px-2 lg:px-3",disabled:o,children:["Reset",(0,r.jsx)(p.A,{className:"ml-2 h-4 w-4"})]}),d&&(0,r.jsxs)(v.E,{variant:"secondary",className:"ml-2",children:[(0,r.jsx)(h.A,{className:"mr-1 h-3 w-3 animate-spin"}),"Updating..."]})]}),(0,r.jsx)(j.i,{table:e})]})}var y=s(14583),A=s(53881),P=s(56090),C=s(93772);let w=e=>{let t=e.systemRole;return"Admin"===t||t===A.i.Admin};function S(){let[e,t]=(0,l.useState)(""),[s,c]=(0,l.useState)(""),[m,x]=(0,l.useState)({pageIndex:0,pageSize:10}),[h,g]=(0,l.useState)([]),[p,f]=(0,l.useState)([]),[j,b]=(0,l.useState)({}),[v,A]=(0,l.useState)({}),{data:S,error:R,isLoading:k,mutate:F}=(0,i.Ay)(["systemAdmin-all-users"],()=>o.i.getAllUsers()),G=(0,l.useMemo)(()=>{if(!S)return[];let t=S;return e&&(t=t.filter(t=>t.firstName?.toLowerCase().includes(e.toLowerCase())||t.lastName?.toLowerCase().includes(e.toLowerCase())||t.email?.toLowerCase().includes(e.toLowerCase()))),s&&(t=t.filter(e=>{let t=w(e);return"Admin"===s?t:!t})),t},[S,e,s]),z=(0,P.N4)({data:G,columns:u,state:{sorting:h,columnVisibility:j,rowSelection:v,columnFilters:p,pagination:m},enableRowSelection:!0,onRowSelectionChange:A,onSortingChange:g,onColumnFiltersChange:f,onColumnVisibilityChange:b,onPaginationChange:x,getCoreRowModel:(0,C.HT)(),getFilteredRowModel:(0,C.hM)(),getPaginationRowModel:(0,C.kW)(),getSortedRowModel:(0,C.h5)(),getFacetedRowModel:(0,C.kQ)(),getFacetedUniqueValues:(0,C.oS)(),manualPagination:!1}),I=G.length,U=Math.max(1,Math.ceil(I/m.pageSize)),M=(0,l.useCallback)(e=>{t(e),x(e=>({...e,pageIndex:0}))},[]),O=(0,l.useCallback)(e=>{c(e),x(e=>({...e,pageIndex:0}))},[]);return(0,r.jsx)("div",{className:"h-full overflow-y-auto bg-background p-6",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h1",{className:"text-2xl md:text-3xl font-bold tracking-tight text-foreground",children:"User Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage system users and their permissions"})]}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium text-card-foreground",children:"Total Users"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:"2,847"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12.5% from last month"})]})]}),(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium text-card-foreground",children:"Active Users"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:"2,234"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+8.1% from last month"})]})]}),(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium text-card-foreground",children:"Pending Invites"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:"156"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+3.2% from last month"})]})]}),(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium text-card-foreground",children:"Inactive Users"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-card-foreground",children:"457"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"-2.1% from last month"})]})]})]}),R&&(0,r.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md border border-red-200 dark:border-red-800",children:[(0,r.jsx)("p",{className:"text-red-800 dark:text-red-300",children:"Failed to load users. Please try again."}),(0,r.jsx)(a.$,{variant:"outline",className:"mt-2",onClick:()=>F(),children:"Retry"})]}),(0,r.jsxs)(n.Zp,{className:"bg-card border-border",children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{className:"text-xl font-bold tracking-tight",children:"Users"}),I>0&&(0,r.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:["Total: ",I," user",1!==I?"s":""]})]})})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsx)(N,{table:z,roles:[{value:"Admin",label:"Admin"},{value:"User",label:"User"}],onSearch:M,onRoleChange:O,searchValue:e,isFiltering:k,isPending:!1}),(0,r.jsx)(d.b,{columns:u,data:G,table:z,isLoading:k,totalCount:I}),(0,r.jsx)(y.d,{currentPage:m.pageIndex+1,pageSize:m.pageSize,totalCount:I,totalPages:U,isLoading:k,isChangingPageSize:!1,isUnknownTotalCount:!1,onPageChange:e=>{x({...m,pageIndex:e-1})},onPageSizeChange:e=>{let t=Math.floor(m.pageIndex*m.pageSize/e);x({pageSize:e,pageIndex:t})}}),!k&&0===G.length&&!R&&(0,r.jsx)("div",{className:"text-center py-10 text-muted-foreground",children:(0,r.jsxs)("p",{children:["No users found."," ",e||s?"Try adjusting your filters.":"Create your first user to get started."]})})]})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23904:(e,t,s)=>{Promise.resolve().then(s.bind(s,72128)),Promise.resolve().then(s.bind(s,2505)),Promise.resolve().then(s.bind(s,92588)),Promise.resolve().then(s.bind(s,48974)),Promise.resolve().then(s.bind(s,31057)),Promise.resolve().then(s.bind(s,50417))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38475:(e,t,s)=>{Promise.resolve().then(s.bind(s,52162)),Promise.resolve().then(s.bind(s,83847)),Promise.resolve().then(s.bind(s,78526)),Promise.resolve().then(s.bind(s,97597)),Promise.resolve().then(s.bind(s,98641)),Promise.resolve().then(s.bind(s,80110))},47565:(e,t,s)=>{"use strict";s.d(t,{i:()=>a});var r=s(51787);let a={getAllUsers:async()=>(0,r.fetchApi)("api/admin/user/get-all",{method:"GET"}),getUserById:async e=>(0,r.fetchApi)(`api/admin/user/detail/${e}`,{method:"GET"}),updateUserInfo:async(e,t)=>(0,r.fetchApi)(`api/admin/user/update-detail/${e}`,{method:"PUT",body:JSON.stringify(t)}),changeUserPassword:async(e,t)=>(0,r.fetchApi)(`api/admin/user/change-password/${e}`,{method:"POST",body:JSON.stringify(t)}),getAllOrganizationUnits:async()=>await r.F.get("/api/admin/organization-unit/get-all"),deleteOrganizationUnit:async e=>{await r.F.delete(`/api/admin/organization-unit/${e}`)}}},47715:(e,t,s)=>{Promise.resolve().then(s.bind(s,18596))},52162:(e,t,s)=>{"use strict";s.d(t,{AdminRouteGuard:()=>i});var r=s(60687),a=s(86522),n=s(16189);s(43210);var l=s(85726);function i({children:e,redirectPath:t="/tenant-selector",loadingComponent:s}){let{isSystemAdmin:i,isLoading:o,isLogout:d}=(0,a.A)();return((0,n.useRouter)(),o)?s||(0,r.jsxs)("div",{className:"w-full p-8 space-y-4",children:[(0,r.jsx)(l.E,{className:"h-12 w-full rounded-lg"}),(0,r.jsx)(l.E,{className:"h-60 w-full rounded-lg"}),(0,r.jsx)(l.E,{className:"h-12 w-2/3 rounded-lg"})]}):i?(0,r.jsx)(r.Fragment,{children:e}):null}},53984:(e,t,s)=>{"use strict";s.d(t,{i:()=>o});var r=s(60687),a=s(4654),n=s(56476),l=s(29523),i=s(21342);function o({table:e}){return(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(a.ty,{asChild:!0,children:(0,r.jsxs)(l.$,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[(0,r.jsx)(n.A,{}),"View"]})}),(0,r.jsxs)(i.SQ,{align:"end",className:"w-[150px]",children:[(0,r.jsx)(i.lp,{children:"Toggle columns"}),(0,r.jsx)(i.mB,{}),e.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()).map(e=>(0,r.jsx)(i.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72128:(e,t,s)=>{"use strict";s.d(t,{AdminRouteGuard:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AdminRouteGuard() from the server but AdminRouteGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\auth\\admin-route-guard.tsx","AdminRouteGuard")},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},82158:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\(systemAdmin)\\\\user-management\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\user-management\\page.tsx","default")},88043:(e,t,s)=>{Promise.resolve().then(s.bind(s,82158))},90943:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=s(65239),a=s(48088),n=s(31369),l=s(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let o={children:["",{children:["(systemAdmin)",{children:["user-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,82158)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\user-management\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,96707)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,31369)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\(systemAdmin)\\user-management\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(systemAdmin)/user-management/page",pathname:"/user-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},96707:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(37413),a=s(50417),n=s(48974),l=s(31057),i=s(92588),o=s(2505),d=s(72128);function c({children:e}){return(0,r.jsx)(d.AdminRouteGuard,{children:(0,r.jsx)(o.ChatProvider,{children:(0,r.jsx)("div",{className:"[--header-height:calc(theme(spacing.14))]",children:(0,r.jsxs)(a.SidebarProvider,{className:"flex flex-col",children:[(0,r.jsx)(l.SiteHeader,{}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)(n.AppSidebar,{}),(0,r.jsx)(a.SidebarInset,{children:(0,r.jsx)(i.SearchProvider,{children:(0,r.jsx)("main",{className:"",children:e})})})]})]})})})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7966,5584,5156,4654,6467,1694,6945,8759,6763,519,4881],()=>s(90943));module.exports=r})();