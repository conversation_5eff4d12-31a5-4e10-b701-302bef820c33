exports.id=6763,exports.ids=[6763],exports.modules={3962:(e,t,r)=>{"use strict";r.d(t,{LocaleProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call LocaleProvider() from the server but LocaleProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\locale-provider.tsx","LocaleProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useLocale() from the server but useLocale is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\locale-provider.tsx","useLocale")},4344:(e,t,r)=>{"use strict";r.d(t,{$:()=>s});let s={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},5446:(e,t,r)=>{Promise.resolve().then(r.bind(r,8811))},7556:(e,t,r)=>{"use strict";r.d(t,{AutoErrorProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AutoErrorProvider() from the server but AutoErrorProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\auto-error-provider.tsx","AutoErrorProvider")},7854:(e,t,r)=>{"use strict";r.d(t,{AG:()=>a,ro:()=>n});var s=r(35674);let o=null;function n(e){s.h.setToastFunction(e)}function a(e,t){!t?.skipToast&&(t?.skipAuth===!1||!e||"object"!=typeof e||!("status"in e)||401!==e.status)&&s.h.handleError(e,t?.context)}},8210:(e,t,r)=>{Promise.resolve().then(r.bind(r,54413))},8811:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(60687);function o({error:e,reset:t}){return(0,s.jsx)("html",{children:(0,s.jsx)("body",{className:"min-h-screen flex items-center justify-center bg-orange-50 text-orange-800 font-sans p-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white shadow-md rounded-xl p-6 border border-orange-200",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-orange-600 mb-2",children:"Something went wrong!"}),(0,s.jsx)("p",{className:"mb-4",children:e.message}),(0,s.jsx)("div",{className:"flex ",children:(0,s.jsx)("button",{onClick:t,className:"bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded w-full",children:"Try again"})})]})})})}},12098:(e,t,r)=>{"use strict";r.d(t,{$:()=>s});let s={app:{name:"OpenAutomate Orchestrator",url:"http://localhost:3001",domain:"localhost"},api:{baseUrl:"http://localhost:5252",defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},auth:{tokenRefreshInterval:84e4,tokenStorageKey:"auth_token",userStorageKey:"user_data"},paths:{auth:{login:"/login",register:"/register",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verificationPending:"/verification-pending",emailVerified:"/email-verified",verifyEmail:"/verify-email",organizationSelector:"/tenant-selector"},defaultRedirect:"/dashboard"}}},13298:(e,t,r)=>{"use strict";r.d(t,{AutoErrorProvider:()=>o});var s=r(60687);function o({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}r(43210),r(7854)},13694:(e,t,r)=>{Promise.resolve().then(r.bind(r,31369))},14947:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>k});var s=r(60687),o=r(43210),n=r(4027),a=r(24224),i=r(11860),l=r(36966);let d=n.Kq,c=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.LM,{ref:r,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));c.displayName=n.LM.displayName;let u=(0,a.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=o.forwardRef(({className:e,variant:t,...r},o)=>(0,s.jsx)(n.bL,{ref:o,className:(0,l.cn)(u({variant:t}),e),...r}));f.displayName=n.bL.displayName,o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.rc,{ref:r,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.rc.displayName;let m=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.bm,{ref:r,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));m.displayName=n.bm.displayName;let p=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.hE,{ref:r,className:(0,l.cn)("text-sm font-semibold",e),...t}));p.displayName=n.hE.displayName;let h=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.VY,{ref:r,className:(0,l.cn)("text-sm opacity-90",e),...t}));h.displayName=n.VY.displayName;var g=r(20140),v=r(93613),b=r(5336),x=r(43649),y=r(96882);let w=e=>{switch(e){case"destructive":return(0,s.jsx)(v.A,{className:"h-5 w-5 text-red-500"});case"success":return(0,s.jsx)(b.A,{className:"h-5 w-5 text-green-500"});case"warning":return(0,s.jsx)(x.A,{className:"h-5 w-5 text-yellow-500"});default:return(0,s.jsx)(y.A,{className:"h-5 w-5 text-blue-500"})}},P=e=>{switch(e){case"destructive":return"border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-900/20 dark:text-red-100";case"success":return"border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-900/20 dark:text-green-100";case"warning":return"border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-100";default:return"border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-100"}};function k(){let{toasts:e,dismiss:t}=(0,g.d)();return(0,s.jsxs)(d,{children:[e.map(({id:e,title:r,description:o,action:n,variant:a,...i})=>{let d=e||crypto.randomUUID();return(0,s.jsxs)(f,{...i,className:(0,l.cn)("group pointer-events-auto relative flex w-full items-start space-x-3 overflow-hidden rounded-lg border p-4 shadow-lg transition-all duration-300 hover:shadow-xl",P(a)),children:[(0,s.jsx)("div",{className:"flex-shrink-0 pt-0.5",children:w(a)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[r&&(0,s.jsx)(p,{className:"text-sm font-semibold leading-5 mb-1",children:r}),o&&(0,s.jsx)(h,{className:"text-sm leading-5 opacity-90",children:o}),n&&(0,s.jsx)("div",{className:"mt-3",children:n})]}),(0,s.jsx)(m,{className:"absolute right-2 top-2 rounded-md p-1 opacity-70 transition-opacity hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2",onClick:()=>t(d)})]},d)}),(0,s.jsx)(c,{className:"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[400px]"})]})}},20140:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,d:()=>n});var s=r(43210);let o=(0,s.createContext)({toasts:[],addToast:()=>"",updateToast:()=>{},dismissToast:()=>{},removeToast:()=>{}});function n(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return{toast:t=>e.addToast(t),dismiss:t=>e.dismissToast(t),toasts:e.toasts}}},27808:(e,t,r)=>{"use strict";r.d(t,{SWRProvider:()=>l});var s=r(60687),o=r(43210),n=r(31207),a=r(70891);r(7854);var i=r(20140);function l({children:e}){let{toast:t}=(0,i.d)(),r=(0,o.useMemo)(()=>t?(0,a.bb)():a.EJ,[t]);return(0,s.jsx)(n.BE,{value:r,children:e})}},29039:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>l});var s=r(60687),o=r(43210),n=r(11329),a=r(24224),i=r(36966);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...a},d)=>{let c=o?n.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:d,...a})});d.displayName="Button"},31369:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\global-error.tsx","default")},35674:(e,t,r)=>{"use strict";r.d(t,{h:()=>s,m:()=>o});class s{static{this.toastFunction=null}static setToastFunction(e){this.toastFunction=e}static error(e,t){this.toastFunction&&this.toastFunction({title:t?.title||"Error",description:e,variant:"destructive",duration:t?.duration||8e3,action:t?.action})}static success(e,t){this.toastFunction&&this.toastFunction({title:t?.title||"Success",description:e,variant:"default",duration:t?.duration||4e3,action:t?.action})}static warning(e,t){this.toastFunction&&this.toastFunction({title:t?.title||"Warning",description:e,variant:"default",duration:t?.duration||6e3,action:t?.action})}static info(e,t){this.toastFunction&&this.toastFunction({title:t?.title||"Information",description:e,variant:"default",duration:t?.duration||5e3,action:t?.action})}static handleError(e,t){let r="An unexpected error occurred",s="Error";if("string"==typeof e)r=e;else if(e&&"object"==typeof e){if("status"in e&&"message"in e){switch(e.status){case 400:s="Invalid Request";break;case 401:s="Authentication Required";break;case 403:s="Access Denied";break;case 404:s="Not Found";break;case 409:s="Conflict";break;case 422:s="Validation Error";break;case 429:s="Rate Limited";break;case 500:case 502:case 503:s="Server Error";break;default:s="Error"}r=e.message}else"message"in e&&"string"==typeof e.message&&(r=e.message)}t&&(s=`${t} Failed`),this.error(r,{title:s})}static handleSuccess(e,t){let r=t?`${t} ${e} successfully`:`${e} completed successfully`;this.success(r,{title:"Success"})}static isAvailable(){return null!==this.toastFunction}}let o={error:s.error.bind(s),success:s.success.bind(s),warning:s.warning.bind(s),info:s.info.bind(s),handleError:s.handleError.bind(s),handleSuccess:s.handleSuccess.bind(s)}},36966:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(49384),o=r(82348);function n(...e){return(0,o.QP)((0,s.$)(e))}},37287:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},38605:(e,t,r)=>{"use strict";r.d(t,{O5:()=>i,c4:()=>a,gV:()=>d,m_:()=>c,wz:()=>l});var s=r(4344);s.$.auth.tokenStorageKey,s.$.auth.userStorageKey;let o=null,n=null,a=()=>o||null,i=e=>{o=e},l=()=>n||null,d=e=>{n=e},c=()=>{o=null,n=null}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>a,wL:()=>c});var s=r(60687);r(43210);var o=r(36966);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,o.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},47996:(e,t,r)=>{Promise.resolve().then(r.bind(r,55738)),Promise.resolve().then(r.bind(r,14947)),Promise.resolve().then(r.bind(r,86522)),Promise.resolve().then(r.bind(r,13298)),Promise.resolve().then(r.bind(r,56992)),Promise.resolve().then(r.bind(r,27808)),Promise.resolve().then(r.bind(r,95397))},51787:(e,t,r)=>{"use strict";r.d(t,{F:()=>x,fetchApi:()=>b});var s=r(38605),o=r(4344);let n=o.$.api.defaultHeaders,a=!1,i=[],l=(e,t=null)=>{i.forEach(r=>{e?r.reject(e):r.resolve(t)}),i=[]},d=async e=>{let t={message:e.statusText,status:e.status};try{let r=await e.json();r.message?(t.message=r.message,t.details=r.details||r.message):r.error?(t.message=r.error,t.details=r.error):t.details=JSON.stringify(r)}catch{t.details=e.statusText}return t},c=()=>{},u=e=>{if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw{message:"Network error. Please check your connection.",status:0,details:e.message};if(e instanceof Error)throw{message:e.message||"Network error occurred",status:0,details:e.stack||e.message};throw{message:"An unexpected error occurred",status:0,details:String(e)}},f=async e=>{if(204===e.status)return{};let t=e.headers.get("content-type"),r=e.headers.get("content-length");if(!t||-1===t.indexOf("application/json")||"0"===r)return{};let s=await e.text();return s?JSON.parse(s):{}},m=async()=>{if(a)return new Promise((e,t)=>{i.push({resolve:e,reject:t})});a=!0;try{let e=(await b("api/auth/refresh-token",{method:"POST",credentials:"include"})).token;return(0,s.O5)(e),l(null,e),e}catch(e){throw l(e),e}finally{a=!1}},p=async(e,t,r,s,o)=>{if(e.includes("refresh-token")||e.includes("login"))return c(),null;try{let e=await m();if(!e)return null;let s=v(r,o);s.Authorization=`Bearer ${e}`;let{body:n}=g(o),a=await fetch(t,{...r,body:n,headers:s,credentials:"include"});if(a.ok)return f(a);return null}catch(e){return c(),console.error("Token refresh failed:",e),null}},h=e=>{if(e.startsWith("http"))return e;let t=e.startsWith("/")?e.slice(1):e;return`${o.$.api.baseUrl}/${t}`},g=e=>e?e instanceof FormData?{body:e,headers:{}}:{body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}:{body:void 0,headers:{}},v=(e,t)=>{let r={...t instanceof FormData?{Accept:n.Accept}:{...n},...e.headers};if(!r.Authorization){let e=(0,s.c4)();e&&(r.Authorization=`Bearer ${e}`)}return r};async function b(e,t={},r){let s=h(e),{body:o,headers:n}=g(r),a={...v(t,r),...n};try{let n=await fetch(s,{...t,body:o,headers:a,credentials:"include"});if(n.ok)return f(n);if(401===n.status){let o=await p(e,s,t,a,r);if(o)return o}throw await d(n)}catch(e){if(e&&"object"==typeof e&&"status"in e&&"message"in e)throw e;u(e)}}let x={get:(e,t)=>b(e,{...t,method:"GET"}),post:(e,t,r)=>{let{body:s,headers:o}=g(t);return b(e,{...r,method:"POST",body:s,headers:{...o,...r?.headers}},t)},put:(e,t,r)=>{let{body:s,headers:o}=g(t);return b(e,{...r,method:"PUT",body:s,headers:{...o,...r?.headers}},t)},patch:(e,t,r)=>{let{body:s,headers:o}=g(t);return b(e,{...r,method:"PATCH",body:s,headers:{...o,...r?.headers}},t)},delete:(e,t)=>b(e,{...t,method:"DELETE"})}},53881:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});var s=function(e){return e[e.User=0]="User",e[e.Admin=1]="Admin",e}({})},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\app\\not-found.tsx","default")},55066:(e,t,r)=>{Promise.resolve().then(r.bind(r,57347))},55738:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>a});var s=r(60687),o=r(43210),n=r(20140);function a({children:e}){let[t,r]=(0,o.useState)([]),a=e=>{let t=e.id||crypto.randomUUID();return r(r=>[{...e,id:t},...r].slice(0,5)),t},i=e=>{e.id&&r(t=>t.map(t=>t.id===e.id?{...t,...e}:t))},l=e=>{r(t=>t.map(t=>t.id===e?{...t,open:!1}:t))},d=e=>{r(t=>t.filter(t=>t.id!==e))},c=(0,o.useMemo)(()=>({toasts:t,addToast:a,updateToast:i,dismissToast:l,removeToast:d}),[t]);return(0,s.jsx)(n.$.Provider,{value:c,children:e})}},56992:(e,t,r)=>{"use strict";r.d(t,{LocaleProvider:()=>a,Y:()=>i});var s=r(60687),o=r(43210);let n=(0,o.createContext)(void 0);function a({children:e}){let[t,r]=(0,o.useState)("en"),[a,i]=(0,o.useState)({}),l=(0,o.useCallback)(e=>e.split(".").reduce((e,t)=>"object"==typeof e&&e?e[t]:void 0,a)??e,[a]);return(0,s.jsx)(n.Provider,{value:{locale:t,setLocale:r,t:l},children:e})}function i(){let e=(0,o.useContext)(n);if(!e)throw Error("useLocale must be used within LocaleProvider");return e}},57347:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),o=r(29523),n=r(44493),a=r(28559),i=r(32192),l=r(85814),d=r.n(l);function c(){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-gray-100 flex items-center justify-center p-4",children:(0,s.jsx)(n.Zp,{className:"w-full max-w-md text-center shadow-lg",children:(0,s.jsxs)(n.Wu,{className:"pt-8 pb-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("div",{className:"text-6xl font-bold text-gray-300 mb-2",children:"404"}),(0,s.jsx)("div",{className:"w-24 h-1 bg-orange-600 mx-auto rounded-full"})]}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8 leading-relaxed",children:"Sorry, we could not find the page you are looking for. It might have been moved, deleted, or you entered the wrong URL."}),(0,s.jsx)("div",{className:"space-y-3",children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(o.$,{variant:"outline",className:"flex-1",onClick:()=>window.history.back(),children:[(0,s.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Go Back"]}),(0,s.jsx)(o.$,{asChild:!0,className:"flex-1 bg-orange-600 hover:bg-orange-700",children:(0,s.jsxs)(d(),{href:"/",children:[(0,s.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Go Home"]})})]})})]})})})}},59321:(e,t,r)=>{"use strict";function s(e){if(!e)return"An unexpected error occurred";if("string"==typeof e)return e;if("object"==typeof e&&null!==e&&"status"in e&&"message"in e&&"number"==typeof e.status&&"string"==typeof e.message)return 0===e.status?e.message||"Network error. Please check your connection.":function(e){if(e.details){let t=function(e){try{let t=JSON.parse(e);return t.error??t.message??null}catch{return null}}(e.details);return t||e.details}return e.message??"An error occurred"}(e);if(e instanceof Error)return e.message.includes("Failed to fetch")||e.message.includes("NetworkError")?"Network error. Please check your connection.":e.message;let t=function(e){if(null!==e&&"object"==typeof e&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response){let t=e.response.data;if("object"==typeof t&&null!==t){if("message"in t&&"string"==typeof t.message)return t.message;if("error"in t&&"string"==typeof t.error)return t.error}if("string"==typeof t)return t}return null}(e);if(null!==t)return t;if("object"==typeof e&&null!==e){let t=void 0!==e.error&&null!==e.error&&"string"==typeof e.error?e.error:void 0!==e.message&&null!==e.message&&"string"==typeof e.message?e.message:null;if(null!==t)return t}return"An unexpected error occurred"}function o(e){return{title:"Error",description:s(e),variant:function(e){if("object"==typeof e&&null!==e&&"status"in e)return e.status<400?"default":"destructive";if("string"==typeof e){let t=e.toLowerCase();if(t.includes("warning")||t.includes("info"))return"default"}return"destructive"}(e)}}r.d(t,{PE:()=>s,m4:()=>o})},61135:()=>{},62395:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\theme-provider.tsx","ThemeProvider")},63506:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\auth-provider.tsx","AuthContext");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\auth-provider.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\auth-provider.tsx","useAuth")},70891:(e,t,r)=>{"use strict";r.d(t,{DC:()=>i,EJ:()=>a,IS:()=>l,bb:()=>n});var s=r(51787),o=r(7854);function n(){return{fetcher:e=>(0,s.fetchApi)(e),onError:function(e){(0,o.AG)(e,{skipAuth:!0})},revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!(e?.status>=400&&e?.status<500),dedupingInterval:2e3,focusThrottleInterval:5e3}}let a={fetcher:e=>(0,s.fetchApi)(e),revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,errorRetryCount:3,errorRetryInterval:1e3,shouldRetryOnError:e=>!(e?.status>=400&&e?.status<500),dedupingInterval:2e3,focusThrottleInterval:5e3},i={executions:()=>["executions"],executionsWithOData:e=>["executions","odata",e],executionById:e=>["executions",e],roles:()=>["roles"],roleById:e=>["roles",e],availableResources:()=>["available-resources"],agents:()=>["agents"],agentsWithOData:e=>["agents","odata",e],agentById:e=>["agents",e],packages:()=>["packages"],packagesWithOData:e=>["packages","odata",e],packageById:e=>["packages",e],packageVersions:e=>["packages",e,"versions"],organizationUnits:()=>["organization-units"],organizationUnitDeletionStatus:e=>["organization-units",e,"deletion-status"],assets:()=>["assets"],assetsWithOData:e=>["assets","odata",e],assetById:e=>["assets",e],assetAgents:e=>["assets",e,"agents"],systemRoles:e=>e?["system-roles",e]:["system-roles"],adminUsers:()=>["system-roles","admin"],standardUsers:()=>["system-roles","user"],usersByRole:e=>["system-roles","users",e],adminAllOrganizationUnits:()=>["system-roles","organization-units"],schedules:()=>["schedules"],schedulesWithOData:e=>["schedules","odata",e],scheduleById:e=>["schedules",e],subscription:()=>["subscription"]},l=e=>{if(e&&"object"==typeof e&&"status"in e){if(401===e.status)return"Authentication required. Please log in again.";if(403===e.status)return"You do not have permission to access this resource.";if(404===e.status)return"The requested resource was not found.";if(e.status>=500)return"Server error. Please try again later."}return e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:"An unexpected error occurred. Please try again."}},76672:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\toast-provider.tsx","ToastProvider")},78706:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(51787);let o={login:"api/auth/login",register:"api/auth/register",user:"api/auth/user",profile:"api/account/profile",refreshToken:"api/auth/refresh-token",revokeToken:"api/auth/revoke-token",forgotPassword:"api/auth/forgot-password",resetPassword:"api/auth/reset-password",changeUserName:"api/account/info",changePassword:"api/account/change-password",resendVerification:"api/email/resend",resendVerificationByEmail:"api/email/resend-public",verifyEmail:"api/email/verify"},n={login:async e=>await s.F.post(o.login,e,{credentials:"include"}),register:async e=>(await s.F.post(o.register,e,{credentials:"include"})).user,getCurrentUser:async()=>await s.F.get(o.user),getUserProfile:async()=>await s.F.get(o.profile),refreshToken:async()=>await s.F.post(o.refreshToken,{},{credentials:"include"}),logout:async()=>{await s.F.post(o.revokeToken,{},{credentials:"include"})},forgotPassword:async e=>{try{console.log("Sending forgot password request for email:",e.email),await s.F.post(o.forgotPassword,e),console.log("Forgot password request sent successfully")}catch(e){throw console.error("Forgot password request failed:",e),e}},resetPassword:async e=>{try{let t={email:e.email.trim(),token:function(e){let t=e.trim();t.includes(" ")&&(console.warn("Token contains spaces - cleaning up"),t=t.replace(/\s/g,""));try{if(t.includes("%"))return console.log("Token appears to be URL encoded - decoding"),decodeURIComponent(t)}catch(e){console.warn("Error trying to decode token:",e)}return t}(e.token.trim()),newPassword:e.newPassword,confirmPassword:e.confirmPassword};console.log("Sending reset password request with data:",{email:t.email,tokenLength:t.token.length,tokenPrefix:t.token.substring(0,10)+"...",newPassword:t.newPassword?"******":"MISSING",confirmPassword:t.confirmPassword?"******":"MISSING"}),console.log("Request payload structure:",JSON.stringify({...t,newPassword:"*****",confirmPassword:"*****"},null,2));let r=await s.F.post(o.resetPassword,t,{headers:{"Content-Type":"application/json"}});console.log("Password reset request successful",r)}catch(e){!function(e){if(console.error("Reset password request failed with error:",e),e?.status&&console.error("Status code:",e.status),e?.message&&console.error("Error message:",e.message),e?.details&&console.error("Error details:",e.details),e?.errors&&console.error("Validation errors:",e.errors),e?.errors){let t=Object.entries(e.errors).map(([e,t])=>`${e}: ${Array.isArray(t)?t.join(", "):t}`).join("; ");throw Error(`Password reset failed with validation errors: ${t}`)}if(e?.message)throw e;throw Error("Password reset failed. Please try again.")}(e)}},changeUserName:async(e,t)=>{await s.F.put(o.changeUserName,t)},changePassword:async e=>{await s.F.post(o.changePassword,e)},resendVerificationEmail:async e=>{await s.F.post(o.resendVerification,{email:e})},resendVerificationEmailByEmail:async e=>{await s.F.post(o.resendVerificationByEmail,{email:e})},verifyEmail:async e=>{try{return await s.F.get(`${o.verifyEmail}?token=${e}`),!0}catch(e){return console.error("Verification failed",e),!1}}}},79737:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\toaster.tsx","Toaster")},86522:(e,t,r)=>{"use strict";r.d(t,{c:()=>p,AuthProvider:()=>h,A:()=>g});var s=r(60687),o=r(43210),n=r(78706),a=r(16189),i=r(53881),l=r(38605);let d=(e,t="info",...r)=>{},c={success:(e,...t)=>{d(e,"success",...t)},error:(e,...t)=>{d(e,"error",...t)},warning:(e,...t)=>{d(e,"warning",...t)},auth:(e,t)=>{}},u={logout:()=>{c.auth("User Logged Out",{timestamp:new Date().toLocaleString(),status:"success"})}};var f=r(4344),m=r(59321);let p=(0,o.createContext)(void 0);function h({children:e}){let[t,r]=(0,o.useState)(null),[d,h]=(0,o.useState)(null),[g,v]=(0,o.useState)(!0),[b,x]=(0,o.useState)(!1),[y,w]=(0,o.useState)(null),P=(0,a.useRouter)(),k=(0,a.useParams)(),j=(0,o.useCallback)(async(e="profile fetch")=>{try{let t=await n.Z.getUserProfile();h(t),c.success(`User profile loaded successfully during ${e}`)}catch(t){c.warning(`Failed to load user profile during ${e}:`,t)}},[]),C=t?.systemRole===i.i.Admin||t?.systemRole==="Admin",A=(0,o.useCallback)((e,t,r)=>{if(!d)return!1;if(C)return!0;let s=r||k.tenant;if(!s)return!1;let o=d.organizationUnits.find(e=>e.slug===s);if(!o)return!1;let n=o.permissions.find(t=>t.resourceName===e);return!!n&&n.permission>=t},[d,C,k.tenant]),N=(0,o.useCallback)(async()=>{try{let e=await n.Z.refreshToken();(0,l.O5)(e.token);let t=e.user||{id:e.id,email:e.email,firstName:e.firstName,lastName:e.lastName,systemRole:e.systemRole||i.i.User};return(0,l.gV)(t),r(t),await j("token refresh"),c.success("Token refreshed successfully"),!0}catch(e){return c.error("Token refresh failed:",e),(0,l.m_)(),r(null),h(null),!1}},[j]),E=(0,o.useCallback)(async e=>{v(!0),w(null);try{let t=await n.Z.login(e);(0,l.O5)(t.token);let s=t.user||{id:t.id,email:t.email,firstName:t.firstName,lastName:t.lastName,systemRole:t.systemRole||i.i.User};return(0,l.gV)(s),r(s),await j("login"),c.success(`User logged in: ${s.email}`),P.push(f.$.paths.defaultRedirect),s}catch(e){throw w((0,m.PE)(e)),c.error("Login failed:",e),e}finally{v(!1)}},[P,j]),R=(0,o.useCallback)(async e=>{v(!0),w(null);try{let t=await n.Z.register(e);return c.auth("Registration Successful",{email:e.email,message:"Verification email sent"}),t}catch(t){let e=(0,m.PE)(t);throw w(e),c.error("Registration failed:",e),t}finally{v(!1)}},[]),T=(0,o.useCallback)(async()=>{v(!0),x(!0);try{await n.Z.logout(),u.logout()}catch(e){c.error("Logout error:",e)}finally{(0,l.m_)(),r(null),h(null),x(!1),P.push("/login"),v(!1)}},[P]),F=(0,o.useCallback)(e=>{if(t){let s={...t,...e};r(s),(0,l.gV)(s),c.success("User data updated successfully")}},[t]);return(0,s.jsx)(p.Provider,{value:(0,o.useMemo)(()=>({user:t,userProfile:d,isLoading:g,isAuthenticated:!!t,isSystemAdmin:C,isLogout:b,login:E,register:R,logout:T,refreshToken:N,updateUser:F,hasPermission:A,error:y}),[t,d,g,C,b,E,R,T,N,F,A,y]),children:e})}f.$.auth.tokenRefreshInterval;let g=()=>{let e=(0,o.useContext)(p);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},89852:(e,t,r)=>{Promise.resolve().then(r.bind(r,76672)),Promise.resolve().then(r.bind(r,79737)),Promise.resolve().then(r.bind(r,63506)),Promise.resolve().then(r.bind(r,7556)),Promise.resolve().then(r.bind(r,3962)),Promise.resolve().then(r.bind(r,92650)),Promise.resolve().then(r.bind(r,62395))},92650:(e,t,r)=>{"use strict";r.d(t,{SWRProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call SWRProvider() from the server but SWRProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\swr-provider.tsx","SWRProvider")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>p});var s=r(37413),o=r(85041),n=r.n(o);r(61135);var a=r(62395),i=r(63506),l=r(12098),d=r(79737),c=r(76672),u=r(3962),f=r(92650),m=r(7556);let p={title:l.$.app.name,description:"Automate your business processes with OpenAutomate",authors:[{name:"OpenAutomate Team"}],metadataBase:new URL("http://localhost:3001"),icons:{icon:[{url:"/favicon.svg",type:"image/svg+xml"}],shortcut:"/favicon.svg",apple:"/favicon.svg"}};function h({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:n().className,children:(0,s.jsx)(u.LocaleProvider,{children:(0,s.jsx)(a.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!0,children:(0,s.jsx)(c.ToastProvider,{children:(0,s.jsx)(m.AutoErrorProvider,{children:(0,s.jsx)(f.SWRProvider,{children:(0,s.jsxs)(i.AuthProvider,{children:[(0,s.jsx)("div",{className:"min-h-screen flex flex-col antialiased bg-background",children:e}),(0,s.jsx)(d.Toaster,{})]})})})})})})})})}},95397:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var s=r(60687);r(43210);var o=r(10218);function n({children:e,...t}){return(0,s.jsx)(o.N,{...t,children:e})}}};