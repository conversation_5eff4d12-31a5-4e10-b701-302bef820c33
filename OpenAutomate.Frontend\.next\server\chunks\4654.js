"use strict";exports.id=4654,exports.ids=[4654],exports.modules={7:(e,t,n)=>{n.d(t,{q7:()=>S,bL:()=>O,RG:()=>R});var r=n(43210);function o(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var i=n(58571);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}var u=n(60687),s=n(19783);n(51215);var c=Symbol("radix.slottable");function f(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,l;let u=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?a(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),a=l.find(f);if(a){let e=a.props.children,o=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,u.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),p=n(67427),m=r.createContext(void 0),h="rovingFocusGroup.onEntryFocus",g={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[v,w,x]=(0,i.N)(y),[b,R]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;n=[...n,o];let a=t=>{let{scope:n,children:o,...a}=t,s=n?.[e]?.[l]||i,c=r.useMemo(()=>a,Object.values(a));return(0,u.jsx)(s.Provider,{value:c,children:o})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=r.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(y,[x]),[C,E]=b(y),N=r.forwardRef((e,t)=>(0,u.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(j,{...e,ref:t})})}));N.displayName=y;var j=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:s,currentTabStopId:c,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:v,onEntryFocus:x,preventScrollOnEntryFocus:b=!1,...R}=e,E=r.useRef(null),N=function(...e){return r.useCallback(a(...e),e)}(t,E),j=function(e){let t=r.useContext(m);return e||t||"ltr"}(s),[M,P]=(0,p.i)({prop:c,defaultProp:f??null,onChange:v,caller:y}),[_,O]=r.useState(!1),S=function(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}(x),D=w(n),T=r.useRef(!1),[k,I]=r.useState(0);return r.useEffect(()=>{let e=E.current;if(e)return e.addEventListener(h,S),()=>e.removeEventListener(h,S)},[S]),(0,u.jsx)(C,{scope:n,orientation:i,dir:j,loop:l,currentTabStopId:M,onItemFocus:r.useCallback(e=>P(e),[P]),onItemShiftTab:r.useCallback(()=>O(!0),[]),onFocusableItemAdd:r.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>I(e=>e-1),[]),children:(0,u.jsx)(d.div,{tabIndex:_||0===k?-1:0,"data-orientation":i,...R,ref:N,style:{outline:"none",...e.style},onMouseDown:o(e.onMouseDown,()=>{T.current=!0}),onFocus:o(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!_){let t=new CustomEvent(h,g);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),b)}}T.current=!1}),onBlur:o(e.onBlur,()=>O(!1))})})}),M="RovingFocusGroupItem",P=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:l=!1,tabStopId:a,children:c,...f}=e,p=(0,s.B)(),m=a||p,h=E(M,n),g=h.currentTabStopId===m,y=w(n),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:R}=h;return r.useEffect(()=>{if(i)return x(),()=>b()},[i,x,b]),(0,u.jsx)(v.ItemSlot,{scope:n,id:m,focusable:i,active:l,children:(0,u.jsx)(d.span,{tabIndex:g?0:-1,"data-orientation":h.orientation,...f,ref:t,onMouseDown:o(e.onMouseDown,e=>{i?h.onItemFocus(m):e.preventDefault()}),onFocus:o(e.onFocus,()=>h.onItemFocus(m)),onKeyDown:o(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>A(n))}}),children:"function"==typeof c?c({isCurrentTabStop:g,hasTabStop:null!=R}):c})})});P.displayName=M;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var O=N,S=P},4654:(e,t,n)=>{n.d(t,{H_:()=>tr,UC:()=>e9,ty:()=>eq,YJ:()=>te,q7:()=>tn,VF:()=>to,JU:()=>tt,ZL:()=>e8,bL:()=>e4,wv:()=>ti,l9:()=>e6});var r=n(43210);function o(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}var a=n(60687),u=n(67427),s=n(51215),c=Symbol("radix.slottable");function f(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,a;let u=(i=n,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?l(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),u=l.find(f);if(u){let e=u.props.children,o=l.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function p(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var m=n(58571);function h(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function g(...e){return t=>{let n=!1,r=e.map(e=>{let r=h(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():h(e[t],null)}}}}function y(...e){return r.useCallback(g(...e),e)}var v=r.createContext(void 0),w=n(42360),x=n(1359),b=n(40481),R=n(19783),C=n(96348),E=n(96929),N=globalThis?.document?r.useLayoutEffect:()=>{},j=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef({}),a=r.useRef(e),u=r.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=M(l.current);u.current="mounted"===s?e:"none"},[s]),N(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=M(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),N(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=M(l.current).includes(n.animationName);if(n.target===o&&r&&(c("ANIMATION_END"),!a.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(u.current=M(l.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),l=y(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||o.isPresent?r.cloneElement(i,{ref:l}):null};function M(e){return e?.animationName||"none"}function P(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,l;let a=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?g(t,a):a),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),u=l.find(A);if(u){let e=u.props.children,o=l.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}j.displayName="Presence";var _=Symbol("radix.slottable");function A(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===_}var O=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=P(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),S=n(7);function D(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}var T=n(63376),k=n(42247),I=["Enter"," "],L=["ArrowUp","PageDown","End"],F=["ArrowDown","PageUp","Home",...L],$={ltr:[...I,"ArrowRight"],rtl:[...I,"ArrowLeft"]},W={ltr:["ArrowLeft"],rtl:["ArrowRight"]},V="Menu",[B,K,H]=(0,m.N)(V),[U,z]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;n=[...n,o];let u=t=>{let{scope:n,children:o,...u}=t,s=n?.[e]?.[l]||i,c=r.useMemo(()=>u,Object.values(u));return(0,a.jsx)(s.Provider,{value:c,children:o})};return u.displayName=t+"Provider",[u,function(n,a){let u=a?.[e]?.[l]||i,s=r.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(V,[H,C.Bk,S.RG]),G=(0,C.Bk)(),X=(0,S.RG)(),[Z,q]=U(V),[Y,J]=U(V),Q=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:l,modal:u=!0}=e,s=G(t),[c,f]=r.useState(null),d=r.useRef(!1),p=D(l),m=function(e){let t=r.useContext(v);return e||t||"ltr"}(i);return r.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,a.jsx)(C.bL,{...s,children:(0,a.jsx)(Z,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:f,children:(0,a.jsx)(Y,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:m,modal:u,children:o})})})};Q.displayName=V;var ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=G(n);return(0,a.jsx)(C.Mz,{...o,...r,ref:t})});ee.displayName="MenuAnchor";var et="MenuPortal",[en,er]=U(et,{forceMount:void 0}),eo=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=q(et,t);return(0,a.jsx)(en,{scope:t,forceMount:n,children:(0,a.jsx)(j,{present:n||i.open,children:(0,a.jsx)(E.Z,{asChild:!0,container:o,children:r})})})};eo.displayName=et;var ei="MenuContent",[el,ea]=U(ei),eu=r.forwardRef((e,t)=>{let n=er(ei,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=q(ei,e.__scopeMenu),l=J(ei,e.__scopeMenu);return(0,a.jsx)(B.Provider,{scope:e.__scopeMenu,children:(0,a.jsx)(j,{present:r||i.open,children:(0,a.jsx)(B.Slot,{scope:e.__scopeMenu,children:l.modal?(0,a.jsx)(es,{...o,ref:t}):(0,a.jsx)(ec,{...o,ref:t})})})})}),es=r.forwardRef((e,t)=>{let n=q(ei,e.__scopeMenu),o=r.useRef(null),i=y(t,o);return r.useEffect(()=>{let e=o.current;if(e)return(0,T.Eq)(e)},[]),(0,a.jsx)(ed,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:p(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ec=r.forwardRef((e,t)=>{let n=q(ei,e.__scopeMenu);return(0,a.jsx)(ed,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),ef=P("MenuContent.ScrollLock"),ed=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:o=!1,trapFocus:i,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:s,onEntryFocus:c,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:m,onInteractOutside:h,onDismiss:g,disableOutsideScroll:v,...R}=e,E=q(ei,n),N=J(ei,n),j=G(n),M=X(n),P=K(n),[_,A]=r.useState(null),O=r.useRef(null),D=y(t,O,E.onContentChange),T=r.useRef(0),I=r.useRef(""),$=r.useRef(0),W=r.useRef(null),V=r.useRef("right"),B=r.useRef(0),H=v?k.A:r.Fragment,U=e=>{let t=I.current+e,n=P().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(n.map(e=>e.textValue),t,o),l=n.find(e=>e.textValue===i)?.ref.current;(function e(t){I.current=t,window.clearTimeout(T.current),""!==t&&(T.current=window.setTimeout(()=>e(""),1e3))})(t),l&&setTimeout(()=>l.focus())};r.useEffect(()=>()=>window.clearTimeout(T.current),[]),(0,x.Oh)();let z=r.useCallback(e=>V.current===W.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,f=a.y;s>r!=f>r&&n<(c-u)*(r-s)/(f-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,W.current?.area),[]);return(0,a.jsx)(el,{scope:n,searchRef:I,onItemEnter:r.useCallback(e=>{z(e)&&e.preventDefault()},[z]),onItemLeave:r.useCallback(e=>{z(e)||(O.current?.focus(),A(null))},[z]),onTriggerLeave:r.useCallback(e=>{z(e)&&e.preventDefault()},[z]),pointerGraceTimerRef:$,onPointerGraceIntentChange:r.useCallback(e=>{W.current=e},[]),children:(0,a.jsx)(H,{...v?{as:ef,allowPinchZoom:!0}:void 0,children:(0,a.jsx)(b.n,{asChild:!0,trapped:i,onMountAutoFocus:p(l,e=>{e.preventDefault(),O.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,a.jsx)(w.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:m,onInteractOutside:h,onDismiss:g,children:(0,a.jsx)(S.bL,{asChild:!0,...M,dir:N.dir,orientation:"vertical",loop:o,currentTabStopId:_,onCurrentTabStopIdChange:A,onEntryFocus:p(c,e=>{N.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,a.jsx)(C.UC,{role:"menu","aria-orientation":"vertical","data-state":eF(E.open),"data-radix-menu-content":"",dir:N.dir,...j,...R,ref:D,style:{outline:"none",...R.style},onKeyDown:p(R.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&U(e.key));let o=O.current;if(e.target!==o||!F.includes(e.key))return;e.preventDefault();let i=P().filter(e=>!e.disabled).map(e=>e.ref.current);L.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:p(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(T.current),I.current="")}),onPointerMove:p(e.onPointerMove,eV(e=>{let t=e.target,n=B.current!==e.clientX;e.currentTarget.contains(t)&&n&&(V.current=e.clientX>B.current?"right":"left",B.current=e.clientX)}))})})})})})})});eu.displayName=ei;var ep=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,a.jsx)(O.div,{role:"group",...r,ref:t})});ep.displayName="MenuGroup";var em=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,a.jsx)(O.div,{...r,ref:t})});em.displayName="MenuLabel";var eh="MenuItem",eg="menu.itemSelect",ey=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:o,...i}=e,l=r.useRef(null),u=J(eh,e.__scopeMenu),c=ea(eh,e.__scopeMenu),f=y(t,l),d=r.useRef(!1);return(0,a.jsx)(ev,{...i,ref:f,disabled:n,onClick:p(e.onClick,()=>{let e=l.current;if(!n&&e){let t=new CustomEvent(eg,{bubbles:!0,cancelable:!0});e.addEventListener(eg,e=>o?.(e),{once:!0}),function(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}(e,t),t.defaultPrevented?d.current=!1:u.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),d.current=!0},onPointerUp:p(e.onPointerUp,e=>{d.current||e.currentTarget?.click()}),onKeyDown:p(e.onKeyDown,e=>{let t=""!==c.searchRef.current;!n&&(!t||" "!==e.key)&&I.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ey.displayName=eh;var ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:o=!1,textValue:i,...l}=e,u=ea(eh,n),s=X(n),c=r.useRef(null),f=y(t,c),[d,m]=r.useState(!1),[h,g]=r.useState("");return r.useEffect(()=>{let e=c.current;e&&g((e.textContent??"").trim())},[l.children]),(0,a.jsx)(B.ItemSlot,{scope:n,disabled:o,textValue:i??h,children:(0,a.jsx)(S.q7,{asChild:!0,...s,focusable:!o,children:(0,a.jsx)(O.div,{role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...l,ref:f,onPointerMove:p(e.onPointerMove,eV(e=>{o?u.onItemLeave(e):(u.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:p(e.onPointerLeave,eV(e=>u.onItemLeave(e))),onFocus:p(e.onFocus,()=>m(!0)),onBlur:p(e.onBlur,()=>m(!1))})})})}),ew=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,a.jsx)(eM,{scope:e.__scopeMenu,checked:n,children:(0,a.jsx)(ey,{role:"menuitemcheckbox","aria-checked":e$(n)?"mixed":n,...o,ref:t,"data-state":eW(n),onSelect:p(o.onSelect,()=>r?.(!!e$(n)||!n),{checkForDefaultPrevented:!1})})})});ew.displayName="MenuCheckboxItem";var ex="MenuRadioGroup",[eb,eR]=U(ex,{value:void 0,onValueChange:()=>{}}),eC=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=D(r);return(0,a.jsx)(eb,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,a.jsx)(ep,{...o,ref:t})})});eC.displayName=ex;var eE="MenuRadioItem",eN=r.forwardRef((e,t)=>{let{value:n,...r}=e,o=eR(eE,e.__scopeMenu),i=n===o.value;return(0,a.jsx)(eM,{scope:e.__scopeMenu,checked:i,children:(0,a.jsx)(ey,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":eW(i),onSelect:p(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});eN.displayName=eE;var ej="MenuItemIndicator",[eM,eP]=U(ej,{checked:!1}),e_=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eP(ej,n);return(0,a.jsx)(j,{present:r||e$(i.checked)||!0===i.checked,children:(0,a.jsx)(O.span,{...o,ref:t,"data-state":eW(i.checked)})})});e_.displayName=ej;var eA=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,a.jsx)(O.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eA.displayName="MenuSeparator";var eO=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=G(n);return(0,a.jsx)(C.i3,{...o,...r,ref:t})});eO.displayName="MenuArrow";var[eS,eD]=U("MenuSub"),eT="MenuSubTrigger",ek=r.forwardRef((e,t)=>{let n=q(eT,e.__scopeMenu),o=J(eT,e.__scopeMenu),i=eD(eT,e.__scopeMenu),l=ea(eT,e.__scopeMenu),u=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=l,f={__scopeMenu:e.__scopeMenu},d=r.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return r.useEffect(()=>d,[d]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,a.jsx)(ee,{asChild:!0,...f,children:(0,a.jsx)(ev,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":i.contentId,"data-state":eF(n.open),...e,ref:g(t,i.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:p(e.onPointerMove,eV(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),d()},100))})),onPointerLeave:p(e.onPointerLeave,eV(e=>{d();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:p(e.onKeyDown,t=>{let r=""!==l.searchRef.current;!e.disabled&&(!r||" "!==t.key)&&$[o.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});ek.displayName=eT;var eI="MenuSubContent",eL=r.forwardRef((e,t)=>{let n=er(ei,e.__scopeMenu),{forceMount:o=n.forceMount,...i}=e,l=q(ei,e.__scopeMenu),u=J(ei,e.__scopeMenu),s=eD(eI,e.__scopeMenu),c=r.useRef(null),f=y(t,c);return(0,a.jsx)(B.Provider,{scope:e.__scopeMenu,children:(0,a.jsx)(j,{present:o||l.open,children:(0,a.jsx)(B.Slot,{scope:e.__scopeMenu,children:(0,a.jsx)(ed,{id:s.contentId,"aria-labelledby":s.triggerId,...i,ref:f,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&c.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:p(e.onFocusOutside,e=>{e.target!==s.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:p(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:p(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=W[u.dir].includes(e.key);t&&n&&(l.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eF(e){return e?"open":"closed"}function e$(e){return"indeterminate"===e}function eW(e){return e$(e)?"indeterminate":e?"checked":"unchecked"}function eV(e){return t=>"mouse"===t.pointerType?e(t):void 0}eL.displayName=eI;var eB="DropdownMenu",[eK,eH]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;n=[...n,o];let u=t=>{let{scope:n,children:o,...u}=t,s=n?.[e]?.[l]||i,c=r.useMemo(()=>u,Object.values(u));return(0,a.jsx)(s.Provider,{value:c,children:o})};return u.displayName=t+"Provider",[u,function(n,a){let u=a?.[e]?.[l]||i,s=r.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(eB,[z]),eU=z(),[ez,eG]=eK(eB),eX=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:l,onOpenChange:s,modal:c=!0}=e,f=eU(t),d=r.useRef(null),[p,m]=(0,u.i)({prop:i,defaultProp:l??!1,onChange:s,caller:eB});return(0,a.jsx)(ez,{scope:t,triggerId:(0,R.B)(),triggerRef:d,contentId:(0,R.B)(),open:p,onOpenChange:m,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),modal:c,children:(0,a.jsx)(Q,{...f,open:p,onOpenChange:m,dir:o,modal:c,children:n})})};eX.displayName=eB;var eZ="DropdownMenuTrigger",eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...i}=e,u=eG(eZ,n),s=eU(n);return(0,a.jsx)(ee,{asChild:!0,...s,children:(0,a.jsx)(d.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...i,ref:l(t,u.triggerRef),onPointerDown:o(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:o(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eq.displayName=eZ;var eY=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eU(t);return(0,a.jsx)(eo,{...r,...n})};eY.displayName="DropdownMenuPortal";var eJ="DropdownMenuContent",eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,l=eG(eJ,n),u=eU(n),s=r.useRef(!1);return(0,a.jsx)(eu,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...i,ref:t,onCloseAutoFocus:o(e.onCloseAutoFocus,e=>{s.current||l.triggerRef.current?.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:o(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!l.modal||r)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eQ.displayName=eJ;var e0=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(ep,{...o,...r,ref:t})});e0.displayName="DropdownMenuGroup";var e1=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(em,{...o,...r,ref:t})});e1.displayName="DropdownMenuLabel";var e2=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(ey,{...o,...r,ref:t})});e2.displayName="DropdownMenuItem";var e5=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(ew,{...o,...r,ref:t})});e5.displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(eC,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(eN,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem";var e3=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(e_,{...o,...r,ref:t})});e3.displayName="DropdownMenuItemIndicator";var e7=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(eA,{...o,...r,ref:t})});e7.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(eO,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(ek,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eU(n);return(0,a.jsx)(eL,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e4=eX,e6=eq,e8=eY,e9=eQ,te=e0,tt=e1,tn=e2,tr=e5,to=e3,ti=e7},58571:(e,t,n)=>{n.d(t,{N:()=>f});var r=n(43210),o=n(60687);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function a(...e){return r.useCallback(l(...e),e)}function u(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,a;let u=(i=n,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?l(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...l}=e,a=r.Children.toArray(i),u=a.find(c);if(u){let e=u.props.children,i=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...l,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var s=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}function f(e){let t=e+"CollectionProvider",[n,i]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[a]||l,c=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}(t),[l,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:n}=e,i=r.useRef(null),a=r.useRef(new Map).current;return(0,o.jsx)(l,{scope:t,itemMap:a,collectionRef:i,children:n})};c.displayName=t;let f=e+"CollectionSlot",d=u(f),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=a(t,s(f,n).collectionRef);return(0,o.jsx)(d,{ref:i,children:r})});p.displayName=f;let m=e+"CollectionItemSlot",h="data-radix-collection-item",g=u(m),y=r.forwardRef((e,t)=>{let{scope:n,children:i,...l}=e,u=r.useRef(null),c=a(t,u),f=s(m,n);return r.useEffect(()=>(f.itemMap.set(u,{ref:u,...l}),()=>void f.itemMap.delete(u))),(0,o.jsx)(g,{[h]:"",ref:c,children:i})});return y.displayName=m,[{Provider:c,Slot:p,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},i]}var d=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=m(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function m(e){return e!=e||0===e?0:Math.trunc(e)}},88805:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=globalThis?.document?r.useLayoutEffect:()=>{};function i(e){let[t,n]=r.useState(void 0);return o(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},96348:(e,t,n)=>{n.d(t,{Mz:()=>e2,i3:()=>e3,UC:()=>e5,bL:()=>e1,Bk:()=>e$});var r=n(43210);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function R(e,t,n){let r,{reference:o,floating:i}=e,l=y(t),a=h(y(t)),u=g(a),s=p(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,v=o[u]/2-i[u]/2;switch(s){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[a]-=v*(n&&c?-1:1);break;case"end":r[a]+=v*(n&&c?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=R(s,r,u),d=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:g,y:y,data:v,reset:w}=await h({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=y?y:f,p={...p,[i]:{...p[i],...v}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:f}=R(s,d,u)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function E(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=d(t,e),h=x(m),g=a[p?"floating"===f?"reference":"floating":f],y=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),v="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),R=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},C=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:u}):v);return{top:(y.top-C.top+h.top)/R.y,bottom:(C.bottom-y.bottom+h.bottom)/R.y,left:(y.left-C.left+h.left)/R.x,right:(C.right-y.right+h.right)/R.x}}function N(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function j(e){return o.some(t=>e[t]>=0)}async function M(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=m(n),u="y"===y(n),s=["left","top"].includes(l)?-1:1,c=i&&u?-1:1,f=d(t,e),{mainAxis:h,crossAxis:g,alignmentAxis:v}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof v&&(g="end"===a?-1*v:v),u?{x:g*c,y:h*s}:{x:h*s,y:g*c}}function P(){return"undefined"!=typeof window}function _(e){return S(e)?(e.nodeName||"").toLowerCase():"#document"}function A(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function O(e){var t;return null==(t=(S(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function S(e){return!!P()&&(e instanceof Node||e instanceof A(e).Node)}function D(e){return!!P()&&(e instanceof Element||e instanceof A(e).Element)}function T(e){return!!P()&&(e instanceof HTMLElement||e instanceof A(e).HTMLElement)}function k(e){return!!P()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof A(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=V(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function L(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=$(),n=D(e)?V(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function $(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function W(e){return["html","body","#document"].includes(_(e))}function V(e){return A(e).getComputedStyle(e)}function B(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function K(e){if("html"===_(e))return e;let t=e.assignedSlot||e.parentNode||k(e)&&e.host||O(e);return k(t)?t.host:t}function H(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=K(t);return W(n)?t.ownerDocument?t.ownerDocument.body:t.body:T(n)&&I(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=A(o);if(i){let e=U(l);return t.concat(l,l.visualViewport||[],I(o)?o:[],e&&n?H(e):[])}return t.concat(o,H(o,[],n))}function U(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function z(e){let t=V(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=T(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function G(e){return D(e)?e:e.contextElement}function X(e){let t=G(e);if(!T(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=z(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let Z=s(0);function q(e){let t=A(e);return $()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Z}function Y(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=G(e),a=s(1);t&&(r?D(r)&&(a=X(r)):a=X(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===A(l))&&o)?q(l):s(0),c=(i.left+u.x)/a.x,f=(i.top+u.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=A(l),t=r&&D(r)?A(r):r,n=e,o=U(n);for(;o&&r&&t!==n;){let e=X(o),t=o.getBoundingClientRect(),r=V(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,f*=e.y,d*=e.x,p*=e.y,c+=i,f+=l,o=U(n=A(o))}}return b({width:d,height:p,x:c,y:f})}function J(e,t){let n=B(e).scrollLeft;return t?t.left+n:Y(O(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=A(e),r=O(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=$();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=O(e),n=B(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===V(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(O(e));else if(D(t))r=function(e,t){let n=Y(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=T(e)?X(e):s(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=q(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===V(e).position}function en(e,t){if(!T(e)||"fixed"===V(e).position)return null;if(t)return t(e);let n=e.offsetParent;return O(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=A(e);if(L(e))return n;if(!T(e)){let t=K(e);for(;t&&!W(t);){if(D(t)&&!et(t))return t;t=K(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(_(r))&&et(r);)r=en(r,t);return r&&W(r)&&et(r)&&!F(r)?n:r||function(e){let t=K(e);for(;T(t)&&!W(t);){if(F(t))return t;if(L(t))break;t=K(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=T(t),o=O(t),i="fixed"===n,l=Y(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!i){if(("body"!==_(t)||I(o))&&(a=B(t)),r){let e=Y(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o))}let c=!o||r||i?s(0):Q(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=O(r),a=!!t&&L(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=s(1),f=s(0),d=T(r);if((d||!d&&!i)&&(("body"!==_(r)||I(l))&&(u=B(r)),T(r))){let e=Y(r);c=X(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||i?s(0):Q(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+f.x+p.x,y:n.y*c.y-u.scrollTop*c.y+f.y+p.y}},getDocumentElement:O,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?L(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=H(e,[],!1).filter(e=>D(e)&&"body"!==_(e)),o=null,i="fixed"===V(e).position,l=i?K(e):e;for(;D(l)&&!W(l);){let t=V(l),n=F(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(l)&&!n&&function e(t,n){let r=K(t);return!(r===n||!D(r)||W(r))&&("fixed"===V(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=K(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],s=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,u,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=z(e);return{width:t,height:n}},getScale:X,isElement:D,isRTL:function(e){return"rtl"===V(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:s,middlewareData:c}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let v=x(p),w={x:n,y:r},b=h(y(o)),R=g(b),C=await u.getDimensions(f),E="y"===b,N=E?"clientHeight":"clientWidth",j=a.reference[R]+a.reference[b]-w[b]-a.floating[R],M=w[b]-a.reference[b],P=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),_=P?P[N]:0;_&&await (null==u.isElement?void 0:u.isElement(P))||(_=s.floating[N]||a.floating[R]);let A=_/2-C[R]/2-1,O=i(v[E?"top":"left"],A),S=i(v[E?"bottom":"right"],A),D=_-C[R]-S,T=_/2-C[R]/2+(j/2-M/2),k=l(O,i(T,D)),I=!c.arrow&&null!=m(o)&&T!==k&&a.reference[R]/2-(T<O?O:S)-C[R]/2<0,L=I?T<O?T-O:T-D:0;return{[b]:w[b]+L,data:{[b]:k,centerOffset:T-k-L,...I&&{alignmentOffset:L}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var es=n(51215),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function em(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await M(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),f={x:n,y:r},m=await E(t,c),g=y(p(o)),v=h(g),w=f[v],x=f[g];if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+m[e],r=w-m[t];w=l(n,i(w,r))}if(u){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+m[e],r=x-m[t];x=l(n,i(x,r))}let b=s.fn({...t,[v]:w,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[v]:a,[g]:u}}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=d(e,t),c={x:n,y:r},f=y(o),m=h(f),g=c[m],v=c[f],w=d(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+x.mainAxis,n=i.reference[m]+i.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(s){var b,R;let e="y"===m?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[f])||0)+(t?0:x.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(R=l.offset)?void 0:R[f])||0)-(t?x.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[m]:g,[f]:v}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:f,elements:x}=t,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:C,fallbackStrategy:N="bestFit",fallbackAxisSideDirection:j="none",flipAlignment:M=!0,...P}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let _=p(a),A=y(c),O=p(c)===c,S=await (null==f.isRTL?void 0:f.isRTL(x.floating)),D=C||(O||!M?[w(c)]:function(e){let t=w(e);return[v(e),t,v(t)]}(c)),T="none"!==j;!C&&T&&D.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(v)))),i}(c,M,j,S));let k=[c,...D],I=await E(t,P),L=[],F=(null==(r=u.flip)?void 0:r.overflows)||[];if(b&&L.push(I[_]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(y(e)),i=g(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(a,s,S);L.push(I[e[0]],I[e[1]])}if(F=[...F,{placement:a,overflows:L}],!L.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=k[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(N){case"bestFit":{let e=null==(l=F.filter(e=>{if(T){let t=y(e.placement);return t===A||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a;let{placement:u,rects:s,platform:c,elements:f}=t,{apply:h=()=>{},...g}=d(e,t),v=await E(t,g),w=p(u),x=m(u),b="y"===y(u),{width:R,height:C}=s.floating;"top"===w||"bottom"===w?(o=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(f.floating))?"start":"end")?"left":"right"):(a=w,o="end"===x?"top":"bottom");let N=C-v.top-v.bottom,j=R-v.left-v.right,M=i(C-v[o],N),P=i(R-v[a],j),_=!t.middlewareData.shift,A=M,O=P;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(O=j),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(A=N),_&&!x){let e=l(v.left,0),t=l(v.right,0),n=l(v.top,0),r=l(v.bottom,0);b?O=R-2*(0!==e||0!==t?e+t:l(v.left,v.right)):A=C-2*(0!==n||0!==r?n+r:l(v.top,v.bottom))}await h({...t,availableWidth:O,availableHeight:A});let S=await c.getDimensions(f.floating);return R!==S.width||C!==S.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=N(await E(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:j(e)}}}case"escaped":{let e=N(await E(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:j(e)}}}default:return{}}}}}(e),options:[e,t]}),eR=(e,t)=>({...eh(e),options:[e,t]});function eC(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var eE=n(60687),eN=Symbol("radix.slottable");function ej(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===eN}var eM=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,l;let a=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=eC(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():eC(e[t],null)}}}}(t,a):a),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),a=l.find(ej);if(a){let e=a.props.children,o=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,eE.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,eE.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,eE.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),eP=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eE.jsx)(eM.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eE.jsx)("polygon",{points:"0,0 30,0 15,10"})})});function e_(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function eA(...e){return t=>{let n=!1,r=e.map(e=>{let r=e_(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():e_(e[t],null)}}}}function eO(...e){return r.useCallback(eA(...e),e)}eP.displayName="Arrow";var eS=Symbol("radix.slottable");function eD(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===eS}var eT=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,l;let a=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?eA(t,a):a),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,l=r.Children.toArray(o),a=l.find(eD);if(a){let e=a.props.children,o=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,eE.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,eE.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,eE.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),ek=globalThis?.document?r.useLayoutEffect:()=>{},eI=n(88805),eL="Popper",[eF,e$]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;n=[...n,o];let a=t=>{let{scope:n,children:o,...a}=t,u=n?.[e]?.[l]||i,s=r.useMemo(()=>a,Object.values(a));return(0,eE.jsx)(u.Provider,{value:s,children:o})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=r.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(eL),[eW,eV]=eF(eL),eB=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eE.jsx)(eW,{scope:t,anchor:o,onAnchorChange:i,children:n})};eB.displayName=eL;var eK="PopperAnchor",eH=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eV(eK,n),a=r.useRef(null),u=eO(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eE.jsx)(eT.div,{...i,ref:u})});eH.displayName=eK;var eU="PopperContent",[ez,eG]=eF(eU),eX=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:s="center",alignOffset:c=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:g=!1,updatePositionStrategy:y="optimized",onPlaced:v,...w}=e,x=eV(eU,n),[b,R]=r.useState(null),C=eO(t,e=>R(e)),[E,N]=r.useState(null),j=(0,eI.X)(E),M=j?.width??0,P=j?.height??0,_="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},A=Array.isArray(p)?p:[p],S=A.length>0,D={padding:_,boundary:A.filter(eJ),altBoundary:S},{refs:T,floatingStyles:k,placement:I,isPositioned:L,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);ef(p,o)||m(o);let[h,g]=r.useState(null),[y,v]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),b=l||h,R=a||y,C=r.useRef(null),E=r.useRef(null),N=r.useRef(f),j=null!=s,M=em(s),P=em(i),_=em(c),A=r.useCallback(()=>{if(!C.current||!E.current)return;let e={placement:t,strategy:n,middleware:p};P.current&&(e.platform=P.current),eu(C.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==_.current};O.current&&!ef(N.current,t)&&(N.current=t,es.flushSync(()=>{d(t)}))})},[p,t,n,P,_]);ec(()=>{!1===c&&N.current.isPositioned&&(N.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let O=r.useRef(!1);ec(()=>(O.current=!0,()=>{O.current=!1}),[]),ec(()=>{if(b&&(C.current=b),R&&(E.current=R),b&&R){if(M.current)return M.current(b,R,A);A()}},[b,R,A,M,j]);let S=r.useMemo(()=>({reference:C,floating:E,setReference:w,setFloating:x}),[w,x]),D=r.useMemo(()=>({reference:b,floating:R}),[b,R]),T=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,f.x),r=ep(D.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:A,refs:S,elements:D,floatingStyles:T}),[f,A,S,D,T])}({strategy:"fixed",placement:o+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=G(e),m=a||s?[...p?H(p):[],...H(t)]:[];m.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,r=null,o=O(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function s(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:g}=d;if(c||t(),!h||!g)return;let y=u(m),v=u(o.clientWidth-(p+h)),w={rootMargin:-y+"px "+-v+"px "+-u(o.clientHeight-(m+g))+"px "+-u(p)+"px",threshold:l(0,i(1,f))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==f){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let v=d?Y(e):null;return d&&function t(){let r=Y(e);v&&!el(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:x.anchor},middleware:[eg({mainAxis:a+P,alignmentAxis:c}),d&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ev():void 0,...D}),d&&ew({...D}),ex({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),E&&eR({element:E,padding:f}),eQ({arrowWidth:M,arrowHeight:P}),g&&eb({strategy:"referenceHidden",...D})]}),[$,W]=e0(I),V=function(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}(v);ek(()=>{L&&V?.()},[L,V]);let B=F.arrow?.x,K=F.arrow?.y,U=F.arrow?.centerOffset!==0,[z,X]=r.useState();return ek(()=>{b&&X(window.getComputedStyle(b).zIndex)},[b]),(0,eE.jsx)("div",{ref:T.setFloating,"data-radix-popper-content-wrapper":"",style:{...k,transform:L?k.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:z,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eE.jsx)(ez,{scope:n,placedSide:$,onArrowChange:N,arrowX:B,arrowY:K,shouldHideArrow:U,children:(0,eE.jsx)(eT.div,{"data-side":$,"data-align":W,...w,ref:C,style:{...w.style,animation:L?void 0:"none"}})})})});eX.displayName=eU;var eZ="PopperArrow",eq={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eG(eZ,n),i=eq[o.placedSide];return(0,eE.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eE.jsx)(eP,{...r,ref:t,style:{...r.style,display:"block"}})})});function eJ(e){return null!==e}eY.displayName=eZ;var eQ=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,s]=e0(n),c={start:"0%",center:"50%",end:"100%"}[s],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",m="";return"bottom"===u?(p=i?c:`${f}px`,m=`${-a}px`):"top"===u?(p=i?c:`${f}px`,m=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,m=i?c:`${d}px`):"left"===u&&(p=`${r.floating.width+a}px`,m=i?c:`${d}px`),{data:{x:p,y:m}}}});function e0(e){let[t,n="center"]=e.split("-");return[t,n]}var e1=eB,e2=eH,e5=eX,e3=eY}};